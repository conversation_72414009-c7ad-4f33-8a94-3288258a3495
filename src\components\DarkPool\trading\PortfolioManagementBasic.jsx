import React, { useState, useEffect, useRef } from 'react';
import { 
  TrendingUp, DollarSign, BarChart3, <PERSON>Chart, Target, AlertTriangle, 
  ArrowUpRight, ArrowDownRight, Eye, EyeOff, Settings, Zap, 
  Activity, Bell, RefreshCw, Download, Upload, Calendar, Clock,
  LineChart, Wallet, Shield, Award, TrendingDown
} from 'lucide-react';

const PortfolioManagementBasic = () => {
  const [selectedAsset, setSelectedAsset] = useState('AAPL');
  const [portfolioValue, setPortfolioValue] = useState(2847392.45);
  const [showAdvancedView, setShowAdvancedView] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [notifications, setNotifications] = useState(3);
  const [timeframe, setTimeframe] = useState('1D');
  const animationRef = useRef(null);

  const portfolioAssets = [
    { 
      symbol: 'AAPL', 
      name: 'Apple Inc.', 
      price: 185.25, 
      change: 2.34, 
      changePercent: 1.28, 
      position: 1500, 
      value: 277875, 
      allocation: 9.8,
      sector: 'Technology',
      avgCost: 165.50,
      unrealizedPnL: 29625
    },
    { 
      symbol: 'MSFT', 
      name: 'Microsoft Corp.', 
      price: 412.80, 
      change: -3.45, 
      changePercent: -0.83, 
      position: 800, 
      value: 330240, 
      allocation: 11.6,
      sector: 'Technology',
      avgCost: 385.20,
      unrealizedPnL: 22080
    },
    { 
      symbol: 'GOOGL', 
      name: 'Alphabet Inc.', 
      price: 145.67, 
      change: 4.23, 
      changePercent: 2.99, 
      position: 1200, 
      value: 174804, 
      allocation: 6.1,
      sector: 'Technology',
      avgCost: 138.90,
      unrealizedPnL: 8124
    },
    { 
      symbol: 'NVDA', 
      name: 'NVIDIA Corp.', 
      price: 875.43, 
      change: 15.67, 
      changePercent: 1.82, 
      position: 300, 
      value: 262629, 
      allocation: 9.2,
      sector: 'Technology',
      avgCost: 720.15,
      unrealizedPnL: 46584
    },
    { 
      symbol: 'TSLA', 
      name: 'Tesla Inc.', 
      price: 248.92, 
      change: -8.34, 
      changePercent: -3.24, 
      position: 600, 
      value: 149352, 
      allocation: 5.2,
      sector: 'Automotive',
      avgCost: 275.40,
      unrealizedPnL: -15888
    },
    { 
      symbol: 'AMZN', 
      name: 'Amazon.com Inc.', 
      price: 178.35, 
      change: 2.89, 
      changePercent: 1.65, 
      position: 900, 
      value: 160515, 
      allocation: 5.6,
      sector: 'E-Commerce',
      avgCost: 165.20,
      unrealizedPnL: 11835
    }
  ];

  const performanceMetrics = {
    totalReturn: 12.45,
    dayChange: 8934.23,
    dayChangePercent: 0.31,
    weekChange: -15420.50,
    weekChangePercent: -0.54,
    monthChange: 89234.75,
    monthChangePercent: 3.23,
    yearChange: 245890.30,
    yearChangePercent: 9.47,
    sharpeRatio: 1.47,
    beta: 1.12,
    maxDrawdown: -8.93,
    volatility: 18.45
  };

  const sectors = [
    { name: 'Technology', allocation: 42.1, value: 1199234, color: '#3b82f6' },
    { name: 'Healthcare', allocation: 18.7, value: 532456, color: '#10b981' },
    { name: 'Financial', allocation: 15.2, value: 432891, color: '#f59e0b' },
    { name: 'Consumer', allocation: 12.8, value: 364521, color: '#ef4444' },
    { name: 'Energy', allocation: 7.3, value: 207892, color: '#8b5cf6' },
    { name: 'Other', allocation: 3.9, value: 110398, color: '#6b7280' }
  ];

  const recentTrades = [
    { symbol: 'AAPL', type: 'BUY', quantity: 100, price: 185.25, time: '09:34:12', status: 'Filled' },
    { symbol: 'MSFT', type: 'SELL', quantity: 50, price: 412.80, time: '09:28:45', status: 'Filled' },
    { symbol: 'GOOGL', type: 'BUY', quantity: 200, price: 145.67, time: '09:15:33', status: 'Partial' },
    { symbol: 'NVDA', type: 'SELL', quantity: 25, price: 875.43, time: '08:57:21', status: 'Filled' },
    { symbol: 'TSLA', type: 'BUY', quantity: 150, price: 248.92, time: '08:43:17', status: 'Cancelled' }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setPortfolioValue(prev => prev + (Math.random() - 0.5) * 1000);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    setTimeout(() => setRefreshing(false), 2000);
  };

  const getChangeColor = (change) => {
    if (change > 0) return '#10b981';
    if (change < 0) return '#ef4444';
    return '#6b7280';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Filled': return '#10b981';
      case 'Partial': return '#f59e0b';
      case 'Cancelled': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(value);
  };

  const formatNumber = (value) => {
    if (Math.abs(value) >= 1e9) {
      return (value / 1e9).toFixed(2) + 'B';
    }
    if (Math.abs(value) >= 1e6) {
      return (value / 1e6).toFixed(2) + 'M';
    }
    if (Math.abs(value) >= 1e3) {
      return (value / 1e3).toFixed(2) + 'K';
    }
    return value.toFixed(2);
  };

  return (
    <div style={{
      width: '100%',
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%)',
      color: '#ffffff',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif',
      padding: '20px'
    }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '30px',
        padding: '20px',
        background: 'rgba(15, 23, 42, 0.8)',
        borderRadius: '16px',
        border: '1px solid rgba(51, 65, 85, 0.5)',
        backdropFilter: 'blur(8px)'
      }}>
        <div>
          <h1 style={{
            fontSize: '32px',
            margin: '0 0 8px 0',
            background: 'linear-gradient(135deg, #3b82f6 0%, #10b981 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontWeight: '700'
          }}>
            Portfolio Management
          </h1>
          <p style={{
            fontSize: '16px',
            color: '#94a3b8',
            margin: 0
          }}>
            Real-time portfolio tracking and analytics
          </p>
        </div>
        
        <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
          <button
            onClick={() => setShowAdvancedView(!showAdvancedView)}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '10px 16px',
              backgroundColor: showAdvancedView ? '#3b82f6' : 'rgba(59, 130, 246, 0.1)',
              color: showAdvancedView ? 'white' : '#3b82f6',
              border: '1px solid #3b82f6',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500',
              transition: 'all 0.2s'
            }}
          >
            {showAdvancedView ? <EyeOff size={16} /> : <Eye size={16} />}
            {showAdvancedView ? 'Simple View' : 'Advanced View'}
          </button>
          
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '10px 16px',
              backgroundColor: 'rgba(16, 185, 129, 0.1)',
              color: '#10b981',
              border: '1px solid #10b981',
              borderRadius: '8px',
              cursor: refreshing ? 'not-allowed' : 'pointer',
              fontSize: '14px',
              fontWeight: '500',
              opacity: refreshing ? 0.6 : 1,
              transition: 'all 0.2s'
            }}
          >
            <RefreshCw size={16} style={{ 
              animation: refreshing ? 'spin 1s linear infinite' : 'none' 
            }} />
            Refresh
          </button>
          
          <div style={{
            position: 'relative',
            padding: '10px',
            backgroundColor: 'rgba(251, 191, 36, 0.1)',
            borderRadius: '8px',
            cursor: 'pointer'
          }}>
            <Bell size={20} style={{ color: '#fbbf24' }} />
            {notifications > 0 && (
              <span style={{
                position: 'absolute',
                top: '-2px',
                right: '-2px',
                backgroundColor: '#ef4444',
                color: 'white',
                borderRadius: '50%',
                width: '18px',
                height: '18px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '10px',
                fontWeight: 'bold'
              }}>
                {notifications}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Portfolio Overview */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
        gap: '20px',
        marginBottom: '30px'
      }}>
        {/* Total Portfolio Value */}
        <div style={{
          background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
          borderRadius: '16px',
          padding: '24px',
          border: '1px solid rgba(51, 65, 85, 0.5)',
          position: 'relative',
          overflow: 'hidden'
        }}>
          <div style={{ 
            position: 'absolute',
            top: '0',
            right: '0',
            width: '100px',
            height: '100px',
            background: 'radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%)',
            borderRadius: '50%',
            transform: 'translate(30px, -30px)'
          }} />
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '16px' }}>
            <div style={{
              padding: '8px',
              backgroundColor: 'rgba(59, 130, 246, 0.1)',
              borderRadius: '8px'
            }}>
              <Wallet size={24} style={{ color: '#3b82f6' }} />
            </div>
            <div>
              <h3 style={{ margin: 0, fontSize: '14px', color: '#94a3b8', fontWeight: '500' }}>
                Total Portfolio Value
              </h3>
            </div>
          </div>
          <div style={{ fontSize: '36px', fontWeight: '700', color: 'white', marginBottom: '8px' }}>
            {formatCurrency(portfolioValue)}
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            {performanceMetrics.dayChange >= 0 ? 
              <ArrowUpRight size={16} style={{ color: '#10b981' }} /> :
              <ArrowDownRight size={16} style={{ color: '#ef4444' }} />
            }
            <span style={{
              color: getChangeColor(performanceMetrics.dayChange),
              fontSize: '14px',
              fontWeight: '600'
            }}>
              {formatCurrency(Math.abs(performanceMetrics.dayChange))} 
              ({Math.abs(performanceMetrics.dayChangePercent)}%)
            </span>
            <span style={{ color: '#64748b', fontSize: '14px' }}>today</span>
          </div>
        </div>

        {/* Performance Metrics */}
        {[
          { 
            title: 'Monthly Return', 
            value: performanceMetrics.monthChangePercent, 
            prefix: '', 
            suffix: '%', 
            icon: TrendingUp, 
            color: '#10b981',
            change: performanceMetrics.monthChange
          },
          { 
            title: 'Annual Return', 
            value: performanceMetrics.yearChangePercent, 
            prefix: '', 
            suffix: '%', 
            icon: BarChart3, 
            color: '#3b82f6',
            change: performanceMetrics.yearChange
          },
          { 
            title: 'Sharpe Ratio', 
            value: performanceMetrics.sharpeRatio, 
            prefix: '', 
            suffix: '', 
            icon: Award, 
            color: '#f59e0b',
            change: null
          }
        ].map((metric, index) => (
          <div key={index} style={{
            background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
            borderRadius: '16px',
            padding: '24px',
            border: '1px solid rgba(51, 65, 85, 0.5)',
            position: 'relative',
            overflow: 'hidden'
          }}>
            <div style={{ 
              position: 'absolute',
              top: '0',
              right: '0',
              width: '80px',
              height: '80px',
              background: `radial-gradient(circle, ${metric.color}10 0%, transparent 70%)`,
              borderRadius: '50%',
              transform: 'translate(25px, -25px)'
            }} />
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '16px' }}>
              <div style={{
                padding: '8px',
                backgroundColor: `${metric.color}10`,
                borderRadius: '8px'
              }}>
                <metric.icon size={20} style={{ color: metric.color }} />
              </div>
              <h3 style={{ margin: 0, fontSize: '14px', color: '#94a3b8', fontWeight: '500' }}>
                {metric.title}
              </h3>
            </div>
            <div style={{ fontSize: '28px', fontWeight: '700', color: 'white', marginBottom: '8px' }}>
              {metric.prefix}{metric.value.toFixed(2)}{metric.suffix}
            </div>
            {metric.change && (
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{
                  color: getChangeColor(metric.change),
                  fontSize: '14px',
                  fontWeight: '600'
                }}>
                  {formatCurrency(Math.abs(metric.change))}
                </span>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Main Content Grid */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: showAdvancedView ? '2fr 1fr' : '1fr',
        gap: '24px'
      }}>
        {/* Portfolio Holdings */}
        <div style={{
          background: 'rgba(15, 23, 42, 0.8)',
          borderRadius: '16px',
          border: '1px solid rgba(51, 65, 85, 0.5)',
          overflow: 'hidden'
        }}>
          <div style={{
            padding: '24px',
            borderBottom: '1px solid rgba(51, 65, 85, 0.5)',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <h2 style={{
              margin: 0,
              fontSize: '20px',
              fontWeight: '600',
              color: 'white'
            }}>
              Portfolio Holdings
            </h2>
            <div style={{ display: 'flex', gap: '8px' }}>
              {['1D', '1W', '1M', '3M', '1Y'].map(period => (
                <button
                  key={period}
                  onClick={() => setTimeframe(period)}
                  style={{
                    padding: '6px 12px',
                    backgroundColor: timeframe === period ? '#3b82f6' : 'rgba(59, 130, 246, 0.1)',
                    color: timeframe === period ? 'white' : '#3b82f6',
                    border: 'none',
                    borderRadius: '6px',
                    cursor: 'pointer',
                    fontSize: '12px',
                    fontWeight: '500',
                    transition: 'all 0.2s'
                  }}
                >
                  {period}
                </button>
              ))}
            </div>
          </div>
          
          <div style={{ padding: '0' }}>
            {portfolioAssets.map((asset, index) => (
              <div
                key={asset.symbol}
                onClick={() => setSelectedAsset(asset.symbol)}
                style={{
                  padding: '16px 24px',
                  borderBottom: index < portfolioAssets.length - 1 ? '1px solid rgba(51, 65, 85, 0.3)' : 'none',
                  cursor: 'pointer',
                  backgroundColor: selectedAsset === asset.symbol ? 'rgba(59, 130, 246, 0.05)' : 'transparent',
                  borderLeft: selectedAsset === asset.symbol ? '3px solid #3b82f6' : '3px solid transparent',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  if (selectedAsset !== asset.symbol) {
                    e.currentTarget.style.backgroundColor = 'rgba(51, 65, 85, 0.3)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (selectedAsset !== asset.symbol) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }
                }}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <div style={{ flex: 1 }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '8px' }}>
                      <div style={{
                        width: '32px',
                        height: '32px',
                        borderRadius: '6px',
                        backgroundColor: getChangeColor(asset.change),
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '12px',
                        fontWeight: 'bold',
                        color: 'white'
                      }}>
                        {asset.symbol.slice(0, 2)}
                      </div>
                      <div>
                        <div style={{ fontSize: '16px', fontWeight: '600', color: 'white', marginBottom: '2px' }}>
                          {asset.symbol}
                        </div>
                        <div style={{ fontSize: '12px', color: '#64748b' }}>
                          {asset.name}
                        </div>
                      </div>
                    </div>
                    
                    <div style={{ 
                      display: 'grid', 
                      gridTemplateColumns: 'repeat(auto-fit, minmax(100px, 1fr))', 
                      gap: '12px',
                      fontSize: '12px'
                    }}>
                      <div>
                        <div style={{ color: '#64748b', marginBottom: '2px' }}>Position</div>
                        <div style={{ color: 'white', fontWeight: '500' }}>{asset.position} shares</div>
                      </div>
                      <div>
                        <div style={{ color: '#64748b', marginBottom: '2px' }}>Value</div>
                        <div style={{ color: 'white', fontWeight: '500' }}>{formatCurrency(asset.value)}</div>
                      </div>
                      <div>
                        <div style={{ color: '#64748b', marginBottom: '2px' }}>Allocation</div>
                        <div style={{ color: 'white', fontWeight: '500' }}>{asset.allocation}%</div>
                      </div>
                      {showAdvancedView && (
                        <>
                          <div>
                            <div style={{ color: '#64748b', marginBottom: '2px' }}>Avg Cost</div>
                            <div style={{ color: 'white', fontWeight: '500' }}>{formatCurrency(asset.avgCost)}</div>
                          </div>
                          <div>
                            <div style={{ color: '#64748b', marginBottom: '2px' }}>P&L</div>
                            <div style={{ 
                              color: getChangeColor(asset.unrealizedPnL), 
                              fontWeight: '500' 
                            }}>
                              {formatCurrency(asset.unrealizedPnL)}
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                  
                  <div style={{ textAlign: 'right' }}>
                    <div style={{ fontSize: '18px', fontWeight: '600', color: 'white', marginBottom: '4px' }}>
                      {formatCurrency(asset.price)}
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '4px', justifyContent: 'flex-end' }}>
                      {asset.change >= 0 ? 
                        <ArrowUpRight size={14} style={{ color: '#10b981' }} /> :
                        <ArrowDownRight size={14} style={{ color: '#ef4444' }} />
                      }
                      <span style={{
                        color: getChangeColor(asset.change),
                        fontSize: '14px',
                        fontWeight: '600'
                      }}>
                        {Math.abs(asset.changePercent)}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Side Panel */}
        {showAdvancedView && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
            {/* Sector Allocation */}
            <div style={{
              background: 'rgba(15, 23, 42, 0.8)',
              borderRadius: '16px',
              border: '1px solid rgba(51, 65, 85, 0.5)',
              padding: '24px'
            }}>
              <h3 style={{
                margin: '0 0 20px 0',
                fontSize: '18px',
                fontWeight: '600',
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                <PieChart size={20} style={{ color: '#3b82f6' }} />
                Sector Allocation
              </h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                {sectors.map((sector, index) => (
                  <div key={sector.name} style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                    <div style={{
                      width: '12px',
                      height: '12px',
                      borderRadius: '2px',
                      backgroundColor: sector.color,
                      flexShrink: 0
                    }} />
                    <div style={{ flex: 1 }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '4px' }}>
                        <span style={{ fontSize: '14px', color: 'white', fontWeight: '500' }}>
                          {sector.name}
                        </span>
                        <span style={{ fontSize: '12px', color: '#64748b' }}>
                          {sector.allocation}%
                        </span>
                      </div>
                      <div style={{
                        height: '6px',
                        backgroundColor: 'rgba(51, 65, 85, 0.5)',
                        borderRadius: '3px',
                        overflow: 'hidden'
                      }}>
                        <div style={{
                          height: '100%',
                          backgroundColor: sector.color,
                          width: `${sector.allocation}%`,
                          borderRadius: '3px',
                          transition: 'width 0.5s ease-in-out'
                        }} />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Recent Trades */}
            <div style={{
              background: 'rgba(15, 23, 42, 0.8)',
              borderRadius: '16px',
              border: '1px solid rgba(51, 65, 85, 0.5)',
              overflow: 'hidden'
            }}>
              <div style={{ padding: '20px', borderBottom: '1px solid rgba(51, 65, 85, 0.5)' }}>
                <h3 style={{
                  margin: 0,
                  fontSize: '18px',
                  fontWeight: '600',
                  color: 'white',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <Activity size={20} style={{ color: '#10b981' }} />
                  Recent Trades
                </h3>
              </div>
              <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                {recentTrades.map((trade, index) => (
                  <div key={index} style={{
                    padding: '16px 20px',
                    borderBottom: index < recentTrades.length - 1 ? '1px solid rgba(51, 65, 85, 0.3)' : 'none'
                  }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '8px' }}>
                      <div>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '4px' }}>
                          <span style={{ 
                            fontSize: '14px', 
                            fontWeight: '600', 
                            color: 'white' 
                          }}>
                            {trade.symbol}
                          </span>
                          <span style={{
                            fontSize: '12px',
                            fontWeight: '600',
                            color: trade.type === 'BUY' ? '#10b981' : '#ef4444',
                            backgroundColor: trade.type === 'BUY' ? 'rgba(16, 185, 129, 0.1)' : 'rgba(239, 68, 68, 0.1)',
                            padding: '2px 6px',
                            borderRadius: '4px'
                          }}>
                            {trade.type}
                          </span>
                        </div>
                        <div style={{ fontSize: '12px', color: '#64748b' }}>
                          {trade.quantity} @ {formatCurrency(trade.price)}
                        </div>
                      </div>
                      <div style={{ textAlign: 'right' }}>
                        <div style={{
                          fontSize: '12px',
                          fontWeight: '500',
                          color: getStatusColor(trade.status),
                          marginBottom: '2px'
                        }}>
                          {trade.status}
                        </div>
                        <div style={{ fontSize: '11px', color: '#64748b' }}>
                          {trade.time}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Add CSS animation */}
      <style>
        {`
          @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );
};

export default PortfolioManagementBasic;
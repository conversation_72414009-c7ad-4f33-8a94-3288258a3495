import React, { useState } from 'react';
import { 
  AlertTriangle, Shield, Zap, Eye, Target, Brain,
  Activity, TrendingUp, BarChart3, Clock, Settings,
  Search, Filter, RefreshCw, Download, Users, Globe,
  FileText, Code, BookOpen, Lightbulb
} from 'lucide-react';

const ThreatDetection = () => {
  const [activeTab, setActiveTab] = useState('questions');

  const interviewCategories = [
    {
      category: 'Threat Detection Fundamentals',
      difficulty: 'Entry Level',
      questions: [
        {
          question: 'What is the difference between IOCs (Indicators of Compromise) and IOAs (Indicators of Attack)?',
          answer: 'IOCs are artifacts that indicate a system has been compromised (file hashes, IP addresses, domains). IOAs focus on the tactics, techniques, and procedures (TTPs) used during an attack, providing insight into the intent and progression of an attack.',
          tags: ['IOC', 'IOA', 'Fundamentals']
        },
        {
          question: 'Explain the MITRE ATT&CK framework and its use in threat detection.',
          answer: 'MITRE ATT&CK is a knowledge base of adversary tactics and techniques based on real-world observations. It provides a structured approach to understanding attack patterns, enabling defenders to develop detection rules, hunting hypotheses, and defensive strategies mapped to specific TTPs.',
          tags: ['MITRE ATT&CK', 'Framework', 'TTPs']
        },
        {
          question: 'What are the key components of a threat hunting program?',
          answer: 'Key components include: hypothesis-driven investigations, data collection and analysis capabilities, threat intelligence integration, skilled analysts, defined processes and workflows, appropriate tooling (SIEM, EDR, network monitoring), and metrics to measure effectiveness.',
          tags: ['Threat Hunting', 'Program Management', 'Process']
        }
      ]
    },
    {
      category: 'Detection Engineering',
      difficulty: 'Mid Level',
      questions: [
        {
          question: 'How would you write a Sigma rule to detect lateral movement using WMI?',
          answer: 'Focus on process creation events where parent process is wmiprvse.exe or wmic.exe, command line contains remote execution parameters, and network connections to internal IPs. Include whitelisting for legitimate admin tools.',
          tags: ['Sigma Rules', 'Lateral Movement', 'WMI']
        },
        {
          question: 'Explain the detection logic for identifying living-off-the-land attacks.',
          answer: 'Monitor for legitimate tools used maliciously: PowerShell execution policies, WMI queries, scheduled task creation, process hollowing, DLL injection, and unusual usage patterns of built-in utilities like certutil, bitsadmin, or regsvr32.',
          tags: ['LOLBAS', 'Detection Logic', 'Behavioral Analysis']
        },
        {
          question: 'How do you reduce false positives in threat detection rules?',
          answer: 'Use statistical baselines, implement whitelisting for known-good activity, add contextual enrichment, tune thresholds based on environment, implement time-based analysis, and continuously validate rules against production data.',
          tags: ['False Positives', 'Rule Tuning', 'Optimization']
        }
      ]
    },
    {
      category: 'Advanced Threat Hunting',
      difficulty: 'Senior Level',
      questions: [
        {
          question: 'Describe a hypothesis-driven threat hunt for APT persistence mechanisms.',
          answer: 'Hypothesis: Adversaries establish persistence through registry modifications, scheduled tasks, or service creation. Hunt by analyzing registry autoruns, service creation events, scheduled task modifications, and correlating with network beacons or unusual process activity.',
          tags: ['APT', 'Persistence', 'Hypothesis-driven']
        },
        {
          question: 'How would you detect command and control (C2) communication using DNS?',
          answer: 'Look for DNS queries to suspicious domains, unusual query patterns (length, entropy, frequency), DNS over HTTPS usage, subdomain generation algorithms, and correlate with network baseline and threat intelligence feeds.',
          tags: ['C2', 'DNS', 'Network Analysis']
        },
        {
          question: 'Explain memory forensics techniques for malware detection.',
          answer: 'Use tools like Volatility to analyze memory dumps: process listing, network connections, DLL injection detection, code injection identification, hidden processes, and comparison against known malware signatures in memory.',
          tags: ['Memory Forensics', 'Malware Analysis', 'Volatility']
        }
      ]
    }
  ];

  const practicalScenarios = [
    {
      title: 'APT Lateral Movement Hunt',
      scenario: 'You notice unusual RDP connections from a compromised workstation to multiple servers during off-hours.',
      objectives: ['Identify initial compromise vector', 'Map lateral movement path', 'Assess data access'],
      huntingSteps: [
        'Analyze authentication logs for unusual RDP sessions',
        'Examine process creation events around RDP connections',
        'Check for credential dumping tools (Mimikatz, ProcDump)',
        'Review file access logs on targeted servers',
        'Correlate with network traffic analysis'
      ],
      detectionRules: `// Sigma rule for unusual RDP activity
title: Suspicious RDP Login from Internal Source
logsource:
    category: authentication
detection:
    selection:
        service: 'rdp'
        logon_type: 10
        source_ip|startswith: '10.'
    timeframe: 1h
    condition: selection and count() > 5`
    },
    {
      title: 'Data Exfiltration Detection',
      scenario: 'Large amounts of data are being transferred to external cloud storage services during business hours.',
      objectives: ['Identify exfiltration methods', 'Determine data sensitivity', 'Block ongoing exfiltration'],
      huntingSteps: [
        'Monitor DNS queries to cloud storage domains',
        'Analyze network traffic for large uploads',
        'Check for file compression and encryption activities',
        'Review user behavior analytics',
        'Examine endpoint activity for staging directories'
      ],
      detectionRules: `// KQL query for data exfiltration
NetworkCommunicationEvents
| where RemoteUrl contains "dropbox" or RemoteUrl contains "googledrive"
| where InitiatingProcessFileName != "chrome.exe"
| summarize UploadSize=sum(SentBytes) by DeviceName, InitiatingProcessFileName
| where UploadSize > 100000000  // 100MB threshold`
    }
  ];

  const detectionTechnologies = [
    {
      name: 'YARA Rules',
      type: 'Static Analysis',
      useCase: 'Malware Detection',
      example: 'File pattern matching and signature-based detection',
      code: `rule APT_Malware_Sample {
    meta:
        description = "Detects APT malware variant"
        author = "Threat Hunter"
        date = "2024-01-01"
    strings:
        $hex = { 4D 5A 90 00 03 00 00 00 }
        $api = "CreateRemoteThread"
        $mutex = "Global\\\\MyMutex123"
    condition:
        $hex at 0 and ($api or $mutex)
}`
    },
    {
      name: 'Sigma Rules',
      type: 'Log Analysis',
      useCase: 'Behavioral Detection',
      example: 'Converting detection logic to multiple SIEM formats',
      code: `title: PowerShell Empire Detection
logsource:
    product: windows
    service: powershell
detection:
    selection:
        EventID: 4103
        Payload|contains|all:
            - 'System.Management.Automation'
            - 'DownloadString'
    condition: selection`
    },
    {
      name: 'KQL Queries',
      type: 'Hunting',
      useCase: 'Threat Hunting',
      example: 'Microsoft Sentinel and Defender hunting queries',
      code: `// Hunt for living-off-the-land techniques
DeviceProcessEvents
| where ProcessCommandLine contains "certutil"
| where ProcessCommandLine contains "-urlcache"
| project Timestamp, DeviceName, ProcessCommandLine
| limit 100`
    }
  ];

  const getDifficultyColor = (difficulty) => {
    switch(difficulty) {
      case 'Entry Level': return '#10b981';
      case 'Mid Level': return '#f59e0b';
      case 'Senior Level': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const renderTabNavigation = () => (
    <div style={{
      display: 'flex',
      gap: '8px',
      marginBottom: '24px',
      borderBottom: '1px solid rgba(51, 65, 85, 0.5)',
      paddingBottom: '16px'
    }}>
      {[
        { key: 'questions', label: 'Interview Questions', icon: BookOpen },
        { key: 'scenarios', label: 'Practical Scenarios', icon: Target },
        { key: 'rules', label: 'Detection Rules', icon: Code },
        { key: 'intel', label: 'Threat Intelligence', icon: Brain }
      ].map((tab) => {
        const IconComponent = tab.icon;
        return (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key)}
            style={{
              padding: '8px 16px',
              borderRadius: '8px',
              border: 'none',
              backgroundColor: activeTab === tab.key ? 'rgba(59, 130, 246, 0.2)' : 'transparent',
              color: activeTab === tab.key ? '#60a5fa' : '#94a3b8',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              fontSize: '14px',
              fontWeight: '500',
              transition: 'all 0.2s ease'
            }}
          >
            <IconComponent size={16} />
            {tab.label}
          </button>
        );
      })}
    </div>
  );

  return (
    <div style={{
      width: '100%',
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%)',
      color: '#ffffff',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif',
      padding: '20px'
    }}>
      <div style={{
        padding: '24px',
        background: 'rgba(15, 23, 42, 0.8)',
        borderRadius: '16px',
        border: '1px solid rgba(51, 65, 85, 0.5)',
        marginBottom: '24px'
      }}>
        <h1 style={{
          fontSize: '36px',
          margin: '0 0 8px 0',
          background: 'linear-gradient(135deg, #ef4444 0%, #f59e0b 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          fontWeight: '700'
        }}>
          Threat Detection & Hunting Interview Prep
        </h1>
        <p style={{
          fontSize: '16px',
          color: '#94a3b8',
          margin: 0
        }}>
          Master threat hunting methodologies, detection engineering, and APT analysis for cybersecurity interviews
        </p>
      </div>

      {renderTabNavigation()}

      {activeTab === 'questions' && (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
          {interviewCategories.map((category, idx) => (
            <div key={idx} style={{
              background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
              borderRadius: '16px',
              border: '1px solid rgba(51, 65, 85, 0.5)',
              padding: '24px'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
                <h2 style={{ fontSize: '24px', fontWeight: '700', color: 'white', margin: 0 }}>
                  {category.category}
                </h2>
                <span style={{
                  fontSize: '12px',
                  padding: '4px 8px',
                  backgroundColor: `${getDifficultyColor(category.difficulty)}20`,
                  color: getDifficultyColor(category.difficulty),
                  borderRadius: '6px',
                  fontWeight: '600'
                }}>
                  {category.difficulty}
                </span>
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                {category.questions.map((qa, qaIdx) => (
                  <div key={qaIdx} style={{
                    padding: '16px',
                    background: 'rgba(30, 41, 59, 0.3)',
                    borderRadius: '12px',
                    border: '1px solid rgba(51, 65, 85, 0.3)'
                  }}>
                    <h4 style={{ 
                      fontSize: '16px', 
                      fontWeight: '600', 
                      color: '#60a5fa', 
                      marginBottom: '12px',
                      lineHeight: '1.4'
                    }}>
                      Q: {qa.question}
                    </h4>
                    <p style={{ 
                      fontSize: '14px', 
                      color: '#d1d5db', 
                      lineHeight: '1.6',
                      marginBottom: '12px' 
                    }}>
                      <strong style={{ color: '#10b981' }}>A:</strong> {qa.answer}
                    </p>
                    <div style={{ display: 'flex', gap: '6px', flexWrap: 'wrap' }}>
                      {qa.tags.map((tag, tagIdx) => (
                        <span key={tagIdx} style={{
                          fontSize: '10px',
                          padding: '2px 6px',
                          backgroundColor: 'rgba(245, 158, 11, 0.1)',
                          color: '#f59e0b',
                          borderRadius: '4px'
                        }}>
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'scenarios' && (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
          {practicalScenarios.map((scenario, idx) => (
            <div key={idx} style={{
              background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
              borderRadius: '16px',
              border: '1px solid rgba(51, 65, 85, 0.5)',
              padding: '24px'
            }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', color: 'white', marginBottom: '12px' }}>
                {scenario.title}
              </h3>
              
              <div style={{
                padding: '16px',
                background: 'rgba(239, 68, 68, 0.1)',
                borderRadius: '8px',
                border: '1px solid rgba(239, 68, 68, 0.2)',
                marginBottom: '20px'
              }}>
                <h4 style={{ fontSize: '14px', color: '#ef4444', marginBottom: '8px' }}>Scenario:</h4>
                <p style={{ fontSize: '14px', color: '#d1d5db', margin: 0, lineHeight: '1.5' }}>
                  {scenario.scenario}
                </p>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px', marginBottom: '20px' }}>
                <div>
                  <h4 style={{ fontSize: '14px', color: '#10b981', marginBottom: '12px' }}>Hunting Objectives:</h4>
                  <ul style={{ margin: 0, paddingLeft: '16px', fontSize: '13px', color: '#d1d5db' }}>
                    {scenario.objectives.map((obj, objIdx) => (
                      <li key={objIdx} style={{ marginBottom: '4px' }}>{obj}</li>
                    ))}
                  </ul>
                </div>
                
                <div>
                  <h4 style={{ fontSize: '14px', color: '#3b82f6', marginBottom: '12px' }}>Investigation Steps:</h4>
                  <ol style={{ margin: 0, paddingLeft: '16px', fontSize: '13px', color: '#d1d5db' }}>
                    {scenario.huntingSteps.map((step, stepIdx) => (
                      <li key={stepIdx} style={{ marginBottom: '4px' }}>{step}</li>
                    ))}
                  </ol>
                </div>
              </div>

              <div style={{
                background: 'rgba(15, 23, 42, 0.8)',
                borderRadius: '8px',
                padding: '16px',
                border: '1px solid rgba(51, 65, 85, 0.5)'
              }}>
                <h4 style={{ fontSize: '14px', color: '#f59e0b', marginBottom: '12px' }}>Sample Detection Rule:</h4>
                <pre style={{
                  fontSize: '12px',
                  color: '#e2e8f0',
                  background: 'rgba(0, 0, 0, 0.3)',
                  padding: '12px',
                  borderRadius: '6px',
                  overflow: 'auto',
                  margin: 0,
                  lineHeight: '1.4'
                }}>
                  {scenario.detectionRules}
                </pre>
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'rules' && (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
          gap: '20px'
        }}>
          {detectionTechnologies.map((tech, idx) => (
            <div key={idx} style={{
              background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
              borderRadius: '16px',
              border: '1px solid rgba(51, 65, 85, 0.5)',
              padding: '20px'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start', marginBottom: '12px' }}>
                <h3 style={{ fontSize: '18px', fontWeight: '600', color: 'white', margin: 0 }}>
                  {tech.name}
                </h3>
                <span style={{
                  fontSize: '11px',
                  padding: '2px 6px',
                  backgroundColor: 'rgba(59, 130, 246, 0.2)',
                  color: '#60a5fa',
                  borderRadius: '4px'
                }}>
                  {tech.type}
                </span>
              </div>
              
              <p style={{ fontSize: '13px', color: '#9ca3af', marginBottom: '12px', lineHeight: '1.4' }}>
                {tech.example}
              </p>
              
              <div style={{ marginBottom: '12px' }}>
                <span style={{ fontSize: '12px', color: '#6b7280' }}>Use Case: </span>
                <span style={{ fontSize: '12px', color: '#10b981', fontWeight: '600' }}>
                  {tech.useCase}
                </span>
              </div>

              <div style={{
                background: 'rgba(0, 0, 0, 0.3)',
                borderRadius: '8px',
                padding: '12px'
              }}>
                <h4 style={{ fontSize: '12px', color: '#f59e0b', marginBottom: '8px' }}>Example Code:</h4>
                <pre style={{
                  fontSize: '11px',
                  color: '#e2e8f0',
                  margin: 0,
                  lineHeight: '1.3',
                  overflow: 'auto'
                }}>
                  {tech.code}
                </pre>
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'intel' && (
        <div style={{
          background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
          borderRadius: '16px',
          border: '1px solid rgba(51, 65, 85, 0.5)',
          padding: '24px'
        }}>
          <h2 style={{ fontSize: '24px', fontWeight: '700', color: 'white', marginBottom: '20px' }}>
            Threat Intelligence Interview Topics
          </h2>
          
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>
            <div style={{
              padding: '20px',
              background: 'rgba(30, 41, 59, 0.5)',
              borderRadius: '12px',
              border: '1px solid rgba(51, 65, 85, 0.3)'
            }}>
              <h3 style={{ fontSize: '18px', color: '#60a5fa', marginBottom: '16px' }}>IOC Analysis</h3>
              <ul style={{ margin: 0, paddingLeft: '16px', fontSize: '14px', color: '#d1d5db', lineHeight: '1.6' }}>
                <li>Hash-based indicators (MD5, SHA1, SHA256)</li>
                <li>Network indicators (IPs, domains, URLs)</li>
                <li>File system artifacts and registry keys</li>
                <li>Behavioral patterns and TTPs</li>
                <li>YARA rule development and testing</li>
              </ul>
            </div>

            <div style={{
              padding: '20px',
              background: 'rgba(30, 41, 59, 0.5)',
              borderRadius: '12px',
              border: '1px solid rgba(51, 65, 85, 0.3)'
            }}>
              <h3 style={{ fontSize: '18px', color: '#10b981', marginBottom: '16px' }}>APT Attribution</h3>
              <ul style={{ margin: 0, paddingLeft: '16px', fontSize: '14px', color: '#d1d5db', lineHeight: '1.6' }}>
                <li>Malware family analysis and clustering</li>
                <li>Infrastructure overlap and reuse</li>
                <li>TTP correlation across campaigns</li>
                <li>Diamond model application</li>
                <li>Confidence levels in attribution</li>
              </ul>
            </div>

            <div style={{
              padding: '20px',
              background: 'rgba(30, 41, 59, 0.5)',
              borderRadius: '12px',
              border: '1px solid rgba(51, 65, 85, 0.3)'
            }}>
              <h3 style={{ fontSize: '18px', color: '#f59e0b', marginBottom: '16px' }}>Threat Feeds</h3>
              <ul style={{ margin: 0, paddingLeft: '16px', fontSize: '14px', color: '#d1d5db', lineHeight: '1.6' }}>
                <li>STIX/TAXII implementation</li>
                <li>Feed quality assessment</li>
                <li>False positive reduction</li>
                <li>Contextualization and enrichment</li>
                <li>Automated ingestion pipelines</li>
              </ul>
            </div>

            <div style={{
              padding: '20px',
              background: 'rgba(30, 41, 59, 0.5)',
              borderRadius: '12px',
              border: '1px solid rgba(51, 65, 85, 0.3)'
            }}>
              <h3 style={{ fontSize: '18px', color: '#ef4444', marginBottom: '16px' }}>MITRE ATT&CK</h3>
              <ul style={{ margin: 0, paddingLeft: '16px', fontSize: '14px', color: '#d1d5db', lineHeight: '1.6' }}>
                <li>Tactic and technique mapping</li>
                <li>Sub-technique identification</li>
                <li>Detection coverage analysis</li>
                <li>Navigator tool utilization</li>
                <li>Adversary emulation planning</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ThreatDetection;
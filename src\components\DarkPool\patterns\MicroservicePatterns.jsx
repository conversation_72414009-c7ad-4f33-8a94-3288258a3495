import React, { useState } from 'react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Server, Cloud, Shield, Zap, Database, Route, Lock, Activity, AlertTriangle, GitBranch, Layers } from 'lucide-react';

const MicroserviceDesignPatterns = () => {
  const [selectedPattern, setSelectedPattern] = useState(null);
  const [activeTab, setActiveTab] = useState('details');

  const patterns = [
    // Service Communication Patterns
    {
      id: 'api-gateway',
      name: 'API Gateway',
      position: { left: 100, top: 150 },
      category: 'communication',
      tech: ['Routing', 'Authentication', 'Rate Limiting'],
      color: '#3b82f6'
    },
    {
      id: 'service-mesh',
      name: 'Service Mesh',
      position: { left: 100, top: 330 },
      category: 'communication',
      tech: ['Traffic Management', 'Security', 'Observability'],
      color: '#3b82f6'
    },
    {
      id: 'circuit-breaker',
      name: 'Circuit Breaker',
      position: { left: 100, top: 510 },
      category: 'communication',
      tech: ['Fault Tolerance', 'Resilience', 'Monitoring'],
      color: '#3b82f6'
    },
    // Data Management Patterns
    {
      id: 'saga',
      name: 'Saga Pattern',
      position: { left: 400, top: 150 },
      category: 'data',
      tech: ['Distributed Transactions', 'Compensation', 'Orchestration'],
      color: '#10b981'
    },
    {
      id: 'cqrs',
      name: 'CQRS',
      position: { left: 400, top: 330 },
      category: 'data',
      tech: ['Command Query Separation', 'Event Sourcing', 'Scalability'],
      color: '#10b981'
    },
    {
      id: 'event-sourcing',
      name: 'Event Sourcing',
      position: { left: 400, top: 510 },
      category: 'data',
      tech: ['Event Store', 'Audit Trail', 'Replay'],
      color: '#10b981'
    },
    // Deployment Patterns
    {
      id: 'blue-green',
      name: 'Blue-Green Deploy',
      position: { left: 700, top: 150 },
      category: 'deployment',
      tech: ['Zero Downtime', 'Risk Reduction', 'Rollback'],
      color: '#ef4444'
    },
    {
      id: 'canary',
      name: 'Canary Deploy',
      position: { left: 700, top: 330 },
      category: 'deployment',
      tech: ['Gradual Rollout', 'A/B Testing', 'Risk Mitigation'],
      color: '#ef4444'
    },
    {
      id: 'strangler-fig',
      name: 'Strangler Fig',
      position: { left: 700, top: 510 },
      category: 'deployment',
      tech: ['Legacy Migration', 'Incremental', 'Risk Management'],
      color: '#ef4444'
    },
    // Observability Patterns
    {
      id: 'distributed-tracing',
      name: 'Distributed Tracing',
      position: { left: 1000, top: 150 },
      category: 'observability',
      tech: ['Request Tracking', 'Performance', 'Debugging'],
      color: '#f59e0b'
    },
    {
      id: 'health-check',
      name: 'Health Check',
      position: { left: 1000, top: 330 },
      category: 'observability',
      tech: ['Service Status', 'Load Balancing', 'Auto-scaling'],
      color: '#f59e0b'
    },
    {
      id: 'log-aggregation',
      name: 'Log Aggregation',
      position: { left: 1000, top: 510 },
      category: 'observability',
      tech: ['Centralized Logging', 'Search', 'Analytics'],
      color: '#f59e0b'
    }
  ];

  const getPatternUML = (patternId) => {
    const umlDiagrams = {
      'api-gateway': {
        viewBox: "0 0 800 500",
        diagram: (
          <>
            {/* Client */}
            <rect x="50" y="100" width="120" height="60" fill="#1e293b" stroke="#3b82f6" strokeWidth="2" rx="4"/>
            <text x="110" y="135" textAnchor="middle" fill="#fff" fontSize="14">Client App</text>

            {/* API Gateway */}
            <rect x="250" y="50" width="200" height="160" fill="rgba(59, 130, 246, 0.1)" stroke="#3b82f6" strokeWidth="3" rx="8"/>
            <text x="350" y="80" textAnchor="middle" fill="#3b82f6" fontSize="16" fontWeight="bold">API Gateway</text>
            
            <rect x="270" y="100" width="160" height="40" fill="#1e293b" stroke="#3b82f6" strokeWidth="1" rx="4"/>
            <text x="350" y="125" textAnchor="middle" fill="#fff" fontSize="12">Authentication</text>
            
            <rect x="270" y="150" width="160" height="40" fill="#1e293b" stroke="#3b82f6" strokeWidth="1" rx="4"/>
            <text x="350" y="175" textAnchor="middle" fill="#fff" fontSize="12">Rate Limiting</text>

            {/* Services */}
            <rect x="550" y="50" width="120" height="60" fill="#1e293b" stroke="#10b981" strokeWidth="2" rx="4"/>
            <text x="610" y="85" textAnchor="middle" fill="#10b981" fontSize="12">Order Service</text>

            <rect x="550" y="130" width="120" height="60" fill="#1e293b" stroke="#10b981" strokeWidth="2" rx="4"/>
            <text x="610" y="165" textAnchor="middle" fill="#10b981" fontSize="12">Portfolio Service</text>

            <rect x="550" y="210" width="120" height="60" fill="#1e293b" stroke="#10b981" strokeWidth="2" rx="4"/>
            <text x="610" y="245" textAnchor="middle" fill="#10b981" fontSize="12">Risk Service</text>

            {/* Arrows */}
            <defs>
              <marker id="arrow-gateway" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="#9ca3af"/>
              </marker>
            </defs>

            <line x1="170" y1="130" x2="250" y2="130" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-gateway)"/>
            <line x1="450" y1="100" x2="550" y2="80" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-gateway)"/>
            <line x1="450" y1="130" x2="550" y2="160" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-gateway)"/>
            <line x1="450" y1="160" x2="550" y2="240" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-gateway)"/>

            <text x="400" y="350" textAnchor="middle" fill="#94a3b8" fontSize="14">
              Single entry point for all client requests
            </text>
          </>
        )
      },
      'circuit-breaker': {
        viewBox: "0 0 800 500",
        diagram: (
          <>
            {/* Service A */}
            <rect x="50" y="100" width="120" height="80" fill="#1e293b" stroke="#3b82f6" strokeWidth="2" rx="4"/>
            <text x="110" y="135" textAnchor="middle" fill="#fff" fontSize="14">Service A</text>
            <text x="110" y="155" textAnchor="middle" fill="#94a3b8" fontSize="10">Client</text>

            {/* Circuit Breaker */}
            <rect x="250" y="50" width="200" height="180" fill="rgba(239, 68, 68, 0.1)" stroke="#ef4444" strokeWidth="3" rx="8"/>
            <text x="350" y="80" textAnchor="middle" fill="#ef4444" fontSize="16" fontWeight="bold">Circuit Breaker</text>
            
            {/* States */}
            <circle cx="300" cy="120" r="20" fill="#10b981" stroke="#fff" strokeWidth="2"/>
            <text x="300" y="125" textAnchor="middle" fill="#fff" fontSize="10">CLOSED</text>
            
            <circle cx="350" cy="160" r="20" fill="#f59e0b" stroke="#fff" strokeWidth="2"/>
            <text x="350" y="165" textAnchor="middle" fill="#fff" fontSize="8">HALF-OPEN</text>
            
            <circle cx="400" cy="120" r="20" fill="#ef4444" stroke="#fff" strokeWidth="2"/>
            <text x="400" y="125" textAnchor="middle" fill="#fff" fontSize="10">OPEN</text>

            {/* Service B */}
            <rect x="550" y="100" width="120" height="80" fill="#1e293b" stroke="#10b981" strokeWidth="2" rx="4"/>
            <text x="610" y="135" textAnchor="middle" fill="#fff" fontSize="14">Service B</text>
            <text x="610" y="155" textAnchor="middle" fill="#94a3b8" fontSize="10">Remote</text>

            {/* Failed Service */}
            <rect x="550" y="220" width="120" height="80" fill="#1e293b" stroke="#ef4444" strokeWidth="2" rx="4" strokeDasharray="5,5"/>
            <text x="610" y="255" textAnchor="middle" fill="#ef4444" fontSize="14">Service C</text>
            <text x="610" y="275" textAnchor="middle" fill="#ef4444" fontSize="10">FAILED</text>

            {/* Arrows and transitions */}
            <line x1="170" y1="140" x2="250" y2="140" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-gateway)"/>
            <line x1="450" y1="130" x2="550" y2="130" stroke="#10b981" strokeWidth="2" markerEnd="url(#arrow-gateway)"/>
            <line x1="450" y1="180" x2="550" y2="250" stroke="#ef4444" strokeWidth="2" strokeDasharray="5,5"/>
            <text x="500" y="215" fill="#ef4444" fontSize="10">BLOCKED</text>

            <text x="400" y="350" textAnchor="middle" fill="#94a3b8" fontSize="14">
              Prevents cascading failures in distributed systems
            </text>
          </>
        )
      },
      'service-mesh': {
        viewBox: "0 0 900 600",
        diagram: (
          <>
            {/* Control Plane */}
            <rect x="350" y="50" width="200" height="100" fill="rgba(59, 130, 246, 0.1)" stroke="#3b82f6" strokeWidth="2" rx="8"/>
            <text x="450" y="80" textAnchor="middle" fill="#3b82f6" fontSize="16" fontWeight="bold">Control Plane</text>
            <text x="450" y="100" textAnchor="middle" fill="#fff" fontSize="12">Istio / Envoy</text>
            <text x="450" y="120" textAnchor="middle" fill="#94a3b8" fontSize="10">Policy • Security • Telemetry</text>

            {/* Services with Sidecars */}
            <rect x="50" y="250" width="160" height="120" fill="rgba(16, 185, 129, 0.1)" stroke="#10b981" strokeWidth="2" rx="6"/>
            <text x="130" y="280" textAnchor="middle" fill="#10b981" fontSize="14">Order Service</text>
            <rect x="70" y="300" width="120" height="30" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="130" y="320" textAnchor="middle" fill="#fff" fontSize="10">Business Logic</text>
            <rect x="70" y="340" width="120" height="20" fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" strokeWidth="1" rx="4"/>
            <text x="130" y="355" textAnchor="middle" fill="#3b82f6" fontSize="9">Envoy Sidecar</text>

            <rect x="270" y="250" width="160" height="120" fill="rgba(16, 185, 129, 0.1)" stroke="#10b981" strokeWidth="2" rx="6"/>
            <text x="350" y="280" textAnchor="middle" fill="#10b981" fontSize="14">Risk Service</text>
            <rect x="290" y="300" width="120" height="30" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="350" y="320" textAnchor="middle" fill="#fff" fontSize="10">Business Logic</text>
            <rect x="290" y="340" width="120" height="20" fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" strokeWidth="1" rx="4"/>
            <text x="350" y="355" textAnchor="middle" fill="#3b82f6" fontSize="9">Envoy Sidecar</text>

            <rect x="490" y="250" width="160" height="120" fill="rgba(16, 185, 129, 0.1)" stroke="#10b981" strokeWidth="2" rx="6"/>
            <text x="570" y="280" textAnchor="middle" fill="#10b981" fontSize="14">Portfolio Service</text>
            <rect x="510" y="300" width="120" height="30" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="570" y="320" textAnchor="middle" fill="#fff" fontSize="10">Business Logic</text>
            <rect x="510" y="340" width="120" height="20" fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" strokeWidth="1" rx="4"/>
            <text x="570" y="355" textAnchor="middle" fill="#3b82f6" fontSize="9">Envoy Sidecar</text>

            <rect x="690" y="250" width="160" height="120" fill="rgba(16, 185, 129, 0.1)" stroke="#10b981" strokeWidth="2" rx="6"/>
            <text x="770" y="280" textAnchor="middle" fill="#10b981" fontSize="14">Auth Service</text>
            <rect x="710" y="300" width="120" height="30" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="770" y="320" textAnchor="middle" fill="#fff" fontSize="10">Business Logic</text>
            <rect x="710" y="340" width="120" height="20" fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" strokeWidth="1" rx="4"/>
            <text x="770" y="355" textAnchor="middle" fill="#3b82f6" fontSize="9">Envoy Sidecar</text>

            {/* Connections between sidecars */}
            <defs>
              <marker id="arrow-mesh" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="#3b82f6"/>
              </marker>
            </defs>

            <line x1="190" y1="350" x2="290" y2="350" stroke="#3b82f6" strokeWidth="2" markerEnd="url(#arrow-mesh)"/>
            <line x1="410" y1="350" x2="510" y2="350" stroke="#3b82f6" strokeWidth="2" markerEnd="url(#arrow-mesh)"/>
            <line x1="630" y1="350" x2="710" y2="350" stroke="#3b82f6" strokeWidth="2" markerEnd="url(#arrow-mesh)"/>

            {/* Control plane connections */}
            <line x1="450" y1="150" x2="130" y2="250" stroke="#9ca3af" strokeWidth="1" strokeDasharray="3,3"/>
            <line x1="450" y1="150" x2="350" y2="250" stroke="#9ca3af" strokeWidth="1" strokeDasharray="3,3"/>
            <line x1="450" y1="150" x2="570" y2="250" stroke="#9ca3af" strokeWidth="1" strokeDasharray="3,3"/>
            <line x1="450" y1="150" x2="770" y2="250" stroke="#9ca3af" strokeWidth="1" strokeDasharray="3,3"/>

            <text x="450" y="450" textAnchor="middle" fill="#94a3b8" fontSize="14">
              Service-to-service communication through intelligent proxies
            </text>
          </>
        )
      },
      'saga': {
        viewBox: "0 0 1000 600",
        diagram: (
          <>
            {/* Saga Orchestrator */}
            <rect x="400" y="50" width="200" height="80" fill="rgba(16, 185, 129, 0.1)" stroke="#10b981" strokeWidth="2" rx="8"/>
            <text x="500" y="80" textAnchor="middle" fill="#10b981" fontSize="16" fontWeight="bold">Saga Orchestrator</text>
            <text x="500" y="100" textAnchor="middle" fill="#94a3b8" fontSize="12">Order Processing Saga</text>

            {/* Services */}
            <rect x="50" y="200" width="150" height="100" fill="#1e293b" stroke="#3b82f6" strokeWidth="2" rx="6"/>
            <text x="125" y="230" textAnchor="middle" fill="#fff" fontSize="14">Payment Service</text>
            <text x="125" y="250" textAnchor="middle" fill="#60a5fa" fontSize="10">+ processPayment()</text>
            <text x="125" y="265" textAnchor="middle" fill="#ef4444" fontSize="10">+ refundPayment()</text>

            <rect x="250" y="200" width="150" height="100" fill="#1e293b" stroke="#3b82f6" strokeWidth="2" rx="6"/>
            <text x="325" y="230" textAnchor="middle" fill="#fff" fontSize="14">Inventory Service</text>
            <text x="325" y="250" textAnchor="middle" fill="#60a5fa" fontSize="10">+ reserveItems()</text>
            <text x="325" y="265" textAnchor="middle" fill="#ef4444" fontSize="10">+ releaseReservation()</text>

            <rect x="450" y="200" width="150" height="100" fill="#1e293b" stroke="#3b82f6" strokeWidth="2" rx="6"/>
            <text x="525" y="230" textAnchor="middle" fill="#fff" fontSize="14">Order Service</text>
            <text x="525" y="250" textAnchor="middle" fill="#60a5fa" fontSize="10">+ createOrder()</text>
            <text x="525" y="265" textAnchor="middle" fill="#ef4444" fontSize="10">+ cancelOrder()</text>

            <rect x="650" y="200" width="150" height="100" fill="#1e293b" stroke="#3b82f6" strokeWidth="2" rx="6"/>
            <text x="725" y="230" textAnchor="middle" fill="#fff" fontSize="14">Shipping Service</text>
            <text x="725" y="250" textAnchor="middle" fill="#60a5fa" fontSize="10">+ scheduleShipping()</text>
            <text x="725" y="265" textAnchor="middle" fill="#ef4444" fontSize="10">+ cancelShipping()</text>

            {/* Success Flow */}
            <text x="500" y="400" textAnchor="middle" fill="#10b981" fontSize="14" fontWeight="bold">Success Flow</text>
            <line x1="125" y1="420" x2="325" y2="420" stroke="#10b981" strokeWidth="3" markerEnd="url(#arrow-mesh)"/>
            <line x1="325" y1="420" x2="525" y2="420" stroke="#10b981" strokeWidth="3" markerEnd="url(#arrow-mesh)"/>
            <line x1="525" y1="420" x2="725" y2="420" stroke="#10b981" strokeWidth="3" markerEnd="url(#arrow-mesh)"/>
            <text x="225" y="435" textAnchor="middle" fill="#10b981" fontSize="10">1. Payment</text>
            <text x="425" y="435" textAnchor="middle" fill="#10b981" fontSize="10">2. Reserve</text>
            <text x="625" y="435" textAnchor="middle" fill="#10b981" fontSize="10">3. Order</text>

            {/* Compensation Flow */}
            <text x="500" y="480" textAnchor="middle" fill="#ef4444" fontSize="14" fontWeight="bold">Compensation Flow</text>
            <line x1="725" y1="500" x2="525" y2="500" stroke="#ef4444" strokeWidth="3" markerEnd="url(#arrow-mesh)" strokeDasharray="5,5"/>
            <line x1="525" y1="500" x2="325" y2="500" stroke="#ef4444" strokeWidth="3" markerEnd="url(#arrow-mesh)" strokeDasharray="5,5"/>
            <line x1="325" y1="500" x2="125" y2="500" stroke="#ef4444" strokeWidth="3" markerEnd="url(#arrow-mesh)" strokeDasharray="5,5"/>
            <text x="625" y="515" textAnchor="middle" fill="#ef4444" fontSize="10">3. Cancel Order</text>
            <text x="425" y="515" textAnchor="middle" fill="#ef4444" fontSize="10">2. Release Items</text>
            <text x="225" y="515" textAnchor="middle" fill="#ef4444" fontSize="10">1. Refund</text>

            {/* Orchestrator connections */}
            <line x1="500" y1="130" x2="125" y2="200" stroke="#9ca3af" strokeWidth="2"/>
            <line x1="500" y1="130" x2="325" y2="200" stroke="#9ca3af" strokeWidth="2"/>
            <line x1="500" y1="130" x2="525" y2="200" stroke="#9ca3af" strokeWidth="2"/>
            <line x1="500" y1="130" x2="725" y2="200" stroke="#9ca3af" strokeWidth="2"/>

            <text x="500" y="570" textAnchor="middle" fill="#94a3b8" fontSize="14">
              Manages distributed transactions with compensation logic
            </text>
          </>
        )
      },
      'cqrs': {
        viewBox: "0 0 900 600",
        diagram: (
          <>
            {/* Command Side */}
            <rect x="50" y="50" width="350" height="200" fill="rgba(239, 68, 68, 0.1)" stroke="#ef4444" strokeWidth="2" rx="8"/>
            <text x="225" y="80" textAnchor="middle" fill="#ef4444" fontSize="16" fontWeight="bold">Command Side (Write)</text>
            
            <rect x="70" y="100" width="120" height="60" fill="#1e293b" stroke="#ef4444" strokeWidth="1" rx="4"/>
            <text x="130" y="135" textAnchor="middle" fill="#fff" fontSize="12">Command Handler</text>
            
            <rect x="210" y="100" width="120" height="60" fill="#1e293b" stroke="#ef4444" strokeWidth="1" rx="4"/>
            <text x="270" y="135" textAnchor="middle" fill="#fff" fontSize="12">Domain Model</text>
            
            <rect x="70" y="180" width="260" height="40" fill="#1e293b" stroke="#ef4444" strokeWidth="1" rx="4"/>
            <text x="200" y="205" textAnchor="middle" fill="#fff" fontSize="12">Write Database (OLTP)</text>

            {/* Query Side */}
            <rect x="500" y="50" width="350" height="200" fill="rgba(59, 130, 246, 0.1)" stroke="#3b82f6" strokeWidth="2" rx="8"/>
            <text x="675" y="80" textAnchor="middle" fill="#3b82f6" fontSize="16" fontWeight="bold">Query Side (Read)</text>
            
            <rect x="520" y="100" width="120" height="60" fill="#1e293b" stroke="#3b82f6" strokeWidth="1" rx="4"/>
            <text x="580" y="135" textAnchor="middle" fill="#fff" fontSize="12">Query Handler</text>
            
            <rect x="660" y="100" width="120" height="60" fill="#1e293b" stroke="#3b82f6" strokeWidth="1" rx="4"/>
            <text x="720" y="135" textAnchor="middle" fill="#fff" fontSize="12">Read Models</text>
            
            <rect x="520" y="180" width="260" height="40" fill="#1e293b" stroke="#3b82f6" strokeWidth="1" rx="4"/>
            <text x="650" y="205" textAnchor="middle" fill="#fff" fontSize="12">Read Database (OLAP)</text>

            {/* Event Bus */}
            <rect x="350" y="320" width="200" height="60" fill="rgba(245, 158, 11, 0.1)" stroke="#f59e0b" strokeWidth="2" rx="8"/>
            <text x="450" y="355" textAnchor="middle" fill="#f59e0b" fontSize="14" fontWeight="bold">Event Bus / Stream</text>

            {/* Client */}
            <rect x="400" y="450" width="100" height="60" fill="#1e293b" stroke="#10b981" strokeWidth="2" rx="4"/>
            <text x="450" y="485" textAnchor="middle" fill="#10b981" fontSize="12">Trading Client</text>

            {/* Arrows */}
            <defs>
              <marker id="arrow-cqrs" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="#9ca3af"/>
              </marker>
            </defs>

            {/* Command flow */}
            <line x1="420" y1="470" x2="200" y2="300" stroke="#ef4444" strokeWidth="2" markerEnd="url(#arrow-cqrs)"/>
            <text x="310" y="385" fill="#ef4444" fontSize="10">Commands</text>

            {/* Query flow */}
            <line x1="480" y1="470" x2="650" y2="300" stroke="#3b82f6" strokeWidth="2" markerEnd="url(#arrow-cqrs)"/>
            <text x="565" y="385" fill="#3b82f6" fontSize="10">Queries</text>

            {/* Event flow */}
            <line x1="200" y1="250" x2="350" y2="340" stroke="#f59e0b" strokeWidth="2" markerEnd="url(#arrow-cqrs)"/>
            <line x1="550" y1="340" x2="650" y2="250" stroke="#f59e0b" strokeWidth="2" markerEnd="url(#arrow-cqrs)"/>

            <text x="450" y="580" textAnchor="middle" fill="#94a3b8" fontSize="14">
              Separate read and write operations for optimal performance
            </text>
          </>
        )
      },
      'event-sourcing': {
        viewBox: "0 0 900 500",
        diagram: (
          <>
            {/* Event Store */}
            <rect x="300" y="50" width="300" height="120" fill="rgba(245, 158, 11, 0.1)" stroke="#f59e0b" strokeWidth="2" rx="8"/>
            <text x="450" y="80" textAnchor="middle" fill="#f59e0b" fontSize="16" fontWeight="bold">Event Store</text>
            
            {/* Events */}
            <rect x="320" y="100" width="80" height="30" fill="#1e293b" stroke="#f59e0b" strokeWidth="1" rx="4"/>
            <text x="360" y="120" textAnchor="middle" fill="#fff" fontSize="10">OrderCreated</text>
            
            <rect x="410" y="100" width="80" height="30" fill="#1e293b" stroke="#f59e0b" strokeWidth="1" rx="4"/>
            <text x="450" y="120" textAnchor="middle" fill="#fff" fontSize="10">OrderPaid</text>
            
            <rect x="500" y="100" width="80" height="30" fill="#1e293b" stroke="#f59e0b" strokeWidth="1" rx="4"/>
            <text x="540" y="120" textAnchor="middle" fill="#fff" fontSize="10">OrderShipped</text>
            
            <text x="450" y="150" textAnchor="middle" fill="#94a3b8" fontSize="12">Immutable Event Log</text>

            {/* Command Handler */}
            <rect x="50" y="200" width="150" height="80" fill="#1e293b" stroke="#ef4444" strokeWidth="2" rx="6"/>
            <text x="125" y="230" textAnchor="middle" fill="#ef4444" fontSize="14">Command Handler</text>
            <text x="125" y="250" textAnchor="middle" fill="#94a3b8" fontSize="10">Process Commands</text>
            <text x="125" y="265" textAnchor="middle" fill="#94a3b8" fontSize="10">Generate Events</text>

            {/* Event Projector */}
            <rect x="700" y="200" width="150" height="80" fill="#1e293b" stroke="#3b82f6" strokeWidth="2" rx="6"/>
            <text x="775" y="230" textAnchor="middle" fill="#3b82f6" fontSize="14">Event Projector</text>
            <text x="775" y="250" textAnchor="middle" fill="#94a3b8" fontSize="10">Replay Events</text>
            <text x="775" y="265" textAnchor="middle" fill="#94a3b8" fontSize="10">Build Read Models</text>

            {/* Read Models */}
            <rect x="650" y="350" width="100" height="50" fill="#1e293b" stroke="#10b981" strokeWidth="2" rx="4"/>
            <text x="700" y="375" textAnchor="middle" fill="#10b981" fontSize="12">Order View</text>
            
            <rect x="770" y="350" width="100" height="50" fill="#1e293b" stroke="#10b981" strokeWidth="2" rx="4"/>
            <text x="820" y="375" textAnchor="middle" fill="#10b981" fontSize="12">Analytics View</text>

            {/* Snapshot */}
            <rect x="300" y="350" width="300" height="50" fill="rgba(139, 92, 246, 0.1)" stroke="#8b5cf6" strokeWidth="2" rx="6"/>
            <text x="450" y="380" textAnchor="middle" fill="#8b5cf6" fontSize="14">Snapshots (Optional)</text>

            {/* Arrows */}
            <defs>
              <marker id="arrow-es" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="#9ca3af"/>
              </marker>
            </defs>

            <line x1="200" y1="240" x2="300" y2="140" stroke="#ef4444" strokeWidth="2" markerEnd="url(#arrow-es)"/>
            <line x1="600" y1="140" x2="700" y2="240" stroke="#3b82f6" strokeWidth="2" markerEnd="url(#arrow-es)"/>
            <line x1="775" y1="280" x2="720" y2="350" stroke="#10b981" strokeWidth="2" markerEnd="url(#arrow-es)"/>
            <line x1="775" y1="280" x2="820" y2="350" stroke="#10b981" strokeWidth="2" markerEnd="url(#arrow-es)"/>

            <text x="450" y="470" textAnchor="middle" fill="#94a3b8" fontSize="14">
              Store events as the source of truth, rebuild state from event history
            </text>
          </>
        )
      },
      'blue-green': {
        viewBox: "0 0 900 600",
        diagram: (
          <>
            {/* Load Balancer */}
            <rect x="350" y="50" width="200" height="60" fill="#1e293b" stroke="#f59e0b" strokeWidth="2" rx="8"/>
            <text x="450" y="85" textAnchor="middle" fill="#f59e0b" fontSize="16" fontWeight="bold">Load Balancer</text>

            {/* Blue Environment (Active) */}
            <rect x="100" y="200" width="250" height="150" fill="rgba(59, 130, 246, 0.1)" stroke="#3b82f6" strokeWidth="3" rx="8"/>
            <text x="225" y="230" textAnchor="middle" fill="#3b82f6" fontSize="16" fontWeight="bold">Blue Environment (v1.0)</text>
            <text x="225" y="250" textAnchor="middle" fill="#10b981" fontSize="14">✓ ACTIVE</text>
            
            <rect x="120" y="270" width="80" height="60" fill="#1e293b" stroke="#3b82f6" strokeWidth="1" rx="4"/>
            <text x="160" y="305" textAnchor="middle" fill="#fff" fontSize="12">Service A</text>
            
            <rect x="210" y="270" width="80" height="60" fill="#1e293b" stroke="#3b82f6" strokeWidth="1" rx="4"/>
            <text x="250" y="305" textAnchor="middle" fill="#fff" fontSize="12">Service B</text>
            
            <rect x="300" y="270" width="80" height="60" fill="#1e293b" stroke="#3b82f6" strokeWidth="1" rx="4"/>
            <text x="340" y="305" textAnchor="middle" fill="#fff" fontSize="12">Database</text>

            {/* Green Environment (Standby) */}
            <rect x="550" y="200" width="250" height="150" fill="rgba(16, 185, 129, 0.1)" stroke="#10b981" strokeWidth="3" rx="8"/>
            <text x="675" y="230" textAnchor="middle" fill="#10b981" fontSize="16" fontWeight="bold">Green Environment (v2.0)</text>
            <text x="675" y="250" textAnchor="middle" fill="#94a3b8" fontSize="14">◯ STANDBY</text>
            
            <rect x="570" y="270" width="80" height="60" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="610" y="305" textAnchor="middle" fill="#fff" fontSize="12">Service A</text>
            
            <rect x="660" y="270" width="80" height="60" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="700" y="305" textAnchor="middle" fill="#fff" fontSize="12">Service B</text>
            
            <rect x="750" y="270" width="80" height="60" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="790" y="305" textAnchor="middle" fill="#fff" fontSize="12">Database</text>

            {/* Shared Database */}
            <rect x="350" y="400" width="200" height="60" fill="#1e293b" stroke="#8b5cf6" strokeWidth="2" rx="6"/>
            <text x="450" y="435" textAnchor="middle" fill="#8b5cf6" fontSize="14">Shared Database</text>

            {/* Traffic Flow */}
            <defs>
              <marker id="arrow-bg" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="#10b981"/>
              </marker>
              <marker id="arrow-bg-inactive" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="#94a3b8"/>
              </marker>
            </defs>

            {/* Active traffic to Blue */}
            <line x1="400" y1="110" x2="225" y2="200" stroke="#10b981" strokeWidth="4" markerEnd="url(#arrow-bg)"/>
            <text x="280" y="150" fill="#10b981" fontSize="12" fontWeight="bold">100% Traffic</text>

            {/* Inactive connection to Green */}
            <line x1="500" y1="110" x2="675" y2="200" stroke="#94a3b8" strokeWidth="2" strokeDasharray="5,5" markerEnd="url(#arrow-bg-inactive)"/>
            <text x="620" y="150" fill="#94a3b8" fontSize="12">0% Traffic</text>

            {/* Database connections */}
            <line x1="340" y1="330" x2="400" y2="400" stroke="#8b5cf6" strokeWidth="2"/>
            <line x1="790" y1="330" x2="500" y2="400" stroke="#8b5cf6" strokeWidth="2"/>

            {/* Switch indicator */}
            <rect x="400" y="500" width="100" height="30" fill="rgba(245, 158, 11, 0.3)" stroke="#f59e0b" strokeWidth="1" rx="4"/>
            <text x="450" y="520" textAnchor="middle" fill="#f59e0b" fontSize="12">Switch Traffic →</text>

            <text x="450" y="570" textAnchor="middle" fill="#94a3b8" fontSize="14">
              Instant switchover between identical production environments
            </text>
          </>
        )
      },
      'canary': {
        viewBox: "0 0 900 600",
        diagram: (
          <>
            {/* Load Balancer */}
            <rect x="350" y="50" width="200" height="60" fill="#1e293b" stroke="#f59e0b" strokeWidth="2" rx="8"/>
            <text x="450" y="85" textAnchor="middle" fill="#f59e0b" fontSize="16" fontWeight="bold">Smart Load Balancer</text>

            {/* Current Version (Stable) */}
            <rect x="100" y="200" width="250" height="150" fill="rgba(59, 130, 246, 0.1)" stroke="#3b82f6" strokeWidth="3" rx="8"/>
            <text x="225" y="230" textAnchor="middle" fill="#3b82f6" fontSize="16" fontWeight="bold">Current Version (v1.0)</text>
            <text x="225" y="250" textAnchor="middle" fill="#10b981" fontSize="14">90% Traffic</text>
            
            <rect x="120" y="270" width="80" height="60" fill="#1e293b" stroke="#3b82f6" strokeWidth="1" rx="4"/>
            <text x="160" y="305" textAnchor="middle" fill="#fff" fontSize="12">Instance 1</text>
            
            <rect x="210" y="270" width="80" height="60" fill="#1e293b" stroke="#3b82f6" strokeWidth="1" rx="4"/>
            <text x="250" y="305" textAnchor="middle" fill="#fff" fontSize="12">Instance 2</text>
            
            <rect x="300" y="270" width="80" height="60" fill="#1e293b" stroke="#3b82f6" strokeWidth="1" rx="4"/>
            <text x="340" y="305" textAnchor="middle" fill="#fff" fontSize="12">Instance 3</text>

            {/* Canary Version */}
            <rect x="550" y="200" width="200" height="150" fill="rgba(16, 185, 129, 0.1)" stroke="#10b981" strokeWidth="3" rx="8"/>
            <text x="650" y="230" textAnchor="middle" fill="#10b981" fontSize="16" fontWeight="bold">Canary Version (v2.0)</text>
            <text x="650" y="250" textAnchor="middle" fill="#f59e0b" fontSize="14">10% Traffic</text>
            
            <rect x="620" y="270" width="80" height="60" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="660" y="305" textAnchor="middle" fill="#fff" fontSize="12">Canary</text>

            {/* Monitoring Dashboard */}
            <rect x="300" y="400" width="300" height="100" fill="rgba(139, 92, 246, 0.1)" stroke="#8b5cf6" strokeWidth="2" rx="6"/>
            <text x="450" y="430" textAnchor="middle" fill="#8b5cf6" fontSize="16" fontWeight="bold">Monitoring Dashboard</text>
            <text x="350" y="450" fill="#94a3b8" fontSize="10">• Error Rate: v1.0: 0.5%, v2.0: 0.7%</text>
            <text x="350" y="465" fill="#94a3b8" fontSize="10">• Response Time: v1.0: 120ms, v2.0: 115ms</text>
            <text x="350" y="480" fill="#94a3b8" fontSize="10">• Success Rate: v1.0: 99.5%, v2.0: 99.3%</text>

            {/* Traffic Flow */}
            <defs>
              <marker id="arrow-canary" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="#10b981"/>
              </marker>
              <marker id="arrow-canary-small" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="#f59e0b"/>
              </marker>
            </defs>

            {/* Main traffic flow */}
            <line x1="400" y1="110" x2="225" y2="200" stroke="#3b82f6" strokeWidth="6" markerEnd="url(#arrow-canary)"/>
            <text x="280" y="150" fill="#3b82f6" fontSize="12" fontWeight="bold">90% Users</text>

            {/* Canary traffic flow */}
            <line x1="500" y1="110" x2="650" y2="200" stroke="#f59e0b" strokeWidth="2" markerEnd="url(#arrow-canary-small)"/>
            <text x="600" y="150" fill="#f59e0b" fontSize="12" fontWeight="bold">10% Users</text>

            {/* Monitoring connections */}
            <line x1="225" y1="350" x2="350" y2="400" stroke="#8b5cf6" strokeWidth="1" strokeDasharray="3,3"/>
            <line x1="650" y1="350" x2="550" y2="400" stroke="#8b5cf6" strokeWidth="1" strokeDasharray="3,3"/>

            <text x="450" y="570" textAnchor="middle" fill="#94a3b8" fontSize="14">
              Gradual rollout with real-time monitoring and automatic rollback
            </text>
          </>
        )
      },
      'strangler-fig': {
        viewBox: "0 0 900 600",
        diagram: (
          <>
            {/* Legacy System */}
            <rect x="50" y="100" width="300" height="200" fill="rgba(239, 68, 68, 0.1)" stroke="#ef4444" strokeWidth="3" rx="8"/>
            <text x="200" y="130" textAnchor="middle" fill="#ef4444" fontSize="16" fontWeight="bold">Legacy Monolith</text>
            
            <rect x="70" y="150" width="80" height="40" fill="#1e293b" stroke="#ef4444" strokeWidth="1" rx="4"/>
            <text x="110" y="175" textAnchor="middle" fill="#ef4444" fontSize="10">User Module</text>
            
            <rect x="160" y="150" width="80" height="40" fill="#1e293b" stroke="#ef4444" strokeWidth="1" rx="4"/>
            <text x="200" y="175" textAnchor="middle" fill="#ef4444" fontSize="10">Order Module</text>
            
            <rect x="250" y="150" width="80" height="40" fill="#1e293b" stroke="#ef4444" strokeWidth="1" rx="4"/>
            <text x="290" y="175" textAnchor="middle" fill="#ef4444" fontSize="10">Payment Mod</text>
            
            <rect x="70" y="200" width="260" height="40" fill="#1e293b" stroke="#ef4444" strokeWidth="1" rx="4"/>
            <text x="200" y="225" textAnchor="middle" fill="#ef4444" fontSize="12">Shared Database</text>
            
            <text x="200" y="270" textAnchor="middle" fill="#94a3b8" fontSize="12">Being gradually replaced</text>

            {/* Strangler Fig Facade */}
            <rect x="400" y="50" width="200" height="120" fill="rgba(245, 158, 11, 0.1)" stroke="#f59e0b" strokeWidth="3" rx="8"/>
            <text x="500" y="80" textAnchor="middle" fill="#f59e0b" fontSize="16" fontWeight="bold">Strangler Facade</text>
            <text x="500" y="100" textAnchor="middle" fill="#94a3b8" fontSize="12">Routing Layer</text>
            
            <rect x="420" y="120" width="60" height="30" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="450" y="140" textAnchor="middle" fill="#10b981" fontSize="9">New API</text>
            
            <rect x="490" y="120" width="60" height="30" fill="#1e293b" stroke="#ef4444" strokeWidth="1" rx="4"/>
            <text x="520" y="140" textAnchor="middle" fill="#ef4444" fontSize="9">Legacy API</text>

            {/* New Microservices */}
            <rect x="650" y="100" width="200" height="200" fill="rgba(16, 185, 129, 0.1)" stroke="#10b981" strokeWidth="3" rx="8"/>
            <text x="750" y="130" textAnchor="middle" fill="#10b981" fontSize="16" fontWeight="bold">New Microservices</text>
            
            <rect x="670" y="150" width="80" height="40" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="710" y="175" textAnchor="middle" fill="#10b981" fontSize="10">User Service</text>
            
            <rect x="760" y="150" width="80" height="40" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="800" y="175" textAnchor="middle" fill="#10b981" fontSize="10">Order Service</text>
            
            <rect x="670" y="200" width="80" height="40" fill="#1e293b" stroke="#94a3b8" strokeWidth="1" rx="4" strokeDasharray="3,3"/>
            <text x="710" y="225" textAnchor="middle" fill="#94a3b8" fontSize="10">Payment Svc</text>
            <text x="710" y="235" textAnchor="middle" fill="#94a3b8" fontSize="8">(planned)</text>
            
            <rect x="760" y="200" width="80" height="40" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="800" y="225" textAnchor="middle" fill="#10b981" fontSize="10">User DB</text>
            
            <rect x="670" y="250" width="80" height="40" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="710" y="275" textAnchor="middle" fill="#10b981" fontSize="10">Order DB</text>

            {/* Client */}
            <rect x="400" y="350" width="200" height="60" fill="#1e293b" stroke="#3b82f6" strokeWidth="2" rx="6"/>
            <text x="500" y="385" textAnchor="middle" fill="#3b82f6" fontSize="14">Client Applications</text>

            {/* Migration Progress */}
            <rect x="50" y="450" width="800" height="80" fill="rgba(139, 92, 246, 0.1)" stroke="#8b5cf6" strokeWidth="2" rx="6"/>
            <text x="450" y="475" textAnchor="middle" fill="#8b5cf6" fontSize="16" fontWeight="bold">Migration Progress</text>
            
            {/* Progress bars */}
            <rect x="70" y="490" width="200" height="15" fill="#374151" stroke="#8b5cf6" strokeWidth="1" rx="2"/>
            <rect x="70" y="490" width="160" height="15" fill="#10b981" rx="2"/>
            <text x="170" y="502" textAnchor="middle" fill="#fff" fontSize="10">User Service: 80%</text>
            
            <rect x="300" y="490" width="200" height="15" fill="#374151" stroke="#8b5cf6" strokeWidth="1" rx="2"/>
            <rect x="300" y="490" width="120" height="15" fill="#10b981" rx="2"/>
            <text x="400" y="502" textAnchor="middle" fill="#fff" fontSize="10">Order Service: 60%</text>
            
            <rect x="530" y="490" width="200" height="15" fill="#374151" stroke="#8b5cf6" strokeWidth="1" rx="2"/>
            <rect x="530" y="490" width="40" height="15" fill="#f59e0b" rx="2"/>
            <text x="630" y="502" textAnchor="middle" fill="#fff" fontSize="10">Payment Service: 20%</text>

            {/* Arrows */}
            <defs>
              <marker id="arrow-sf" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="#9ca3af"/>
              </marker>
            </defs>

            {/* Client to facade */}
            <line x1="500" y1="350" x2="500" y2="170" stroke="#3b82f6" strokeWidth="2" markerEnd="url(#arrow-sf)"/>

            {/* Facade to systems */}
            <line x1="450" y1="150" x2="350" y2="150" stroke="#ef4444" strokeWidth="2" markerEnd="url(#arrow-sf)"/>
            <line x1="550" y1="150" x2="650" y2="150" stroke="#10b981" strokeWidth="2" markerEnd="url(#arrow-sf)"/>

            <text x="450" y="580" textAnchor="middle" fill="#94a3b8" fontSize="14">
              Incrementally replace legacy system components with microservices
            </text>
          </>
        )
      },
      'distributed-tracing': {
        viewBox: "0 0 1000 600",
        diagram: (
          <>
            {/* Client Request */}
            <rect x="50" y="50" width="120" height="60" fill="#1e293b" stroke="#3b82f6" strokeWidth="2" rx="4"/>
            <text x="110" y="85" textAnchor="middle" fill="#3b82f6" fontSize="12">Client Request</text>
            <text x="110" y="95" textAnchor="middle" fill="#94a3b8" fontSize="9">trace-id: abc123</text>

            {/* API Gateway */}
            <rect x="220" y="50" width="120" height="60" fill="#1e293b" stroke="#f59e0b" strokeWidth="2" rx="4"/>
            <text x="280" y="80" textAnchor="middle" fill="#f59e0b" fontSize="12">API Gateway</text>
            <text x="280" y="95" textAnchor="middle" fill="#94a3b8" fontSize="9">span-1</text>

            {/* Order Service */}
            <rect x="390" y="50" width="120" height="60" fill="#1e293b" stroke="#10b981" strokeWidth="2" rx="4"/>
            <text x="450" y="80" textAnchor="middle" fill="#10b981" fontSize="12">Order Service</text>
            <text x="450" y="95" textAnchor="middle" fill="#94a3b8" fontSize="9">span-2</text>

            {/* Risk Service */}
            <rect x="560" y="150" width="120" height="60" fill="#1e293b" stroke="#ef4444" strokeWidth="2" rx="4"/>
            <text x="620" y="180" textAnchor="middle" fill="#ef4444" fontSize="12">Risk Service</text>
            <text x="620" y="195" textAnchor="middle" fill="#94a3b8" fontSize="9">span-3</text>

            {/* Portfolio Service */}
            <rect x="390" y="250" width="120" height="60" fill="#1e293b" stroke="#8b5cf6" strokeWidth="2" rx="4"/>
            <text x="450" y="280" textAnchor="middle" fill="#8b5cf6" fontSize="12">Portfolio Service</text>
            <text x="450" y="295" textAnchor="middle" fill="#94a3b8" fontSize="9">span-4</text>

            {/* Database */}
            <rect x="220" y="350" width="120" height="60" fill="#1e293b" stroke="#f97316" strokeWidth="2" rx="4"/>
            <text x="280" y="380" textAnchor="middle" fill="#f97316" fontSize="12">Database</text>
            <text x="280" y="395" textAnchor="middle" fill="#94a3b8" fontSize="9">span-5</text>

            {/* Trace Timeline */}
            <rect x="750" y="50" width="200" height="400" fill="rgba(31, 41, 55, 0.5)" stroke="#64748b" strokeWidth="2" rx="8"/>
            <text x="850" y="80" textAnchor="middle" fill="#64748b" fontSize="16" fontWeight="bold">Trace Timeline</text>
            
            {/* Timeline bars */}
            <rect x="770" y="100" width="160" height="20" fill="#f59e0b" rx="2"/>
            <text x="850" y="115" textAnchor="middle" fill="#000" fontSize="10">API Gateway (50ms)</text>
            
            <rect x="790" y="130" width="120" height="20" fill="#10b981" rx="2"/>
            <text x="850" y="145" textAnchor="middle" fill="#000" fontSize="10">Order Service (30ms)</text>
            
            <rect x="810" y="160" width="80" height="20" fill="#ef4444" rx="2"/>
            <text x="850" y="175" textAnchor="middle" fill="#fff" fontSize="10">Risk (20ms)</text>
            
            <rect x="790" y="190" width="100" height="20" fill="#8b5cf6" rx="2"/>
            <text x="840" y="205" textAnchor="middle" fill="#fff" fontSize="10">Portfolio (25ms)</text>
            
            <rect x="770" y="220" width="140" height="20" fill="#f97316" rx="2"/>
            <text x="840" y="235" textAnchor="middle" fill="#000" fontSize="10">Database (35ms)</text>

            {/* Trace Details */}
            <text x="850" y="270" textAnchor="middle" fill="#94a3b8" fontSize="12">Total Duration: 160ms</text>
            <text x="850" y="290" textAnchor="middle" fill="#94a3b8" fontSize="11">Trace ID: abc123</text>
            <text x="850" y="310" textAnchor="middle" fill="#94a3b8" fontSize="11">5 spans across 5 services</text>

            {/* Error indicator */}
            <circle cx="620" cy="160" r="8" fill="#ef4444" stroke="#fff" strokeWidth="2"/>
            <text x="620" y="165" textAnchor="middle" fill="#fff" fontSize="10">!</text>
            <text x="685" y="165" fill="#ef4444" fontSize="10">Slow query detected</text>

            {/* Flow arrows */}
            <defs>
              <marker id="arrow-trace" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="#9ca3af"/>
              </marker>
            </defs>

            <line x1="170" y1="80" x2="220" y2="80" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-trace)"/>
            <line x1="340" y1="80" x2="390" y2="80" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-trace)"/>
            <line x1="510" y1="80" x2="560" y2="150" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-trace)"/>
            <line x1="450" y1="110" x2="450" y2="250" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-trace)"/>
            <line x1="390" y1="280" x2="340" y2="350" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-trace)"/>

            <text x="500" y="500" textAnchor="middle" fill="#94a3b8" fontSize="14">
              Track requests across distributed services with correlation IDs
            </text>
          </>
        )
      },
      'health-check': {
        viewBox: "0 0 900 600",
        diagram: (
          <>
            {/* Load Balancer */}
            <rect x="350" y="50" width="200" height="60" fill="#1e293b" stroke="#f59e0b" strokeWidth="2" rx="8"/>
            <text x="450" y="80" textAnchor="middle" fill="#f59e0b" fontSize="16" fontWeight="bold">Load Balancer</text>
            <text x="450" y="95" textAnchor="middle" fill="#94a3b8" fontSize="10">Health Check Enabled</text>

            {/* Healthy Services */}
            <rect x="100" y="200" width="150" height="120" fill="rgba(16, 185, 129, 0.1)" stroke="#10b981" strokeWidth="2" rx="6"/>
            <text x="175" y="230" textAnchor="middle" fill="#10b981" fontSize="14" fontWeight="bold">Service A</text>
            <circle cx="175" cy="250" r="12" fill="#10b981"/>
            <text x="175" y="255" textAnchor="middle" fill="#fff" fontSize="12" fontWeight="bold">✓</text>
            <text x="175" y="275" textAnchor="middle" fill="#10b981" fontSize="12">HEALTHY</text>
            <text x="175" y="290" textAnchor="middle" fill="#94a3b8" fontSize="10">CPU: 45%</text>
            <text x="175" y="305" textAnchor="middle" fill="#94a3b8" fontSize="10">Memory: 60%</text>

            <rect x="300" y="200" width="150" height="120" fill="rgba(16, 185, 129, 0.1)" stroke="#10b981" strokeWidth="2" rx="6"/>
            <text x="375" y="230" textAnchor="middle" fill="#10b981" fontSize="14" fontWeight="bold">Service B</text>
            <circle cx="375" cy="250" r="12" fill="#10b981"/>
            <text x="375" y="255" textAnchor="middle" fill="#fff" fontSize="12" fontWeight="bold">✓</text>
            <text x="375" y="275" textAnchor="middle" fill="#10b981" fontSize="12">HEALTHY</text>
            <text x="375" y="290" textAnchor="middle" fill="#94a3b8" fontSize="10">CPU: 30%</text>
            <text x="375" y="305" textAnchor="middle" fill="#94a3b8" fontSize="10">Memory: 40%</text>

            {/* Unhealthy Service */}
            <rect x="500" y="200" width="150" height="120" fill="rgba(239, 68, 68, 0.1)" stroke="#ef4444" strokeWidth="2" rx="6"/>
            <text x="575" y="230" textAnchor="middle" fill="#ef4444" fontSize="14" fontWeight="bold">Service C</text>
            <circle cx="575" cy="250" r="12" fill="#ef4444"/>
            <text x="575" y="255" textAnchor="middle" fill="#fff" fontSize="12" fontWeight="bold">✗</text>
            <text x="575" y="275" textAnchor="middle" fill="#ef4444" fontSize="12">UNHEALTHY</text>
            <text x="575" y="290" textAnchor="middle" fill="#94a3b8" fontSize="10">CPU: 95%</text>
            <text x="575" y="305" textAnchor="middle" fill="#94a3b8" fontSize="10">Memory: 90%</text>

            {/* New Instance Being Started */}
            <rect x="700" y="200" width="150" height="120" fill="rgba(245, 158, 11, 0.1)" stroke="#f59e0b" strokeWidth="2" rx="6" strokeDasharray="5,5"/>
            <text x="775" y="230" textAnchor="middle" fill="#f59e0b" fontSize="14" fontWeight="bold">Service C2</text>
            <circle cx="775" cy="250" r="12" fill="#f59e0b"/>
            <text x="775" y="255" textAnchor="middle" fill="#fff" fontSize="12" fontWeight="bold">?</text>
            <text x="775" y="275" textAnchor="middle" fill="#f59e0b" fontSize="12">STARTING</text>
            <text x="775" y="290" textAnchor="middle" fill="#94a3b8" fontSize="10">CPU: 15%</text>
            <text x="775" y="305" textAnchor="middle" fill="#94a3b8" fontSize="10">Memory: 25%</text>

            {/* Health Check Endpoints */}
            <rect x="250" y="380" width="400" height="80" fill="rgba(139, 92, 246, 0.1)" stroke="#8b5cf6" strokeWidth="2" rx="6"/>
            <text x="450" y="410" textAnchor="middle" fill="#8b5cf6" fontSize="16" fontWeight="bold">Health Check Endpoints</text>
            <text x="300" y="430" fill="#94a3b8" fontSize="11">GET /actuator/health/liveness</text>
            <text x="300" y="445" fill="#94a3b8" fontSize="11">GET /actuator/health/readiness</text>
            <text x="550" y="430" fill="#94a3b8" fontSize="11">Response: 200 OK / 503 Service Unavailable</text>
            <text x="550" y="445" fill="#94a3b8" fontSize="11">Checks: DB, Redis, External APIs</text>

            {/* Auto-scaling Trigger */}
            <rect x="100" y="500" width="300" height="60" fill="rgba(16, 185, 129, 0.1)" stroke="#10b981" strokeWidth="2" rx="6"/>
            <text x="250" y="525" textAnchor="middle" fill="#10b981" fontSize="14" fontWeight="bold">Auto-scaling Triggered</text>
            <text x="250" y="545" textAnchor="middle" fill="#94a3b8" fontSize="12">New instance started due to unhealthy service</text>

            {/* Alert System */}
            <rect x="500" y="500" width="300" height="60" fill="rgba(239, 68, 68, 0.1)" stroke="#ef4444" strokeWidth="2" rx="6"/>
            <text x="650" y="525" textAnchor="middle" fill="#ef4444" fontSize="14" fontWeight="bold">Alert Sent</text>
            <text x="650" y="545" textAnchor="middle" fill="#94a3b8" fontSize="12">Operations team notified of service failure</text>

            {/* Traffic Flow */}
            <defs>
              <marker id="arrow-health" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="#10b981"/>
              </marker>
              <marker id="arrow-health-blocked" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="#ef4444"/>
              </marker>
            </defs>

            {/* Active traffic */}
            <line x1="400" y1="110" x2="175" y2="200" stroke="#10b981" strokeWidth="3" markerEnd="url(#arrow-health)"/>
            <line x1="500" y1="110" x2="375" y2="200" stroke="#10b981" strokeWidth="3" markerEnd="url(#arrow-health)"/>
            
            {/* Blocked traffic */}
            <line x1="520" y1="110" x2="575" y2="200" stroke="#ef4444" strokeWidth="3" strokeDasharray="5,5" markerEnd="url(#arrow-health-blocked)"/>
            <text x="600" y="150" fill="#ef4444" fontSize="10">BLOCKED</text>

            {/* Health check probes */}
            <line x1="450" y1="110" x2="450" y2="380" stroke="#8b5cf6" strokeWidth="1" strokeDasharray="3,3"/>

            <text x="450" y="590" textAnchor="middle" fill="#94a3b8" fontSize="14">
              Continuous health monitoring with automatic traffic routing and scaling
            </text>
          </>
        )
      },
      'log-aggregation': {
        viewBox: "0 0 1000 600",
        diagram: (
          <>
            {/* Microservices generating logs */}
            <rect x="50" y="100" width="120" height="80" fill="#1e293b" stroke="#3b82f6" strokeWidth="2" rx="4"/>
            <text x="110" y="135" textAnchor="middle" fill="#3b82f6" fontSize="12">Order Service</text>
            <text x="110" y="155" textAnchor="middle" fill="#94a3b8" fontSize="9">Logs: order.log</text>

            <rect x="200" y="100" width="120" height="80" fill="#1e293b" stroke="#10b981" strokeWidth="2" rx="4"/>
            <text x="260" y="135" textAnchor="middle" fill="#10b981" fontSize="12">Payment Service</text>
            <text x="260" y="155" textAnchor="middle" fill="#94a3b8" fontSize="9">Logs: payment.log</text>

            <rect x="350" y="100" width="120" height="80" fill="#1e293b" stroke="#ef4444" strokeWidth="2" rx="4"/>
            <text x="410" y="135" textAnchor="middle" fill="#ef4444" fontSize="12">Risk Service</text>
            <text x="410" y="155" textAnchor="middle" fill="#94a3b8" fontSize="9">Logs: risk.log</text>

            <rect x="500" y="100" width="120" height="80" fill="#1e293b" stroke="#8b5cf6" strokeWidth="2" rx="4"/>
            <text x="560" y="135" textAnchor="middle" fill="#8b5cf6" fontSize="12">Portfolio Service</text>
            <text x="560" y="155" textAnchor="middle" fill="#94a3b8" fontSize="9">Logs: portfolio.log</text>

            {/* Log Shippers */}
            <rect x="200" y="230" width="220" height="60" fill="rgba(245, 158, 11, 0.1)" stroke="#f59e0b" strokeWidth="2" rx="6"/>
            <text x="310" y="255" textAnchor="middle" fill="#f59e0b" fontSize="14" fontWeight="bold">Log Shippers</text>
            <text x="310" y="275" textAnchor="middle" fill="#94a3b8" fontSize="12">Filebeat / Fluentd / Logstash</text>

            {/* Message Queue */}
            <rect x="200" y="340" width="220" height="60" fill="rgba(139, 92, 246, 0.1)" stroke="#8b5cf6" strokeWidth="2" rx="6"/>
            <text x="310" y="365" textAnchor="middle" fill="#8b5cf6" fontSize="14" fontWeight="bold">Message Queue</text>
            <text x="310" y="385" textAnchor="middle" fill="#94a3b8" fontSize="12">Apache Kafka / RabbitMQ</text>

            {/* Elasticsearch Cluster */}
            <rect x="650" y="200" width="200" height="200" fill="rgba(16, 185, 129, 0.1)" stroke="#10b981" strokeWidth="2" rx="8"/>
            <text x="750" y="230" textAnchor="middle" fill="#10b981" fontSize="16" fontWeight="bold">Elasticsearch</text>
            
            <rect x="670" y="250" width="70" height="40" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="705" y="275" textAnchor="middle" fill="#10b981" fontSize="10">Node 1</text>
            
            <rect x="750" y="250" width="70" height="40" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="785" y="275" textAnchor="middle" fill="#10b981" fontSize="10">Node 2</text>
            
            <rect x="670" y="300" width="70" height="40" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="705" y="325" textAnchor="middle" fill="#10b981" fontSize="10">Node 3</text>
            
            <rect x="750" y="300" width="70" height="40" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="785" y="325" textAnchor="middle" fill="#10b981" fontSize="10">Node 4</text>
            
            <text x="750" y="365" textAnchor="middle" fill="#94a3b8" fontSize="12">Distributed Search & Analytics</text>

            {/* Kibana Dashboard */}
            <rect x="650" y="450" width="200" height="100" fill="rgba(59, 130, 246, 0.1)" stroke="#3b82f6" strokeWidth="2" rx="6"/>
            <text x="750" y="480" textAnchor="middle" fill="#3b82f6" fontSize="16" fontWeight="bold">Kibana Dashboard</text>
            <text x="750" y="500" textAnchor="middle" fill="#94a3b8" fontSize="11">• Log Search & Filter</text>
            <text x="750" y="515" textAnchor="middle" fill="#94a3b8" fontSize="11">• Real-time Monitoring</text>
            <text x="750" y="530" textAnchor="middle" fill="#94a3b8" fontSize="11">• Custom Dashboards</text>

            {/* Sample Log Entries */}
            <rect x="50" y="450" width="550" height="120" fill="rgba(31, 41, 55, 0.5)" stroke="#64748b" strokeWidth="1" rx="6"/>
            <text x="325" y="475" textAnchor="middle" fill="#64748b" fontSize="14" fontWeight="bold">Sample Aggregated Logs</text>
            
            <text x="60" y="495" fill="#3b82f6" fontSize="10">[2024-01-15 10:30:15] order-service INFO: Order created &#123;orderId: ORD-001, userId: USR-123&#125;</text>
            <text x="60" y="510" fill="#ef4444" fontSize="10">[2024-01-15 10:30:16] risk-service ERROR: Risk calculation failed &#123;orderId: ORD-001, error: timeout&#125;</text>
            <text x="60" y="525" fill="#10b981" fontSize="10">[2024-01-15 10:30:17] payment-service INFO: Payment processed &#123;paymentId: PAY-456, amount: $1000&#125;</text>
            <text x="60" y="540" fill="#8b5cf6" fontSize="10">[2024-01-15 10:30:18] portfolio-service WARN: Position limit exceeded &#123;userId: USR-123, limit: 80%&#125;</text>
            <text x="60" y="555" fill="#f59e0b" fontSize="10">[2024-01-15 10:30:19] order-service DEBUG: Order state transition &#123;orderId: ORD-001, from: pending, to: executed&#125;</text>

            {/* Flow arrows */}
            <defs>
              <marker id="arrow-log" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="#9ca3af"/>
              </marker>
            </defs>

            {/* Services to shippers */}
            <line x1="110" y1="180" x2="250" y2="230" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-log)"/>
            <line x1="260" y1="180" x2="290" y2="230" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-log)"/>
            <line x1="410" y1="180" x2="330" y2="230" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-log)"/>
            <line x1="560" y1="180" x2="370" y2="230" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-log)"/>

            {/* Shippers to queue */}
            <line x1="310" y1="290" x2="310" y2="340" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-log)"/>

            {/* Queue to Elasticsearch */}
            <line x1="420" y1="370" x2="650" y2="300" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-log)"/>

            {/* Elasticsearch to Kibana */}
            <line x1="750" y1="400" x2="750" y2="450" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-log)"/>

            <text x="500" y="590" textAnchor="middle" fill="#94a3b8" fontSize="14">
              Centralized logging with search, analytics, and real-time monitoring
            </text>
          </>
        )
      }
    };
    
    return umlDiagrams[patternId] || null;
  };

  const getPatternDetails = (patternId) => {
    const details = {
      'api-gateway': {
        functions: [
          'Single entry point for all client requests',
          'Request routing to appropriate microservices',
          'Authentication and authorization',
          'Rate limiting and throttling',
          'Request/response transformation',
          'Protocol translation (HTTP to gRPC)',
          'Cross-cutting concerns handling',
          'Load balancing across service instances',
          'SSL termination and security',
          'Request/response logging and monitoring'
        ],
        workflow: `1. Client sends request to API Gateway
2. Gateway authenticates the request
3. Rate limiting rules are applied
4. Request is routed to appropriate service
5. Service processes the request
6. Response is transformed if needed
7. Gateway logs the transaction
8. Response is returned to client
9. Monitoring metrics are collected
10. Security policies are enforced`,
        code: `// API Gateway Implementation with Spring Cloud Gateway
@RestController
@CrossOrigin
public class TradingApiGateway {
    
    private final OrderServiceClient orderService;
    private final PortfolioServiceClient portfolioService;
    private final RiskServiceClient riskService;
    private final AuthenticationService authService;
    private final RateLimitingService rateLimiter;
    
    // Order Management Endpoints
    @PostMapping("/api/v1/orders")
    @PreAuthorize("hasRole('TRADER')")
    public ResponseEntity<OrderResponse> createOrder(
            @RequestHeader("Authorization") String token,
            @RequestBody @Valid OrderRequest request) {
        
        try {
            // 1. Authenticate and authorize
            UserContext user = authService.validateToken(token);
            if (!user.hasPermission("CREATE_ORDER")) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }
            
            // 2. Apply rate limiting
            if (!rateLimiter.allowRequest(user.getUserId(), "CREATE_ORDER")) {
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS)
                    .header("Retry-After", "60")
                    .build();
            }
            
            // 3. Validate request
            ValidationResult validation = validateOrderRequest(request);
            if (!validation.isValid()) {
                return ResponseEntity.badRequest()
                    .body(OrderResponse.error(validation.getErrors()));
            }
            
            // 4. Route to order service
            OrderResponse response = orderService.createOrder(request, user);
            
            // 5. Log transaction
            auditLog.info("Order created: userId={}, orderId={}, symbol={}", 
                         user.getUserId(), response.getOrderId(), request.getSymbol());
            
            return ResponseEntity.ok(response);
            
        } catch (ServiceUnavailableException e) {
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                .body(OrderResponse.error("Order service temporarily unavailable"));
        } catch (Exception e) {
            log.error("Order creation failed", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(OrderResponse.error("Internal server error"));
        }
    }
    
    // Portfolio Management Endpoints
    @GetMapping("/api/v1/portfolio/{userId}")
    @PreAuthorize("hasRole('TRADER') or hasRole('RISK_MANAGER')")
    public ResponseEntity<PortfolioResponse> getPortfolio(
            @PathVariable String userId,
            @RequestHeader("Authorization") String token) {
        
        try {
            UserContext user = authService.validateToken(token);
            
            // Check if user can access this portfolio
            if (!user.canAccessPortfolio(userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }
            
            // Rate limiting
            if (!rateLimiter.allowRequest(user.getUserId(), "GET_PORTFOLIO")) {
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
            }
            
            // Route to portfolio service
            PortfolioResponse portfolio = portfolioService.getPortfolio(userId);
            
            // Transform response based on user role
            if (user.hasRole("RISK_MANAGER")) {
                portfolio = enhanceWithRiskMetrics(portfolio);
            }
            
            return ResponseEntity.ok(portfolio);
            
        } catch (Exception e) {
            log.error("Portfolio retrieval failed for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    // Risk Assessment Endpoints
    @PostMapping("/api/v1/risk/assess")
    @PreAuthorize("hasRole('RISK_MANAGER')")
    public ResponseEntity<RiskAssessment> assessRisk(
            @RequestBody @Valid RiskAssessmentRequest request,
            @RequestHeader("Authorization") String token) {
        
        try {
            UserContext user = authService.validateToken(token);
            
            // Enhanced rate limiting for risk operations
            if (!rateLimiter.allowRequest(user.getUserId(), "ASSESS_RISK", 10, Duration.ofMinutes(1))) {
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
            }
            
            // Route to risk service with timeout
            CompletableFuture<RiskAssessment> riskFuture = CompletableFuture
                .supplyAsync(() -> riskService.assessRisk(request))
                .orTimeout(5, TimeUnit.SECONDS);
            
            RiskAssessment assessment = riskFuture.get();
            
            // Log high-risk assessments
            if (assessment.getRiskLevel() == RiskLevel.HIGH) {
                alertService.sendHighRiskAlert(user, assessment);
            }
            
            return ResponseEntity.ok(assessment);
            
        } catch (TimeoutException e) {
            return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT)
                .body(RiskAssessment.timeout("Risk assessment timed out"));
        } catch (Exception e) {
            log.error("Risk assessment failed", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    // Health Check Endpoint
    @GetMapping("/api/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", Instant.now());
        
        // Check downstream services
        Map<String, String> services = new HashMap<>();
        services.put("orderService", orderService.isHealthy() ? "UP" : "DOWN");
        services.put("portfolioService", portfolioService.isHealthy() ? "UP" : "DOWN");
        services.put("riskService", riskService.isHealthy() ? "UP" : "DOWN");
        
        health.put("services", services);
        
        boolean allHealthy = services.values().stream().allMatch("UP"::equals);
        return ResponseEntity.status(allHealthy ? HttpStatus.OK : HttpStatus.SERVICE_UNAVAILABLE)
                           .body(health);
    }
    
    // Request transformation helper
    private ValidationResult validateOrderRequest(OrderRequest request) {
        List<String> errors = new ArrayList<>();
        
        if (request.getSymbol() == null || request.getSymbol().trim().isEmpty()) {
            errors.add("Symbol is required");
        }
        
        if (request.getQuantity() <= 0) {
            errors.add("Quantity must be positive");
        }
        
        if (request.getPrice() != null && request.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
            errors.add("Price must be positive");
        }
        
        return errors.isEmpty() ? ValidationResult.valid() : ValidationResult.invalid(errors);
    }
    
    // Response enhancement for risk managers
    private PortfolioResponse enhanceWithRiskMetrics(PortfolioResponse portfolio) {
        RiskMetrics risk = riskService.calculatePortfolioRisk(portfolio);
        return portfolio.toBuilder()
                       .riskMetrics(risk)
                       .build();
    }
}

// Configuration for API Gateway routing
@Configuration
@EnableWebFluxSecurity
public class GatewayConfig {
    
    @Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
        return builder.routes()
            // Order Service Routes
            .route("orders", r -> r.path("/api/v1/orders/**")
                .filters(f -> f
                    .addRequestHeader("X-Gateway-Request-Id", UUID.randomUUID().toString())
                    .addResponseHeader("X-Gateway-Response-Time", String.valueOf(System.currentTimeMillis()))
                    .circuitBreaker(c -> c.setName("order-service-cb").setFallbackUri("forward:/fallback/orders"))
                )
                .uri("http://order-service:8081"))
            
            // Portfolio Service Routes
            .route("portfolio", r -> r.path("/api/v1/portfolio/**")
                .filters(f -> f
                    .addRequestHeader("X-Gateway-Request-Id", UUID.randomUUID().toString())
                    .retry(3)
                    .circuitBreaker(c -> c.setName("portfolio-service-cb").setFallbackUri("forward:/fallback/portfolio"))
                )
                .uri("http://portfolio-service:8082"))
            
            // Risk Service Routes  
            .route("risk", r -> r.path("/api/v1/risk/**")
                .filters(f -> f
                    .addRequestHeader("X-Gateway-Request-Id", UUID.randomUUID().toString())
                    .requestRateLimiter(c -> c
                        .setRateLimiter(redisRateLimiter())
                        .setKeyResolver(userKeyResolver()))
                    .circuitBreaker(c -> c.setName("risk-service-cb"))
                )
                .uri("http://risk-service:8083"))
            
            .build();
    }
    
    @Bean
    public RedisRateLimiter redisRateLimiter() {
        return new RedisRateLimiter(10, 20, 1); // 10 requests per second, burst of 20
    }
    
    @Bean
    public KeyResolver userKeyResolver() {
        return exchange -> exchange.getRequest().getHeaders().getFirst("X-User-ID");
    }
}`,
        technologies: {
          'Spring Cloud Gateway': 'Reactive API gateway with routing and filtering',
          'Netflix Zuul': 'JVM-based router and server-side load balancer',
          'Kong': 'Cloud-native API gateway with plugins',
          'AWS API Gateway': 'Fully managed API gateway service'
        }
      },
      'circuit-breaker': {
        functions: [
          'Prevent cascading failures in distributed systems',
          'Fast failure detection and response',
          'Automatic recovery detection',
          'Fallback mechanism execution',
          'System stability maintenance',
          'Resource protection under load',
          'Monitoring and alerting integration',
          'Configurable failure thresholds',
          'Half-open state testing',
          'Metrics collection and reporting'
        ],
        workflow: `1. Monitor service call success/failure rates
2. Track failure count and response times
3. Open circuit when failure threshold exceeded
4. Return cached/fallback response immediately
5. Periodically test service in half-open state
6. Close circuit if service recovers
7. Reset failure counters on recovery
8. Log state transitions for monitoring
9. Alert operations team on circuit open
10. Collect metrics for system health`,
        code: `// Circuit Breaker Implementation using Resilience4j
@Service
@Slf4j
public class TradingServiceWithCircuitBreaker {
    
    private final RiskServiceClient riskServiceClient;
    private final PortfolioServiceClient portfolioServiceClient;
    private final CircuitBreakerRegistry circuitBreakerRegistry;
    
    public TradingServiceWithCircuitBreaker(
            RiskServiceClient riskServiceClient,
            PortfolioServiceClient portfolioServiceClient,
            CircuitBreakerRegistry circuitBreakerRegistry) {
        this.riskServiceClient = riskServiceClient;
        this.portfolioServiceClient = portfolioServiceClient;
        this.circuitBreakerRegistry = circuitBreakerRegistry;
    }
    
    // Circuit breaker for risk service calls
    @CircuitBreaker(name = "risk-service", fallbackMethod = "fallbackRiskAssessment")
    @TimeLimiter(name = "risk-service")
    @Retry(name = "risk-service")
    public CompletableFuture<RiskAssessment> assessOrderRisk(OrderRequest order) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.debug("Calling risk service for order: {}", order.getOrderId());
                
                RiskAssessmentRequest request = RiskAssessmentRequest.builder()
                    .orderId(order.getOrderId())
                    .symbol(order.getSymbol())
                    .quantity(order.getQuantity())
                    .price(order.getPrice())
                    .orderType(order.getOrderType())
                    .userId(order.getUserId())
                    .build();
                
                RiskAssessment assessment = riskServiceClient.assessRisk(request);
                
                log.debug("Risk assessment completed: orderId={}, riskLevel={}", 
                         order.getOrderId(), assessment.getRiskLevel());
                
                return assessment;
                
            } catch (ServiceException e) {
                log.error("Risk service call failed: orderId={}, error={}", 
                         order.getOrderId(), e.getMessage());
                throw e;
            }
        });
    }
    
    // Fallback method for risk assessment
    public CompletableFuture<RiskAssessment> fallbackRiskAssessment(OrderRequest order, Exception ex) {
        log.warn("Risk service circuit breaker activated, using fallback: orderId={}, error={}", 
                order.getOrderId(), ex.getMessage());
        
        // Return conservative risk assessment
        RiskAssessment fallbackAssessment = RiskAssessment.builder()
            .orderId(order.getOrderId())
            .riskLevel(RiskLevel.HIGH) // Conservative approach
            .riskScore(0.8) // High risk score
            .assessmentType(AssessmentType.FALLBACK)
            .timestamp(Instant.now())
            .reason("Risk service unavailable - using fallback assessment")
            .build();
        
        // Alert risk managers about fallback usage
        alertService.sendRiskServiceFallbackAlert(order, ex);
        
        return CompletableFuture.completedFuture(fallbackAssessment);
    }
    
    // Circuit breaker for portfolio service calls
    @CircuitBreaker(name = "portfolio-service", fallbackMethod = "fallbackPortfolioUpdate")
    @TimeLimiter(name = "portfolio-service")
    @Retry(name = "portfolio-service")
    public CompletableFuture<PortfolioUpdateResult> updatePortfolio(TradeExecution execution) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.debug("Updating portfolio for trade: {}", execution.getTradeId());
                
                PortfolioUpdateRequest request = PortfolioUpdateRequest.builder()
                    .tradeId(execution.getTradeId())
                    .userId(execution.getUserId())
                    .symbol(execution.getSymbol())
                    .quantity(execution.getQuantity())
                    .price(execution.getExecutedPrice())
                    .side(execution.getSide())
                    .executionTime(execution.getExecutionTime())
                    .build();
                
                PortfolioUpdateResult result = portfolioServiceClient.updatePortfolio(request);
                
                log.debug("Portfolio updated successfully: tradeId={}, newPosition={}", 
                         execution.getTradeId(), result.getNewPosition());
                
                return result;
                
            } catch (ServiceException e) {
                log.error("Portfolio service call failed: tradeId={}, error={}", 
                         execution.getTradeId(), e.getMessage());
                throw e;
            }
        });
    }
    
    // Fallback method for portfolio updates
    public CompletableFuture<PortfolioUpdateResult> fallbackPortfolioUpdate(TradeExecution execution, Exception ex) {
        log.error("Portfolio service circuit breaker activated: tradeId={}, error={}", 
                 execution.getTradeId(), ex.getMessage());
        
        // Queue update for later processing
        portfolioUpdateQueue.enqueue(execution);
        
        // Return placeholder result
        PortfolioUpdateResult fallbackResult = PortfolioUpdateResult.builder()
            .tradeId(execution.getTradeId())
            .status(UpdateStatus.QUEUED)
            .message("Portfolio update queued due to service unavailability")
            .timestamp(Instant.now())
            .build();
        
        // Alert operations team
        alertService.sendPortfolioServiceFallbackAlert(execution, ex);
        
        return CompletableFuture.completedFuture(fallbackResult);
    }
    
    // Method to check circuit breaker states
    public Map<String, CircuitBreakerState> getCircuitBreakerStates() {
        Map<String, CircuitBreakerState> states = new HashMap<>();
        
        circuitBreakerRegistry.getAllCircuitBreakers().forEach(cb -> {
            CircuitBreaker.State state = cb.getState();
            CircuitBreaker.Metrics metrics = cb.getMetrics();
            
            CircuitBreakerState cbState = CircuitBreakerState.builder()
                .name(cb.getName())
                .state(state)
                .failureRate(metrics.getFailureRate())
                .numberOfCalls(metrics.getNumberOfCalls())
                .numberOfFailedCalls(metrics.getNumberOfFailedCalls())
                .numberOfSuccessfulCalls(metrics.getNumberOfSuccessfulCalls())
                .build();
            
            states.put(cb.getName(), cbState);
        });
        
        return states;
    }
    
    // Custom circuit breaker event listener
    @EventListener
    public void handleCircuitBreakerEvent(CircuitBreakerEvent event) {
        switch (event.getEventType()) {
            case STATE_TRANSITION:
                CircuitBreakerOnStateTransitionEvent stateEvent = (CircuitBreakerOnStateTransitionEvent) event;
                log.warn("Circuit breaker '{}' transitioned from {} to {}", 
                        event.getCircuitBreakerName(),
                        stateEvent.getStateTransition().getFromState(),
                        stateEvent.getStateTransition().getToState());
                
                // Send alert for critical state transitions
                if (stateEvent.getStateTransition().getToState() == CircuitBreaker.State.OPEN) {
                    alertService.sendCircuitBreakerOpenAlert(event.getCircuitBreakerName());
                }
                break;
                
            case ERROR:
                CircuitBreakerOnErrorEvent errorEvent = (CircuitBreakerOnErrorEvent) event;
                log.debug("Circuit breaker '{}' recorded error: {}", 
                         event.getCircuitBreakerName(), errorEvent.getThrowable().getMessage());
                break;
                
            case SUCCESS:
                log.debug("Circuit breaker '{}' recorded success", event.getCircuitBreakerName());
                break;
        }
        
        // Update metrics
        meterRegistry.counter("circuit_breaker_events_total", 
                             "name", event.getCircuitBreakerName(),
                             "type", event.getEventType().toString()).increment();
    }
}

// Configuration for circuit breakers
@Configuration
public class CircuitBreakerConfiguration {
    
    @Bean
    public CircuitBreakerConfig riskServiceCircuitBreakerConfig() {
        return CircuitBreakerConfig.custom()
            .failureRateThreshold(50) // Open circuit if 50% of calls fail
            .waitDurationInOpenState(Duration.ofSeconds(30)) // Stay open for 30 seconds
            .slidingWindowSize(20) // Consider last 20 calls
            .minimumNumberOfCalls(5) // At least 5 calls before calculating failure rate
            .permittedNumberOfCallsInHalfOpenState(3) // Allow 3 calls in half-open state
            .slowCallRateThreshold(50) // Consider calls > 2s as slow
            .slowCallDurationThreshold(Duration.ofSeconds(2))
            .recordExceptions(ServiceException.class, TimeoutException.class)
            .ignoreExceptions(ValidationException.class)
            .build();
    }
    
    @Bean
    public CircuitBreakerConfig portfolioServiceCircuitBreakerConfig() {
        return CircuitBreakerConfig.custom()
            .failureRateThreshold(60) // More lenient for portfolio service
            .waitDurationInOpenState(Duration.ofSeconds(60)) // Longer recovery time
            .slidingWindowSize(30)
            .minimumNumberOfCalls(10)
            .permittedNumberOfCallsInHalfOpenState(5)
            .build();
    }
    
    @Bean
    public TimeLimiterConfig timeLimiterConfig() {
        return TimeLimiterConfig.custom()
            .timeoutDuration(Duration.ofSeconds(3))
            .cancelRunningFuture(true)
            .build();
    }
    
    @Bean
    public RetryConfig retryConfig() {
        return RetryConfig.custom()
            .maxAttempts(3)
            .waitDuration(Duration.ofMillis(500))
            .exponentialBackoffMultiplier(2)
            .retryExceptions(ServiceException.class)
            .ignoreExceptions(ValidationException.class)
            .build();
    }
}`,
        technologies: {
          'Resilience4j': 'Fault tolerance library for Java applications',
          'Netflix Hystrix': 'Latency and fault tolerance library (deprecated)',
          'Spring Cloud Circuit Breaker': 'Abstraction over circuit breaker implementations',
          'Istio': 'Service mesh with built-in circuit breaker capabilities'
        }
      },
      'service-mesh': {
        functions: [
          'Manage service-to-service communication',
          'Provide traffic routing and load balancing',
          'Implement security policies (mTLS)',
          'Enable observability and monitoring',
          'Handle circuit breaking and retries',
          'Support canary deployments',
          'Enforce rate limiting and quotas',
          'Provide distributed tracing',
          'Enable A/B testing',
          'Centralize configuration management'
        ],
        workflow: `1. Deploy sidecar proxies alongside services
2. Configure service mesh control plane
3. Define traffic policies and routing rules
4. Implement security policies (mTLS, RBAC)
5. Enable observability features
6. Services communicate through sidecars
7. Control plane manages configuration
8. Collect metrics and traces
9. Apply policies automatically
10. Monitor service mesh health`,
        code: `// Istio Service Mesh Configuration for Trading Platform
// Virtual Service for Order Service
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: order-service
spec:
  http:
  - match:
    - headers:
        canary:
          exact: "true"
    route:
    - destination:
        host: order-service
        subset: v2
      weight: 100
  - route:
    - destination:
        host: order-service
        subset: v1
      weight: 90
    - destination:
        host: order-service
        subset: v2
      weight: 10

---
// Destination Rule with Circuit Breaker
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: order-service
spec:
  host: order-service
  trafficPolicy:
    circuitBreaker:
      connectionTimeout: 10s
      retries:
        attempts: 3
        perTryTimeout: 5s
      maxConnections: 10
      maxPendingRequests: 5
      maxRetries: 3
      consecutiveErrors: 5
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 50
  subsets:
  - name: v1
    labels:
      version: v1
  - name: v2
    labels:
      version: v2

---
// Security Policy for mTLS
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: trading-services-mtls
spec:
  mtls:
    mode: STRICT

---
// Authorization Policy
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: order-service-authz
spec:
  selector:
    matchLabels:
      app: order-service
  rules:
  - from:
    - source:
        principals: ["cluster.local/ns/default/sa/api-gateway"]
  - to:
    - operation:
        methods: ["POST"]
        paths: ["/api/v1/orders"]
  - when:
    - key: request.headers[authorization]
      values: ["Bearer *"]

---
// Java Application with Service Mesh Integration
@RestController
@RequestMapping("/api/v1/orders")
public class OrderServiceController {
    
    private final OrderService orderService;
    private final RiskServiceClient riskServiceClient;
    private final MeterRegistry meterRegistry;
    
    @PostMapping
    public ResponseEntity<OrderResponse> createOrder(@RequestBody OrderRequest request,
                                                   HttpServletRequest httpRequest) {
        
        Timer.Sample sample = Timer.start(meterRegistry);
        
        try {
            // Extract trace context from Istio headers
            String traceId = httpRequest.getHeader("x-request-id");
            String spanId = httpRequest.getHeader("x-b3-spanid");
            
            log.info("Processing order creation: traceId={}, orderId={}", 
                    traceId, request.getOrderId());
            
            // Call risk service - will go through service mesh
            RiskAssessment riskAssessment = riskServiceClient.assessRisk(request);
            
            if (riskAssessment.getRiskLevel() == RiskLevel.HIGH) {
                meterRegistry.counter("orders.rejected", "reason", "high_risk").increment();
                return ResponseEntity.badRequest()
                    .body(OrderResponse.rejection("Order rejected due to high risk"));
            }
            
            // Create order
            Order order = orderService.createOrder(request, riskAssessment);
            
            // Emit business event (will be traced by service mesh)
            orderEventPublisher.publishOrderCreated(order);
            
            meterRegistry.counter("orders.created", "status", "success").increment();
            
            return ResponseEntity.ok(OrderResponse.success(order));
            
        } catch (Exception e) {
            log.error("Order creation failed: traceId={}, error={}", traceId, e.getMessage(), e);
            meterRegistry.counter("orders.created", "status", "error").increment();
            throw e;
            
        } finally {
            sample.stop(Timer.builder("order.creation.duration")
                .register(meterRegistry));
        }
    }
}

// Risk Service Client with Service Mesh Features
@Component
public class RiskServiceClient {
    
    private final WebClient webClient;
    private final MeterRegistry meterRegistry;
    
    public RiskServiceClient(WebClient.Builder webClientBuilder, 
                           MeterRegistry meterRegistry) {
        // Service mesh handles service discovery and load balancing
        this.webClient = webClientBuilder
            .baseUrl("http://risk-service") // Service mesh resolves this
            .build();
        this.meterRegistry = meterRegistry;
    }
    
    public RiskAssessment assessRisk(OrderRequest orderRequest) {
        return webClient.post()
            .uri("/api/v1/risk/assess")
            .bodyValue(RiskAssessmentRequest.from(orderRequest))
            .headers(headers -> {
                // Propagate trace headers (Istio handles this automatically)
                headers.add("x-user-id", orderRequest.getUserId());
                headers.add("x-request-timeout", "5000");
            })
            .retrieve()
            .onStatus(HttpStatus::is4xxClientError, response -> {
                meterRegistry.counter("risk_service.errors", "type", "client_error").increment();
                return Mono.error(new RiskServiceException("Client error: " + response.statusCode()));
            })
            .onStatus(HttpStatus::is5xxServerError, response -> {
                meterRegistry.counter("risk_service.errors", "type", "server_error").increment();
                return Mono.error(new RiskServiceException("Server error: " + response.statusCode()));
            })
            .bodyToMono(RiskAssessment.class)
            .timeout(Duration.ofSeconds(5))
            .doOnSuccess(result -> {
                meterRegistry.counter("risk_service.requests", "status", "success").increment();
                meterRegistry.timer("risk_service.response_time").record(Duration.ofMillis(
                    System.currentTimeMillis() - orderRequest.getTimestamp()));
            })
            .doOnError(error -> {
                meterRegistry.counter("risk_service.requests", "status", "error").increment();
                log.error("Risk assessment failed: {}", error.getMessage());
            })
            .block();
    }
}

// Configuration for Service Mesh Integration
@Configuration
public class ServiceMeshConfiguration {
    
    @Bean
    public WebClient.Builder webClientBuilder() {
        return WebClient.builder()
            .filter(tracingExchangeFilterFunction()) // Distributed tracing
            .filter(metricsExchangeFilterFunction()) // Metrics collection
            .defaultHeader("User-Agent", "trading-platform/1.0");
    }
    
    @Bean
    public ExchangeFilterFunction tracingExchangeFilterFunction() {
        return (request, next) -> {
            // Add distributed tracing headers
            ClientRequest filtered = ClientRequest.from(request)
                .headers(headers -> {
                    String traceId = MDC.get("traceId");
                    if (traceId != null) {
                        headers.add("x-trace-id", traceId);
                    }
                })
                .build();
            
            return next.exchange(filtered);
        };
    }
    
    @Bean
    public ExchangeFilterFunction metricsExchangeFilterFunction() {
        return ExchangeFilterFunctions.basicAuthentication("metrics", "password");
    }
}

// Kubernetes Deployment with Istio Injection
apiVersion: apps/v1
kind: Deployment
metadata:
  name: order-service
  labels:
    app: order-service
    version: v1
spec:
  replicas: 3
  selector:
    matchLabels:
      app: order-service
      version: v1
  template:
    metadata:
      labels:
        app: order-service
        version: v1
      annotations:
        sidecar.istio.io/inject: "true"  # Enable Istio sidecar injection
        sidecar.istio.io/proxyCPU: "100m"
        sidecar.istio.io/proxyMemory: "128Mi"
    spec:
      serviceAccountName: order-service-sa
      containers:
      - name: order-service
        image: trading-platform/order-service:v1.0
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "kubernetes,istio"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health/liveness
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: order-service
  labels:
    app: order-service
spec:
  ports:
  - port: 80
    targetPort: 8080
    name: http
  selector:
    app: order-service`,
        technologies: {
          'Istio': 'Complete service mesh with advanced traffic management',
          'Linkerd': 'Lightweight service mesh focused on simplicity',
          'Consul Connect': 'Service mesh solution from HashiCorp',
          'Envoy Proxy': 'High-performance proxy used by most service meshes'
        }
      },
      'saga': {
        functions: [
          'Manage distributed transactions across services',
          'Implement compensation logic for failures',
          'Coordinate complex business workflows',
          'Ensure data consistency without 2PC',
          'Handle long-running transactions',
          'Provide transaction rollback capabilities',
          'Monitor transaction progress',
          'Handle service failures gracefully',
          'Support both orchestration and choreography',
          'Maintain audit trail of transactions'
        ],
        workflow: `1. Client initiates saga transaction
2. Saga coordinator starts transaction
3. Execute first service transaction
4. Record transaction state and compensation
5. Continue to next service in sequence
6. If service fails, trigger compensation
7. Execute compensation actions in reverse order
8. Update saga state at each step
9. Complete saga or mark as failed
10. Notify client of final result`,
        code: `// Saga Pattern Implementation for Order Processing
@Service
@Slf4j
public class OrderProcessingSaga {
    
    private final PaymentService paymentService;
    private final InventoryService inventoryService;
    private final OrderService orderService;
    private final ShippingService shippingService;
    private final SagaStateRepository sagaStateRepository;
    private final EventPublisher eventPublisher;
    
    @Transactional
    public SagaResult processOrder(OrderSagaRequest request) {
        String sagaId = UUID.randomUUID().toString();
        
        // Initialize saga state
        SagaState sagaState = SagaState.builder()
            .sagaId(sagaId)
            .orderId(request.getOrderId())
            .status(SagaStatus.STARTED)
            .currentStep(0)
            .totalSteps(4)
            .compensations(new ArrayList<>())
            .createdAt(Instant.now())
            .build();
        
        sagaStateRepository.save(sagaState);
        
        try {
            log.info("Starting order processing saga: sagaId={}, orderId={}", 
                    sagaId, request.getOrderId());
            
            // Step 1: Process Payment
            sagaState = executeStep(sagaState, () -> {
                PaymentResult payment = paymentService.processPayment(
                    request.getOrderId(), 
                    request.getAmount(), 
                    request.getPaymentMethod()
                );
                
                // Record compensation action
                CompensationAction compensation = CompensationAction.builder()
                    .action("REFUND_PAYMENT")
                    .data(Map.of(
                        "paymentId", payment.getPaymentId(),
                        "amount", request.getAmount()
                    ))
                    .build();
                
                return StepResult.success(payment, compensation);
            });
            
            // Step 2: Reserve Inventory
            sagaState = executeStep(sagaState, () -> {
                InventoryReservation reservation = inventoryService.reserveItems(
                    request.getOrderId(),
                    request.getItems()
                );
                
                CompensationAction compensation = CompensationAction.builder()
                    .action("RELEASE_INVENTORY")
                    .data(Map.of(
                        "reservationId", reservation.getReservationId(),
                        "items", request.getItems()
                    ))
                    .build();
                
                return StepResult.success(reservation, compensation);
            });
            
            // Step 3: Create Order
            sagaState = executeStep(sagaState, () -> {
                Order order = orderService.createOrder(
                    request.getOrderId(),
                    request.getItems(),
                    request.getCustomerId()
                );
                
                CompensationAction compensation = CompensationAction.builder()
                    .action("CANCEL_ORDER")
                    .data(Map.of("orderId", order.getOrderId()))
                    .build();
                
                return StepResult.success(order, compensation);
            });
            
            // Step 4: Schedule Shipping
            sagaState = executeStep(sagaState, () -> {
                ShippingSchedule shipping = shippingService.scheduleShipping(
                    request.getOrderId(),
                    request.getShippingAddress()
                );
                
                CompensationAction compensation = CompensationAction.builder()
                    .action("CANCEL_SHIPPING")
                    .data(Map.of("shippingId", shipping.getShippingId()))
                    .build();
                
                return StepResult.success(shipping, compensation);
            });
            
            // Mark saga as completed
            sagaState.setStatus(SagaStatus.COMPLETED);
            sagaState.setCompletedAt(Instant.now());
            sagaStateRepository.save(sagaState);
            
            // Publish success event
            eventPublisher.publish(new OrderProcessingCompletedEvent(
                sagaId, request.getOrderId(), sagaState.getResults()
            ));
            
            log.info("Order processing saga completed successfully: sagaId={}, orderId={}", 
                    sagaId, request.getOrderId());
            
            return SagaResult.success(sagaId, sagaState.getResults());
            
        } catch (SagaException e) {
            log.error("Saga execution failed, starting compensation: sagaId={}, error={}", 
                     sagaId, e.getMessage());
            
            // Execute compensations in reverse order
            compensate(sagaState);
            
            return SagaResult.failure(sagaId, e.getMessage());
        }
    }
    
    private SagaState executeStep(SagaState sagaState, SagaStep step) {
        try {
            log.debug("Executing saga step: sagaId={}, step={}/{}", 
                     sagaState.getSagaId(), sagaState.getCurrentStep() + 1, sagaState.getTotalSteps());
            
            StepResult result = step.execute();
            
            // Update saga state
            sagaState.getResults().put("step_" + (sagaState.getCurrentStep() + 1), result.getData());
            sagaState.getCompensations().add(result.getCompensation());
            sagaState.setCurrentStep(sagaState.getCurrentStep() + 1);
            sagaState.setUpdatedAt(Instant.now());
            
            sagaStateRepository.save(sagaState);
            
            log.debug("Saga step completed: sagaId={}, step={}/{}", 
                     sagaState.getSagaId(), sagaState.getCurrentStep(), sagaState.getTotalSteps());
            
            return sagaState;
            
        } catch (Exception e) {
            sagaState.setStatus(SagaStatus.FAILED);
            sagaState.setFailureReason(e.getMessage());
            sagaState.setUpdatedAt(Instant.now());
            sagaStateRepository.save(sagaState);
            
            throw new SagaException("Step execution failed: " + e.getMessage(), e);
        }
    }
    
    private void compensate(SagaState sagaState) {
        log.info("Starting compensation for saga: sagaId={}", sagaState.getSagaId());
        
        sagaState.setStatus(SagaStatus.COMPENSATING);
        sagaStateRepository.save(sagaState);
        
        // Execute compensations in reverse order
        List<CompensationAction> compensations = new ArrayList<>(sagaState.getCompensations());
        Collections.reverse(compensations);
        
        for (CompensationAction compensation : compensations) {
            try {
                executeCompensation(compensation);
                log.debug("Compensation executed: sagaId={}, action={}", 
                         sagaState.getSagaId(), compensation.getAction());
                
            } catch (Exception e) {
                log.error("Compensation failed: sagaId={}, action={}, error={}", 
                         sagaState.getSagaId(), compensation.getAction(), e.getMessage());
                
                // Continue with other compensations even if one fails
                // In production, you might want to retry or alert operations
            }
        }
        
        sagaState.setStatus(SagaStatus.COMPENSATED);
        sagaState.setCompletedAt(Instant.now());
        sagaStateRepository.save(sagaState);
        
        // Publish compensation completed event
        eventPublisher.publish(new OrderProcessingCompensatedEvent(
            sagaState.getSagaId(), sagaState.getOrderId()
        ));
        
        log.info("Compensation completed for saga: sagaId={}", sagaState.getSagaId());
    }
    
    private void executeCompensation(CompensationAction compensation) {
        switch (compensation.getAction()) {
            case "REFUND_PAYMENT":
                String paymentId = (String) compensation.getData().get("paymentId");
                BigDecimal amount = (BigDecimal) compensation.getData().get("amount");
                paymentService.refundPayment(paymentId, amount);
                break;
                
            case "RELEASE_INVENTORY":
                String reservationId = (String) compensation.getData().get("reservationId");
                inventoryService.releaseReservation(reservationId);
                break;
                
            case "CANCEL_ORDER":
                String orderId = (String) compensation.getData().get("orderId");
                orderService.cancelOrder(orderId);
                break;
                
            case "CANCEL_SHIPPING":
                String shippingId = (String) compensation.getData().get("shippingId");
                shippingService.cancelShipping(shippingId);
                break;
                
            default:
                log.warn("Unknown compensation action: {}", compensation.getAction());
        }
    }
    
    // Get saga status for monitoring
    public SagaState getSagaState(String sagaId) {
        return sagaStateRepository.findById(sagaId)
            .orElseThrow(() -> new SagaNotFoundException("Saga not found: " + sagaId));
    }
    
    // Retry failed saga
    @Transactional
    public SagaResult retrySaga(String sagaId) {
        SagaState sagaState = getSagaState(sagaId);
        
        if (sagaState.getStatus() != SagaStatus.FAILED) {
            throw new IllegalStateException("Cannot retry saga in status: " + sagaState.getStatus());
        }
        
        log.info("Retrying saga: sagaId={}", sagaId);
        
        // Reset saga state for retry
        sagaState.setStatus(SagaStatus.STARTED);
        sagaState.setFailureReason(null);
        sagaState.setCurrentStep(0);
        sagaState.getCompensations().clear();
        sagaState.getResults().clear();
        sagaState.setUpdatedAt(Instant.now());
        
        return processOrder(OrderSagaRequest.fromSagaState(sagaState));
    }
}

// Saga State Entity
@Entity
@Table(name = "saga_states")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SagaState {
    
    @Id
    private String sagaId;
    
    @Column(nullable = false)
    private String orderId;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private SagaStatus status;
    
    private int currentStep;
    private int totalSteps;
    
    @Column(columnDefinition = "TEXT")
    private String failureReason;
    
    @Convert(converter = MapConverter.class)
    @Column(columnDefinition = "TEXT")
    private Map<String, Object> results = new HashMap<>();
    
    @Convert(converter = CompensationListConverter.class)
    @Column(columnDefinition = "TEXT")
    private List<CompensationAction> compensations = new ArrayList<>();
    
    @CreationTimestamp
    private Instant createdAt;
    
    @UpdateTimestamp
    private Instant updatedAt;
    
    private Instant completedAt;
}

// Functional interfaces for saga steps
@FunctionalInterface
public interface SagaStep {
    StepResult execute() throws Exception;
}

@Data
@Builder
public class StepResult {
    private Object data;
    private CompensationAction compensation;
    
    public static StepResult success(Object data, CompensationAction compensation) {
        return StepResult.builder()
                        .data(data)
                        .compensation(compensation)
                        .build();
    }
}

@Data
@Builder
public class CompensationAction {
    private String action;
    private Map<String, Object> data;
}

public enum SagaStatus {
    STARTED,
    IN_PROGRESS,
    COMPLETED,
    FAILED,
    COMPENSATING,
    COMPENSATED
}`,
        technologies: {
          'Spring Boot': 'Framework for building saga orchestrators',
          'Apache Camel': 'Integration framework with saga support',
          'Axon Framework': 'CQRS and Event Sourcing with saga patterns',
          'Eclipse MicroProfile': 'Long Running Actions (LRA) specification'
        }
      }
    };
    
    return details[patternId] || {
      functions: ['Pattern details coming soon...'],
      workflow: 'Detailed workflow will be provided.',
      code: '// Implementation examples will be added soon',
      technologies: {}
    };
  };

  const getCategoryStyle = (category) => {
    const styles = {
      communication: { color: '#3b82f6', bg: 'rgba(59, 130, 246, 0.1)' },
      data: { color: '#10b981', bg: 'rgba(16, 185, 129, 0.1)' },
      deployment: { color: '#ef4444', bg: 'rgba(239, 68, 68, 0.1)' },
      observability: { color: '#f59e0b', bg: 'rgba(245, 158, 11, 0.1)' }
    };
    return styles[category] || styles.communication;
  };

  return (
    <div style={{ 
      padding: '40px',
      backgroundColor: '#0f172a',
      minHeight: '100vh',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      <div style={{
        maxWidth: '1400px',
        margin: '0 auto'
      }}>
        {/* Header */}
        <div style={{ marginBottom: '40px', textAlign: 'center' }}>
          <div style={{
            display: 'inline-flex',
            alignItems: 'center',
            gap: '16px',
            marginBottom: '16px'
          }}>
            <Server size={32} style={{ color: '#fbbf24' }} />
            <h1 style={{
              fontSize: '32px',
              fontWeight: '700',
              color: 'white',
              margin: 0
            }}>
              Microservice Design Patterns
            </h1>
          </div>
          <p style={{
            fontSize: '16px',
            color: '#9ca3af',
            margin: 0,
            maxWidth: '600px',
            margin: '0 auto'
          }}>
            Essential patterns for building resilient and scalable microservice architectures
          </p>
        </div>

        {/* Pattern Categories */}
        <div style={{ marginBottom: '40px' }}>
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(4, 1fr)', 
            gap: '16px',
            marginBottom: '32px'
          }}>
            {['communication', 'data', 'deployment', 'observability'].map(category => {
              const style = getCategoryStyle(category);
              return (
                <div key={category} style={{
                  padding: '16px',
                  backgroundColor: style.bg,
                  border: `1px solid ${style.color}`,
                  borderRadius: '8px',
                  textAlign: 'center'
                }}>
                  <div style={{ color: style.color, fontSize: '14px', fontWeight: '600', textTransform: 'capitalize' }}>
                    {category === 'communication' ? 'Service Communication' : 
                     category === 'data' ? 'Data Management' :
                     category === 'deployment' ? 'Deployment' : 'Observability'} Patterns
                  </div>
                  <div style={{ color: '#9ca3af', fontSize: '12px', marginTop: '4px' }}>
                    {patterns.filter(p => p.category === category).length} patterns
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Patterns Grid */}
        <div style={{ position: 'relative', width: '100%', minHeight: '700px', marginBottom: '40px' }}>
          {/* Category Headers */}
          <div style={{ 
            position: 'absolute',
            top: '20px',
            left: '100px',
            right: '100px',
            display: 'grid',
            gridTemplateColumns: 'repeat(4, 1fr)',
            gap: '200px',
            pointerEvents: 'none'
          }}>
            {[
              { name: 'Service Communication', color: '#3b82f6' },
              { name: 'Data Management', color: '#10b981' },
              { name: 'Deployment', color: '#ef4444' },
              { name: 'Observability', color: '#f59e0b' }
            ].map((category, index) => (
              <div key={index} style={{
                textAlign: 'center',
                color: category.color,
                fontSize: '18px',
                fontWeight: '600',
                textTransform: 'uppercase',
                letterSpacing: '1px'
              }}>
                {category.name}
              </div>
            ))}
          </div>

          {/* Vertical Separators */}
          {[300, 600, 900].map(x => (
            <div key={x} style={{
              position: 'absolute',
              left: `${x}px`,
              top: '50px',
              bottom: '50px',
              width: '1px',
              background: 'linear-gradient(to bottom, transparent, #374151, transparent)',
              pointerEvents: 'none'
            }} />
          ))}

          {/* Pattern Components */}
          {patterns.map((pattern) => {
            const isSelected = selectedPattern?.id === pattern.id;
            const style = getCategoryStyle(pattern.category);
            
            return (
              <div
                key={pattern.id}
                style={{
                  position: 'absolute',
                  left: `${pattern.position.left}px`,
                  top: `${pattern.position.top}px`,
                  width: '240px',
                  height: '140px',
                  backgroundColor: isSelected ? style.bg : 'rgba(17, 24, 39, 0.8)',
                  border: `2px solid ${isSelected ? style.color : '#374151'}`,
                  borderRadius: '12px',
                  padding: '20px',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  backdropFilter: 'blur(8px)',
                  boxShadow: isSelected ? `0 0 20px ${style.color}40` : '0 4px 6px rgba(0, 0, 0, 0.1)'
                }}
                onClick={() => setSelectedPattern(pattern)}
                onMouseEnter={(e) => {
                  if (!isSelected) {
                    e.target.style.borderColor = style.color;
                    e.target.style.backgroundColor = style.bg;
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isSelected) {
                    e.target.style.borderColor = '#374151';
                    e.target.style.backgroundColor = 'rgba(17, 24, 39, 0.8)';
                  }
                }}
              >
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: isSelected ? '#fff' : style.color, marginBottom: '8px' }}>
                  {pattern.name}
                </div>
                <div style={{ fontSize: '11px', color: isSelected ? '#d1d5db' : '#6b7280' }}>
                  {pattern.tech.join(' • ')}
                </div>
              </div>
            );
          })}
        </div>

        {/* Pattern Details Panel */}
        {selectedPattern && (
          <div style={{
            backgroundColor: 'rgba(17, 24, 39, 0.95)',
            backdropFilter: 'blur(12px)',
            border: '1px solid #374151',
            borderRadius: '12px',
            overflow: 'hidden'
          }}>
            {/* Panel Header */}
            <div style={{
              padding: '24px',
              borderBottom: '1px solid #374151',
              background: 'linear-gradient(135deg, #1f2937 0%, #111827 100%)'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start' }}>
                <div>
                  <h2 style={{ 
                    fontSize: '24px', 
                    fontWeight: 'bold', 
                    color: '#fbbf24', 
                    margin: '0 0 8px 0' 
                  }}>
                    {selectedPattern.name}
                  </h2>
                  <p style={{ 
                    fontSize: '14px', 
                    color: '#9ca3af', 
                    margin: 0 
                  }}>
                    {selectedPattern.tech.join(' • ')}
                  </p>
                </div>
                <button
                  onClick={() => setSelectedPattern(null)}
                  style={{
                    backgroundColor: 'transparent',
                    border: '1px solid #374151',
                    borderRadius: '6px',
                    padding: '8px 12px',
                    color: '#9ca3af',
                    cursor: 'pointer',
                    fontSize: '14px'
                  }}
                >
                  Close
                </button>
              </div>
            </div>

            {/* Tabs */}
            <div style={{
              display: 'flex',
              borderBottom: '1px solid #374151',
              backgroundColor: '#1f2937'
            }}>
              {['details', 'uml', 'code'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  style={{
                    padding: '16px 24px',
                    border: 'none',
                    backgroundColor: activeTab === tab ? '#374151' : 'transparent',
                    color: activeTab === tab ? '#fbbf24' : '#9ca3af',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: '500',
                    textTransform: 'capitalize',
                    transition: 'all 0.2s ease'
                  }}
                >
                  {tab === 'uml' ? 'UML Diagram' : tab}
                </button>
              ))}
            </div>

            {/* Tab Content */}
            <div style={{ padding: '24px' }}>
              {activeTab === 'details' && (
                <div>
                  <div style={{ marginBottom: '24px' }}>
                    <h3 style={{ color: '#fbbf24', fontSize: '18px', marginBottom: '12px' }}>Functions</h3>
                    <ul style={{ color: '#e5e7eb', fontSize: '14px', lineHeight: '1.6' }}>
                      {getPatternDetails(selectedPattern.id).functions.map((func, i) => (
                        <li key={i} style={{ marginBottom: '4px' }}>{func}</li>
                      ))}
                    </ul>
                  </div>
                  
                  <div style={{ marginBottom: '24px' }}>
                    <h3 style={{ color: '#fbbf24', fontSize: '18px', marginBottom: '12px' }}>Workflow</h3>
                    <pre style={{ 
                      color: '#e5e7eb', 
                      fontSize: '14px', 
                      lineHeight: '1.6',
                      whiteSpace: 'pre-wrap',
                      fontFamily: 'inherit'
                    }}>
                      {getPatternDetails(selectedPattern.id).workflow}
                    </pre>
                  </div>

                  {getPatternDetails(selectedPattern.id).technologies && (
                    <div>
                      <h3 style={{ color: '#fbbf24', fontSize: '18px', marginBottom: '12px' }}>Technologies</h3>
                      {Object.entries(getPatternDetails(selectedPattern.id).technologies).map(([key, value]) => (
                        <div key={key} style={{ marginBottom: '8px' }}>
                          <span style={{ color: '#60a5fa', fontWeight: '500' }}>{key}:</span>
                          <span style={{ color: '#e5e7eb', marginLeft: '8px' }}>{value}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'uml' && (
                <div>
                  <h3 style={{ color: '#fbbf24', fontSize: '18px', marginBottom: '16px' }}>
                    {selectedPattern.name} UML Diagram
                  </h3>
                  <div style={{ 
                    backgroundColor: 'rgba(0, 0, 0, 0.3)', 
                    border: '1px solid #374151',
                    borderRadius: '8px',
                    padding: '16px',
                    overflow: 'auto'
                  }}>
                    {getPatternUML(selectedPattern.id) ? (
                      <svg 
                        viewBox={getPatternUML(selectedPattern.id).viewBox}
                        style={{ width: '100%', height: 'auto', maxHeight: '500px' }}
                      >
                        {getPatternUML(selectedPattern.id).diagram}
                      </svg>
                    ) : (
                      <div style={{ color: '#9ca3af', textAlign: 'center', padding: '40px' }}>
                        UML diagram for {selectedPattern.name} will be available soon
                      </div>
                    )}
                  </div>
                </div>
              )}

              {activeTab === 'code' && (
                <div>
                  <h3 style={{ color: '#fbbf24', fontSize: '18px', marginBottom: '16px' }}>
                    {selectedPattern.name} Implementation
                  </h3>
                  <SyntaxHighlighter 
                    language="java" 
                    style={oneDark}
                    customStyle={{
                      backgroundColor: 'rgba(0, 0, 0, 0.3)',
                      border: '1px solid #374151',
                      borderRadius: '8px',
                      fontSize: '13px'
                    }}
                  >
                    {getPatternDetails(selectedPattern.id).code}
                  </SyntaxHighlighter>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MicroserviceDesignPatterns;
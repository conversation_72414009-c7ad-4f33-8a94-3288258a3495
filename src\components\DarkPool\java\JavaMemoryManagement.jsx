import React, { useState } from 'react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Brain, Server, Database, Cpu, AlertTriangle, TrendingUp, Zap, Shield, Activity, Settings, FileText, Monitor, HardDrive, Gauge, Wrench } from 'lucide-react';

const JavaMemoryManagement = () => {
  const [selectedTopic, setSelectedTopic] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [detailsTab, setDetailsTab] = useState('definition');

  const memoryTopics = [
    {
      id: 'heap',
      name: 'Heap Memory',
      icon: <Database size={20} />,
      description: 'Object allocation and storage',
      color: '#ef4444'
    },
    {
      id: 'stack',
      name: 'Stack Memory',
      icon: <Server size={20} />,
      description: 'Method calls and local variables',
      color: '#3b82f6'
    },
    {
      id: 'gc-algorithms',
      name: 'GC Algorithms',
      icon: <Brain size={20} />,
      description: 'G1GC, ZGC, Shenandoah, Parallel GC',
      color: '#10b981'
    },
    {
      id: 'off-heap',
      name: 'Off-Heap Memory',
      icon: <HardDrive size={20} />,
      description: 'Direct ByteBuffers, Unsafe operations',
      color: '#06b6d4'
    },
    {
      id: 'memory-regions',
      name: 'Memory Regions',
      icon: <Database size={20} />,
      description: 'Young/Old generation, Eden, Survivor spaces',
      color: '#f59e0b'
    },
    {
      id: 'reference-types',
      name: 'Reference Types',
      icon: <Shield size={20} />,
      description: 'WeakReference, SoftReference, PhantomReference',
      color: '#8b5cf6'
    },
    {
      id: 'object-pooling',
      name: 'Object Pooling',
      icon: <Activity size={20} />,
      description: 'Reducing allocation pressure',
      color: '#ec4899'
    },
    {
      id: 'memory-mapped',
      name: 'Memory-Mapped Files',
      icon: <FileText size={20} />,
      description: 'Zero-copy operations for large datasets',
      color: '#84cc16'
    },
    {
      id: 'tlab',
      name: 'TLAB',
      icon: <Zap size={20} />,
      description: 'Thread Local Allocation Buffers',
      color: '#a855f7'
    },
    {
      id: 'native-tracking',
      name: 'Native Memory Tracking',
      icon: <Monitor size={20} />,
      description: 'Monitoring off-heap usage',
      color: '#14b8a6'
    },
    {
      id: 'tuning-flags',
      name: 'JVM Tuning Flags',
      icon: <Settings size={20} />,
      description: '-Xmx, -Xms, GC-specific parameters',
      color: '#f97316'
    },
    {
      id: 'memory-barriers',
      name: 'Memory Barriers & CPU Cache',
      icon: <Cpu size={20} />,
      description: 'False sharing prevention, cache-line padding',
      color: '#ef4444'
    },
    {
      id: 'profiling-tools',
      name: 'Profiling Tools',
      icon: <Wrench size={20} />,
      description: 'JProfiler, YourKit, Java Flight Recorder',
      color: '#6366f1'
    },
    {
      id: 'leak-detection',
      name: 'Memory Leak Detection',
      icon: <AlertTriangle size={20} />,
      description: 'Detection and prevention strategies',
      color: '#dc2626'
    },
    {
      id: 'optimization',
      name: 'Memory Optimization',
      icon: <TrendingUp size={20} />,
      description: 'Performance tuning and best practices',
      color: '#059669'
    }
  ];

  const codeExamples = {
    heap: `// Heap Memory Example - Object Pool Pattern for Trading Orders
public class OrderPool {
    private static final int POOL_SIZE = 1000;
    private final Queue<Order> pool = new ConcurrentLinkedQueue<>();
    
    public OrderPool() {
        // Pre-allocate orders to reduce GC pressure
        for (int i = 0; i < POOL_SIZE; i++) {
            pool.offer(new Order());
        }
    }
    
    public Order borrowOrder() {
        Order order = pool.poll();
        if (order == null) {
            // Pool exhausted, create new instance
            return new Order();
        }
        return order.reset(); // Reset and reuse
    }
    
    public void returnOrder(Order order) {
        if (pool.size() < POOL_SIZE) {
            order.clear(); // Clear sensitive data
            pool.offer(order);
        }
    }
}`,
    stack: `// Stack Memory Example - Recursive Risk Calculation
public class RiskCalculator {
    
    // Stack-efficient iterative approach
    public double calculatePortfolioRisk(List<Position> positions) {
        double totalRisk = 0.0;
        
        // Using iteration instead of recursion to avoid StackOverflowError
        Deque<Position> stack = new ArrayDeque<>(positions);
        
        while (!stack.isEmpty()) {
            Position pos = stack.pop();
            totalRisk += calculatePositionRisk(pos);
            
            // Add sub-positions if any
            if (pos.hasSubPositions()) {
                stack.addAll(pos.getSubPositions());
            }
        }
        
        return totalRisk;
    }
    
    private double calculatePositionRisk(Position position) {
        // Local variables stored on stack
        double quantity = position.getQuantity();
        double price = position.getCurrentPrice();
        double volatility = position.getVolatility();
        
        return quantity * price * volatility;
    }
}`,
    gc: `// Garbage Collection Tuning for Low-Latency Trading
public class TradingSystemGC {
    
    // JVM flags for G1GC optimization:
    // -XX:+UseG1GC
    // -XX:MaxGCPauseMillis=10
    // -XX:+UseStringDeduplication
    // -XX:InitiatingHeapOccupancyPercent=45
    
    // Use primitive collections to reduce object overhead
    private final TDoubleArrayList prices = new TDoubleArrayList(10000);
    private final TLongArrayList timestamps = new TLongArrayList(10000);
    
    // Weak references for cache that can be GC'd under memory pressure
    private final Map<String, WeakReference<MarketData>> cache = 
        new ConcurrentHashMap<>();
    
    public void addMarketData(String symbol, MarketData data) {
        cache.put(symbol, new WeakReference<>(data));
    }
    
    public MarketData getMarketData(String symbol) {
        WeakReference<MarketData> ref = cache.get(symbol);
        if (ref != null) {
            MarketData data = ref.get();
            if (data != null) {
                return data;
            }
        }
        // Data was GC'd, need to reload
        return loadMarketData(symbol);
    }
}`,
    metaspace: `// Metaspace Management - Dynamic Strategy Loading
public class StrategyLoader {
    
    // Custom ClassLoader for hot-swapping trading strategies
    private static class StrategyClassLoader extends URLClassLoader {
        
        public StrategyClassLoader(URL[] urls) {
            super(urls, null); // Null parent to isolate classes
        }
        
        public void unload() {
            try {
                close(); // Releases metaspace memory
            } catch (IOException e) {
                log.error("Failed to unload classes", e);
            }
        }
    }
    
    private Map<String, StrategyClassLoader> loaders = new HashMap<>();
    
    public TradingStrategy loadStrategy(String jarPath) throws Exception {
        URL url = new File(jarPath).toURI().toURL();
        StrategyClassLoader loader = new StrategyClassLoader(new URL[]{url});
        
        Class<?> strategyClass = loader.loadClass("com.trading.Strategy");
        TradingStrategy strategy = (TradingStrategy) strategyClass.newInstance();
        
        loaders.put(jarPath, loader);
        return strategy;
    }
    
    public void unloadStrategy(String jarPath) {
        StrategyClassLoader loader = loaders.remove(jarPath);
        if (loader != null) {
            loader.unload();
        }
    }
}`,
    leak: `// Memory Leak Detection and Prevention
public class MemoryLeakPrevention {
    
    // BAD: Static collection holding references
    // private static final List<Order> ALL_ORDERS = new ArrayList<>();
    
    // GOOD: Use weak references or proper cleanup
    private static final Set<WeakReference<Order>> activeOrders = 
        Collections.newSetFromMap(new WeakHashMap<>());
    
    // Resource management with try-with-resources
    public void processMarketData(String file) {
        try (BufferedReader reader = new BufferedReader(
                new FileReader(file));
             Stream<String> lines = reader.lines()) {
            
            lines.parallel()
                .map(this::parseMarketData)
                .forEach(this::processData);
            
        } catch (IOException e) {
            log.error("Error processing market data", e);
        }
        // Resources automatically closed, no memory leak
    }
    
    // ThreadLocal cleanup to prevent leaks
    private static final ThreadLocal<SimpleDateFormat> DATE_FORMAT = 
        ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));
    
    public void cleanup() {
        DATE_FORMAT.remove(); // Prevent memory leak in thread pools
    }
}`,
    optimization: `// Memory Optimization Techniques
public class MemoryOptimizedTrading {
    
    // 1. Use primitive types instead of wrappers
    private double[] prices;  // Not Double[]
    private long[] volumes;    // Not Long[]
    
    // 2. Flyweight pattern for common objects
    private static final Map<String, Symbol> SYMBOL_CACHE = new HashMap<>();
    
    public static Symbol getSymbol(String name) {
        return SYMBOL_CACHE.computeIfAbsent(name, Symbol::new);
    }
    
    // 3. Compact data structures
    public class CompactOrder {
        // Pack multiple fields into single long
        private long packedData; // Contains: price(32), quantity(16), type(8), side(8)
        
        public void setPrice(int price) {
            packedData = (packedData & 0xFFFFFFFF00000000L) | price;
        }
        
        public int getPrice() {
            return (int)(packedData & 0xFFFFFFFFL);
        }
    }
    
    // 4. Off-heap memory for large datasets
    public class OffHeapOrderBook {
        private final ByteBuffer buffer;
        
        public OffHeapOrderBook(int capacity) {
            // Allocate direct memory (off-heap)
            buffer = ByteBuffer.allocateDirect(capacity * 32);
        }
        
        public void addOrder(int index, Order order) {
            int position = index * 32;
            buffer.putLong(position, order.getId());
            buffer.putDouble(position + 8, order.getPrice());
            buffer.putInt(position + 16, order.getQuantity());
            // ... more fields
        }
    }
}`,
    'gc-algorithms': `// GC Algorithms Comparison
// G1GC Configuration for Low Latency Trading
-XX:+UseG1GC
-XX:MaxGCPauseMillis=10
-XX:G1HeapRegionSize=32m
-XX:G1NewSizePercent=20
-XX:G1MaxNewSizePercent=40

// ZGC for Ultra-Low Latency (Java 15+)
-XX:+UseZGC
-XX:+UnlockExperimentalVMOptions
-Xmx32g

// Shenandoah GC for Consistent Low Latency
-XX:+UseShenandoahGC
-XX:ShenandoahGCHeuristics=aggressive

public class GCTuningStrategy {
    public static void chooseBestCollector() {
        // Throughput-focused: Parallel GC
        // Latency-sensitive: G1GC with pause target
        // Ultra-low latency: ZGC or Shenandoah
        // Mixed workload: G1GC with balanced settings
    }
    
    // Monitor GC metrics
    public void monitorGCPerformance() {
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        List<GarbageCollectorMXBean> gcBeans = 
            ManagementFactory.getGarbageCollectorMXBeans();
        
        for (GarbageCollectorMXBean gcBean : gcBeans) {
            long collections = gcBean.getCollectionCount();
            long time = gcBean.getCollectionTime();
            System.out.println(gcBean.getName() + ": " + 
                collections + " collections, " + time + "ms total");
        }
    }
}`,
    'off-heap': `// Off-Heap Memory for Market Data Cache
import sun.misc.Unsafe;
import java.nio.ByteBuffer;

public class OffHeapMarketDataCache {
    private final ByteBuffer directBuffer;
    private static final Unsafe unsafe = getUnsafe();
    private final long baseAddress;
    private final int capacity;
    
    public OffHeapMarketDataCache(int maxSymbols) {
        this.capacity = maxSymbols * 64; // 64 bytes per symbol
        this.directBuffer = ByteBuffer.allocateDirect(capacity);
        this.baseAddress = ((DirectBuffer) directBuffer).address();
    }
    
    // Zero-copy price update
    public void updatePrice(int symbolIndex, double bid, double ask, long timestamp) {
        long offset = baseAddress + (symbolIndex * 64L);
        unsafe.putDouble(offset, bid);        // 8 bytes
        unsafe.putDouble(offset + 8, ask);    // 8 bytes
        unsafe.putLong(offset + 16, timestamp); // 8 bytes
    }
    
    // Direct memory access without GC pressure
    public double getBid(int symbolIndex) {
        long offset = baseAddress + (symbolIndex * 64L);
        return unsafe.getDouble(offset);
    }
    
    // Chronicle Map alternative for persistent off-heap storage
    public void useChronicleMap() {
        ChronicleMap<String, MarketData> marketDataMap = ChronicleMap
            .of(String.class, MarketData.class)
            .entries(1_000_000)
            .averageKey("SYMBOL")
            .create();
    }
    
    private static Unsafe getUnsafe() {
        try {
            Field field = Unsafe.class.getDeclaredField("theUnsafe");
            field.setAccessible(true);
            return (Unsafe) field.get(null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}`,
    'memory-regions': `// Memory Regions and Generational Collection
public class MemoryRegionExample {
    
    // Young Generation objects (short-lived)
    public void processOrderFlow() {
        for (int i = 0; i < 1000000; i++) {
            // These objects die young - allocated in Eden
            OrderEvent event = new OrderEvent();
            processEvent(event); // Becomes garbage immediately
        }
    }
    
    // Long-lived objects (promoted to Old Generation)
    private final Map<String, InstrumentData> instruments = new HashMap<>();
    private final ExecutorService threadPool = Executors.newFixedThreadPool(8);
    
    public void demonstratePromotionToOld() {
        // Objects that survive multiple GC cycles get promoted
        InstrumentData instrument = new InstrumentData("AAPL");
        instruments.put("AAPL", instrument); // Likely to be promoted to Old Gen
    }
    
    // Survivor space management
    public void optimizeForSurvivorSpaces() {
        // JVM flags for survivor space tuning:
        // -XX:SurvivorRatio=8 (Eden:Survivor = 8:1:1)
        // -XX:MaxTenuringThreshold=15 (promotion threshold)
        // -XX:TargetSurvivorRatio=90 (survivor space utilization)
    }
    
    // Understanding object lifecycle
    public class ObjectLifecycleTracker {
        // Eden -> S0 -> S1 -> Old Gen -> (eventually GC'd)
        
        public void trackObjectAging() {
            // Short-lived: Order processing, temporary calculations
            Order order = new Order(); // Dies in Eden
            
            // Medium-lived: Session data, temporary caches
            SessionData session = new SessionData(); // May reach Survivor
            
            // Long-lived: Configuration, connection pools
            ConnectionPool pool = new ConnectionPool(); // Promotes to Old Gen
        }
    }
}`,
    'reference-types': `// Reference Types for Cache Management
import java.lang.ref.*;
import java.util.concurrent.ConcurrentHashMap;

public class SmartCacheManager {
    // WeakReference cache - cleared when memory pressure
    private final Map<String, WeakReference<MarketData>> weakCache = 
        new ConcurrentHashMap<>();
    
    // SoftReference cache - survives minor GCs but cleared on memory pressure
    private final Map<String, SoftReference<HistoricalData>> softCache = 
        new ConcurrentHashMap<>();
    
    // PhantomReference for cleanup tracking
    private final Map<String, PhantomReference<Object>> phantomRefs = 
        new ConcurrentHashMap<>();
    private final ReferenceQueue<Object> refQueue = new ReferenceQueue<>();
    
    // WeakReference usage for real-time data
    public MarketData getMarketData(String symbol) {
        WeakReference<MarketData> ref = weakCache.get(symbol);
        MarketData data = (ref != null) ? ref.get() : null;
        
        if (data == null) {
            data = fetchMarketData(symbol);
            weakCache.put(symbol, new WeakReference<>(data));
        }
        return data;
    }
    
    // SoftReference for historical data (large objects)
    public HistoricalData getHistoricalData(String symbol) {
        SoftReference<HistoricalData> ref = softCache.get(symbol);
        HistoricalData data = (ref != null) ? ref.get() : null;
        
        if (data == null) {
            data = loadHistoricalData(symbol); // Expensive operation
            softCache.put(symbol, new SoftReference<>(data));
        }
        return data;
    }
    
    // PhantomReference for resource cleanup
    public void registerForCleanup(Object obj, Runnable cleanup) {
        PhantomReference<Object> phantom = new PhantomReference<>(obj, refQueue) {
            @Override
            public void clear() {
                cleanup.run(); // Custom cleanup logic
                super.clear();
            }
        };
        phantomRefs.put(obj.toString(), phantom);
    }
    
    // Reference queue processing
    public void processCleanupQueue() {
        Reference<?> ref;
        while ((ref = refQueue.poll()) != null) {
            ref.clear(); // Perform cleanup
        }
    }
}`,
    'object-pooling': `// Advanced Object Pooling for Trading Systems
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;

public class HighPerformanceObjectPool<T> {
    private final ConcurrentLinkedQueue<T> pool = new ConcurrentLinkedQueue<>();
    private final ObjectFactory<T> factory;
    private final AtomicInteger poolSize = new AtomicInteger(0);
    private final int maxPoolSize;
    
    public interface ObjectFactory<T> {
        T create();
        void reset(T obj);
        boolean validate(T obj);
    }
    
    public HighPerformanceObjectPool(ObjectFactory<T> factory, int initialSize, int maxSize) {
        this.factory = factory;
        this.maxPoolSize = maxSize;
        
        // Pre-populate pool
        for (int i = 0; i < initialSize; i++) {
            pool.offer(factory.create());
            poolSize.incrementAndGet();
        }
    }
    
    public T acquire() {
        T obj = pool.poll();
        if (obj != null) {
            poolSize.decrementAndGet();
        } else {
            obj = factory.create(); // Pool exhausted, create new
        }
        return obj;
    }
    
    public void release(T obj) {
        if (obj == null) return;
        
        factory.reset(obj);
        if (factory.validate(obj) && poolSize.get() < maxPoolSize) {
            pool.offer(obj);
            poolSize.incrementAndGet();
        }
        // Otherwise let GC handle it (pool is full)
    }
}

// Order Pool Implementation
class OrderFactory implements HighPerformanceObjectPool.ObjectFactory<Order> {
    @Override
    public Order create() {
        return new Order();
    }
    
    @Override
    public void reset(Order order) {
        order.clear(); // Reset all fields
        order.setTimestamp(0);
        order.setPrice(0);
        order.setQuantity(0);
    }
    
    @Override
    public boolean validate(Order order) {
        return order != null && order.isReusable();
    }
}

// Usage in trading system
public class TradingEngine {
    private final HighPerformanceObjectPool<Order> orderPool = 
        new HighPerformanceObjectPool<>(new OrderFactory(), 1000, 10000);
    
    public void processNewOrder(String symbol, double price, int quantity) {
        Order order = orderPool.acquire();
        try {
            order.setSymbol(symbol);
            order.setPrice(price);
            order.setQuantity(quantity);
            order.setTimestamp(System.nanoTime());
            
            processOrder(order);
        } finally {
            orderPool.release(order); // Always return to pool
        }
    }
}`,
    'memory-mapped': `// Memory-Mapped Files for Historical Data
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;

public class MemoryMappedHistoricalData {
    private final MappedByteBuffer mappedBuffer;
    private final FileChannel channel;
    private static final int RECORD_SIZE = 32; // 8+8+8+8 bytes per tick
    
    public MemoryMappedHistoricalData(String filename, long fileSize) throws Exception {
        this.channel = FileChannel.open(
            Paths.get(filename), 
            StandardOpenOption.READ,
            StandardOpenOption.WRITE,
            StandardOpenOption.CREATE
        );
        
        // Map entire file into memory
        this.mappedBuffer = channel.map(
            FileChannel.MapMode.READ_WRITE, 0, fileSize
        );
    }
    
    // Zero-copy write of tick data
    public void writeTick(long index, long timestamp, double bid, double ask, long volume) {
        int position = (int)(index * RECORD_SIZE);
        mappedBuffer.putLong(position, timestamp);
        mappedBuffer.putDouble(position + 8, bid);
        mappedBuffer.putDouble(position + 16, ask);
        mappedBuffer.putLong(position + 24, volume);
    }
    
    // Zero-copy read of tick data
    public TickData readTick(long index) {
        int position = (int)(index * RECORD_SIZE);
        return new TickData(
            mappedBuffer.getLong(position),      // timestamp
            mappedBuffer.getDouble(position + 8), // bid
            mappedBuffer.getDouble(position + 16), // ask
            mappedBuffer.getLong(position + 24)   // volume
        );
    }
    
    // Efficient range queries
    public List<TickData> getTicksInRange(long startIndex, long endIndex) {
        List<TickData> ticks = new ArrayList<>();
        for (long i = startIndex; i <= endIndex; i++) {
            ticks.add(readTick(i));
        }
        return ticks;
    }
    
    // Force sync to disk
    public void flush() {
        mappedBuffer.force();
    }
    
    // Process large datasets efficiently
    public double calculateVWAP(long startIndex, long endIndex) {
        double totalValue = 0;
        long totalVolume = 0;
        
        for (long i = startIndex; i <= endIndex; i++) {
            int pos = (int)(i * RECORD_SIZE);
            double price = (mappedBuffer.getDouble(pos + 8) + 
                           mappedBuffer.getDouble(pos + 16)) / 2; // mid price
            long volume = mappedBuffer.getLong(pos + 24);
            
            totalValue += price * volume;
            totalVolume += volume;
        }
        
        return totalVolume > 0 ? totalValue / totalVolume : 0;
    }
}`,
    'tlab': `// Thread Local Allocation Buffer Optimization
public class TLABOptimization {
    
    // TLAB-friendly allocation patterns
    public void demonstrateTLABUsage() {
        // Small objects allocated in TLAB (thread-local)
        // No synchronization overhead for allocation
        
        for (int i = 0; i < 100000; i++) {
            // These small objects fit in TLAB
            SmallOrder order = new SmallOrder();
            MarketTick tick = new MarketTick();
            
            // Process immediately while in TLAB
            processOrder(order, tick);
        }
    }
    
    // Avoid TLAB waste with large objects
    public void avoidTLABWaste() {
        // Large objects don't fit in TLAB and go directly to heap
        byte[] largeArray = new byte[1024 * 1024]; // > TLAB size
        
        // This causes TLAB to be retired early, wasting space
        // Better to use object pools for large objects
    }
    
    // TLAB tuning parameters
    public void configureTLAB() {
        // -XX:TLABSize=1m (set TLAB size)
        // -XX:MinTLABSize=2k (minimum TLAB size)
        // -XX:TLABWasteTargetPercent=1 (waste threshold)
        // -XX:TLABWasteIncrement=4 (increment for waste)
        // -XX:ResizeTLAB (enable TLAB resizing)
    }
    
    // Allocation-intensive operation optimized for TLAB
    public class FastMarketDataProcessor {
        public void processMarketFeed(MarketFeed feed) {
            // Allocate many small objects in tight loop
            // Each thread gets its own TLAB for fast allocation
            while (feed.hasNext()) {
                MarketEvent event = new MarketEvent(); // TLAB allocation
                Quote quote = new Quote();            // TLAB allocation
                
                parseEvent(feed.next(), event, quote);
                publishToSubscribers(event, quote);
                
                // Objects become garbage quickly, promoting
                // efficient TLAB usage pattern
            }
        }
    }
    
    // Monitor TLAB effectiveness
    public void monitorTLABStats() {
        // Use -XX:+PrintTLAB to see TLAB statistics
        // Watch for:
        // - TLAB waste percentage
        // - Allocation rate vs TLAB size
        // - Thread allocation patterns
    }
}`,
    'native-tracking': `// Native Memory Tracking and Monitoring
public class NativeMemoryTracker {
    
    public void enableNativeMemoryTracking() {
        // JVM flags for Native Memory Tracking:
        // -XX:NativeMemoryTracking=summary (basic tracking)
        // -XX:NativeMemoryTracking=detail (detailed tracking)
        
        // Command line tools:
        // jcmd <pid> VM.native_memory summary
        // jcmd <pid> VM.native_memory detail
    }
    
    public void monitorDirectMemoryUsage() {
        // Monitor off-heap direct memory usage
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        MemoryUsage nonHeap = memoryBean.getNonHeapMemoryUsage();
        
        System.out.println("Non-heap used: " + nonHeap.getUsed());
        System.out.println("Non-heap max: " + nonHeap.getMax());
        
        // Direct memory specific monitoring
        List<MemoryPoolMXBean> pools = ManagementFactory.getMemoryPoolMXBeans();
        for (MemoryPoolMXBean pool : pools) {
            if (pool.getName().contains("Direct")) {
                System.out.println("Direct memory: " + pool.getUsage());
            }
        }
    }
    
    // Custom direct memory tracking
    private static final AtomicLong directMemoryUsed = new AtomicLong(0);
    private static final long maxDirectMemory = getMaxDirectMemory();
    
    public ByteBuffer allocateDirectMemory(int size) {
        long current = directMemoryUsed.get();
        if (current + size > maxDirectMemory) {
            throw new OutOfMemoryError("Direct memory exhausted");
        }
        
        directMemoryUsed.addAndGet(size);
        return ByteBuffer.allocateDirect(size);
    }
    
    public void releaseDirectMemory(ByteBuffer buffer) {
        if (buffer.isDirect()) {
            directMemoryUsed.addAndGet(-buffer.capacity());
            ((DirectBuffer) buffer).cleaner().clean();
        }
    }
    
    private static long getMaxDirectMemory() {
        try {
            Field field = sun.misc.VM.class.getDeclaredField("maxDirectMemory");
            field.setAccessible(true);
            return (Long) field.get(null);
        } catch (Exception e) {
            return -1; // Unknown
        }
    }
    
    // Native memory breakdown monitoring
    public void analyzeNativeMemoryBreakdown() {
        // Categories tracked by NMT:
        // - Java Heap
        // - Class metadata
        // - Thread stacks
        // - Code cache
        // - GC overhead
        // - Compiler overhead
        // - Internal structures
        // - Symbol tables
        // - Native method stacks
        // - Direct ByteBuffers
    }
}`,
    'tuning-flags': `// Comprehensive JVM Tuning for Trading Systems
public class JVMTuningGuide {
    
    public void heapTuningFlags() {
        // Heap sizing
        // -Xms8g -Xmx8g (set equal to avoid heap expansion)
        // -XX:NewRatio=3 (Old:Young = 3:1)
        // -XX:NewSize=2g -XX:MaxNewSize=2g (fixed young gen size)
        
        // Survivor spaces
        // -XX:SurvivorRatio=8 (Eden:Survivor = 8:1:1)
        // -XX:MaxTenuringThreshold=6 (promotion threshold)
    }
    
    public void gcTuningFlags() {
        // G1GC for low latency
        // -XX:+UseG1GC
        // -XX:MaxGCPauseMillis=10
        // -XX:G1HeapRegionSize=32m
        // -XX:G1NewSizePercent=20
        // -XX:G1MaxNewSizePercent=40
        // -XX:G1MixedGCCountTarget=8
        // -XX:G1OldCSetRegionThreshold=20
        
        // ZGC for ultra-low latency (Java 15+)
        // -XX:+UnlockExperimentalVMOptions
        // -XX:+UseZGC
        // -XX:+UseLargePages
        
        // Parallel GC for throughput
        // -XX:+UseParallelGC
        // -XX:ParallelGCThreads=8
        // -XX:+UseParallelOldGC
    }
    
    public void directMemoryTuning() {
        // Direct memory limits
        // -XX:MaxDirectMemorySize=4g
        // -XX:+DisableExplicitGC (prevent System.gc() calls)
        
        // Large pages for better performance
        // -XX:+UseLargePages
        // -XX:LargePageSizeInBytes=2m
    }
    
    public void compilationTuning() {
        // JIT compiler tuning
        // -XX:+TieredCompilation
        // -XX:TieredStopAtLevel=4 (C2 compiler)
        // -XX:CompileThreshold=1000 (method compilation threshold)
        // -XX:+UseStringDeduplication (reduce String memory usage)
        
        // Code cache sizing
        // -XX:InitialCodeCacheSize=64m
        // -XX:ReservedCodeCacheSize=256m
    }
    
    public void loggingAndMonitoring() {
        // GC logging (Java 11+)
        // -Xlog:gc*:gc.log:time,tags
        // -Xlog:safepoint:safepoint.log:time,tags
        
        // JFR (Java Flight Recorder)
        // -XX:+FlightRecorder
        // -XX:StartFlightRecording=duration=60s,filename=trading.jfr
        
        // Native Memory Tracking
        // -XX:NativeMemoryTracking=summary
    }
    
    // Complete trading system JVM configuration
    public String getOptimalTradingFlags() {
        return """
            # Heap Configuration
            -Xms16g -Xmx16g
            -XX:NewSize=4g -XX:MaxNewSize=4g
            
            # G1GC Low Latency
            -XX:+UseG1GC
            -XX:MaxGCPauseMillis=5
            -XX:G1HeapRegionSize=32m
            
            # Direct Memory
            -XX:MaxDirectMemorySize=8g
            -XX:+UseLargePages
            
            # Performance
            -XX:+TieredCompilation
            -XX:+UseStringDeduplication
            -XX:+OptimizeStringConcat
            
            # Monitoring
            -XX:+FlightRecorder
            -XX:NativeMemoryTracking=summary
            -Xlog:gc*:gc.log:time,tags
            """;
    }
}`,
    'memory-barriers': `// Memory Barriers and CPU Cache Optimization
import java.util.concurrent.locks.LockSupport;

public class MemoryBarrierOptimization {
    
    // False sharing problem example
    static class FalseSharingExample {
        private volatile long value1;
        private volatile long value2; // Likely in same cache line as value1
    }
    
    // Cache-line padding to prevent false sharing
    static class PaddedCounter {
        // Padding to ensure different cache lines
        private long p1, p2, p3, p4, p5, p6, p7;
        private volatile long counter;
        private long p8, p9, p10, p11, p12, p13, p14;
        
        public void increment() {
            counter++;
        }
        
        public long get() {
            return counter;
        }
    }
    
    // Using @Contended annotation (JDK 8+)
    @sun.misc.Contended
    static class ContendedCounter {
        private volatile long counter;
        
        public void increment() {
            counter++;
        }
    }
    
    // Memory barrier example with volatile
    private volatile boolean flag = false;
    private int data = 0;
    
    // Thread 1: Writer
    public void writer() {
        data = 42;        // Store
        flag = true;      // Volatile store (release barrier)
    }
    
    // Thread 2: Reader
    public void reader() {
        if (flag) {       // Volatile load (acquire barrier)
            int value = data; // Guaranteed to see data = 42
        }
    }
    
    // Explicit memory barriers using Unsafe
    private static final sun.misc.Unsafe UNSAFE = getUnsafe();
    
    public void explicitBarriers() {
        // Store barrier - ensures all stores before this point
        // are visible before any store after this point
        UNSAFE.storeFence();
        
        // Load barrier - ensures all loads before this point
        // complete before any load after this point
        UNSAFE.loadFence();
        
        // Full barrier - both store and load fence
        UNSAFE.fullFence();
    }
    
    // Cache-conscious data structure
    static class CacheOptimizedOrderBook {
        // Hot data together in same cache line
        @sun.misc.Contended("group1")
        private volatile double bestBid;
        
        @sun.misc.Contended("group1")
        private volatile double bestAsk;
        
        @sun.misc.Contended("group1")
        private volatile long lastUpdateTime;
        
        // Separate cache line for different access pattern
        @sun.misc.Contended("group2")
        private volatile int bidLevels;
        
        @sun.misc.Contended("group2")
        private volatile int askLevels;
        
        public void updateTopOfBook(double bid, double ask) {
            // Group updates to minimize cache line transfers
            bestBid = bid;
            bestAsk = ask;
            lastUpdateTime = System.nanoTime();
        }
    }
    
    // NUMA-aware allocation
    public void numaOptimization() {
        // Use -XX:+UseNUMA flag
        // Bind threads to specific NUMA nodes
        // Allocate data structures close to processing threads
        
        Thread.currentThread().setName("NUMA-Node-0-Thread");
        // Pin thread to specific CPU/NUMA node using external tools
    }
    
    private static sun.misc.Unsafe getUnsafe() {
        try {
            java.lang.reflect.Field field = sun.misc.Unsafe.class.getDeclaredField("theUnsafe");
            field.setAccessible(true);
            return (sun.misc.Unsafe) field.get(null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}`,
    'profiling-tools': `// Memory Profiling and Analysis Tools
import java.lang.management.*;
import javax.management.*;

public class MemoryProfilingToolsGuide {
    
    public void jvmBuiltInProfiling() {
        // Java Flight Recorder (JFR)
        // -XX:+FlightRecorder
        // -XX:StartFlightRecording=duration=300s,filename=memory-profile.jfr
        
        // Heap dump on OutOfMemoryError
        // -XX:+HeapDumpOnOutOfMemoryError
        // -XX:HeapDumpPath=/tmp/heapdump.hprof
        
        // GC logging for memory analysis
        // -Xlog:gc*,heap*:gc-memory.log:time,tags
    }
    
    public void programmaticMemoryMonitoring() {
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        
        // Overall heap usage
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        System.out.println("Heap Usage: " + heapUsage);
        
        // Non-heap (metaspace, direct memory, etc.)
        MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();
        System.out.println("Non-Heap Usage: " + nonHeapUsage);
        
        // Individual memory pools
        List<MemoryPoolMXBean> memoryPools = ManagementFactory.getMemoryPoolMXBeans();
        for (MemoryPoolMXBean pool : memoryPools) {
            System.out.println(pool.getName() + ": " + pool.getUsage());
        }
        
        // Set up memory usage threshold notifications
        setupMemoryThresholds();
    }
    
    private void setupMemoryThresholds() {
        List<MemoryPoolMXBean> pools = ManagementFactory.getMemoryPoolMXBeans();
        MBeanServer server = ManagementFactory.getPlatformMBeanServer();
        
        for (MemoryPoolMXBean pool : pools) {
            if (pool.getType() == MemoryType.HEAP && pool.isUsageThresholdSupported()) {
                // Set threshold at 80% of max
                long threshold = (long) (pool.getUsage().getMax() * 0.8);
                pool.setUsageThreshold(threshold);
                
                // Register listener
                NotificationEmitter emitter = (NotificationEmitter) pool;
                emitter.addNotificationListener(
                    (notification, handback) -> {
                        System.out.println("Memory threshold exceeded: " + notification);
                        // Trigger cleanup, logging, alerts, etc.
                    },
                    null, null
                );
            }
        }
    }
    
    // Custom memory tracking for trading objects
    public class TradingMemoryTracker {
        private final AtomicLong orderObjectsCreated = new AtomicLong();
        private final AtomicLong orderObjectsDestroyed = new AtomicLong();
        private final AtomicLong marketDataObjectsCreated = new AtomicLong();
        
        public void trackObjectCreation(String objectType) {
            switch (objectType) {
                case "Order":
                    orderObjectsCreated.incrementAndGet();
                    break;
                case "MarketData":
                    marketDataObjectsCreated.incrementAndGet();
                    break;
            }
        }
        
        public void generateMemoryReport() {
            System.out.println("=== Trading System Memory Report ===");
            System.out.println("Orders created: " + orderObjectsCreated.get());
            System.out.println("Orders destroyed: " + orderObjectsDestroyed.get());
            System.out.println("Market data objects: " + marketDataObjectsCreated.get());
            
            // Memory pool analysis
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            System.out.println("Used memory: " + (usedMemory / 1024 / 1024) + " MB");
            System.out.println("Free memory: " + (freeMemory / 1024 / 1024) + " MB");
        }
    }
    
    // Integration with external profiling tools
    public void externalProfilerIntegration() {
        // JProfiler programmatic API
        /*
        if (Controller.isConnected()) {
            Controller.startCPURecording(true);
            Controller.startAllocRecording(true);
            
            // Run trading logic here
            
            Controller.saveSnapshot(new File("trading-profile.jps"));
            Controller.stopCPURecording();
            Controller.stopAllocRecording();
        }
        */
        
        // YourKit profiler API
        /*
        com.yourkit.api.Controller controller = new com.yourkit.api.Controller();
        controller.startAllocationRecording();
        controller.startCPUProfiling(ProfilingModes.CPU_SAMPLING, "", "");
        
        // Trading system execution
        
        controller.captureSnapshot(ProfilingModes.SNAPSHOT_WITHOUT_HEAP);
        */
    }
    
    // Memory leak detection patterns
    public void memoryLeakDetection() {
        // Watch for growing collections
        Map<String, List<Object>> potentialLeak = new HashMap<>();
        
        // Periodic check for memory leaks
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
        scheduler.scheduleAtFixedRate(() -> {
            // Check collection sizes
            potentialLeak.forEach((key, list) -> {
                if (list.size() > 10000) {
                    System.out.println("WARNING: Potential memory leak in " + key + 
                                     ", size: " + list.size());
                }
            });
            
            // Force weak references to be cleared and check what remains
            System.gc();
            
            // Analyze memory usage trends
            Runtime runtime = Runtime.getRuntime();
            long usedMemory = runtime.totalMemory() - runtime.freeMemory();
            System.out.println("Current memory usage: " + (usedMemory / 1024 / 1024) + " MB");
            
        }, 0, 30, TimeUnit.SECONDS);
    }
}`,
    'leak-detection': `// Memory Leak Detection and Prevention
import java.lang.ref.WeakReference;
import java.util.concurrent.*;

public class MemoryLeakDetector {
    
    // Common leak pattern: Growing collections
    private final Map<String, List<Order>> ordersBySymbol = new HashMap<>(); // Potential leak
    
    // Leak-safe alternative with size limits
    private final Map<String, List<Order>> safeLimitedCache = new ConcurrentHashMap<String, List<Order>>() {
        @Override
        public List<Order> put(String key, List<Order> value) {
            // Limit collection size to prevent leaks
            if (value.size() > 1000) {
                value = value.subList(value.size() - 1000, value.size());
            }
            return super.put(key, value);
        }
    };
    
    // Leak detection through monitoring
    private final ScheduledExecutorService leakDetector = 
        Executors.newScheduledThreadPool(1);
    
    public void startLeakDetection() {
        leakDetector.scheduleAtFixedRate(() -> {
            // Monitor collection growth
            ordersBySymbol.forEach((symbol, orders) -> {
                if (orders.size() > 10000) {
                    System.err.println("LEAK WARNING: " + symbol + " has " + 
                                     orders.size() + " orders");
                    
                    // Auto-cleanup to prevent OOM
                    if (orders.size() > 50000) {
                        orders.clear();
                        System.gc(); // Force cleanup
                    }
                }
            });
            
            // Check overall memory usage
            Runtime runtime = Runtime.getRuntime();
            long used = runtime.totalMemory() - runtime.freeMemory();
            long max = runtime.maxMemory();
            double usage = (double) used / max;
            
            if (usage > 0.9) {
                System.err.println("HIGH MEMORY USAGE: " + (usage * 100) + "%");
                generateHeapDump();
            }
            
        }, 0, 30, TimeUnit.SECONDS);
    }
    
    // Prevent listener leaks
    public class SafeEventHandler {
        private final Set<WeakReference<EventListener>> listeners = 
            ConcurrentHashMap.newKeySet();
        
        public void addListener(EventListener listener) {
            // Use WeakReference to prevent listener leaks
            listeners.add(new WeakReference<>(listener));
        }
        
        public void fireEvent(Event event) {
            Iterator<WeakReference<EventListener>> it = listeners.iterator();
            while (it.hasNext()) {
                WeakReference<EventListener> ref = it.next();
                EventListener listener = ref.get();
                if (listener == null) {
                    it.remove(); // Remove dead references
                } else {
                    listener.handleEvent(event);
                }
            }
        }
    }
    
    // Thread leak prevention
    public class ThreadLeakPrevention {
        private final ThreadGroup applicationThreads = new ThreadGroup("AppThreads");
        
        public Thread createManagedThread(Runnable task, String name) {
            Thread thread = new Thread(applicationThreads, task, name);
            thread.setDaemon(true); // Prevent JVM shutdown hang
            
            // Monitor thread count
            if (applicationThreads.activeCount() > 100) {
                System.err.println("WARNING: High thread count: " + 
                                 applicationThreads.activeCount());
            }
            
            return thread;
        }
        
        public void shutdownAllThreads() {
            applicationThreads.interrupt(); // Interrupt all app threads
        }
    }
    
    // Connection leak prevention
    public class ConnectionPoolManager {
        private final BlockingQueue<Connection> pool = new LinkedBlockingQueue<>();
        private final AtomicInteger activeConnections = new AtomicInteger(0);
        private final int maxConnections = 20;
        
        public Connection borrowConnection() throws Exception {
            Connection conn = pool.poll(5, TimeUnit.SECONDS);
            if (conn == null || conn.isClosed()) {
                if (activeConnections.get() < maxConnections) {
                    conn = createConnection();
                    activeConnections.incrementAndGet();
                } else {
                    throw new SQLException("Connection pool exhausted");
                }
            }
            return conn;
        }
        
        public void returnConnection(Connection conn) {
            if (conn != null) {
                try {
                    if (!conn.isClosed()) {
                        pool.offer(conn);
                    } else {
                        activeConnections.decrementAndGet();
                    }
                } catch (SQLException e) {
                    activeConnections.decrementAndGet();
                }
            }
        }
        
        // Periodic connection validation
        private void validateConnections() {
            pool.removeIf(conn -> {
                try {
                    if (conn.isClosed()) {
                        activeConnections.decrementAndGet();
                        return true;
                    }
                    return false;
                } catch (SQLException e) {
                    activeConnections.decrementAndGet();
                    return true;
                }
            });
        }
    }
    
    // Automatic heap dump generation
    private void generateHeapDump() {
        try {
            MBeanServer server = ManagementFactory.getPlatformMBeanServer();
            ObjectName objectName = new ObjectName("com.sun.management:type=HotSpotDiagnostic");
            Object[] params = new Object[2];
            params[0] = "/tmp/leak-detection-" + System.currentTimeMillis() + ".hprof";
            params[1] = true; // live objects only
            String[] signature = new String[]{"java.lang.String", "boolean"};
            server.invoke(objectName, "dumpHeap", params, signature);
            
            System.out.println("Heap dump generated: " + params[0]);
        } catch (Exception e) {
            System.err.println("Failed to generate heap dump: " + e.getMessage());
        }
    }
    
    // Leak-prone patterns to avoid
    public void commonLeakPatterns() {
        // 1. Static collections that grow indefinitely
        // private static final List<Object> staticList = new ArrayList<>(); // LEAK
        
        // 2. Listeners not being removed
        // eventSource.addListener(listener); // Must call removeListener()
        
        // 3. ThreadLocal not being cleared
        // ThreadLocal<Object> tl = new ThreadLocal<>(); // Must call remove()
        
        // 4. Caches without eviction policy
        // Map<String, Object> cache = new HashMap<>(); // Grows forever
        
        // 5. Inner classes holding references to outer class
        // class Outer { class Inner {} } // Inner holds reference to Outer
    }
}`
  };

  return (
    <div style={{
      padding: '40px',
      paddingTop: '100px',
      minHeight: '100vh',
      backgroundColor: '#0f172a',
      color: 'white'
    }}>
      <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
        <div style={{ marginBottom: '40px' }}>
          <h1 style={{ fontSize: '36px', fontWeight: 'bold', marginBottom: '16px', color: '#10b981' }}>
            Java Memory Management
          </h1>
          <p style={{ fontSize: '18px', color: '#94a3b8' }}>
            Understanding heap, stack, garbage collection, and optimization for high-performance trading systems
          </p>
        </div>

        <div style={{ display: 'flex', gap: '20px', marginBottom: '40px' }}>
          {['overview', 'definition', 'code', 'gc-tuning', 'monitoring'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              style={{
                padding: '12px 24px',
                backgroundColor: activeTab === tab ? '#10b981' : 'transparent',
                border: `2px solid ${activeTab === tab ? '#10b981' : '#374151'}`,
                borderRadius: '8px',
                color: activeTab === tab ? 'white' : '#9ca3af',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                textTransform: 'capitalize',
                transition: 'all 0.2s'
              }}
            >
              {tab.replace('-', ' ')}
            </button>
          ))}
        </div>

        {activeTab === 'definition' && (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <div style={{ marginBottom: '20px' }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px' }}>
                {selectedTopic === 'heap' ? 'Heap Memory' :
                 selectedTopic === 'stack' ? 'Stack Memory' :
                 selectedTopic === 'gc' ? 'Garbage Collection' :
                 selectedTopic === 'metaspace' ? 'Metaspace' :
                 selectedTopic === 'leak' ? 'Memory Leaks' :
                 'Memory Optimization'} Definition
              </h3>
            </div>
            
            {selectedTopic === 'heap' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What is Heap Memory?</h4>
                <p style={{ marginBottom: '16px' }}>
                  The heap is the runtime memory area where Java objects are allocated. It's divided into different generations (Young, Old) and is shared among all threads. All instance variables and objects are stored here.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why is it crucial for Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>Stores all trading objects like Orders, Positions, Market Data</li>
                  <li>Proper heap management prevents OutOfMemoryError in high-volume trading</li>
                  <li>Heap sizing affects GC performance and application latency</li>
                  <li>Object pooling reduces heap allocation pressure</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to manage it effectively?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Size heap appropriately with -Xmx and -Xms flags</li>
                  <li>Use object pools for frequently created/destroyed objects</li>
                  <li>Monitor heap usage with profiling tools</li>
                  <li>Consider off-heap storage for large datasets</li>
                </ul>
              </div>
            )}
            
            {selectedTopic === 'gc' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What is Garbage Collection?</h4>
                <p style={{ marginBottom: '16px' }}>
                  Garbage Collection (GC) is the automatic memory management process that reclaims memory used by objects that are no longer reachable in your program. It prevents memory leaks and OutOfMemoryErrors.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why is it critical for Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>GC pauses can cause trade execution delays and missed opportunities</li>
                  <li>Poorly tuned GC can introduce latency spikes in high-frequency trading</li>
                  <li>Memory pressure from rapid object creation requires efficient collection</li>
                  <li>GC overhead affects overall system throughput</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to optimize it?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Choose appropriate GC algorithm (G1, ZGC, Shenandoah for low latency)</li>
                  <li>Tune GC parameters for your specific workload</li>
                  <li>Minimize object allocation in critical paths</li>
                  <li>Use GC logging and monitoring to identify issues</li>
                </ul>
              </div>
            )}
            
            {selectedTopic === 'stack' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What is Stack Memory?</h4>
                <p style={{ marginBottom: '16px' }}>
                  Stack memory is used for method call management and local variable storage. Each thread has its own stack, and it operates in a LIFO (Last In, First Out) manner. It's much faster than heap access.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why is it important for Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>Fast access for local variables and method parameters</li>
                  <li>Stack overflow can occur with deep recursion in risk calculations</li>
                  <li>Thread-local storage doesn't require synchronization</li>
                  <li>Automatic cleanup when methods return</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to use it effectively?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Prefer local variables over instance variables when possible</li>
                  <li>Avoid deep recursion; use iteration instead</li>
                  <li>Use -Xss flag to control stack size if needed</li>
                  <li>Leverage stack allocation for short-lived objects</li>
                </ul>
              </div>
            )}
          </div>
        )}

        {activeTab === 'overview' && (
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>
            {memoryTopics.map((topic) => (
              <div
                key={topic.id}
                onClick={() => setSelectedTopic(topic)}
                style={{
                  padding: '24px',
                  backgroundColor: selectedTopic?.id === topic.id ? 'rgba(16, 185, 129, 0.1)' : 'rgba(31, 41, 55, 0.5)',
                  border: `2px solid ${selectedTopic?.id === topic.id ? '#10b981' : 'transparent'}`,
                  borderRadius: '12px',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '12px' }}>
                  <div style={{ color: topic.color }}>{topic.icon}</div>
                  <h3 style={{ fontSize: '20px', fontWeight: '600' }}>{topic.name}</h3>
                </div>
                <p style={{ color: '#94a3b8', fontSize: '14px' }}>{topic.description}</p>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'code' && (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <div style={{ marginBottom: '20px' }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px', textTransform: 'capitalize' }}>
                {selectedTopic === 'gc' ? 'Garbage Collection' : selectedTopic ? selectedTopic.replace('-', ' ') : 'Memory Management'} Example
              </h3>
            </div>
            <SyntaxHighlighter
              language="java"
              style={oneDark}
              customStyle={{
                backgroundColor: '#1e293b',
                padding: '20px',
                borderRadius: '8px',
                fontSize: '14px',
                lineHeight: '1.6'
              }}
            >
              {selectedTopic ? codeExamples[selectedTopic] : '// Select a topic to view code examples'}
            </SyntaxHighlighter>
          </div>
        )}

        {activeTab === 'gc-tuning' && (
          <div style={{ display: 'grid', gap: '20px' }}>
            <div style={{
              padding: '24px',
              backgroundColor: 'rgba(31, 41, 55, 0.5)',
              borderRadius: '12px',
              border: '1px solid #374151'
            }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px', color: '#10b981' }}>
                GC Algorithms for Trading Systems
              </h3>
              <div style={{ display: 'grid', gap: '12px' }}>
                {[
                  { name: 'G1GC', desc: 'Best for: Low-latency with predictable pauses', flags: '-XX:+UseG1GC -XX:MaxGCPauseMillis=10' },
                  { name: 'ZGC', desc: 'Best for: Ultra-low latency (<10ms pauses)', flags: '-XX:+UseZGC -XX:ZCollectionInterval=5' },
                  { name: 'Shenandoah', desc: 'Best for: Concurrent processing with minimal pauses', flags: '-XX:+UseShenandoahGC' },
                  { name: 'Serial GC', desc: 'Best for: Small applications, single-threaded', flags: '-XX:+UseSerialGC' }
                ].map((gc, index) => (
                  <div key={index} style={{
                    padding: '16px',
                    backgroundColor: '#1e293b',
                    borderRadius: '8px',
                    borderLeft: '4px solid #10b981'
                  }}>
                    <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '8px', color: '#10b981' }}>
                      {gc.name}
                    </h4>
                    <p style={{ color: '#94a3b8', fontSize: '14px', marginBottom: '8px' }}>
                      {gc.desc}
                    </p>
                    <code style={{ color: '#60a5fa', fontSize: '12px', fontFamily: 'monospace' }}>
                      {gc.flags}
                    </code>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'monitoring' && (
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
            <div style={{
              padding: '24px',
              backgroundColor: 'rgba(31, 41, 55, 0.5)',
              borderRadius: '12px',
              border: '1px solid #374151'
            }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px', color: '#3b82f6' }}>
                Memory Monitoring Tools
              </h3>
              <ul style={{ listStyle: 'none', padding: 0 }}>
                {[
                  'JConsole - Basic JVM monitoring',
                  'VisualVM - Comprehensive profiling',
                  'JProfiler - Commercial profiler',
                  'Eclipse MAT - Heap dump analysis',
                  'jmap/jhat - Command line tools',
                  'GC logs - Detailed GC analysis'
                ].map((tool, index) => (
                  <li key={index} style={{ padding: '8px 0', color: '#94a3b8', fontSize: '14px' }}>
                    <span style={{ color: '#3b82f6', marginRight: '8px' }}>•</span>
                    {tool}
                  </li>
                ))}
              </ul>
            </div>
            <div style={{
              padding: '24px',
              backgroundColor: 'rgba(31, 41, 55, 0.5)',
              borderRadius: '12px',
              border: '1px solid #374151'
            }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px', color: '#f59e0b' }}>
                Key Metrics to Monitor
              </h3>
              <ul style={{ listStyle: 'none', padding: 0 }}>
                {[
                  'Heap utilization percentage',
                  'GC pause times and frequency',
                  'Object allocation rate',
                  'Survivor space usage',
                  'Metaspace growth',
                  'Thread count and states'
                ].map((metric, index) => (
                  <li key={index} style={{ padding: '8px 0', color: '#94a3b8', fontSize: '14px' }}>
                    <span style={{ color: '#f59e0b', marginRight: '8px' }}>•</span>
                    {metric}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}
      </div>
      
        {/* Details Panel - Moved to bottom */}
      {selectedTopic && (
        <div style={{
          backgroundColor: 'rgba(31, 41, 55, 0.5)',
          borderRadius: '12px',
          border: '1px solid #374151',
          padding: '24px',
          marginTop: '32px'
                  }}>
          {/* Panel Header */}
          <div style={{
            padding: '20px',
            borderBottom: '1px solid #374151',
            background: 'linear-gradient(135deg, #1f2937 0%, #111827 100%)'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start' }}>
              <div>
                <h2 style={{ fontSize: '18px', fontWeight: 'bold', color: '#fbbf24', margin: '0 0 8px 0' }}>
                  {selectedTopic.name}
                </h2>
                <div style={{ fontSize: '12px', color: '#9ca3af' }}>
                  {selectedTopic.description}
                </div>
              </div>
              <button
                onClick={() => setSelectedTopic(null)}
                style={{
                  background: 'transparent',
                  border: 'none',
                  color: '#9ca3af',
                  fontSize: '24px',
                  cursor: 'pointer',
                  padding: 0,
                  lineHeight: 1
                }}
              >
                ×
              </button>
            </div>
          </div>

          {/* Tabs */}
          <div style={{ display: 'flex', borderBottom: '1px solid #374151' }}>
            {['definition', 'code', 'monitoring', 'best-practices'].map(tab => (
              <button
                key={tab}
                onClick={() => setDetailsTab(tab)}
                style={{
                  flex: 1,
                  padding: '12px',
                  background: detailsTab === tab ? '#1f2937' : 'transparent',
                  border: 'none',
                  color: detailsTab === tab ? '#fbbf24' : '#9ca3af',
                  fontSize: '12px',
                  fontWeight: '600',
                  textTransform: 'capitalize',
                  cursor: 'pointer'
                }}
              >
                {tab.replace('-', ' ')}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div style={{ flex: 1, padding: '20px', overflowY: 'auto' }}>
            {detailsTab === 'definition' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  What is {selectedTopic.name}?
                </h3>
                <div style={{ color: '#d1d5db', fontSize: '13px', lineHeight: '1.8' }}>
                  {selectedTopic.id === 'heap' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Heap memory is where Java objects are allocated and stored. It's divided into Young Generation (Eden, Survivor spaces) and Old Generation, managed by the garbage collector for automatic memory management.
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Key Characteristics:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>Shared among all threads</li>
                        <li>Automatically garbage collected</li>
                        <li>Divided into generations for efficient GC</li>
                        <li>Size configurable via JVM flags</li>
                      </ul>
                    </>
                  )}
                  {selectedTopic.id === 'stack' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Stack memory stores method call information, local variables, and partial results. Each thread has its own stack, making it thread-safe by design.
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Key Characteristics:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>Thread-specific memory area</li>
                        <li>Stores method frames and local variables</li>
                        <li>LIFO (Last In, First Out) access pattern</li>
                        <li>Automatically cleaned when methods return</li>
                      </ul>
                    </>
                  )}
                  {selectedTopic.id === 'gc' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Garbage Collection automatically reclaims memory used by objects that are no longer reachable, preventing memory leaks and managing heap space efficiently.
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>GC Algorithms:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>Serial GC - Single-threaded collection</li>
                        <li>Parallel GC - Multi-threaded collection</li>
                        <li>G1 GC - Low-latency collector</li>
                        <li>ZGC/Shenandoah - Ultra-low latency</li>
                      </ul>
                    </>
                  )}
                </div>
              </div>
            )}

            {detailsTab === 'code' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  Implementation Example
                </h3>
                <div style={{
                  background: '#0f172a',
                  padding: '16px',
                  borderRadius: '8px',
                  maxHeight: '600px',
                  overflow: 'auto'
                }}>
                  <SyntaxHighlighter
                    language="java"
                    style={oneDark}
                    customStyle={{
                      background: 'transparent',
                      padding: 0,
                      margin: 0,
                      fontSize: '12px',
                      lineHeight: '1.5'
                    }}
                  >
                    {codeExamples[selectedTopic.id] || '// Code example not available'}
                  </SyntaxHighlighter>
                </div>
              </div>
            )}

            {detailsTab === 'monitoring' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  Monitoring and Tools
                </h3>
                <div style={{ color: '#d1d5db', fontSize: '13px', lineHeight: '1.8' }}>
                  <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Essential Tools:</h4>
                  <ul style={{ paddingLeft: '20px', marginBottom: '16px' }}>
                    <li>JConsole - Built-in JVM monitoring</li>
                    <li>VisualVM - Comprehensive profiling</li>
                    <li>Eclipse MAT - Memory leak analysis</li>
                    <li>JProfiler - Commercial profiler</li>
                  </ul>
                  <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Key Metrics:</h4>
                  <ul style={{ paddingLeft: '20px' }}>
                    <li>Heap utilization and growth trends</li>
                    <li>GC pause times and frequency</li>
                    <li>Object allocation rates</li>
                    <li>Thread count and memory usage</li>
                  </ul>
                </div>
              </div>
            )}

            {detailsTab === 'best-practices' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  Best Practices
                </h3>
                <div style={{ display: 'grid', gap: '12px' }}>
                  {[
                    'Use object pools for frequently allocated objects',
                    'Minimize object creation in hot paths',
                    'Choose appropriate GC algorithm for your workload',
                    'Monitor and tune heap sizes based on usage patterns',
                    'Avoid memory leaks with proper resource management',
                    'Use weak references for caching scenarios',
                    'Profile regularly to identify memory bottlenecks'
                  ].map((practice, idx) => (
                    <div key={idx} style={{
                      padding: '12px',
                      background: '#1f2937',
                      borderRadius: '6px',
                      borderLeft: '3px solid #10b981',
                      color: '#d1d5db',
                      fontSize: '13px'
                    }}>
                      {practice}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default JavaMemoryManagement;
import React, { useState, useEffect } from 'react';
import { 
  Shield, Globe, Lock, Wifi, Server, Eye, AlertTriangle,
  Network, Router, Firewall, Key, Clock, Activity, 
  TrendingUp, BarChart3, Zap, Settings, Info, CheckCircle,
  X, RefreshCw, Download, Upload, Search, Filter,
  MapPin, Users, Database, Cloud, Smartphone, Monitor
} from 'lucide-react';

const NetworkSecurity = () => {
  const [activeTab, setActiveTab] = useState('firewall');
  const [selectedProtection, setSelectedProtection] = useState('waf');
  const [liveAttacks, setLiveAttacks] = useState([]);
  const [isMonitoring, setIsMonitoring] = useState(true);
  const [trafficData, setTrafficData] = useState({
    legitimate: 85,
    suspicious: 12,
    blocked: 3
  });

  // Network Security Layers
  const securityLayers = {
    'perimeter': {
      name: 'Perimeter Security',
      description: 'First line of defense at network boundaries',
      components: [
        {
          name: 'Next-Generation Firewall (NGFW)',
          type: 'Hardware/Software',
          protection: 'Application-aware deep packet inspection',
          throughput: '40 Gbps',
          features: ['IPS/IDS', 'Application Control', 'User Identity', 'SSL Inspection']
        },
        {
          name: 'Web Application Firewall (WAF)',
          type: 'Application Layer',
          protection: 'HTTP/HTTPS traffic filtering and monitoring',
          throughput: '10 Gbps',
          features: ['SQL Injection Protection', 'XSS Prevention', 'DDoS Mitigation', 'Bot Management']
        },
        {
          name: 'DDoS Protection',
          type: 'Cloud Service',
          protection: 'Volumetric and application layer attack mitigation',
          throughput: '3.47 Tbps',
          features: ['Always-on Protection', 'Adaptive Thresholds', 'Global Scrubbing', 'Real-time Analytics']
        }
      ]
    },
    'internal': {
      name: 'Internal Network Security',
      description: 'Protection within the trusted network perimeter',
      components: [
        {
          name: 'Network Segmentation',
          type: 'Architecture',
          protection: 'Micro-segmentation and zero-trust networking',
          throughput: 'Line Rate',
          features: ['VLAN Isolation', 'Micro-segmentation', 'East-West Traffic Control', 'Policy Enforcement']
        },
        {
          name: 'Intrusion Prevention System (IPS)',
          type: 'Inline Security',
          protection: 'Real-time threat detection and prevention',
          throughput: '20 Gbps',
          features: ['Signature-based Detection', 'Behavioral Analysis', 'Protocol Anomaly Detection', 'Automated Response']
        },
        {
          name: 'Network Access Control (NAC)',
          type: 'Access Management',
          protection: 'Device authentication and authorization',
          throughput: 'N/A',
          features: ['Device Profiling', 'Policy Enforcement', 'Quarantine Capabilities', 'Guest Access']
        }
      ]
    },
    'encryption': {
      name: 'Encryption & VPN',
      description: 'Data protection in transit and at rest',
      components: [
        {
          name: 'IPSec VPN',
          type: 'Network Encryption',
          protection: 'Site-to-site secure connectivity',
          throughput: '10 Gbps',
          features: ['AES-256 Encryption', 'Perfect Forward Secrecy', 'Certificate-based Auth', 'High Availability']
        },
        {
          name: 'SSL/TLS Termination',
          type: 'Application Encryption',
          protection: 'Secure application communications',
          throughput: '5 Gbps',
          features: ['TLS 1.3 Support', 'Certificate Management', 'HSTS Enforcement', 'Perfect Forward Secrecy']
        },
        {
          name: 'Zero Trust Network Access',
          type: 'Remote Access',
          protection: 'Secure remote connectivity',
          throughput: '1 Gbps per tunnel',
          features: ['Identity Verification', 'Device Trust', 'Continuous Monitoring', 'Least Privilege Access']
        }
      ]
    }
  };

  // Security Protocols
  const securityProtocols = [
    {
      name: 'TLS 1.3',
      layer: 'Application',
      purpose: 'Secure HTTP communications',
      strength: 'Very High',
      latency: '< 10ms',
      features: ['Perfect Forward Secrecy', 'Reduced Handshake', 'Enhanced Privacy', 'Post-Quantum Ready'],
      implementation: `// Spring Boot TLS 1.3 Configuration
@Configuration
@EnableWebSecurity
public class TLSConfig {
    
    @Bean
    public TomcatServletWebServerFactory servletContainer() {
        TomcatServletWebServerFactory tomcat = new TomcatServletWebServerFactory();
        
        tomcat.addAdditionalTomcatConnectors(createHttpsConnector());
        return tomcat;
    }
    
    private Connector createHttpsConnector() {
        Connector connector = new Connector("org.apache.coyote.http11.Http11NioProtocol");
        Http11NioProtocol protocol = (Http11NioProtocol) connector.getProtocolHandler();
        
        connector.setScheme("https");
        connector.setSecure(true);
        connector.setPort(8443);
        
        // TLS 1.3 Configuration
        protocol.setSSLEnabled(true);
        protocol.setSslProtocols("TLSv1.3");
        protocol.setCiphers("TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256");
        protocol.setHonorCipherOrder(true);
        protocol.setUseServerCipherSuitesOrder(true);
        
        // Certificate Configuration
        protocol.setKeystoreFile("/path/to/keystore.p12");
        protocol.setKeystorePass("keystorePassword");
        protocol.setKeyAlias("trading-system");
        
        return connector;
    }
}`
    },
    {
      name: 'IPSec',
      layer: 'Network',
      purpose: 'Site-to-site VPN connectivity',
      strength: 'Very High',
      latency: '< 5ms',
      features: ['ESP Encryption', 'AH Authentication', 'Perfect Forward Secrecy', 'NAT Traversal'],
      implementation: `# IPSec Configuration Example (StrongSwan)
conn trading-site-to-site
    left=%defaultroute
    leftid=@trading-primary.darkpool.com
    leftcert=trading-primary.crt
    leftsubnet=********/24
    
    right=***********
    rightid=@trading-secondary.darkpool.com
    rightcert=trading-secondary.crt
    rightsubnet=********/24
    
    # Security Parameters
    ike=aes256-sha256-modp2048!
    esp=aes256-sha256-modp2048!
    
    # Policies
    auto=start
    keyexchange=ikev2
    rekey=yes
    dpdaction=restart
    dpddelay=30s
    dpdtimeout=120s
    
    # Perfect Forward Secrecy
    pfs=yes
    lifetime=8h
    margintime=9m
    rekeyfuzz=100%`
    },
    {
      name: 'WireGuard',
      layer: 'Network',
      purpose: 'Modern VPN protocol',
      strength: 'Very High',
      latency: '< 2ms',
      features: ['ChaCha20 Encryption', 'Curve25519 Keys', 'Simple Configuration', 'High Performance'],
      implementation: `# WireGuard Configuration
[Interface]
PrivateKey = <server-private-key>
Address = ********/24
ListenPort = 51820
PostUp = iptables -A FORWARD -i wg0 -j ACCEPT; iptables -t nat -A POSTROUTING -o eth0 -j MASQUERADE
PostDown = iptables -D FORWARD -i wg0 -j ACCEPT; iptables -t nat -D POSTROUTING -o eth0 -j MASQUERADE

# Trading System Peer
[Peer]
PublicKey = <peer-public-key>
AllowedIPs = ********/32
Endpoint = trading-node1.darkpool.com:51820
PersistentKeepalive = 25

# Risk Management Peer
[Peer]
PublicKey = <risk-peer-public-key>
AllowedIPs = ********/32
Endpoint = risk-node1.darkpool.com:51820
PersistentKeepalive = 25`
    }
  ];

  // Live Attack Data (simulated)
  const attackTypes = [
    { type: 'SQL Injection', count: 234, severity: 'High', blocked: true },
    { type: 'DDoS', count: 1847, severity: 'Critical', blocked: true },
    { type: 'Port Scanning', count: 56, severity: 'Medium', blocked: true },
    { type: 'Brute Force', count: 89, severity: 'High', blocked: true },
    { type: 'XSS Attempt', count: 12, severity: 'Medium', blocked: true },
    { type: 'Command Injection', count: 3, severity: 'Critical', blocked: true }
  ];

  const networkMetrics = {
    'Packets Inspected': '2.3B/day',
    'Threats Blocked': '45,892',
    'Latency Impact': '< 2ms',
    'False Positives': '0.02%',
    'Uptime': '99.99%',
    'Bandwidth Utilization': '73%'
  };

  // Simulate live data updates
  useEffect(() => {
    const interval = setInterval(() => {
      if (isMonitoring) {
        // Update traffic data
        setTrafficData(prev => ({
          legitimate: Math.max(70, Math.min(95, prev.legitimate + (Math.random() - 0.5) * 4)),
          suspicious: Math.max(2, Math.min(25, prev.suspicious + (Math.random() - 0.5) * 2)),
          blocked: Math.max(1, Math.min(10, prev.blocked + (Math.random() - 0.5) * 1))
        }));

        // Add new attack (simulated)
        if (Math.random() > 0.7) {
          const newAttack = {
            id: Date.now(),
            type: attackTypes[Math.floor(Math.random() * attackTypes.length)].type,
            source: `${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
            time: new Date().toLocaleTimeString(),
            severity: ['Low', 'Medium', 'High', 'Critical'][Math.floor(Math.random() * 4)]
          };
          
          setLiveAttacks(prev => [newAttack, ...prev.slice(0, 9)]);
        }
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [isMonitoring]);

  const getSeverityColor = (severity) => {
    switch(severity) {
      case 'Critical': return '#ef4444';
      case 'High': return '#f59e0b';
      case 'Medium': return '#3b82f6';
      case 'Low': return '#10b981';
      default: return '#6b7280';
    }
  };

  return (
    <div style={{
      width: '100%',
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%)',
      color: '#ffffff',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif',
      padding: '20px'
    }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '30px',
        padding: '24px',
        background: 'rgba(15, 23, 42, 0.8)',
        borderRadius: '16px',
        border: '1px solid rgba(51, 65, 85, 0.5)',
        backdropFilter: 'blur(8px)'
      }}>
        <div>
          <h1 style={{
            fontSize: '36px',
            margin: '0 0 8px 0',
            background: 'linear-gradient(135deg, #3b82f6 0%, #10b981 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontWeight: '700'
          }}>
            Network Security Interview Prep
          </h1>
          <p style={{
            fontSize: '16px',
            color: '#94a3b8',
            margin: 0
          }}>
            Multi-layered network protection and traffic monitoring
          </p>
        </div>
        
        <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
          <div style={{
            padding: '12px 16px',
            backgroundColor: isMonitoring ? 'rgba(16, 185, 129, 0.1)' : 'rgba(107, 114, 128, 0.1)',
            borderRadius: '8px',
            border: `1px solid ${isMonitoring ? '#10b981' : '#6b7280'}`,
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            <div style={{
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              backgroundColor: isMonitoring ? '#10b981' : '#6b7280',
              animation: isMonitoring ? 'pulse 2s infinite' : 'none'
            }} />
            <div>
              <div style={{ fontSize: '12px', color: isMonitoring ? '#10b981' : '#6b7280', fontWeight: '600' }}>
                Security Status
              </div>
              <div style={{ fontSize: '14px', color: 'white', fontWeight: '500' }}>
                {isMonitoring ? 'Active Monitoring' : 'Offline'}
              </div>
            </div>
          </div>
          
          <button
            onClick={() => setIsMonitoring(!isMonitoring)}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '10px 16px',
              backgroundColor: isMonitoring ? 'rgba(239, 68, 68, 0.1)' : 'rgba(16, 185, 129, 0.1)',
              color: isMonitoring ? '#ef4444' : '#10b981',
              border: `1px solid ${isMonitoring ? '#ef4444' : '#10b981'}`,
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500',
              transition: 'all 0.2s'
            }}
          >
            {isMonitoring ? <X size={16} /> : <Activity size={16} />}
            {isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'}
          </button>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div style={{
        display: 'flex',
        gap: '4px',
        marginBottom: '24px',
        backgroundColor: 'rgba(15, 23, 42, 0.6)',
        borderRadius: '12px',
        padding: '8px',
        border: '1px solid rgba(51, 65, 85, 0.5)'
      }}>
        {[
          { id: 'firewall', label: 'Firewall & WAF', icon: <Shield size={16} /> },
          { id: 'protocols', label: 'Security Protocols', icon: <Lock size={16} /> },
          { id: 'monitoring', label: 'Live Monitoring', icon: <Activity size={16} /> },
          { id: 'topology', label: 'Network Topology', icon: <Network size={16} /> },
          { id: 'metrics', label: 'Security Metrics', icon: <BarChart3 size={16} /> }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '12px 16px',
              backgroundColor: activeTab === tab.id ? '#3b82f6' : 'transparent',
              color: activeTab === tab.id ? 'white' : '#94a3b8',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500',
              transition: 'all 0.2s',
              flex: 1,
              justifyContent: 'center'
            }}
            onMouseEnter={(e) => {
              if (activeTab !== tab.id) {
                e.currentTarget.style.backgroundColor = 'rgba(59, 130, 246, 0.1)';
                e.currentTarget.style.color = '#3b82f6';
              }
            }}
            onMouseLeave={(e) => {
              if (activeTab !== tab.id) {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = '#94a3b8';
              }
            }}
          >
            {tab.icon}
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'firewall' && (
        <div>
          {/* Security Layers Overview */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
            {Object.entries(securityLayers).map(([key, layer]) => (
              <div key={key} style={{
                background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
                borderRadius: '16px',
                border: '1px solid rgba(51, 65, 85, 0.5)',
                padding: '24px'
              }}>
                <div style={{ marginBottom: '20px' }}>
                  <h3 style={{ fontSize: '24px', fontWeight: '600', color: 'white', marginBottom: '8px' }}>
                    {layer.name}
                  </h3>
                  <p style={{ fontSize: '14px', color: '#94a3b8', margin: 0 }}>
                    {layer.description}
                  </p>
                </div>

                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))', gap: '16px' }}>
                  {layer.components.map((component, idx) => (
                    <div key={idx} style={{
                      backgroundColor: 'rgba(31, 41, 55, 0.5)',
                      borderRadius: '12px',
                      border: '1px solid rgba(51, 65, 85, 0.3)',
                      padding: '20px'
                    }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start', marginBottom: '12px' }}>
                        <h4 style={{ fontSize: '16px', fontWeight: '600', color: 'white', margin: 0 }}>
                          {component.name}
                        </h4>
                        <span style={{
                          fontSize: '11px',
                          padding: '2px 6px',
                          backgroundColor: 'rgba(59, 130, 246, 0.2)',
                          color: '#60a5fa',
                          borderRadius: '4px'
                        }}>
                          {component.type}
                        </span>
                      </div>
                      
                      <p style={{ fontSize: '12px', color: '#9ca3af', marginBottom: '12px', lineHeight: '1.4' }}>
                        {component.protection}
                      </p>
                      
                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '12px' }}>
                        <span style={{ fontSize: '12px', color: '#6b7280' }}>Throughput:</span>
                        <span style={{ fontSize: '12px', color: '#10b981', fontWeight: '600' }}>
                          {component.throughput}
                        </span>
                      </div>

                      <div>
                        <h5 style={{ fontSize: '12px', color: '#f59e0b', marginBottom: '8px' }}>Key Features:</h5>
                        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '4px' }}>
                          {component.features.map((feature, featureIdx) => (
                            <div key={featureIdx} style={{
                              fontSize: '10px',
                              padding: '2px 6px',
                              backgroundColor: 'rgba(16, 185, 129, 0.1)',
                              color: '#10b981',
                              borderRadius: '3px',
                              textAlign: 'center'
                            }}>
                              {feature}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'protocols' && (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
          {securityProtocols.map((protocol, index) => (
            <div key={index} style={{
              background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
              borderRadius: '16px',
              border: '1px solid rgba(51, 65, 85, 0.5)',
              padding: '24px'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start', marginBottom: '16px' }}>
                <div>
                  <h3 style={{ fontSize: '24px', fontWeight: '600', color: 'white', marginBottom: '8px' }}>
                    {protocol.name}
                  </h3>
                  <div style={{ display: 'flex', gap: '8px', alignItems: 'center', marginBottom: '8px' }}>
                    <span style={{
                      fontSize: '12px',
                      padding: '4px 8px',
                      backgroundColor: 'rgba(59, 130, 246, 0.2)',
                      color: '#60a5fa',
                      borderRadius: '6px'
                    }}>
                      {protocol.layer} Layer
                    </span>
                    <span style={{
                      fontSize: '12px',
                      padding: '4px 8px',
                      backgroundColor: 'rgba(16, 185, 129, 0.2)',
                      color: '#10b981',
                      borderRadius: '6px'
                    }}>
                      {protocol.strength} Security
                    </span>
                  </div>
                </div>
                
                <div style={{ textAlign: 'right' }}>
                  <div style={{ fontSize: '12px', color: '#9ca3af', marginBottom: '4px' }}>
                    Latency Impact
                  </div>
                  <div style={{ fontSize: '16px', fontWeight: '600', color: '#f59e0b' }}>
                    {protocol.latency}
                  </div>
                </div>
              </div>

              <p style={{ fontSize: '14px', color: '#94a3b8', marginBottom: '16px' }}>
                {protocol.purpose}
              </p>

              <div style={{ marginBottom: '20px' }}>
                <h4 style={{ fontSize: '14px', color: '#8b5cf6', marginBottom: '8px' }}>Key Features:</h4>
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '6px' }}>
                  {protocol.features.map((feature, idx) => (
                    <span key={idx} style={{
                      fontSize: '11px',
                      padding: '4px 8px',
                      backgroundColor: 'rgba(139, 92, 246, 0.1)',
                      color: '#8b5cf6',
                      borderRadius: '4px'
                    }}>
                      {feature}
                    </span>
                  ))}
                </div>
              </div>

              <div>
                <h4 style={{ fontSize: '14px', color: '#10b981', marginBottom: '8px', display: 'flex', alignItems: 'center', gap: '4px' }}>
                  <Settings size={14} />
                  Configuration Example
                </h4>
                <pre style={{
                  fontSize: '10px',
                  backgroundColor: '#0f172a',
                  color: '#d1d5db',
                  padding: '16px',
                  borderRadius: '8px',
                  overflow: 'auto',
                  maxHeight: '300px',
                  lineHeight: '1.5',
                  fontFamily: 'Consolas, Monaco, monospace'
                }}>
                  {protocol.implementation}
                </pre>
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'monitoring' && (
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '24px' }}>
          {/* Live Attacks */}
          <div style={{
            background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
            borderRadius: '16px',
            border: '1px solid rgba(51, 65, 85, 0.5)',
            padding: '20px'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
              <h3 style={{ fontSize: '18px', fontWeight: '600', color: 'white', margin: 0 }}>
                Live Attack Feed
              </h3>
              <div style={{
                width: '8px',
                height: '8px',
                borderRadius: '50%',
                backgroundColor: '#ef4444',
                animation: isMonitoring ? 'pulse 1s infinite' : 'none'
              }} />
            </div>
            
            <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
              {liveAttacks.length === 0 ? (
                <div style={{ textAlign: 'center', color: '#6b7280', padding: '40px 0' }}>
                  {isMonitoring ? 'No recent attacks detected' : 'Monitoring disabled'}
                </div>
              ) : (
                liveAttacks.map(attack => (
                  <div key={attack.id} style={{
                    padding: '12px',
                    marginBottom: '8px',
                    backgroundColor: 'rgba(31, 41, 55, 0.5)',
                    borderLeft: `3px solid ${getSeverityColor(attack.severity)}`,
                    borderRadius: '6px'
                  }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start', marginBottom: '4px' }}>
                      <span style={{ fontSize: '14px', color: 'white', fontWeight: '500' }}>
                        {attack.type}
                      </span>
                      <span style={{
                        fontSize: '10px',
                        padding: '2px 6px',
                        backgroundColor: `${getSeverityColor(attack.severity)}20`,
                        color: getSeverityColor(attack.severity),
                        borderRadius: '4px'
                      }}>
                        {attack.severity}
                      </span>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px' }}>
                      <span style={{ color: '#9ca3af' }}>From: {attack.source}</span>
                      <span style={{ color: '#6b7280' }}>{attack.time}</span>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Attack Summary */}
          <div style={{
            background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
            borderRadius: '16px',
            border: '1px solid rgba(51, 65, 85, 0.5)',
            padding: '20px'
          }}>
            <h3 style={{ fontSize: '18px', fontWeight: '600', color: 'white', marginBottom: '16px' }}>
              Attack Summary (24h)
            </h3>
            
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              {attackTypes.map((attack, idx) => (
                <div key={idx} style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '12px',
                  backgroundColor: 'rgba(31, 41, 55, 0.5)',
                  borderRadius: '8px'
                }}>
                  <div>
                    <div style={{ fontSize: '14px', color: 'white', fontWeight: '500', marginBottom: '2px' }}>
                      {attack.type}
                    </div>
                    <div style={{ fontSize: '11px', color: '#9ca3af' }}>
                      Severity: <span style={{ color: getSeverityColor(attack.severity) }}>{attack.severity}</span>
                    </div>
                  </div>
                  <div style={{ textAlign: 'right' }}>
                    <div style={{ fontSize: '16px', fontWeight: '600', color: '#ef4444' }}>
                      {attack.count.toLocaleString()}
                    </div>
                    <div style={{ fontSize: '10px', color: '#10b981' }}>
                      {attack.blocked ? 'BLOCKED' : 'ALLOWED'}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Traffic Distribution */}
          <div style={{
            background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
            borderRadius: '16px',
            border: '1px solid rgba(51, 65, 85, 0.5)',
            padding: '20px',
            gridColumn: 'span 2'
          }}>
            <h3 style={{ fontSize: '18px', fontWeight: '600', color: 'white', marginBottom: '16px' }}>
              Network Traffic Distribution
            </h3>
            
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '16px' }}>
              {[
                { label: 'Legitimate Traffic', value: trafficData.legitimate, color: '#10b981' },
                { label: 'Suspicious Activity', value: trafficData.suspicious, color: '#f59e0b' },
                { label: 'Blocked Threats', value: trafficData.blocked, color: '#ef4444' }
              ].map((item, idx) => (
                <div key={idx} style={{
                  textAlign: 'center',
                  padding: '20px',
                  backgroundColor: 'rgba(31, 41, 55, 0.5)',
                  borderRadius: '8px',
                  border: `1px solid ${item.color}20`
                }}>
                  <div style={{ fontSize: '32px', fontWeight: '700', color: item.color, marginBottom: '8px' }}>
                    {item.value.toFixed(1)}%
                  </div>
                  <div style={{ fontSize: '12px', color: '#9ca3af' }}>
                    {item.label}
                  </div>
                  
                  {/* Visual bar */}
                  <div style={{
                    width: '100%',
                    height: '4px',
                    backgroundColor: 'rgba(55, 65, 81, 0.5)',
                    borderRadius: '2px',
                    marginTop: '12px',
                    overflow: 'hidden'
                  }}>
                    <div style={{
                      width: `${item.value}%`,
                      height: '100%',
                      backgroundColor: item.color,
                      borderRadius: '2px',
                      transition: 'width 0.5s ease-in-out'
                    }} />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'topology' && (
        <div style={{
          background: 'rgba(15, 23, 42, 0.8)',
          borderRadius: '16px',
          border: '1px solid rgba(51, 65, 85, 0.5)',
          padding: '24px',
          textAlign: 'center'
        }}>
          <Network size={64} style={{ color: '#3b82f6', margin: '0 auto 24px' }} />
          <h3 style={{ fontSize: '28px', color: 'white', marginBottom: '16px' }}>
            Network Security Topology
          </h3>
          <p style={{ color: '#94a3b8', fontSize: '16px', marginBottom: '32px', maxWidth: '600px', margin: '0 auto 32px' }}>
            Visual representation of the multi-layered security architecture protecting the dark pool trading system.
          </p>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(3, 1fr)',
            gap: '24px',
            maxWidth: '800px',
            margin: '0 auto'
          }}>
            {[
              { icon: <Globe size={32} />, title: 'Internet', desc: 'External threats', color: '#ef4444' },
              { icon: <Shield size={32} />, title: 'Perimeter Defense', desc: 'Firewall & WAF', color: '#f59e0b' },
              { icon: <Network size={32} />, title: 'Internal Network', desc: 'Segmented & monitored', color: '#10b981' }
            ].map((item, idx) => (
              <div key={idx} style={{
                padding: '24px',
                backgroundColor: 'rgba(31, 41, 55, 0.5)',
                borderRadius: '12px',
                border: `2px solid ${item.color}20`
              }}>
                <div style={{ color: item.color, marginBottom: '16px' }}>
                  {item.icon}
                </div>
                <h4 style={{ fontSize: '16px', color: 'white', marginBottom: '8px' }}>
                  {item.title}
                </h4>
                <p style={{ fontSize: '12px', color: '#9ca3af', margin: 0 }}>
                  {item.desc}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'metrics' && (
        <div>
          {/* Metrics Overview */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '16px',
            marginBottom: '24px'
          }}>
            {Object.entries(networkMetrics).map(([key, value]) => (
              <div key={key} style={{
                background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
                borderRadius: '12px',
                border: '1px solid rgba(51, 65, 85, 0.5)',
                padding: '20px',
                textAlign: 'center'
              }}>
                <div style={{ fontSize: '24px', fontWeight: '700', color: '#3b82f6', marginBottom: '8px' }}>
                  {value}
                </div>
                <div style={{ fontSize: '12px', color: '#94a3b8' }}>
                  {key}
                </div>
              </div>
            ))}
          </div>

          {/* Performance Chart */}
          <div style={{
            background: 'rgba(15, 23, 42, 0.8)',
            borderRadius: '16px',
            border: '1px solid rgba(51, 65, 85, 0.5)',
            padding: '24px'
          }}>
            <h3 style={{ fontSize: '20px', color: 'white', marginBottom: '20px', textAlign: 'center' }}>
              Security Performance Dashboard
            </h3>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(2, 1fr)',
              gap: '24px'
            }}>
              {[
                { title: 'Threat Detection Rate', value: '99.8%', trend: '+0.2%', color: '#10b981' },
                { title: 'False Positive Rate', value: '0.02%', trend: '-0.01%', color: '#10b981' },
                { title: 'Average Response Time', value: '1.2ms', trend: '-0.3ms', color: '#10b981' },
                { title: 'Network Utilization', value: '73%', trend: '+2%', color: '#3b82f6' }
              ].map((metric, idx) => (
                <div key={idx} style={{
                  padding: '20px',
                  backgroundColor: 'rgba(31, 41, 55, 0.5)',
                  borderRadius: '8px',
                  textAlign: 'center'
                }}>
                  <h4 style={{ fontSize: '14px', color: '#9ca3af', marginBottom: '8px' }}>
                    {metric.title}
                  </h4>
                  <div style={{ fontSize: '28px', fontWeight: '700', color: metric.color, marginBottom: '4px' }}>
                    {metric.value}
                  </div>
                  <div style={{ fontSize: '12px', color: metric.trend.startsWith('+') && !metric.title.includes('False') ? '#f59e0b' : '#10b981' }}>
                    {metric.trend} from last week
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      <style>
        {`
          @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
          }
        `}
      </style>
    </div>
  );
};

export default NetworkSecurity;
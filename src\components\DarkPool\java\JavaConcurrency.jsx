import React, { useState } from 'react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { GitBranch, Lock, Cpu, Activity, Zap, Shield } from 'lucide-react';

const JavaConcurrency = () => {
  const [selectedTopic, setSelectedTopic] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [detailsTab, setDetailsTab] = useState('overview');

  const topics = [
    {
      id: 'threads',
      name: 'Threads & Executors',
      icon: <GitBranch size={20} />,
      description: 'Thread management and execution',
      color: '#ef4444'
    },
    {
      id: 'fork-join',
      name: 'Fork/Join Framework',
      icon: <GitBranch size={20} />,
      description: 'Parallel processing with work-stealing',
      color: '#06b6d4'
    },
    {
      id: 'synchronization',
      name: 'Synchronization',
      icon: <Lock size={20} />,
      description: 'Locks, monitors, and coordination',
      color: '#3b82f6'
    },
    {
      id: 'coordination',
      name: 'Coordination Primitives',
      icon: <Shield size={20} />,
      description: 'CountDownLatch, Semaphore, CyclicBarrier',
      color: '#8b5cf6'
    },
    {
      id: 'blocking-queues',
      name: 'Blocking Queues',
      icon: <Activity size={20} />,
      description: 'Producer-consumer patterns',
      color: '#f59e0b'
    },
    {
      id: 'memory-model',
      name: 'Memory Model & Visibility',
      icon: <Cpu size={20} />,
      description: 'volatile, happens-before relationships',
      color: '#ec4899'
    },
    {
      id: 'lock-types',
      name: 'Advanced Lock Types',
      icon: <Lock size={20} />,
      description: 'ReadWriteLock, StampedLock',
      color: '#10b981'
    },
    {
      id: 'thread-pool-tuning',
      name: 'Thread Pool Tuning',
      icon: <Zap size={20} />,
      description: 'Performance optimization strategies',
      color: '#f97316'
    },
    {
      id: 'deadlock-prevention',
      name: 'Deadlock Prevention',
      icon: <Shield size={20} />,
      description: 'Detection and prevention strategies',
      color: '#ef4444'
    },
    {
      id: 'flow-control',
      name: 'Flow Control',
      icon: <Activity size={20} />,
      description: 'Backpressure and rate limiting',
      color: '#84cc16'
    },
    {
      id: 'atomic',
      name: 'Atomic Operations',
      icon: <Zap size={20} />,
      description: 'Lock-free programming',
      color: '#a855f7'
    },
    {
      id: 'concurrent-collections',
      name: 'Concurrent Collections',
      icon: <Shield size={20} />,
      description: 'Thread-safe data structures',
      color: '#14b8a6'
    }
  ];

  const codeExamples = {
    threads: `// Thread Pool for Order Processing
public class OrderProcessingService {
    private final ExecutorService executorService;
    private final ScheduledExecutorService scheduledExecutor;
    
    public OrderProcessingService() {
        // Fixed thread pool for order processing
        this.executorService = Executors.newFixedThreadPool(
            Runtime.getRuntime().availableProcessors(),
            new ThreadFactory() {
                private final AtomicInteger counter = new AtomicInteger();
                
                @Override
                public Thread newThread(Runnable r) {
                    Thread thread = new Thread(r);
                    thread.setName("OrderProcessor-" + counter.incrementAndGet());
                    thread.setPriority(Thread.MAX_PRIORITY);
                    return thread;
                }
            }
        );
        
        // Scheduled executor for periodic tasks
        this.scheduledExecutor = Executors.newScheduledThreadPool(2);
        
        // Schedule market data refresh
        scheduledExecutor.scheduleAtFixedRate(
            this::refreshMarketData, 0, 100, TimeUnit.MILLISECONDS
        );
    }
    
    public Future<OrderResult> processOrder(Order order) {
        return executorService.submit(() -> {
            validateOrder(order);
            checkRisk(order);
            executeOrder(order);
            return new OrderResult(order.getId(), Status.EXECUTED);
        });
    }
}`,
    synchronization: `// Advanced Synchronization for Order Book
public class SynchronizedOrderBook {
    private final ReadWriteLock lock = new ReentrantReadWriteLock();
    private final Lock readLock = lock.readLock();
    private final Lock writeLock = lock.writeLock();
    
    private final Map<Double, Queue<Order>> buyOrders = new TreeMap<>(Collections.reverseOrder());
    private final Map<Double, Queue<Order>> sellOrders = new TreeMap<>();
    
    // Semaphore to limit concurrent modifications
    private final Semaphore modificationPermits = new Semaphore(5);
    
    // CountDownLatch for initialization
    private final CountDownLatch initialized = new CountDownLatch(1);
    
    public void addOrder(Order order) throws InterruptedException {
        modificationPermits.acquire();
        try {
            writeLock.lock();
            try {
                Map<Double, Queue<Order>> book = order.isBuy() ? buyOrders : sellOrders;
                book.computeIfAbsent(order.getPrice(), k -> new LinkedList<>()).add(order);
            } finally {
                writeLock.unlock();
            }
        } finally {
            modificationPermits.release();
        }
    }
    
    public List<Order> getOrdersAtPrice(double price, boolean isBuy) {
        readLock.lock();
        try {
            Map<Double, Queue<Order>> book = isBuy ? buyOrders : sellOrders;
            Queue<Order> orders = book.get(price);
            return orders != null ? new ArrayList<>(orders) : Collections.emptyList();
        } finally {
            readLock.unlock();
        }
    }
    
    // StampedLock for optimistic reading
    private final StampedLock stampedLock = new StampedLock();
    
    public double getBestBid() {
        long stamp = stampedLock.tryOptimisticRead();
        Double bestBid = buyOrders.isEmpty() ? null : buyOrders.keySet().iterator().next();
        
        if (!stampedLock.validate(stamp)) {
            stamp = stampedLock.readLock();
            try {
                bestBid = buyOrders.isEmpty() ? null : buyOrders.keySet().iterator().next();
            } finally {
                stampedLock.unlockRead(stamp);
            }
        }
        return bestBid != null ? bestBid : 0.0;
    }
}`,
    atomic: `// Lock-Free Trading Counter System
public class LockFreeTradingMetrics {
    // Atomic variables for lock-free operations
    private final AtomicLong totalTrades = new AtomicLong();
    private final AtomicLong totalVolume = new AtomicLong();
    private final AtomicReference<BigDecimal> lastPrice = new AtomicReference<>(BigDecimal.ZERO);
    
    // LongAdder for high-contention counters
    private final LongAdder executedOrders = new LongAdder();
    private final LongAdder rejectedOrders = new LongAdder();
    
    // Atomic field updaters for better performance
    private static final AtomicLongFieldUpdater<LockFreeTradingMetrics> PROFIT_UPDATER =
        AtomicLongFieldUpdater.newUpdater(LockFreeTradingMetrics.class, "profitCents");
    
    private volatile long profitCents = 0;
    
    public void recordTrade(Trade trade) {
        // Atomic increment
        totalTrades.incrementAndGet();
        
        // Atomic add
        totalVolume.addAndGet(trade.getVolume());
        
        // Compare and swap for last price
        BigDecimal newPrice = trade.getPrice();
        BigDecimal currentPrice;
        do {
            currentPrice = lastPrice.get();
        } while (!lastPrice.compareAndSet(currentPrice, newPrice));
        
        // LongAdder for high contention
        executedOrders.increment();
        
        // Field updater for profit
        long profitInCents = (long)(trade.getProfit() * 100);
        PROFIT_UPDATER.addAndGet(this, profitInCents);
    }
    
    // Atomic array for per-symbol metrics
    private final AtomicReferenceArray<SymbolMetrics> symbolMetrics = 
        new AtomicReferenceArray<>(1000);
    
    public void updateSymbolMetrics(int symbolId, SymbolMetrics newMetrics) {
        SymbolMetrics current;
        do {
            current = symbolMetrics.get(symbolId);
        } while (!symbolMetrics.compareAndSet(symbolId, current, newMetrics));
    }
}`,
    'concurrent-collections': `// Concurrent Collections for Trading System
public class ConcurrentTradingEngine {
    // ConcurrentHashMap for symbol data
    private final ConcurrentHashMap<String, MarketData> marketData = new ConcurrentHashMap<>();
    
    // ConcurrentLinkedQueue for order processing
    private final ConcurrentLinkedQueue<Order> orderQueue = new ConcurrentLinkedQueue<>();
    
    // ConcurrentSkipListMap for sorted price levels
    private final ConcurrentSkipListMap<Double, AtomicInteger> priceLevels = 
        new ConcurrentSkipListMap<>();
    
    // BlockingQueue for producer-consumer pattern
    private final BlockingQueue<Trade> tradeQueue = new LinkedBlockingQueue<>(10000);
    
    // TransferQueue for direct handoff
    private final TransferQueue<Order> priorityOrders = new LinkedTransferQueue<>();
    
    public void processMarketData(String symbol, double price, long volume) {
        // Atomic update using compute
        marketData.compute(symbol, (k, existing) -> {
            if (existing == null) {
                return new MarketData(symbol, price, volume);
            }
            existing.update(price, volume);
            return existing;
        });
        
        // Update price levels atomically
        priceLevels.compute(price, (k, count) -> {
            if (count == null) {
                return new AtomicInteger(1);
            }
            count.incrementAndGet();
            return count;
        });
    }
    
    // Producer
    public void submitOrder(Order order) {
        if (order.isPriority()) {
            // Direct handoff to waiting consumer
            try {
                priorityOrders.transfer(order);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        } else {
            orderQueue.offer(order);
        }
    }
    
    // Consumer
    public void consumeOrders() throws InterruptedException {
        // Try priority orders first
        Order priorityOrder = priorityOrders.poll(10, TimeUnit.MILLISECONDS);
        if (priorityOrder != null) {
            processPriorityOrder(priorityOrder);
            return;
        }
        
        // Process regular orders
        Order order = orderQueue.poll();
        if (order != null) {
            Trade trade = executeOrder(order);
            tradeQueue.put(trade); // Blocks if queue is full
        }
    }
}`,
    async: `// Async Order Processing with CompletableFuture
public class AsyncOrderService {
    private final ExecutorService executor = ForkJoinPool.commonPool();
    
    public CompletableFuture<OrderResult> submitOrderAsync(Order order) {
        return CompletableFuture
            .supplyAsync(() -> validateOrder(order), executor)
            .thenCompose(validOrder -> checkRiskAsync(validOrder))
            .thenCompose(checkedOrder -> executeOrderAsync(checkedOrder))
            .thenApply(execution -> new OrderResult(execution))
            .exceptionally(throwable -> {
                log.error("Order processing failed", throwable);
                return new OrderResult(Status.FAILED, throwable.getMessage());
            });
    }
    
    public CompletableFuture<List<Trade>> executeBatchOrders(List<Order> orders) {
        List<CompletableFuture<Trade>> futures = orders.stream()
            .map(this::executeOrderAsync)
            .map(future -> future.thenApply(this::createTrade))
            .collect(Collectors.toList());
        
        // Wait for all orders to complete
        return CompletableFuture.allOf(
            futures.toArray(new CompletableFuture[0])
        ).thenApply(v -> 
            futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList())
        );
    }
    
    // Combine multiple async operations
    public CompletableFuture<TradingDecision> makeTradingDecision(String symbol) {
        CompletableFuture<MarketData> marketDataFuture = fetchMarketDataAsync(symbol);
        CompletableFuture<RiskMetrics> riskFuture = calculateRiskAsync(symbol);
        CompletableFuture<Position> positionFuture = getPositionAsync(symbol);
        
        return CompletableFuture.allOf(marketDataFuture, riskFuture, positionFuture)
            .thenApply(v -> {
                MarketData marketData = marketDataFuture.join();
                RiskMetrics risk = riskFuture.join();
                Position position = positionFuture.join();
                
                return analyzeTradingOpportunity(marketData, risk, position);
            });
    }
    
    // Timeout handling
    public CompletableFuture<OrderResult> submitOrderWithTimeout(Order order) {
        return submitOrderAsync(order)
            .orTimeout(5, TimeUnit.SECONDS)
            .whenComplete((result, throwable) -> {
                if (throwable instanceof TimeoutException) {
                    log.warn("Order timeout: {}", order.getId());
                    cancelOrder(order);
                }
            });
    }
}`,
    'fork-join': `// Fork/Join Framework for Risk Calculations
import java.util.concurrent.*;

public class RiskCalculationTask extends RecursiveTask<Double> {
    private static final int THRESHOLD = 1000;
    private final Position[] positions;
    private final int start;
    private final int end;
    
    public RiskCalculationTask(Position[] positions, int start, int end) {
        this.positions = positions;
        this.start = start;
        this.end = end;
    }
    
    @Override
    protected Double compute() {
        if (end - start <= THRESHOLD) {
            // Direct computation for small datasets
            return calculateRiskSequentially();
        } else {
            // Fork into smaller tasks
            int mid = (start + end) / 2;
            RiskCalculationTask leftTask = new RiskCalculationTask(positions, start, mid);
            RiskCalculationTask rightTask = new RiskCalculationTask(positions, mid, end);
            
            // Fork left task and compute right task in current thread
            leftTask.fork();
            Double rightResult = rightTask.compute();
            Double leftResult = leftTask.join();
            
            return leftResult + rightResult;
        }
    }
    
    private Double calculateRiskSequentially() {
        double totalRisk = 0.0;
        for (int i = start; i < end; i++) {
            totalRisk += calculatePositionRisk(positions[i]);
        }
        return totalRisk;
    }
}

// Usage with work-stealing pool
ForkJoinPool forkJoinPool = new ForkJoinPool();
RiskCalculationTask task = new RiskCalculationTask(portfolio, 0, portfolio.length);
Double totalRisk = forkJoinPool.invoke(task);`,
    'coordination': `// Coordination Primitives for Market Events
import java.util.concurrent.*;

public class MarketCoordinator {
    private final CountDownLatch marketOpenLatch = new CountDownLatch(3);
    private final CyclicBarrier batchProcessingBarrier = new CyclicBarrier(4);
    private final Semaphore connectionSemaphore = new Semaphore(100);
    private final Phaser tradingSessionPhaser = new Phaser(1);
    
    // Market open coordination
    public void initializeMarketSystems() {
        // Risk engine initialization
        CompletableFuture.runAsync(() -> {
            initializeRiskEngine();
            marketOpenLatch.countDown();
        });
        
        // Market data feed initialization  
        CompletableFuture.runAsync(() -> {
            initializeMarketData();
            marketOpenLatch.countDown();
        });
        
        // Order routing initialization
        CompletableFuture.runAsync(() -> {
            initializeOrderRouting();
            marketOpenLatch.countDown();
        });
        
        // Wait for all systems to be ready
        try {
            marketOpenLatch.await(30, TimeUnit.SECONDS);
            log.info("Market systems ready - opening market");
        } catch (InterruptedException e) {
            log.error("Market initialization timeout");
        }
    }
    
    // Batch processing coordination
    public void processBatch() {
        try {
            // Each worker thread waits at barrier
            batchProcessingBarrier.await();
            // All threads proceed together for next batch
        } catch (InterruptedException | BrokenBarrierException e) {
            log.error("Batch processing coordination failed", e);
        }
    }
    
    // Connection throttling
    public void processOrder(Order order) {
        try {
            connectionSemaphore.acquire();
            try {
                // Process order with limited connections
                executeOrder(order);
            } finally {
                connectionSemaphore.release();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    // Trading session phases
    public void registerTradingPhase() {
        tradingSessionPhaser.register();
    }
    
    public void waitForPhaseCompletion() {
        tradingSessionPhaser.arriveAndAwaitAdvance();
    }
}`,
    'blocking-queues': `// Blocking Queues for Order Flow
import java.util.concurrent.*;

public class OrderProcessingSystem {
    // Different queue types for different use cases
    private final BlockingQueue<Order> incomingOrders = 
        new LinkedBlockingQueue<>(10000);  // Unbounded for incoming
        
    private final BlockingQueue<Order> priorityOrders = 
        new PriorityBlockingQueue<>(1000, 
            Comparator.comparing(Order::getPriority).reversed());
            
    private final BlockingQueue<Order> processingQueue = 
        new ArrayBlockingQueue<>(5000);  // Fixed capacity
        
    private final BlockingQueue<MarketData> marketDataQueue =
        new LinkedBlockingQueue<>();
    
    // Producer - Order ingestion
    public void submitOrder(Order order) {
        try {
            if (order.getPriority() > 5) {
                priorityOrders.put(order);  // Blocks if full
            } else {
                boolean added = incomingOrders.offer(order, 100, TimeUnit.MILLISECONDS);
                if (!added) {
                    log.warn("Order queue full, rejecting order: " + order.getId());
                    rejectOrder(order, "Queue capacity exceeded");
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    // Consumer - Order processing
    public void processOrders() {
        while (!Thread.currentThread().isInterrupted()) {
            try {
                // Process priority orders first
                Order order = priorityOrders.poll();
                if (order == null) {
                    // Then regular orders with timeout
                    order = incomingOrders.poll(50, TimeUnit.MILLISECONDS);
                }
                
                if (order != null) {
                    validateAndProcess(order);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
    
    // Market data producer-consumer
    public void publishMarketData(MarketData data) {
        marketDataQueue.offer(data); // Non-blocking
    }
    
    public void consumeMarketData() {
        try {
            MarketData data = marketDataQueue.take(); // Blocks until available
            updatePricing(data);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}`,
    'memory-model': `// Memory Model and Visibility
public class MarketDataCache {
    // Volatile ensures visibility across threads
    private volatile boolean marketOpen = false;
    private volatile long lastUpdateTime = 0;
    
    // Double-checked locking with volatile
    private volatile PriceData cachedData = null;
    private final Object lock = new Object();
    
    // Proper volatile usage for flags
    public void openMarket() {
        marketOpen = true;  // Visible to all threads immediately
        lastUpdateTime = System.currentTimeMillis();
    }
    
    public boolean isMarketOpen() {
        return marketOpen;  // Always reads the latest value
    }
    
    // Happens-before relationship with volatile
    public PriceData getLatestPrice(String symbol) {
        if (cachedData == null) {  // First check without locking
            synchronized (lock) {
                if (cachedData == null) {  // Double-checked locking
                    cachedData = loadPriceData(symbol);
                }
            }
        }
        return cachedData;
    }
    
    // Memory barriers and ordering
    public class OrderProcessor {
        private volatile boolean processingEnabled = false;
        private Order lastProcessedOrder;
        
        // Writer thread
        public void enableProcessing() {
            // All operations before this are visible after processingEnabled = true
            prepareOrderProcessing();
            processingEnabled = true;  // Memory barrier
        }
        
        // Reader thread
        public void processOrdersIfEnabled() {
            if (processingEnabled) {  // Memory barrier
                // All operations from writer thread are now visible
                processOrder(lastProcessedOrder);
            }
        }
    }
    
    // Avoiding common visibility issues
    public class CounterExample {
        private int counter = 0;  // NOT volatile - visibility issues
        private volatile int volatileCounter = 0;  // Proper visibility
        
        public void increment() {
            counter++;           // May not be visible to other threads
            volatileCounter++;   // Always visible to other threads
        }
    }
}`,
    'lock-types': `// Advanced Lock Types for Performance
import java.util.concurrent.locks.*;

public class OptimizedMarketDataCache {
    private final ReadWriteLock rwLock = new ReentrantReadWriteLock();
    private final Lock readLock = rwLock.readLock();
    private final Lock writeLock = rwLock.writeLock();
    
    // StampedLock for even better performance
    private final StampedLock stampedLock = new StampedLock();
    private final Map<String, PriceData> priceCache = new HashMap<>();
    
    // ReadWriteLock - Multiple readers, single writer
    public PriceData getPrice(String symbol) {
        readLock.lock();
        try {
            return priceCache.get(symbol);
        } finally {
            readLock.unlock();
        }
    }
    
    public void updatePrice(String symbol, PriceData price) {
        writeLock.lock();
        try {
            priceCache.put(symbol, price);
            notifySubscribers(symbol, price);
        } finally {
            writeLock.unlock();
        }
    }
    
    // StampedLock - Optimistic reading
    public PriceData getOptimisticPrice(String symbol) {
        long stamp = stampedLock.tryOptimisticRead();
        PriceData price = priceCache.get(symbol);
        
        if (!stampedLock.validate(stamp)) {
            // Fall back to pessimistic read lock
            stamp = stampedLock.readLock();
            try {
                price = priceCache.get(symbol);
            } finally {
                stampedLock.unlockRead(stamp);
            }
        }
        return price;
    }
    
    public void updateOptimisticPrice(String symbol, PriceData price) {
        long stamp = stampedLock.writeLock();
        try {
            priceCache.put(symbol, price);
        } finally {
            stampedLock.unlockWrite(stamp);
        }
    }
    
    // Lock upgrading with StampedLock
    public PriceData getOrCompute(String symbol) {
        long stamp = stampedLock.readLock();
        try {
            PriceData price = priceCache.get(symbol);
            if (price == null) {
                // Upgrade to write lock
                long writeStamp = stampedLock.tryConvertToWriteLock(stamp);
                if (writeStamp != 0L) {
                    stamp = writeStamp;
                    price = computePrice(symbol);
                    priceCache.put(symbol, price);
                } else {
                    // Upgrade failed, release read and acquire write
                    stampedLock.unlockRead(stamp);
                    stamp = stampedLock.writeLock();
                    price = priceCache.get(symbol);
                    if (price == null) {
                        price = computePrice(symbol);
                        priceCache.put(symbol, price);
                    }
                }
            }
            return price;
        } finally {
            stampedLock.unlock(stamp);
        }
    }
}`,
    'thread-pool-tuning': `// Thread Pool Tuning and Monitoring
import java.util.concurrent.*;

public class TunedThreadPoolManager {
    private final ThreadPoolExecutor orderProcessor;
    private final ScheduledExecutorService monitor;
    
    public TunedThreadPoolManager() {
        // Custom thread pool with tuned parameters
        this.orderProcessor = new ThreadPoolExecutor(
            4,                           // corePoolSize - minimum threads
            16,                          // maximumPoolSize - max threads  
            60L, TimeUnit.SECONDS,       // keepAliveTime - idle thread timeout
            new ArrayBlockingQueue<>(1000),  // workQueue - bounded queue
            new ThreadFactory() {
                private final AtomicInteger threadNumber = new AtomicInteger(1);
                public Thread newThread(Runnable r) {
                    Thread t = new Thread(r, "OrderProcessor-" + 
                                         threadNumber.getAndIncrement());
                    t.setDaemon(false);
                    t.setPriority(Thread.NORM_PRIORITY + 1);
                    return t;
                }
            },
            new ThreadPoolExecutor.CallerRunsPolicy()  // Rejection policy
        );
        
        // Allow core threads to timeout
        orderProcessor.allowCoreThreadTimeOut(true);
        
        // Monitoring
        this.monitor = Executors.newScheduledThreadPool(1);
        monitor.scheduleAtFixedRate(this::logThreadPoolStats, 
                                  0, 30, TimeUnit.SECONDS);
    }
    
    // Different rejection policies
    public ThreadPoolExecutor createProcessorWithPolicy(String type) {
        RejectedExecutionHandler handler;
        switch (type) {
            case "abort":
                handler = new ThreadPoolExecutor.AbortPolicy();  // Throw exception
                break;
            case "discard":
                handler = new ThreadPoolExecutor.DiscardPolicy();  // Silently discard
                break;
            case "discard-oldest":
                handler = new ThreadPoolExecutor.DiscardOldestPolicy();  // Drop oldest
                break;
            case "caller-runs":
            default:
                handler = new ThreadPoolExecutor.CallerRunsPolicy();  // Run in caller thread
                break;
        }
        
        return new ThreadPoolExecutor(2, 8, 60L, TimeUnit.SECONDS,
                                    new LinkedBlockingQueue<>(500), handler);
    }
    
    // Performance monitoring
    private void logThreadPoolStats() {
        log.info("Thread Pool Stats: " +
                "Active: {} / {} | " +
                "Pool Size: {} | " +
                "Queue Size: {} | " +
                "Completed Tasks: {} | " +
                "Total Tasks: {}",
                orderProcessor.getActiveCount(),
                orderProcessor.getMaximumPoolSize(),
                orderProcessor.getPoolSize(),
                orderProcessor.getQueue().size(),
                orderProcessor.getCompletedTaskCount(),
                orderProcessor.getTaskCount());
                
        // Alert if queue is getting full
        if (orderProcessor.getQueue().size() > 800) {
            log.warn("Thread pool queue approaching capacity!");
        }
    }
    
    // Dynamic resizing based on load
    public void adjustPoolSize(double cpuUsage, int queueSize) {
        if (cpuUsage > 80 && queueSize > 100) {
            int newSize = Math.min(orderProcessor.getMaximumPoolSize(), 
                                 orderProcessor.getCorePoolSize() + 2);
            orderProcessor.setCorePoolSize(newSize);
        } else if (cpuUsage < 30 && queueSize < 10) {
            int newSize = Math.max(2, orderProcessor.getCorePoolSize() - 1);
            orderProcessor.setCorePoolSize(newSize);
        }
    }
}`,
    'deadlock-prevention': `// Deadlock Prevention and Detection
import java.util.concurrent.locks.*;
import java.lang.management.*;

public class DeadlockPrevention {
    private static final int NUM_ACCOUNTS = 1000;
    private static final Object[] accountLocks = new Object[NUM_ACCOUNTS];
    private static final long[] balances = new long[NUM_ACCOUNTS];
    
    static {
        for (int i = 0; i < NUM_ACCOUNTS; i++) {
            accountLocks[i] = new Object();
            balances[i] = 1000000; // Initial balance
        }
    }
    
    // WRONG: Can cause deadlock
    public void transferDeadlockProne(int from, int to, long amount) {
        synchronized (accountLocks[from]) {
            synchronized (accountLocks[to]) {
                if (balances[from] >= amount) {
                    balances[from] -= amount;
                    balances[to] += amount;
                }
            }
        }
    }
    
    // CORRECT: Ordered locking prevents deadlock
    public void transferSafe(int from, int to, long amount) {
        int firstLock = Math.min(from, to);
        int secondLock = Math.max(from, to);
        
        synchronized (accountLocks[firstLock]) {
            synchronized (accountLocks[secondLock]) {
                if (balances[from] >= amount) {
                    balances[from] -= amount;
                    balances[to] += amount;
                    log.debug("Transferred {} from {} to {}", amount, from, to);
                }
            }
        }
    }
    
    // Timeout-based locking
    private final ReentrantLock lock1 = new ReentrantLock();
    private final ReentrantLock lock2 = new ReentrantLock();
    
    public boolean transferWithTimeout(int from, int to, long amount) {
        boolean acquired1 = false, acquired2 = false;
        try {
            acquired1 = lock1.tryLock(100, TimeUnit.MILLISECONDS);
            if (acquired1) {
                acquired2 = lock2.tryLock(100, TimeUnit.MILLISECONDS);
                if (acquired2) {
                    // Perform transfer
                    return doTransfer(from, to, amount);
                }
            }
            return false;  // Could not acquire both locks
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        } finally {
            if (acquired2) lock2.unlock();
            if (acquired1) lock1.unlock();
        }
    }
    
    // Deadlock detection
    public class DeadlockMonitor {
        private final ThreadMXBean threadBean;
        private final ScheduledExecutorService scheduler;
        
        public DeadlockMonitor() {
            this.threadBean = ManagementFactory.getThreadMXBean();
            this.scheduler = Executors.newScheduledThreadPool(1);
            
            // Check for deadlocks every 5 seconds
            scheduler.scheduleAtFixedRate(this::checkForDeadlock, 
                                        0, 5, TimeUnit.SECONDS);
        }
        
        private void checkForDeadlock() {
            long[] deadlockedThreads = threadBean.findDeadlockedThreads();
            if (deadlockedThreads != null) {
                ThreadInfo[] threadInfos = threadBean.getThreadInfo(deadlockedThreads);
                
                log.error("DEADLOCK DETECTED!");
                for (ThreadInfo threadInfo : threadInfos) {
                    log.error("Thread {} is blocked on {}, owned by {}",
                            threadInfo.getThreadName(),
                            threadInfo.getLockName(),
                            threadInfo.getLockOwnerName());
                }
                
                // Take corrective action
                handleDeadlock(threadInfos);
            }
        }
        
        private void handleDeadlock(ThreadInfo[] deadlockedThreads) {
            // Log detailed thread dump
            // Alert monitoring systems
            // Potentially restart affected components
        }
    }
}`,
    'flow-control': `// Flow Control and Backpressure
import java.util.concurrent.*;
import java.util.concurrent.atomic.*;

public class FlowControlSystem {
    private final Semaphore rateLimiter;
    private final AtomicLong requestCount = new AtomicLong();
    private final AtomicLong droppedCount = new AtomicLong();
    
    // Rate limiting
    public FlowControlSystem(int maxRequestsPerSecond) {
        this.rateLimiter = new Semaphore(maxRequestsPerSecond);
        
        // Replenish permits every second
        ScheduledExecutorService replenisher = Executors.newScheduledThreadPool(1);
        replenisher.scheduleAtFixedRate(() -> {
            int currentPermits = rateLimiter.availablePermits();
            int permitsToAdd = maxRequestsPerSecond - currentPermits;
            if (permitsToAdd > 0) {
                rateLimiter.release(permitsToAdd);
            }
        }, 1, 1, TimeUnit.SECONDS);
    }
    
    // Backpressure handling for market data
    public class MarketDataProcessor {
        private final BlockingQueue<MarketData> buffer = 
            new ArrayBlockingQueue<>(1000);
        private final AtomicInteger processingRate = new AtomicInteger(100);
        
        public void onMarketData(MarketData data) {
            requestCount.incrementAndGet();
            
            // Try to acquire rate limit permit
            if (!rateLimiter.tryAcquire()) {
                droppedCount.incrementAndGet();
                log.warn("Rate limit exceeded, dropping market data for {}", 
                        data.getSymbol());
                return;
            }
            
            // Apply backpressure
            if (!buffer.offer(data)) {
                // Buffer full - implement backpressure strategy
                handleBackpressure(data);
            }
        }
        
        private void handleBackpressure(MarketData data) {
            // Strategy 1: Drop oldest data
            MarketData dropped = buffer.poll();
            if (dropped != null) {
                log.debug("Dropped old market data for backpressure: {}", 
                         dropped.getSymbol());
            }
            buffer.offer(data);
            
            // Strategy 2: Reduce processing rate
            int currentRate = processingRate.get();
            processingRate.compareAndSet(currentRate, Math.max(50, currentRate - 10));
        }
        
        // Adaptive processing
        public void processMarketData() {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    MarketData data = buffer.poll(
                        1000 / processingRate.get(), TimeUnit.MILLISECONDS);
                    
                    if (data != null) {
                        updatePricing(data);
                        
                        // Adjust processing rate based on queue size
                        adaptProcessingRate();
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        private void adaptProcessingRate() {
            int queueSize = buffer.size();
            int currentRate = processingRate.get();
            
            if (queueSize > 800) {
                // Queue getting full, increase processing rate
                processingRate.compareAndSet(currentRate, 
                                           Math.min(500, currentRate + 20));
            } else if (queueSize < 100 && currentRate > 100) {
                // Queue low, can reduce processing rate to save CPU
                processingRate.compareAndSet(currentRate, currentRate - 5);
            }
        }
    }
    
    // Circuit breaker pattern
    public class CircuitBreaker {
        private enum State { CLOSED, OPEN, HALF_OPEN }
        
        private volatile State state = State.CLOSED;
        private final AtomicInteger failureCount = new AtomicInteger();
        private final AtomicLong lastFailureTime = new AtomicLong();
        private final int threshold;
        private final long timeout;
        
        public CircuitBreaker(int failureThreshold, long timeoutMs) {
            this.threshold = failureThreshold;
            this.timeout = timeoutMs;
        }
        
        public <T> CompletableFuture<T> execute(Supplier<T> operation) {
            if (state == State.OPEN) {
                if (System.currentTimeMillis() - lastFailureTime.get() > timeout) {
                    state = State.HALF_OPEN;
                } else {
                    return CompletableFuture.failedFuture(
                        new RuntimeException("Circuit breaker is OPEN"));
                }
            }
            
            return CompletableFuture.supplyAsync(() -> {
                try {
                    T result = operation.get();
                    onSuccess();
                    return result;
                } catch (Exception e) {
                    onFailure();
                    throw new RuntimeException(e);
                }
            });
        }
        
        private void onSuccess() {
            failureCount.set(0);
            state = State.CLOSED;
        }
        
        private void onFailure() {
            int failures = failureCount.incrementAndGet();
            lastFailureTime.set(System.currentTimeMillis());
            
            if (failures >= threshold) {
                state = State.OPEN;
                log.warn("Circuit breaker opened after {} failures", failures);
            }
        }
    }
}`
  };

  return (
    <div style={{
      padding: '40px',
      paddingTop: '100px',
      minHeight: '100vh',
      backgroundColor: '#0f172a',
      color: 'white'
    }}>
      <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
        <div style={{ marginBottom: '40px' }}>
          <h1 style={{ fontSize: '36px', fontWeight: 'bold', marginBottom: '16px', color: '#10b981' }}>
            Java Concurrency
          </h1>
          <p style={{ fontSize: '18px', color: '#94a3b8' }}>
            Multi-threading, synchronization, and async programming for trading systems
          </p>
        </div>

        <div style={{ display: 'flex', gap: '20px', marginBottom: '40px' }}>
          {['overview', 'definition', 'code', 'patterns', 'best-practices'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              style={{
                padding: '12px 24px',
                backgroundColor: activeTab === tab ? '#10b981' : 'transparent',
                border: `2px solid ${activeTab === tab ? '#10b981' : '#374151'}`,
                borderRadius: '8px',
                color: activeTab === tab ? 'white' : '#9ca3af',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                textTransform: 'capitalize',
                transition: 'all 0.2s'
              }}
            >
              {tab.replace('-', ' ')}
            </button>
          ))}
        </div>

        {activeTab === 'definition' && (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <div style={{ marginBottom: '20px' }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px' }}>
                {selectedTopic === 'threads' ? 'Threads & Executors' :
                 selectedTopic === 'synchronization' ? 'Synchronization' :
                 selectedTopic === 'atomic' ? 'Atomic Operations' :
                 selectedTopic === 'concurrent-collections' ? 'Concurrent Collections' :
                 'Async Programming'} Definition
              </h3>
            </div>
            
            {selectedTopic === 'threads' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What are Threads & Executors?</h4>
                <p style={{ marginBottom: '16px' }}>
                  Threads allow concurrent execution of code, while Executors provide a higher-level framework for managing thread pools and asynchronous task execution. They enable parallel processing and better resource utilization.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why are they critical for Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>Handle multiple orders simultaneously without blocking</li>
                  <li>Process market data feeds in parallel</li>
                  <li>Enable real-time risk calculations while executing trades</li>
                  <li>Scale system performance with available CPU cores</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to use them effectively?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Use thread pools instead of creating raw threads</li>
                  <li>Size thread pools based on workload characteristics</li>
                  <li>Use appropriate executor types (fixed, cached, scheduled)</li>
                  <li>Always shutdown executors to prevent resource leaks</li>
                </ul>
              </div>
            )}
            
            {selectedTopic === 'synchronization' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What is Synchronization?</h4>
                <p style={{ marginBottom: '16px' }}>
                  Synchronization controls access to shared resources in multi-threaded environments. It prevents race conditions and ensures data consistency using locks, monitors, and coordination primitives.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why is it essential for Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>Protect order book integrity during concurrent updates</li>
                  <li>Ensure position calculations are thread-safe</li>
                  <li>Coordinate access to shared market data</li>
                  <li>Prevent data corruption in high-frequency scenarios</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to implement it properly?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Use appropriate lock types (ReentrantLock, ReadWriteLock)</li>
                  <li>Minimize critical sections to reduce contention</li>
                  <li>Consider lock-free alternatives when possible</li>
                  <li>Use synchronization utilities (CountDownLatch, Semaphore)</li>
                </ul>
              </div>
            )}
          </div>
        )}

        {activeTab === 'overview' && (
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>
            {console.log('Rendering topics:', topics.length, 'topics')}
            {topics.map((topic) => (
              <div
                key={topic.id}
                onClick={() => setSelectedTopic(topic.id)}
                style={{
                  padding: '24px',
                  backgroundColor: selectedTopic === topic.id ? 'rgba(16, 185, 129, 0.1)' : 'rgba(31, 41, 55, 0.5)',
                  border: `2px solid ${selectedTopic === topic.id ? '#10b981' : 'transparent'}`,
                  borderRadius: '12px',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '12px' }}>
                  <div style={{ color: topic.color }}>{topic.icon}</div>
                  <h3 style={{ fontSize: '20px', fontWeight: '600' }}>{topic.name}</h3>
                </div>
                <p style={{ color: '#94a3b8', fontSize: '14px' }}>{topic.description}</p>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'code' && (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <div style={{ marginBottom: '20px' }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px', textTransform: 'capitalize' }}>
                {selectedTopic ? selectedTopic.replace('-', ' ') : 'Concurrency'} Example
              </h3>
            </div>
            <SyntaxHighlighter
              language="java"
              style={oneDark}
              customStyle={{
                backgroundColor: '#1e293b',
                padding: '20px',
                borderRadius: '8px',
                fontSize: '14px',
                lineHeight: '1.6'
              }}
            >
              {selectedTopic ? codeExamples[selectedTopic] : '// Select a topic to view code examples'}
            </SyntaxHighlighter>
          </div>
        )}

        {activeTab === 'patterns' && (
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
            <div style={{
              padding: '24px',
              backgroundColor: 'rgba(31, 41, 55, 0.5)',
              borderRadius: '12px',
              border: '1px solid #374151'
            }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px', color: '#10b981' }}>
                Concurrency Patterns
              </h3>
              <ul style={{ listStyle: 'none', padding: 0 }}>
                {[
                  'Producer-Consumer with BlockingQueue',
                  'Fork-Join for parallel processing',
                  'Actor Model with message passing',
                  'Read-Write Lock for data access',
                  'Double-Checked Locking for singletons'
                ].map((pattern, index) => (
                  <li key={index} style={{ padding: '8px 0', color: '#94a3b8', fontSize: '14px' }}>
                    <span style={{ color: '#10b981', marginRight: '8px' }}>•</span>
                    {pattern}
                  </li>
                ))}
              </ul>
            </div>
            <div style={{
              padding: '24px',
              backgroundColor: 'rgba(31, 41, 55, 0.5)',
              borderRadius: '12px',
              border: '1px solid #374151'
            }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px', color: '#ef4444' }}>
                Common Pitfalls
              </h3>
              <ul style={{ listStyle: 'none', padding: 0 }}>
                {[
                  'Deadlocks from circular dependencies',
                  'Race conditions in shared state',
                  'Memory visibility issues',
                  'Thread pool exhaustion',
                  'Excessive synchronization overhead'
                ].map((pitfall, index) => (
                  <li key={index} style={{ padding: '8px 0', color: '#94a3b8', fontSize: '14px' }}>
                    <span style={{ color: '#ef4444', marginRight: '8px' }}>•</span>
                    {pitfall}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {activeTab === 'best-practices' && (
          <div style={{
            padding: '24px',
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            border: '1px solid #374151'
          }}>
            <h3 style={{ fontSize: '24px', fontWeight: '600', marginBottom: '20px', color: '#10b981' }}>
              Concurrency Best Practices
            </h3>
            <div style={{ display: 'grid', gap: '16px' }}>
              {[
                {
                  title: 'Minimize Shared State',
                  description: 'Use immutable objects and message passing to reduce synchronization needs'
                },
                {
                  title: 'Prefer High-Level Constructs',
                  description: 'Use ExecutorService, CompletableFuture, and concurrent collections over low-level threads'
                },
                {
                  title: 'Lock-Free When Possible',
                  description: 'Use atomic variables and CAS operations for better performance'
                },
                {
                  title: 'Proper Resource Management',
                  description: 'Always shutdown executors and release locks in finally blocks'
                },
                {
                  title: 'Monitor and Profile',
                  description: 'Use JMX, thread dumps, and profilers to identify bottlenecks'
                }
              ].map((practice, index) => (
                <div key={index} style={{
                  padding: '16px',
                  backgroundColor: '#1e293b',
                  borderRadius: '8px',
                  borderLeft: '4px solid #10b981'
                }}>
                  <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '8px' }}>
                    {practice.title}
                  </h4>
                  <p style={{ color: '#94a3b8', fontSize: '14px' }}>
                    {practice.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Details Panel - Now at the bottom */}
        {selectedTopic && (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            border: '1px solid #374151',
            padding: '24px',
            marginTop: '32px'
          }}>
            <div style={{ marginBottom: '20px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
              <h2 style={{ fontSize: '24px', fontWeight: 'bold', color: '#10b981' }}>
                {topics.find(t => t.id === selectedTopic)?.name}
              </h2>
              <button
                onClick={() => setSelectedTopic(null)}
                style={{
                  background: 'none',
                  border: 'none',
                  color: '#9ca3af',
                  fontSize: '24px',
                  cursor: 'pointer'
                }}
              >
                ×
              </button>
            </div>
            
            {/* Concurrency Specific Tabs */}
            <div style={{ display: 'flex', borderBottom: '1px solid #374151' }}>
              {['overview', 'patterns', 'implementations', 'performance', 'troubleshooting'].map(tab => (
                <button
                  key={tab}
                  onClick={() => setDetailsTab(tab)}
                  style={{
                    padding: '8px 16px',
                    backgroundColor: detailsTab === tab ? '#10b981' : 'transparent',
                    color: detailsTab === tab ? 'white' : '#9ca3af',
                    border: 'none',
                    borderBottom: detailsTab === tab ? '2px solid #10b981' : '2px solid transparent',
                    cursor: 'pointer',
                    fontSize: '14px',
                    textTransform: 'capitalize',
                    transition: 'all 0.2s'
                  }}
                >
                  {tab}
                </button>
              ))}
            </div>
          </div>
          
          {detailsTab === 'overview' && (
            <div>
              <div style={{ marginBottom: '24px' }}>
                <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '12px', color: '#10b981' }}>
                  Concurrency in Trading Systems
                </h3>
                <p style={{ color: '#e2e8f0', lineHeight: '1.6' }}>
                  {selectedTopic === 'threads' ? (
                    "Thread management and executor services provide controlled parallel execution. ExecutorService implementations like ThreadPoolExecutor allow efficient task scheduling and resource management for concurrent operations."
                  ) : selectedTopic === 'fork-join' ? (
                    "Fork/Join framework implements work-stealing parallelism for recursive problems. It efficiently divides large tasks into smaller subtasks, processes them in parallel, and combines results using a work-stealing queue algorithm."
                  ) : selectedTopic === 'synchronization' ? (
                    "Synchronization mechanisms coordinate access to shared resources. Locks, monitors, and synchronized blocks prevent race conditions and ensure thread-safe operations in concurrent environments."
                  ) : selectedTopic === 'coordination' ? (
                    "Coordination primitives like CountDownLatch, Semaphore, CyclicBarrier, and Phaser provide thread coordination patterns. These utilities help synchronize multiple threads at specific execution points."
                  ) : selectedTopic === 'blocking-queues' ? (
                    "Blocking queues implement producer-consumer patterns with thread-safe operations. They provide blocking put/take operations and are fundamental building blocks for concurrent applications."
                  ) : selectedTopic === 'memory-model' ? (
                    "Java Memory Model defines how threads interact through memory. Understanding volatile variables, happens-before relationships, and memory visibility is crucial for writing correct concurrent code."
                  ) : selectedTopic === 'lock-types' ? (
                    "Advanced lock types like ReadWriteLock and StampedLock provide optimized locking strategies. They offer better performance for specific access patterns like read-heavy workloads."
                  ) : selectedTopic === 'thread-pool-tuning' ? (
                    "Thread pool tuning optimizes executor performance through proper configuration. Core pool size, maximum pool size, queue types, and rejection policies affect throughput and resource usage."
                  ) : selectedTopic === 'deadlock-prevention' ? (
                    "Deadlock prevention strategies include lock ordering, timeouts, and deadlock detection. Understanding common deadlock scenarios helps design robust concurrent systems."
                  ) : selectedTopic === 'flow-control' ? (
                    "Flow control mechanisms manage rate and backpressure in concurrent systems. Techniques like throttling, circuit breakers, and reactive streams prevent system overload."
                  ) : selectedTopic === 'atomic' ? (
                    "Atomic operations provide lock-free thread-safe updates using compare-and-swap (CAS). AtomicReference, AtomicInteger, and related classes offer better performance than synchronized methods."
                  ) : selectedTopic === 'concurrent-collections' ? (
                    "Concurrent collections provide thread-safe data structures optimized for concurrent access. ConcurrentHashMap, CopyOnWriteArrayList, and BlockingQueue implementations eliminate manual synchronization."
                  ) : (
                    "Concurrency utilities in java.util.concurrent provide high-level abstractions for parallel programming. These tools simplify complex coordination patterns and improve application performance."
                  )}
                </p>
              </div>
              
              <div style={{ marginBottom: '24px' }}>
                <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Key Benefits</h4>
                <ul style={{ color: '#e2e8f0', paddingLeft: '20px' }}>
                  {selectedTopic === 'threads' && [
                    'Controlled parallel execution with thread pools',
                    'Efficient task scheduling and resource management',
                    'Scalable architecture utilizing available cores',
                    'Separation of task submission from execution'
                  ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                  
                  {selectedTopic === 'fork-join' && [
                    'Automatic work distribution across threads',
                    'Work-stealing for optimal load balancing',
                    'Recursive problem decomposition',
                    'Efficient utilization of all CPU cores'
                  ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                  
                  {selectedTopic === 'synchronization' && [
                    'Data consistency across concurrent operations',
                    'Prevention of race conditions',
                    'Coordinated access to shared resources',
                    'Fine-grained locking strategies'
                  ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                  
                  {selectedTopic === 'coordination' && [
                    'Thread synchronization at specific points',
                    'Resource limiting with semaphores',
                    'Phased execution coordination',
                    'Reusable synchronization barriers'
                  ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                  
                  {selectedTopic === 'blocking-queues' && [
                    'Built-in producer-consumer coordination',
                    'Thread-safe data exchange',
                    'Automatic blocking on empty/full conditions',
                    'Various queue implementations for different needs'
                  ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                  
                  {selectedTopic === 'memory-model' && [
                    'Predictable memory visibility guarantees',
                    'Understanding of thread interactions',
                    'Proper use of volatile for lightweight synchronization',
                    'Prevention of subtle concurrency bugs'
                  ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                  
                  {selectedTopic === 'lock-types' && [
                    'Optimized locking for specific access patterns',
                    'Better performance for read-heavy workloads',
                    'Lock-free operations with StampedLock',
                    'Reduced contention through appropriate lock choice'
                  ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                  
                  {selectedTopic === 'thread-pool-tuning' && [
                    'Optimal resource utilization',
                    'Controlled memory usage',
                    'Predictable performance characteristics',
                    'Proper handling of task overload scenarios'
                  ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                  
                  {selectedTopic === 'deadlock-prevention' && [
                    'Systematic deadlock avoidance strategies',
                    'Early detection of potential deadlocks',
                    'Robust system design patterns',
                    'Recovery mechanisms for deadlock situations'
                  ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                  
                  {selectedTopic === 'flow-control' && [
                    'System overload protection',
                    'Graceful degradation under load',
                    'Rate limiting and throttling mechanisms',
                    'Backpressure handling in reactive systems'
                  ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                  
                  {selectedTopic === 'atomic' && [
                    'Lock-free performance improvements',
                    'Reduced thread contention and blocking',
                    'Better scalability in high-contention scenarios',
                    'Simple atomic updates using CAS operations'
                  ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                  
                  {selectedTopic === 'concurrent-collections' && [
                    'Built-in thread safety without manual locking',
                    'Optimized performance for concurrent access',
                    'Consistent iteration behavior under modification',
                    'Memory-efficient concurrent data structures'
                  ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                </ul>
              </div>
              
              <div>
                <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#ef4444' }}>Dark Pool Implementation</h4>
                <div style={{
                  backgroundColor: '#1e293b',
                  padding: '16px',
                  borderRadius: '8px',
                  border: '1px solid #374151'
                }}>
                  <SyntaxHighlighter
                    language="java"
                    style={oneDark}
                    customStyle={{
                      backgroundColor: 'transparent',
                      padding: 0,
                      margin: 0,
                      fontSize: '12px'
                    }}
                  >
                    {selectedTopic === 'threads' ? `// Thread Pool Executor Service
public class TaskExecutorService {
    private final ThreadPoolExecutor executor;
    
    public TaskExecutorService() {
        this.executor = new ThreadPoolExecutor(
            2, 4, 60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(100),
            new ThreadFactory() {
                private final AtomicInteger threadNumber = new AtomicInteger(1);
                public Thread newThread(Runnable r) {
                    return new Thread(r, "Worker-" + threadNumber.getAndIncrement());
                }
            }
        );
    }
    
    public <T> Future<T> submit(Callable<T> task) {
        return executor.submit(task);
    }
    
    public void shutdown() {
        executor.shutdown();
    }
}` : selectedTopic === 'fork-join' ? `// Fork/Join Example for Parallel Processing
public class ParallelDataProcessor extends RecursiveTask<Long> {
    private static final int THRESHOLD = 1000;
    private final int[] data;
    private final int start, end;
    
    public ParallelDataProcessor(int[] data, int start, int end) {
        this.data = data;
        this.start = start;
        this.end = end;
    }
    
    @Override
    protected Long compute() {
        if (end - start <= THRESHOLD) {
            // Process sequentially for small datasets
            long sum = 0;
            for (int i = start; i < end; i++) {
                sum += processItem(data[i]);
            }
            return sum;
        } else {
            // Divide and conquer
            int mid = (start + end) / 2;
            ParallelDataProcessor left = new ParallelDataProcessor(data, start, mid);
            ParallelDataProcessor right = new ParallelDataProcessor(data, mid, end);
            
            left.fork();
            Long rightResult = right.compute();
            Long leftResult = left.join();
            
            return leftResult + rightResult;
        }
    }
}` : selectedTopic === 'synchronization' ? `// Synchronization with ReadWriteLock
public class ConcurrentCache<K, V> {
    private final Map<K, V> cache = new HashMap<>();
    private final ReadWriteLock lock = new ReentrantReadWriteLock();
    private final Lock readLock = lock.readLock();
    private final Lock writeLock = lock.writeLock();
    
    public V get(K key) {
        readLock.lock();
        try {
            return cache.get(key);
        } finally {
            readLock.unlock();
        }
    }
    
    public V put(K key, V value) {
        writeLock.lock();
        try {
            return cache.put(key, value);
        } finally {
            writeLock.unlock();
        }
    }
    
    public void clear() {
        writeLock.lock();
        try {
            cache.clear();
        } finally {
            writeLock.unlock();
        }
    }
}` : selectedTopic === 'coordination' ? `// Coordination Primitives Example
public class BatchProcessor {
    private final CountDownLatch startLatch = new CountDownLatch(1);
    private final CountDownLatch doneLatch = new CountDownLatch(3);
    private final CyclicBarrier barrier = new CyclicBarrier(3);
    private final Semaphore permits = new Semaphore(2);
    
    public void processInPhases() {
        // Phase 1: Wait for all workers to be ready
        startLatch.countDown(); // Release all waiting workers
        
        try {
            // Phase 2: Coordinate at barrier
            barrier.await();
            
            // Phase 3: Limited concurrent processing
            permits.acquire();
            try {
                // Process with limited concurrency
                performWork();
            } finally {
                permits.release();
                doneLatch.countDown();
            }
            
            // Wait for all to complete
            doneLatch.await();
        } catch (InterruptedException | BrokenBarrierException e) {
            Thread.currentThread().interrupt();
        }
    }
}` : selectedTopic === 'blocking-queues' ? `// Producer-Consumer with BlockingQueue
public class OrderProcessingPipeline {
    private final BlockingQueue<Order> incomingOrders = 
        new LinkedBlockingQueue<>(1000);
    private final BlockingQueue<ProcessedOrder> processedOrders = 
        new ArrayBlockingQueue<>(500);
    
    // Producer
    public void submitOrder(Order order) throws InterruptedException {
        incomingOrders.put(order); // Blocks if queue is full
    }
    
    // Consumer/Producer
    public void processOrders() {
        while (!Thread.currentThread().isInterrupted()) {
            try {
                Order order = incomingOrders.take(); // Blocks if empty
                ProcessedOrder processed = validate(order);
                processedOrders.put(processed); // Blocks if full
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
    
    // Consumer
    public ProcessedOrder getProcessedOrder() throws InterruptedException {
        return processedOrders.take(); // Blocks if empty
    }
}` : selectedTopic === 'memory-model' ? `// Memory Model and Visibility
public class VisibilityExample {
    private volatile boolean flag = false;
    private int counter = 0;
    
    // Writer thread
    public void setFlag() {
        counter = 42;        // Happens-before volatile write
        flag = true;         // Volatile write
    }
    
    // Reader thread  
    public void checkFlag() {
        if (flag) {          // Volatile read
            // counter is guaranteed to be 42 due to happens-before
            assert counter == 42;
        }
    }
    
    // Properly synchronized singleton
    private static volatile VisibilityExample instance;
    
    public static VisibilityExample getInstance() {
        if (instance == null) {
            synchronized (VisibilityExample.class) {
                if (instance == null) {
                    instance = new VisibilityExample();
                }
            }
        }
        return instance;
    }
}` : selectedTopic === 'lock-types' ? `// Advanced Lock Types
public class OptimizedCache {
    private final Map<String, Object> cache = new HashMap<>();
    private final StampedLock sl = new StampedLock();
    
    // Optimistic read
    public Object get(String key) {
        long stamp = sl.tryOptimisticRead();
        Object value = cache.get(key);
        
        if (!sl.validate(stamp)) {
            // Fall back to read lock
            stamp = sl.readLock();
            try {
                value = cache.get(key);
            } finally {
                sl.unlockRead(stamp);
            }
        }
        return value;
    }
    
    // Write lock
    public Object put(String key, Object value) {
        long stamp = sl.writeLock();
        try {
            return cache.put(key, value);
        } finally {
            sl.unlockWrite(stamp);
        }
    }
    
    // Read lock
    public int size() {
        long stamp = sl.readLock();
        try {
            return cache.size();
        } finally {
            sl.unlockRead(stamp);
        }
    }
}` : selectedTopic === 'thread-pool-tuning' ? `// Thread Pool Tuning
public class TunedExecutorService {
    private final ThreadPoolExecutor executor;
    
    public TunedExecutorService() {
        int processors = Runtime.getRuntime().availableProcessors();
        
        this.executor = new ThreadPoolExecutor(
            processors,                    // corePoolSize
            processors * 2,               // maximumPoolSize
            60L,                          // keepAliveTime
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(200), // Bounded queue
            new ThreadFactory() {
                private final AtomicInteger counter = new AtomicInteger();
                public Thread newThread(Runnable r) {
                    Thread t = new Thread(r, "Worker-" + counter.incrementAndGet());
                    t.setDaemon(false);
                    t.setPriority(Thread.NORM_PRIORITY);
                    return t;
                }
            },
            new ThreadPoolExecutor.CallerRunsPolicy() // Rejection policy
        );
        
        // Allow core threads to timeout
        executor.allowCoreThreadTimeOut(true);
    }
    
    public void monitorHealth() {
        System.out.println("Active: " + executor.getActiveCount());
        System.out.println("Queue size: " + executor.getQueue().size());
        System.out.println("Completed: " + executor.getCompletedTaskCount());
    }
}` : selectedTopic === 'deadlock-prevention' ? `// Deadlock Prevention Strategies
public class BankAccount {
    private final Object lock = new Object();
    private int balance;
    private final int id;
    
    public BankAccount(int id, int initialBalance) {
        this.id = id;
        this.balance = initialBalance;
    }
    
    // Ordered locking to prevent deadlock
    public static void transfer(BankAccount from, BankAccount to, int amount) {
        BankAccount firstLock = from.id < to.id ? from : to;
        BankAccount secondLock = from.id < to.id ? to : from;
        
        synchronized (firstLock.lock) {
            synchronized (secondLock.lock) {
                if (from.balance >= amount) {
                    from.balance -= amount;
                    to.balance += amount;
                }
            }
        }
    }
    
    // Timeout-based locking
    public boolean tryTransfer(BankAccount to, int amount, long timeout) 
            throws InterruptedException {
        if (lock.equals(to.lock)) return false; // Self-transfer check
        
        boolean fromLocked = false, toLocked = false;
        try {
            fromLocked = tryLock(timeout / 2);
            if (fromLocked) {
                toLocked = to.tryLock(timeout / 2);
                if (toLocked && balance >= amount) {
                    balance -= amount;
                    to.balance += amount;
                    return true;
                }
            }
        } finally {
            if (toLocked) to.unlock();
            if (fromLocked) unlock();
        }
        return false;
    }
}` : selectedTopic === 'flow-control' ? `// Flow Control and Backpressure
public class RateLimitedProcessor {
    private final Semaphore rateLimiter;
    private final ScheduledExecutorService scheduler = 
        Executors.newScheduledThreadPool(1);
    
    public RateLimitedProcessor(int requestsPerSecond) {
        this.rateLimiter = new Semaphore(requestsPerSecond);
        
        // Replenish permits every second
        scheduler.scheduleAtFixedRate(() -> {
            rateLimiter.release(requestsPerSecond - rateLimiter.availablePermits());
        }, 1, 1, TimeUnit.SECONDS);
    }
    
    public CompletableFuture<String> processRequest(String request) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                if (rateLimiter.tryAcquire(100, TimeUnit.MILLISECONDS)) {
                    return performProcessing(request);
                } else {
                    throw new RuntimeException("Rate limit exceeded");
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Processing interrupted");
            }
        });
    }
    
    // Circuit breaker pattern
    private volatile boolean circuitOpen = false;
    private volatile int failureCount = 0;
    private final int failureThreshold = 5;
    
    public String processWithCircuitBreaker(String request) {
        if (circuitOpen) {
            throw new RuntimeException("Circuit breaker is open");
        }
        
        try {
            String result = performProcessing(request);
            failureCount = 0; // Reset on success
            return result;
        } catch (Exception e) {
            if (++failureCount >= failureThreshold) {
                circuitOpen = true;
                scheduler.schedule(() -> circuitOpen = false, 30, TimeUnit.SECONDS);
            }
            throw e;
        }
    }
}` : selectedTopic === 'atomic' ? `// Atomic Operations and Lock-Free Programming
public class AtomicCounter {
    private final AtomicInteger count = new AtomicInteger(0);
    private final AtomicReference<Node> head = new AtomicReference<>();
    
    // Lock-free increment
    public int incrementAndGet() {
        return count.incrementAndGet();
    }
    
    // Compare-and-swap operation
    public boolean conditionalIncrement(int expectedValue) {
        return count.compareAndSet(expectedValue, expectedValue + 1);
    }
    
    // Lock-free stack using AtomicReference
    private static class Node {
        final Object data;
        final Node next;
        Node(Object data, Node next) {
            this.data = data;
            this.next = next;
        }
    }
    
    public void push(Object item) {
        Node newNode = new Node(item, null);
        Node currentHead;
        do {
            currentHead = head.get();
            newNode.next = currentHead;
        } while (!head.compareAndSet(currentHead, newNode));
    }
    
    public Object pop() {
        Node currentHead;
        Node newHead;
        do {
            currentHead = head.get();
            if (currentHead == null) return null;
            newHead = currentHead.next;
        } while (!head.compareAndSet(currentHead, newHead));
        
        return currentHead.data;
    }
}` : `// Concurrent Collections Usage
public class ConcurrentDataProcessor {
    // Thread-safe map with high concurrency
    private final ConcurrentHashMap<String, ProcessingResult> cache = 
        new ConcurrentHashMap<>();
    
    // Copy-on-write for read-heavy scenarios
    private final CopyOnWriteArrayList<Observer> observers = 
        new CopyOnWriteArrayList<>();
    
    // Thread-safe sorted set
    private final ConcurrentSkipListSet<Task> priorityTasks = 
        new ConcurrentSkipListSet<>(Comparator.comparing(Task::getPriority));
    
    public void processData(String key, Object data) {
        // Atomic computation
        ProcessingResult result = cache.computeIfAbsent(key, k -> {
            return new ProcessingResult(process(data));
        });
        
        // Notify observers (read-heavy, infrequent writes)
        observers.forEach(observer -> observer.onResult(result));
        
        // Add follow-up task
        priorityTasks.add(new Task(result.getNextStep(), result.getPriority()));
    }
    
    public void addObserver(Observer observer) {
        observers.add(observer); // Thread-safe
    }
    
    public Task getHighestPriorityTask() {
        return priorityTasks.pollFirst(); // Thread-safe removal
    }
    
    public int getCacheSize() {
        return cache.size(); // Weakly consistent
    }
}`}
                  </SyntaxHighlighter>
                </div>
              </div>
            </div>
          )}
          
          {detailsTab === 'patterns' && (
            <div>
              <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px', color: '#10b981' }}>Concurrency Design Patterns</h3>
              
              <div style={{ marginBottom: '24px' }}>
                <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Producer-Consumer Pattern</h4>
                <p style={{ color: '#e2e8f0', marginBottom: '12px', lineHeight: '1.6' }}>
                  Essential for handling continuous market data feeds and order processing pipelines in trading systems.
                </p>
                <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', fontSize: '12px' }}>
                  <SyntaxHighlighter language="java" style={oneDark} customStyle={{ backgroundColor: 'transparent', padding: 0, margin: 0, fontSize: '12px' }}>
{`BlockingQueue<Order> orderQueue = new LinkedBlockingQueue<>();

// Producer thread
public void receiveOrder(Order order) {
    orderQueue.put(order); // Blocks if queue is full
}

// Consumer thread
while (!Thread.currentThread().isInterrupted()) {
    Order order = orderQueue.take(); // Blocks until available
    processOrder(order);
}`}
                  </SyntaxHighlighter>
                </div>
              </div>
              
              <div style={{ marginBottom: '24px' }}>
                <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Fork-Join Pattern</h4>
                <p style={{ color: '#e2e8f0', marginBottom: '12px', lineHeight: '1.6' }}>
                  Parallel processing of large datasets like historical trade analysis or portfolio risk calculations.
                </p>
                <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', fontSize: '12px' }}>
                  <SyntaxHighlighter language="java" style={oneDark} customStyle={{ backgroundColor: 'transparent', padding: 0, margin: 0, fontSize: '12px' }}>
{`public class RiskCalculationTask extends RecursiveTask<Double> {
    protected Double compute() {
        if (positions.size() < THRESHOLD) {
            return calculateRiskDirectly();
        } else {
            RiskCalculationTask left = new RiskCalculationTask(leftHalf);
            RiskCalculationTask right = new RiskCalculationTask(rightHalf);
            left.fork();
            return right.compute() + left.join();
        }
    }
}`}
                  </SyntaxHighlighter>
                </div>
              </div>
              
              <div style={{ marginBottom: '24px' }}>
                <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Double-Checked Locking</h4>
                <p style={{ color: '#e2e8f0', marginBottom: '12px', lineHeight: '1.6' }}>
                  Thread-safe singleton initialization for critical system components like market data managers.
                </p>
                <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', fontSize: '12px' }}>
                  <SyntaxHighlighter language="java" style={oneDark} customStyle={{ backgroundColor: 'transparent', padding: 0, margin: 0, fontSize: '12px' }}>
{`public class MarketDataManager {
    private static volatile MarketDataManager instance;
    
    public static MarketDataManager getInstance() {
        if (instance == null) {
            synchronized (MarketDataManager.class) {
                if (instance == null) {
                    instance = new MarketDataManager();
                }
            }
        }
        return instance;
    }
}`}
                  </SyntaxHighlighter>
                </div>
              </div>
              
              <div>
                <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Thread Pool Pattern</h4>
                <p style={{ color: '#e2e8f0', marginBottom: '12px', lineHeight: '1.6' }}>
                  Efficient resource management for handling variable loads in trading systems.
                </p>
                <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', fontSize: '12px' }}>
                  <SyntaxHighlighter language="java" style={oneDark} customStyle={{ backgroundColor: 'transparent', padding: 0, margin: 0, fontSize: '12px' }}>
{`ThreadPoolExecutor executor = new ThreadPoolExecutor(
    2, // core pool size
    10, // maximum pool size
    60L, TimeUnit.SECONDS, // keep alive time
    new LinkedBlockingQueue<>(100), // work queue
    new ThreadFactoryBuilder()
        .setNameFormat("trading-pool-%d")
        .build()
);`}
                  </SyntaxHighlighter>
                </div>
              </div>
            </div>
          )}
          
          {detailsTab === 'performance' && (
            <div>
              <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px', color: '#10b981' }}>Performance Optimization</h3>
              
              <div style={{ marginBottom: '24px' }}>
                <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Lock Contention Reduction</h4>
                <ul style={{ color: '#e2e8f0', paddingLeft: '20px' }}>
                  <li style={{ marginBottom: '8px' }}>Use fine-grained locking instead of coarse-grained locks</li>
                  <li style={{ marginBottom: '8px' }}>Prefer lock-free data structures when possible</li>
                  <li style={{ marginBottom: '8px' }}>Minimize critical section duration</li>
                  <li style={{ marginBottom: '8px' }}>Consider lock-free algorithms for high contention</li>
                </ul>
              </div>
              
              <div style={{ marginBottom: '24px' }}>
                <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Memory Model Optimization</h4>
                <ul style={{ color: '#e2e8f0', paddingLeft: '20px' }}>
                  <li style={{ marginBottom: '8px' }}>Use volatile for simple shared variables</li>
                  <li style={{ marginBottom: '8px' }}>Avoid false sharing with @Contended annotation</li>
                  <li style={{ marginBottom: '8px' }}>Minimize object creation in hot paths</li>
                  <li style={{ marginBottom: '8px' }}>Use ThreadLocal for thread-specific data</li>
                </ul>
              </div>
              
              <div style={{ marginBottom: '24px' }}>
                <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Thread Pool Tuning</h4>
                <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px' }}>
                  <p style={{ color: '#e2e8f0', fontSize: '14px', marginBottom: '8px' }}>CPU-bound tasks:</p>
                  <code style={{ color: '#10b981', fontSize: '12px' }}>corePoolSize = Runtime.getRuntime().availableProcessors()</code>
                  
                  <p style={{ color: '#e2e8f0', fontSize: '14px', marginBottom: '8px', marginTop: '12px' }}>I/O-bound tasks:</p>
                  <code style={{ color: '#10b981', fontSize: '12px' }}>corePoolSize = availableProcessors * (1 + waitTime/serviceTime)</code>
                  
                  <p style={{ color: '#e2e8f0', fontSize: '14px', marginBottom: '8px', marginTop: '12px' }}>Mixed workload:</p>
                  <code style={{ color: '#10b981', fontSize: '12px' }}>Use separate pools for different task types</code>
                </div>
              </div>
              
              <div>
                <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Monitoring Metrics</h4>
                <ul style={{ color: '#e2e8f0', paddingLeft: '20px' }}>
                  <li style={{ marginBottom: '8px' }}>Thread pool utilization and queue size</li>
                  <li style={{ marginBottom: '8px' }}>Lock contention time and frequency</li>
                  <li style={{ marginBottom: '8px' }}>GC pressure from concurrent operations</li>
                  <li style={{ marginBottom: '8px' }}>Context switching overhead</li>
                </ul>
              </div>
            </div>
          )}
          
          {detailsTab === 'troubleshooting' && (
            <div>
              <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px', color: '#10b981' }}>Troubleshooting Guide</h3>
              
              <div style={{ marginBottom: '24px' }}>
                <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#ef4444' }}>Deadlock Detection</h4>
                <p style={{ color: '#e2e8f0', marginBottom: '12px', lineHeight: '1.6' }}>
                  Identify circular dependencies in lock acquisition and implement consistent lock ordering.
                </p>
                <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', fontSize: '12px' }}>
                  <SyntaxHighlighter language="bash" style={oneDark} customStyle={{ backgroundColor: 'transparent', padding: 0, margin: 0, fontSize: '12px' }}>
{`# Generate thread dump
jstack <pid> > threaddump.txt

# Look for "Found Java-level deadlock" in output
# Or use JConsole/VisualVM for real-time monitoring`}
                  </SyntaxHighlighter>
                </div>
              </div>
              
              <div style={{ marginBottom: '24px' }}>
                <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#ef4444' }}>Race Condition Analysis</h4>
                <ul style={{ color: '#e2e8f0', paddingLeft: '20px' }}>
                  <li style={{ marginBottom: '8px' }}>Use happens-before relationships to identify issues</li>
                  <li style={{ marginBottom: '8px' }}>Add logging with thread IDs and timestamps</li>
                  <li style={{ marginBottom: '8px' }}>Use ThreadSanitizer or similar tools</li>
                  <li style={{ marginBottom: '8px' }}>Implement invariant checks in critical sections</li>
                </ul>
              </div>
              
              <div style={{ marginBottom: '24px' }}>
                <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#ef4444' }}>Performance Bottlenecks</h4>
                <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px' }}>
                  <p style={{ color: '#e2e8f0', fontSize: '14px', marginBottom: '8px' }}>High CPU with low throughput:</p>
                  <p style={{ color: '#9ca3af', fontSize: '12px', marginBottom: '8px' }}>→ Excessive lock contention or spinning</p>
                  
                  <p style={{ color: '#e2e8f0', fontSize: '14px', marginBottom: '8px', marginTop: '12px' }}>Thread pool queue buildup:</p>
                  <p style={{ color: '#9ca3af', fontSize: '12px', marginBottom: '8px' }}>→ Undersized pool or slow task processing</p>
                  
                  <p style={{ color: '#e2e8f0', fontSize: '14px', marginBottom: '8px', marginTop: '12px' }}>Memory leaks:</p>
                  <p style={{ color: '#9ca3af', fontSize: '12px' }}>→ Thread-local variables not cleaned up</p>
                </div>
              </div>
              
              <div>
                <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#ef4444' }}>Common Issues & Solutions</h4>
                <div style={{ display: 'grid', gap: '12px' }}>
                  <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', borderLeft: '3px solid #ef4444' }}>
                    <p style={{ color: '#ef4444', fontSize: '14px', fontWeight: 'bold' }}>Issue: OutOfMemoryError</p>
                    <p style={{ color: '#e2e8f0', fontSize: '12px' }}>Solution: Check for thread leaks, increase heap size, or implement backpressure</p>
                  </div>
                  <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', borderLeft: '3px solid #f59e0b' }}>
                    <p style={{ color: '#f59e0b', fontSize: '14px', fontWeight: 'bold' }}>Issue: RejectedExecutionException</p>
                    <p style={{ color: '#e2e8f0', fontSize: '12px' }}>Solution: Increase queue capacity, add custom rejection handler, or use CallerRunsPolicy</p>
                  </div>
                  <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', borderLeft: '3px solid #8b5cf6' }}>
                    <p style={{ color: '#8b5cf6', fontSize: '14px', fontWeight: 'bold' }}>Issue: Livelock</p>
                    <p style={{ color: '#e2e8f0', fontSize: '12px' }}>Solution: Add randomized backoff, implement priority schemes, or use timeout mechanisms</p>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {detailsTab === 'implementations' && (
            <div>
              <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px', color: '#10b981' }}>
                Trading System Implementations
              </h3>
              
              <div style={{ marginBottom: '24px' }}>
                <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#3b82f6' }}>
                  Order Processing Pipeline
                </h4>
                <div style={{ backgroundColor: '#1e293b', padding: '16px', borderRadius: '8px', marginBottom: '12px' }}>
                  <SyntaxHighlighter language="java" style={oneDark} customStyle={{ backgroundColor: 'transparent', padding: 0, margin: 0, fontSize: '12px' }}>
{`@Component
public class OrderProcessingPipeline {
    
    private final ExecutorService orderProcessors = 
        Executors.newFixedThreadPool(8);
    private final BlockingQueue<Order> orderQueue = 
        new ArrayBlockingQueue<>(10000);
    private final CompletionService<OrderResult> completionService;
    
    public OrderProcessingPipeline() {
        this.completionService = new ExecutorCompletionService<>(orderProcessors);
    }
    
    @PostConstruct
    public void startProcessing() {
        // Start multiple consumer threads
        for (int i = 0; i < 8; i++) {
            orderProcessors.submit(() -> {
                while (!Thread.currentThread().isInterrupted()) {
                    try {
                        Order order = orderQueue.take();
                        processOrder(order);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            });
        }
    }
    
    public CompletableFuture<OrderResult> submitOrder(Order order) {
        CompletableFuture<OrderResult> future = new CompletableFuture<>();
        
        if (!orderQueue.offer(order)) {
            future.completeExceptionally(
                new RejectedExecutionException("Order queue is full"));
            return future;
        }
        
        // Asynchronous processing with callback
        return CompletableFuture.supplyAsync(() -> {
            try {
                return riskService.validate(order)
                    .thenCompose(this::executeOrder)
                    .thenCompose(this::recordTrade)
                    .get(5, TimeUnit.SECONDS);
            } catch (Exception e) {
                throw new RuntimeException("Order processing failed", e);
            }
        }, orderProcessors);
    }
}`}
                  </SyntaxHighlighter>
                </div>
              </div>
              
              <div style={{ marginBottom: '24px' }}>
                <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>
                  Real-time Market Data Handler
                </h4>
                <div style={{ backgroundColor: '#1e293b', padding: '16px', borderRadius: '8px', marginBottom: '12px' }}>
                  <SyntaxHighlighter language="java" style={oneDark} customStyle={{ backgroundColor: 'transparent', padding: 0, margin: 0, fontSize: '12px' }}>
{`@Service
public class MarketDataProcessor {
    
    private final ConcurrentHashMap<String, AtomicReference<Price>> latestPrices = 
        new ConcurrentHashMap<>();
    private final DisruptorRingBuffer<MarketDataUpdate> ringBuffer;
    private final EventPublisher eventPublisher;
    
    public MarketDataProcessor() {
        // High-performance ring buffer for market data
        this.ringBuffer = RingBuffer.createMultiProducer(
            MarketDataUpdate::new, 1024 * 64);
            
        // Single consumer for sequential processing
        SequenceBarrier barrier = ringBuffer.newBarrier();
        BatchEventProcessor<MarketDataUpdate> processor = 
            new BatchEventProcessor<>(ringBuffer, barrier, this::handleUpdate);
            
        ringBuffer.addGatingSequences(processor.getSequence());
        
        // Start processor thread
        Executors.newSingleThreadExecutor()
            .submit(processor);
    }
    
    // Multi-producer: multiple market data feeds
    public void onMarketDataReceived(String symbol, double price, long volume) {
        long sequence = ringBuffer.next();
        try {
            MarketDataUpdate update = ringBuffer.get(sequence);
            update.setSymbol(symbol);
            update.setPrice(price);
            update.setVolume(volume);
            update.setTimestamp(System.nanoTime());
        } finally {
            ringBuffer.publish(sequence);
        }
    }
    
    // Single consumer: sequential processing for consistency
    private void handleUpdate(MarketDataUpdate update, long sequence, boolean endOfBatch) {
        // Update latest price atomically
        latestPrices.computeIfAbsent(update.getSymbol(), 
            k -> new AtomicReference<>())
            .set(new Price(update.getPrice(), update.getTimestamp()));
            
        // Trigger dependent calculations
        if (update.isPriceSignificantChange()) {
            triggerRiskRecalculation(update.getSymbol());
        }
        
        // Publish to subscribers (non-blocking)
        eventPublisher.publishAsync(update);
    }
}`}
                  </SyntaxHighlighter>
                </div>
              </div>
              
              <div style={{ marginBottom: '24px' }}>
                <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#8b5cf6' }}>
                  Portfolio Risk Calculator
                </h4>
                <div style={{ backgroundColor: '#1e293b', padding: '16px', borderRadius: '8px', marginBottom: '12px' }}>
                  <SyntaxHighlighter language="java" style={oneDark} customStyle={{ backgroundColor: 'transparent', padding: 0, margin: 0, fontSize: '12px' }}>
{`@Component
public class ParallelRiskCalculator {
    
    private final ForkJoinPool forkJoinPool = 
        ForkJoinPool.commonPool();
    private final ConcurrentMap<String, Double> positionCache = 
        new ConcurrentHashMap<>();
    
    public CompletableFuture<RiskMetrics> calculatePortfolioRisk(
            Portfolio portfolio) {
        
        List<Position> positions = portfolio.getPositions();
        
        // Parallel calculation using Fork-Join
        RecursiveTask<RiskMetrics> riskTask = new RecursiveTask<RiskMetrics>() {
            @Override
            protected RiskMetrics compute() {
                if (positions.size() <= 100) {
                    // Small enough - calculate directly
                    return calculateRiskDirect(positions);
                } else {
                    // Split and calculate in parallel
                    int mid = positions.size() / 2;
                    
                    RecursiveTask<RiskMetrics> leftTask = 
                        new PositionRiskTask(positions.subList(0, mid));
                    RecursiveTask<RiskMetrics> rightTask = 
                        new PositionRiskTask(positions.subList(mid, positions.size()));
                    
                    leftTask.fork();
                    RiskMetrics rightResult = rightTask.compute();
                    RiskMetrics leftResult = leftTask.join();
                    
                    return combineRiskMetrics(leftResult, rightResult);
                }
            }
        };
        
        return CompletableFuture.supplyAsync(() -> 
            forkJoinPool.invoke(riskTask), forkJoinPool);
    }
    
    // Concurrent VaR calculation with parallel streams
    public double calculateValueAtRisk(List<Position> positions, double confidence) {
        return positions.parallelStream()
            .mapToDouble(position -> {
                // Each position's VaR calculated independently
                double marketValue = position.getQuantity() * 
                    getCurrentPrice(position.getSymbol());
                double volatility = getHistoricalVolatility(position.getSymbol());
                
                // Monte Carlo simulation in parallel
                return runMonteCarloVaR(marketValue, volatility, confidence);
            })
            .sum();
    }
    
    private double runMonteCarloVaR(double marketValue, double volatility, double confidence) {
        // Parallel random simulation
        return IntStream.range(0, 10000)
            .parallel()
            .mapToDouble(i -> {
                double randomReturn = ThreadLocalRandom.current()
                    .nextGaussian() * volatility;
                return marketValue * randomReturn;
            })
            .sorted()
            .limit((int) ((1 - confidence) * 10000))
            .max()
            .orElse(0.0);
    }
}`}
                  </SyntaxHighlighter>
                </div>
              </div>
              
              <div style={{ marginBottom: '24px' }}>
                <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#ef4444' }}>
                  Trade Settlement Engine
                </h4>
                <div style={{ backgroundColor: '#1e293b', padding: '16px', borderRadius: '8px' }}>
                  <SyntaxHighlighter language="java" style={oneDark} customStyle={{ backgroundColor: 'transparent', padding: 0, margin: 0, fontSize: '12px' }}>
{`@Service
public class TradeSettlementEngine {
    
    private final ExecutorService settlementPool = 
        Executors.newFixedThreadPool(4);
    private final Phaser settlementPhaser = new Phaser(1);
    
    // Coordinated settlement with multiple counterparties
    public CompletableFuture<SettlementResult> settleTrades(List<Trade> trades) {
        
        Map<String, List<Trade>> tradesByCounterparty = trades.stream()
            .collect(Collectors.groupingBy(Trade::getCounterparty));
        
        List<CompletableFuture<CounterpartySettlement>> settlements = 
            new ArrayList<>();
            
        // Process each counterparty in parallel
        tradesByCounterparty.forEach((counterparty, counterpartyTrades) -> {
            CompletableFuture<CounterpartySettlement> future = 
                CompletableFuture.supplyAsync(() -> {
                    
                    settlementPhaser.register();
                    try {
                        return settleWithCounterparty(counterparty, counterpartyTrades);
                    } finally {
                        settlementPhaser.arriveAndDeregister();
                    }
                    
                }, settlementPool);
                
            settlements.add(future);
        });
        
        // Wait for all settlements to complete
        return CompletableFuture.allOf(
            settlements.toArray(new CompletableFuture[0]))
            .thenApply(v -> {
                List<CounterpartySettlement> results = settlements.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList());
                    
                return new SettlementResult(results);
            });
    }
    
    // Batch processing with backpressure
    private CounterpartySettlement settleWithCounterparty(
            String counterparty, List<Trade> trades) {
        
        // Process in batches to avoid overwhelming external systems
        int batchSize = 50;
        List<SettlementInstruction> instructions = new ArrayList<>();
        
        for (int i = 0; i < trades.size(); i += batchSize) {
            List<Trade> batch = trades.subList(i, 
                Math.min(i + batchSize, trades.size()));
                
            // Submit batch and wait for confirmation
            SettlementBatch batchResult = submitSettlementBatch(
                counterparty, batch);
                
            instructions.addAll(batchResult.getInstructions());
            
            // Backpressure: small delay between batches
            if (i + batchSize < trades.size()) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Settlement interrupted", e);
                }
            }
        }
        
        return new CounterpartySettlement(counterparty, instructions);
    }
}`}
                  </SyntaxHighlighter>
                </div>
              </div>
            </div>
          )}
          </div>
        )}
      </div>
    </div>
  );
};

export default JavaConcurrency;
import React, { useState } from 'react';
import { 
  Cpu, Database, Server, GitBranch, Activity, Shield, 
  BarChart3, T<PERSON>dingUp, AlertTriangle, PieChart, 
  Zap, Lock, Cloud, Code, Settings, Monitor
} from 'lucide-react';

const DarkPoolMatchingEngineAdvanced = () => {
  const [selectedComponent, setSelectedComponent] = useState(null);

  const components = [
    {
      id: 'order-gateway',
      name: 'Order Gateway',
      icon: <Shield size={24} />,
      position: { top: 50, left: 50 },
      tech: ['Spring Boot + Netty', 'FIX Protocol', 'Rate Limiting'],
      metrics: { latency: '< 50μs', throughput: '1M+ ops/s' }
    },
    {
      id: 'order-validator',
      name: 'Order Validator',
      icon: <Shield size={24} />,
      position: { top: 50, left: 250 },
      tech: ['Java 17', 'Virtual Threads', 'Validation Rules'],
      metrics: { latency: '< 100μs', throughput: '500K ops/s' }
    },
    {
      id: 'matching-engine',
      name: 'Matching Engine',
      icon: <Cpu size={24} />,
      position: { top: 200, left: 150 },
      tech: ['Java + Chronicle', 'Lock-free Algorithms', 'Zero GC'],
      metrics: { latency: '< 10μs', throughput: '10M+ ops/s' }
    },
    {
      id: 'risk-engine',
      name: 'Risk Engine',
      icon: <AlertTriangle size={24} />,
      position: { top: 50, left: 450 },
      tech: ['Go + gRPC', 'Real-time Risk', 'VaR/CVaR'],
      metrics: { latency: '< 200μs', throughput: '100K ops/s' }
    },
    {
      id: 'market-data',
      name: 'Market Data Feed',
      icon: <Activity size={24} />,
      position: { top: 350, left: 50 },
      tech: ['C++ + UDP', 'Multicast', 'ITCH Protocol'],
      metrics: { latency: '< 5μs', throughput: '50M msgs/s' }
    },
    {
      id: 'settlement',
      name: 'Settlement Hub',
      icon: <Server size={24} />,
      position: { top: 350, left: 250 },
      tech: ['Spring Boot', 'T+0 Settlement', 'SWIFT'],
      metrics: { latency: '< 500ms', throughput: '10K ops/s' }
    },
    {
      id: 'reporting',
      name: 'Regulatory Reporting',
      icon: <BarChart3 size={24} />,
      position: { top: 350, left: 450 },
      tech: ['Python + Spark', 'Real-time Reports', 'MiFID II'],
      metrics: { latency: '< 1s', throughput: '1K reports/s' }
    },
    {
      id: 'surveillance',
      name: 'Trade Surveillance',
      icon: <Monitor size={24} />,
      position: { top: 200, left: 350 },
      tech: ['ML Models', 'Anomaly Detection', 'Pattern Recognition'],
      metrics: { accuracy: '99.9%', falsePositives: '< 0.1%' }
    },
    {
      id: 'data-lake',
      name: 'Data Lake',
      icon: <Database size={24} />,
      position: { top: 500, left: 250 },
      tech: ['Apache Iceberg', 'S3 Compatible', 'Time Travel'],
      metrics: { storage: 'PB scale', queryLatency: '< 100ms' }
    }
  ];

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #0a0a0a 0%, #111111 100%)',
      color: '#ffffff',
      padding: '40px',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
        <h1 style={{
          textAlign: 'center',
          fontSize: '32px',
          marginBottom: '20px',
          background: 'linear-gradient(45deg, #00ff88, #00aaff)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          fontWeight: 'bold'
        }}>
          Enhanced Dark Pool Trading Architecture
        </h1>
        <p style={{
          textAlign: 'center',
          color: '#94a3b8',
          marginBottom: '40px'
        }}>
          Ultra-Low Latency Electronic Trading System with Advanced Risk Management
        </p>

        <div style={{
          position: 'relative',
          background: 'rgba(17, 24, 39, 0.5)',
          borderRadius: '16px',
          border: '1px solid rgba(59, 130, 246, 0.3)',
          padding: '40px',
          minHeight: '600px'
        }}>
          {/* Components */}
          {components.map(comp => (
            <div
              key={comp.id}
              style={{
                position: 'absolute',
                top: `${comp.position.top}px`,
                left: `${comp.position.left}px`,
                background: selectedComponent === comp.id ? 'rgba(16, 185, 129, 0.2)' : 'rgba(30, 41, 59, 0.8)',
                border: selectedComponent === comp.id ? '2px solid #10b981' : '2px solid #374151',
                borderRadius: '12px',
                padding: '16px',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                minWidth: '160px'
              }}
              onClick={() => setSelectedComponent(comp.id === selectedComponent ? null : comp.id)}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
                <div style={{ color: '#10b981' }}>{comp.icon}</div>
                <h3 style={{ fontSize: '14px', fontWeight: '600', margin: 0 }}>{comp.name}</h3>
              </div>
              {selectedComponent === comp.id && (
                <div style={{ fontSize: '11px', color: '#94a3b8', marginTop: '8px' }}>
                  <div style={{ marginBottom: '4px' }}>
                    <strong>Tech:</strong> {comp.tech.join(', ')}
                  </div>
                  <div>
                    <strong>Metrics:</strong>
                    {Object.entries(comp.metrics).map(([key, value]) => (
                      <div key={key} style={{ marginLeft: '10px' }}>
                        {key}: {value}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}

          {/* Connection Lines */}
          <svg style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            pointerEvents: 'none',
            zIndex: 0
          }}>
            <defs>
              <marker
                id="arrowhead"
                markerWidth="10"
                markerHeight="7"
                refX="9"
                refY="3.5"
                orient="auto"
              >
                <polygon
                  points="0 0, 10 3.5, 0 7"
                  fill="#374151"
                />
              </marker>
            </defs>
            {/* Order flow connections */}
            <line x1="210" y1="74" x2="250" y2="74" stroke="#374151" strokeWidth="2" markerEnd="url(#arrowhead)" />
            <line x1="410" y1="74" x2="450" y2="74" stroke="#374151" strokeWidth="2" markerEnd="url(#arrowhead)" />
            <line x1="330" y1="100" x2="230" y2="200" stroke="#374151" strokeWidth="2" markerEnd="url(#arrowhead)" />
            <line x1="530" y1="100" x2="430" y2="200" stroke="#374151" strokeWidth="2" markerEnd="url(#arrowhead)" />
            {/* Market data connections */}
            <line x1="130" y1="350" x2="230" y2="250" stroke="#374151" strokeWidth="2" markerEnd="url(#arrowhead)" />
            {/* Settlement connections */}
            <line x1="230" y1="250" x2="330" y2="350" stroke="#374151" strokeWidth="2" markerEnd="url(#arrowhead)" />
            {/* Reporting connections */}
            <line x1="430" y1="250" x2="530" y2="350" stroke="#374151" strokeWidth="2" markerEnd="url(#arrowhead)" />
            {/* Data lake connections */}
            <line x1="330" y1="400" x2="330" y2="500" stroke="#374151" strokeWidth="2" markerEnd="url(#arrowhead)" />
            <line x1="530" y1="400" x2="410" y2="500" stroke="#374151" strokeWidth="2" markerEnd="url(#arrowhead)" />
          </svg>
        </div>

        <div style={{
          marginTop: '40px',
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '16px'
        }}>
          <div style={{
            background: 'rgba(16, 185, 129, 0.1)',
            borderRadius: '8px',
            border: '1px solid rgba(16, 185, 129, 0.3)',
            padding: '16px'
          }}>
            <h4 style={{ color: '#10b981', marginBottom: '8px' }}>Total Latency</h4>
            <p style={{ fontSize: '24px', fontWeight: 'bold' }}>{'< 1ms'}</p>
            <p style={{ fontSize: '12px', color: '#94a3b8' }}>End-to-end</p>
          </div>
          <div style={{
            background: 'rgba(59, 130, 246, 0.1)',
            borderRadius: '8px',
            border: '1px solid rgba(59, 130, 246, 0.3)',
            padding: '16px'
          }}>
            <h4 style={{ color: '#3b82f6', marginBottom: '8px' }}>Throughput</h4>
            <p style={{ fontSize: '24px', fontWeight: 'bold' }}>10M+</p>
            <p style={{ fontSize: '12px', color: '#94a3b8' }}>Orders/second</p>
          </div>
          <div style={{
            background: 'rgba(251, 146, 60, 0.1)',
            borderRadius: '8px',
            border: '1px solid rgba(251, 146, 60, 0.3)',
            padding: '16px'
          }}>
            <h4 style={{ color: '#fb923c', marginBottom: '8px' }}>Availability</h4>
            <p style={{ fontSize: '24px', fontWeight: 'bold' }}>99.999%</p>
            <p style={{ fontSize: '12px', color: '#94a3b8' }}>Five nines</p>
          </div>
          <div style={{
            background: 'rgba(168, 85, 247, 0.1)',
            borderRadius: '8px',
            border: '1px solid rgba(168, 85, 247, 0.3)',
            padding: '16px'
          }}>
            <h4 style={{ color: '#a855f7', marginBottom: '8px' }}>Components</h4>
            <p style={{ fontSize: '24px', fontWeight: 'bold' }}>9</p>
            <p style={{ fontSize: '12px', color: '#94a3b8' }}>Microservices</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DarkPoolMatchingEngineAdvanced;
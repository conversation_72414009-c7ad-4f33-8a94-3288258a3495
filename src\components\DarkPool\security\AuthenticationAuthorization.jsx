import React, { useState } from 'react';
import { 
  Shield, Lock, Key, CheckCircle, AlertTriangle, 
  GitBranch, Code, X, Search
} from 'lucide-react';

const AuthenticationAuthorization = () => {
  const [selectedQuestion, setSelectedQuestion] = useState(null);
  const [showAnswer, setShowAnswer] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const interviewQuestions = [
    {
      id: 1,
      category: 'Authentication Basics',
      difficulty: 'Easy',
      question: 'What is the difference between authentication and authorization?',
      answer: {
        short: 'Authentication verifies WHO you are, Authorization determines WHAT you can do.',
        detailed: `
**Authentication** is the process of verifying the identity of a user or system. It answers "Who are you?"
- Examples: Username/password, biometrics, certificates
- Methods: Basic auth, OAuth, SAML, JWT

**Authorization** is the process of determining what actions an authenticated user can perform. It answers "What can you do?"
- Examples: Role-based access control (RBAC), permissions, ACLs
- Methods: Claims-based, attribute-based, policy-based

**Real-world analogy**: 
- Authentication = Showing your ID at airport security (proving who you are)
- Authorization = Your boarding pass determining which plane you can board (what you can access)
        `,
        codeExample: `// Authentication
public boolean authenticate(String username, String password) {
    User user = userService.findByUsername(username);
    return passwordEncoder.matches(password, user.getPassword());
}

// Authorization  
public boolean hasPermission(User user, String resource, String action) {
    return user.getRoles().stream()
        .flatMap(role -> role.getPermissions().stream())
        .anyMatch(permission -> 
            permission.getResource().equals(resource) && 
            permission.getAction().equals(action));
}`
      }
    },
    {
      id: 2,
      category: 'JWT',
      difficulty: 'Medium',
      question: 'Explain JWT structure and how to implement JWT authentication in a Spring Boot application.',
      answer: {
        short: 'JWT has 3 parts: Header.Payload.Signature. Implement using Spring Security with @EnableWebSecurity.',
        detailed: `
**JWT Structure**: Header.Payload.Signature

**Header**: Contains algorithm and token type
\`\`\`json
{
  "alg": "HS256",
  "typ": "JWT"
}
\`\`\`

**Payload**: Contains claims (user data)
\`\`\`json
{
  "sub": "**********",
  "name": "John Doe", 
  "admin": true,
  "exp": **********
}
\`\`\`

**Signature**: Ensures integrity
\`\`\`
HMACSHA256(
  base64UrlEncode(header) + "." +
  base64UrlEncode(payload),
  secret)
\`\`\`

**Advantages**: Stateless, scalable, cross-domain
**Disadvantages**: Cannot revoke, larger size, vulnerable if secret compromised
        `,
        codeExample: `@Component
public class JwtTokenProvider {
    private String jwtSecret = "mySecretKey";
    private int jwtExpirationMs = 86400000; // 24 hours

    public String generateToken(UserDetails userDetails) {
        Date expiryDate = new Date(System.currentTimeMillis() + jwtExpirationMs);
        
        return Jwts.builder()
                .setSubject(userDetails.getUsername())
                .setIssuedAt(new Date())
                .setExpiration(expiryDate)
                .signWith(SignatureAlgorithm.HS512, jwtSecret)
                .compact();
    }

    public String getUsernameFromToken(String token) {
        Claims claims = Jwts.parser()
                .setSigningKey(jwtSecret)
                .parseClaimsJws(token)
                .getBody();
        return claims.getSubject();
    }

    public boolean validateToken(String authToken) {
        try {
            Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(authToken);
            return true;
        } catch (JwtException | IllegalArgumentException e) {
            return false;
        }
    }
}`
      }
    },
    {
      id: 3,
      category: 'OAuth 2.0',
      difficulty: 'Hard',
      question: 'Explain OAuth 2.0 flow types and implement OAuth 2.0 authorization code flow.',
      answer: {
        short: 'OAuth 2.0 has 4 flows: Authorization Code, Implicit, Client Credentials, Password Grant. Authorization Code is most secure.',
        detailed: `
**OAuth 2.0 Grant Types**:

1. **Authorization Code Flow** (Most Secure)
   - For server-side apps
   - Uses temporary authorization code
   - Client secret required

2. **Implicit Flow** (Deprecated)
   - For browser-based apps
   - Access token returned directly
   - No client secret

3. **Client Credentials Flow**
   - For service-to-service
   - No user involvement
   - Client authenticates directly

4. **Resource Owner Password Credentials** (Legacy)
   - User provides credentials directly
   - Only for trusted applications

**Authorization Code Flow Steps**:
1. Client redirects user to authorization server
2. User authenticates and grants permission
3. Authorization server redirects back with code
4. Client exchanges code for access token
5. Client uses token to access protected resources
        `,
        codeExample: `@RestController
@RequestMapping("/oauth")
public class OAuthController {

    @GetMapping("/authorize")
    public ResponseEntity<?> authorize(
            @RequestParam String clientId,
            @RequestParam String redirectUri,
            @RequestParam String responseType,
            @RequestParam String scope,
            @RequestParam String state) {
        
        // Validate client and parameters
        OAuthClient client = clientService.findByClientId(clientId);
        if (client == null || !client.getRedirectUris().contains(redirectUri)) {
            throw new InvalidClientException("Invalid client or redirect URI");
        }
        
        // Generate authorization code
        String authCode = generateAuthorizationCode(clientId, scope, redirectUri);
        
        // Redirect with code
        String redirectUrl = redirectUri + "?code=" + authCode + "&state=" + state;
        return ResponseEntity.status(302)
                .header("Location", redirectUrl)
                .build();
    }

    @PostMapping("/token")
    public ResponseEntity<?> token(
            @RequestParam String grantType,
            @RequestParam String code,
            @RequestParam String redirectUri,
            @RequestParam String clientId,
            @RequestParam String clientSecret) {
        
        // Validate authorization code
        AuthorizationCode authCode = codeService.findByCode(code);
        if (authCode == null || authCode.isExpired()) {
            throw new InvalidGrantException("Invalid or expired authorization code");
        }
        
        // Generate access token
        AccessToken accessToken = tokenService.generateAccessToken(
                authCode.getUserId(), 
                authCode.getScope()
        );
        
        return ResponseEntity.ok(Map.of(
                "access_token", accessToken.getToken(),
                "token_type", "Bearer",
                "expires_in", accessToken.getExpiresIn(),
                "scope", accessToken.getScope()
        ));
    }
}`
      }
    },
    {
      id: 4,
      category: 'Session Management',
      difficulty: 'Medium',
      question: 'How do you implement secure session management? What are session fixation and session hijacking?',
      answer: {
        short: 'Use secure session tokens, regenerate session IDs, implement timeouts. Session fixation = attacker sets session ID. Session hijacking = stealing session token.',
        detailed: `
**Secure Session Management Best Practices**:

1. **Session Token Generation**
   - Use cryptographically secure random generators
   - Minimum 128-bit entropy
   - Unpredictable tokens

2. **Session Storage**
   - Server-side session storage
   - Encrypt session data
   - Use secure session stores (Redis, database)

3. **Session Lifecycle**
   - Regenerate session ID after login
   - Implement session timeouts
   - Proper session invalidation on logout

**Session Fixation Attack**:
- Attacker sets victim's session ID to known value
- Victim authenticates with that session ID
- Attacker uses the same session ID to impersonate victim

**Session Hijacking**:
- Stealing session tokens through XSS, network sniffing, or man-in-the-middle attacks
- Attacker uses stolen token to impersonate user

**Prevention**:
- HTTPS only
- HttpOnly and Secure cookies
- Session ID regeneration
- IP address validation
- User agent validation
        `,
        codeExample: `@Configuration
@EnableWebSecurity
public class SessionSecurityConfig {

    @Bean
    public HttpSessionEventPublisher httpSessionEventPublisher() {
        return new HttpSessionEventPublisher();
    }

    @Bean
    public SessionRegistry sessionRegistry() {
        return new SessionRegistryImpl();
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
            .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.IF_REQUIRED)
                .maximumSessions(1)
                .maxSessionsPreventsLogin(false)
                .sessionRegistry(sessionRegistry())
                .and()
                .sessionFixation().migrateSession() // Prevent session fixation
                .invalidSessionUrl("/login?expired")
            .and()
            .rememberMe()
                .tokenValiditySeconds(86400)
                .userDetailsService(userDetailsService)
            .and()
            .logout()
                .logoutUrl("/logout")
                .deleteCookies("JSESSIONID")
                .invalidateHttpSession(true)
                .clearAuthentication(true);
    }
}

// Custom session management
@Service
public class SessionService {
    
    public void invalidateUserSessions(String username) {
        List<SessionInformation> sessions = sessionRegistry
                .getAllSessions(username, false);
        
        sessions.forEach(SessionInformation::expireNow);
    }
    
    public void regenerateSession(HttpServletRequest request) {
        HttpSession oldSession = request.getSession(false);
        if (oldSession != null) {
            Map<String, Object> attributes = new HashMap<>();
            Enumeration<String> names = oldSession.getAttributeNames();
            
            while (names.hasMoreElements()) {
                String name = names.nextElement();
                attributes.put(name, oldSession.getAttribute(name));
            }
            
            oldSession.invalidate();
            HttpSession newSession = request.getSession(true);
            
            attributes.forEach(newSession::setAttribute);
        }
    }
}`
      }
    },
    {
      id: 5,
      category: 'RBAC',
      difficulty: 'Medium',
      question: 'Design and implement Role-Based Access Control (RBAC) system.',
      answer: {
        short: 'RBAC assigns permissions to roles, roles to users. Core entities: User, Role, Permission. Implement with Spring Security @PreAuthorize.',
        detailed: `
**RBAC Components**:

1. **Users**: Individual entities requiring access
2. **Roles**: Collections of permissions (Admin, Manager, User)
3. **Permissions**: Specific actions on resources (READ_USER, WRITE_ORDER)
4. **Resources**: Protected entities (User, Order, Report)

**RBAC Principles**:
- Least Privilege: Users get minimum necessary permissions
- Separation of Duties: Critical operations require multiple roles
- Role Hierarchy: Roles can inherit from other roles

**Implementation Strategy**:
1. Define permission structure
2. Create role hierarchy
3. Implement permission checking
4. Add role assignment interface
5. Audit access patterns

**Advanced Features**:
- Dynamic role assignment
- Time-based permissions
- Context-aware access control
- Permission inheritance
        `,
        codeExample: `// Domain Models
@Entity
public class User {
    @Id
    private Long id;
    private String username;
    
    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "user_roles")
    private Set<Role> roles = new HashSet<>();
}

@Entity
public class Role {
    @Id
    private Long id;
    private String name;
    
    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "role_permissions")
    private Set<Permission> permissions = new HashSet<>();
}

@Entity
public class Permission {
    @Id
    private Long id;
    private String name; // READ_USER, WRITE_ORDER, DELETE_REPORT
    private String resource; // USER, ORDER, REPORT
    private String action; // READ, WRITE, DELETE
}

// Service Implementation
@Service
public class RBACService {
    
    public boolean hasPermission(String username, String resource, String action) {
        User user = userRepository.findByUsername(username);
        
        return user.getRoles().stream()
                .flatMap(role -> role.getPermissions().stream())
                .anyMatch(permission -> 
                    permission.getResource().equals(resource) && 
                    permission.getAction().equals(action));
    }
    
    public void assignRole(String username, String roleName) {
        User user = userRepository.findByUsername(username);
        Role role = roleRepository.findByName(roleName);
        
        user.getRoles().add(role);
        userRepository.save(user);
    }
}

// Controller with RBAC
@RestController
@RequestMapping("/api/users")
public class UserController {
    
    @GetMapping
    @PreAuthorize("hasPermission('USER', 'READ')")
    public List<User> getAllUsers() {
        return userService.findAll();
    }
    
    @PostMapping
    @PreAuthorize("hasPermission('USER', 'WRITE')")
    public User createUser(@RequestBody User user) {
        return userService.save(user);
    }
    
    @DeleteMapping("/{id}")
    @PreAuthorize("hasPermission('USER', 'DELETE')")
    public void deleteUser(@PathVariable Long id) {
        userService.delete(id);
    }
}`
      }
    },
    {
      id: 6,
      category: 'Security Vulnerabilities',
      difficulty: 'Hard',
      question: 'Explain OWASP Top 10 security vulnerabilities and how to prevent them in Java applications.',
      answer: {
        short: 'OWASP Top 10: Injection, Auth issues, Sensitive data exposure, XXE, Access control, Security misconfig, XSS, Insecure deserialization, Components, Logging. Prevent with input validation, encryption, proper configs.',
        detailed: `
**OWASP Top 10 (2021)**:

1. **Broken Access Control**
   - Users access unauthorized functionality/data
   - Prevention: Implement proper RBAC, deny by default, log access failures

2. **Cryptographic Failures** (formerly Sensitive Data Exposure)
   - Inadequate protection of sensitive data
   - Prevention: Encrypt data at rest/transit, use strong algorithms, proper key management

3. **Injection**
   - Malicious code executed via untrusted input
   - Prevention: Parameterized queries, input validation, escape output

4. **Insecure Design**
   - Missing security controls in design phase
   - Prevention: Secure design patterns, threat modeling, security requirements

5. **Security Misconfiguration**
   - Insecure default configs, incomplete setups
   - Prevention: Secure configurations, disable unused features, regular updates

6. **Vulnerable Components**
   - Using components with known vulnerabilities
   - Prevention: Inventory components, monitor vulnerabilities, update regularly

7. **Authentication Failures**
   - Broken authentication and session management
   - Prevention: MFA, strong passwords, session management, rate limiting

8. **Software Data Integrity Failures**
   - Code/infrastructure that doesn't protect against integrity violations
   - Prevention: Digital signatures, CI/CD security, dependency verification

9. **Logging and Monitoring Failures**
   - Insufficient logging and monitoring
   - Prevention: Log security events, monitor suspicious activity, incident response

10. **Server-Side Request Forgery (SSRF)**
    - App fetches remote resources without validating URL
    - Prevention: Validate URLs, whitelist allowed destinations, network segmentation
        `,
        codeExample: `// 1. SQL Injection Prevention
@Repository
public class UserRepository {
    
    // BAD - Vulnerable to SQL injection
    public User findByUsernameBad(String username) {
        String sql = "SELECT * FROM users WHERE username = '" + username + "'";
        return jdbcTemplate.queryForObject(sql, User.class);
    }
    
    // GOOD - Using parameterized queries
    public User findByUsernameGood(String username) {
        String sql = "SELECT * FROM users WHERE username = ?";
        return jdbcTemplate.queryForObject(sql, User.class, username);
    }
}

// 2. XSS Prevention
@Service
public class ContentService {
    
    public String sanitizeInput(String input) {
        return HtmlUtils.htmlEscape(input); // Escape HTML
    }
    
    @GetMapping("/content")
    public String getContent(@RequestParam String userInput) {
        String sanitized = sanitizeInput(userInput);
        return "User content: " + sanitized;
    }
}

// 3. CSRF Prevention
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
            .csrf()
                .csrfTokenRepository(CookieCsrfTokenRepository.withHttpOnlyFalse())
            .and()
            .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS);
    }
}

// 4. Secure Headers
@Component
public class SecurityHeadersFilter implements Filter {
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, 
                        FilterChain chain) throws IOException, ServletException {
        
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        // Prevent clickjacking
        httpResponse.setHeader("X-Frame-Options", "DENY");
        
        // XSS Protection
        httpResponse.setHeader("X-XSS-Protection", "1; mode=block");
        
        // Content Type Options
        httpResponse.setHeader("X-Content-Type-Options", "nosniff");
        
        // HSTS
        httpResponse.setHeader("Strict-Transport-Security", 
                "max-age=31536000; includeSubDomains");
        
        chain.doFilter(request, response);
    }
}`
      }
    },
    {
      id: 7,
      category: 'Password Security',
      difficulty: 'Medium',
      question: 'How do you implement secure password hashing and what are the best practices for password security?',
      answer: {
        short: 'Use bcrypt/scrypt/Argon2 for hashing. Implement salt, proper cost factor, password policies, MFA, and secure reset flows.',
        detailed: `
**Password Hashing Best Practices**:

1. **Never store plaintext passwords**
2. **Use adaptive hashing functions** (bcrypt, scrypt, Argon2)
3. **Use salt to prevent rainbow table attacks**
4. **Implement proper cost factors** (slow enough to prevent brute force)
5. **Use secure random salt generation**

**Hashing Algorithm Comparison**:
- **bcrypt**: Industry standard, good performance
- **scrypt**: Memory-hard function, better against hardware attacks  
- **Argon2**: Latest recommendation, winner of password hashing competition

**Password Policy Best Practices**:
- Minimum 8 characters (12+ recommended)
- Mix of uppercase, lowercase, numbers, symbols
- Prevent common passwords
- No password reuse
- Regular password rotation for sensitive accounts

**Additional Security Measures**:
- Multi-factor authentication (MFA)
- Account lockout after failed attempts
- Secure password reset flows
- Password breach monitoring
        `,
        codeExample: `@Service
public class PasswordService {
    
    private final BCryptPasswordEncoder encoder = new BCryptPasswordEncoder(12);
    
    // Hash password with bcrypt
    public String hashPassword(String plainPassword) {
        return encoder.encode(plainPassword);
    }
    
    // Verify password
    public boolean verifyPassword(String plainPassword, String hashedPassword) {
        return encoder.matches(plainPassword, hashedPassword);
    }
    
    // Validate password strength
    public PasswordValidationResult validatePassword(String password) {
        List<String> errors = new ArrayList<>();
        
        if (password.length() < 8) {
            errors.add("Password must be at least 8 characters long");
        }
        
        if (!password.matches(".*[A-Z].*")) {
            errors.add("Password must contain uppercase letter");
        }
        
        if (!password.matches(".*[a-z].*")) {
            errors.add("Password must contain lowercase letter");
        }
        
        if (!password.matches(".*[0-9].*")) {
            errors.add("Password must contain number");
        }
        
        if (!password.matches(".*[!@#$%^&*()].*")) {
            errors.add("Password must contain special character");
        }
        
        // Check against common passwords
        if (isCommonPassword(password)) {
            errors.add("Password is too common");
        }
        
        return new PasswordValidationResult(errors.isEmpty(), errors);
    }
    
    // Secure password reset
    public void initiatePasswordReset(String email) {
        User user = userService.findByEmail(email);
        if (user != null) {
            // Generate secure reset token
            String resetToken = generateSecureToken();
            
            // Store token with expiration (15 minutes)
            passwordResetTokenService.save(new PasswordResetToken(
                user.getId(), 
                resetToken,
                LocalDateTime.now().plusMinutes(15)
            ));
            
            // Send secure email
            emailService.sendPasswordResetEmail(user.getEmail(), resetToken);
        }
        // Always return success to prevent user enumeration
    }
    
    public boolean resetPassword(String token, String newPassword) {
        PasswordResetToken resetToken = passwordResetTokenService.findByToken(token);
        
        if (resetToken == null || resetToken.isExpired()) {
            return false;
        }
        
        // Validate new password
        PasswordValidationResult validation = validatePassword(newPassword);
        if (!validation.isValid()) {
            throw new InvalidPasswordException(validation.getErrors());
        }
        
        // Update password
        User user = userService.findById(resetToken.getUserId());
        user.setPassword(hashPassword(newPassword));
        userService.save(user);
        
        // Invalidate reset token
        passwordResetTokenService.delete(resetToken);
        
        // Log security event
        securityEventService.logPasswordReset(user.getUsername());
        
        return true;
    }
    
    private String generateSecureToken() {
        SecureRandom random = new SecureRandom();
        byte[] bytes = new byte[32];
        random.nextBytes(bytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(bytes);
    }
}`
      }
    }
  ];

  const filteredQuestions = interviewQuestions.filter(q => 
    q.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
    q.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getDifficultyColor = (difficulty) => {
    switch(difficulty) {
      case 'Easy': return '#10b981';
      case 'Medium': return '#f59e0b'; 
      case 'Hard': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getCategoryIcon = (category) => {
    switch(category) {
      case 'Authentication Basics': return <Key size={16} />;
      case 'JWT': return <Code size={16} />;
      case 'OAuth 2.0': return <Shield size={16} />;
      case 'Session Management': return <Lock size={16} />;
      case 'RBAC': return <GitBranch size={16} />;
      case 'Security Vulnerabilities': return <AlertTriangle size={16} />;
      case 'Password Security': return <Lock size={16} />;
      default: return <Key size={16} />;
    }
  };

  return (
    <div style={{
      width: '100%',
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%)',
      color: '#ffffff',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif',
      padding: '20px'
    }}>
      {/* Header */}
      <div style={{
        padding: '24px',
        background: 'rgba(15, 23, 42, 0.8)',
        borderRadius: '16px',
        border: '1px solid rgba(51, 65, 85, 0.5)',
        marginBottom: '24px'
      }}>
        <h1 style={{
          fontSize: '36px',
          margin: '0 0 8px 0',
          background: 'linear-gradient(135deg, #3b82f6 0%, #10b981 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          fontWeight: '700'
        }}>
          Authentication & Authorization Interview Questions
        </h1>
        <p style={{
          fontSize: '16px',
          color: '#94a3b8',
          margin: '0 0 16px 0'
        }}>
          Master security concepts with comprehensive interview questions and detailed answers
        </p>

        {/* Search Bar */}
        <div style={{ position: 'relative', maxWidth: '400px' }}>
          <Search size={20} style={{
            position: 'absolute',
            left: '12px',
            top: '50%',
            transform: 'translateY(-50%)',
            color: '#6b7280'
          }} />
          <input
            type="text"
            placeholder="Search questions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              width: '100%',
              padding: '12px 12px 12px 44px',
              background: 'rgba(31, 41, 55, 0.5)',
              border: '1px solid rgba(75, 85, 99, 0.3)',
              borderRadius: '8px',
              color: '#ffffff',
              fontSize: '14px'
            }}
          />
        </div>
      </div>

      {/* Questions Grid */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: selectedQuestion ? '1fr 1fr' : '1fr',
        gap: '24px'
      }}>
        {/* Questions List */}
        <div>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            {filteredQuestions.map((question) => (
              <div 
                key={question.id}
                onClick={() => {
                  setSelectedQuestion(question);
                  setShowAnswer(false);
                }}
                style={{
                  background: selectedQuestion?.id === question.id 
                    ? 'linear-gradient(135deg, #1e40af 0%, #059669 100%)' 
                    : 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
                  borderRadius: '12px',
                  border: '1px solid rgba(51, 65, 85, 0.5)',
                  padding: '20px',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start', marginBottom: '12px' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    {getCategoryIcon(question.category)}
                    <span style={{ fontSize: '12px', color: '#94a3b8' }}>
                      {question.category}
                    </span>
                  </div>
                  <span style={{
                    fontSize: '11px',
                    padding: '2px 6px',
                    backgroundColor: `${getDifficultyColor(question.difficulty)}20`,
                    color: getDifficultyColor(question.difficulty),
                    borderRadius: '4px',
                    fontWeight: '600'
                  }}>
                    {question.difficulty}
                  </span>
                </div>
                
                <h3 style={{ 
                  fontSize: '16px', 
                  fontWeight: '600', 
                  color: 'white', 
                  margin: '0',
                  lineHeight: '1.4'
                }}>
                  {question.question}
                </h3>
              </div>
            ))}
          </div>
        </div>

        {/* Answer Panel */}
        {selectedQuestion && (
          <div style={{
            background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
            borderRadius: '16px',
            border: '1px solid rgba(51, 65, 85, 0.5)',
            padding: '24px',
            position: 'sticky',
            top: '20px',
            height: 'fit-content'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
              <h2 style={{ fontSize: '18px', color: 'white', margin: 0 }}>Answer</h2>
              <button
                onClick={() => setSelectedQuestion(null)}
                style={{
                  background: 'rgba(239, 68, 68, 0.1)',
                  border: '1px solid rgba(239, 68, 68, 0.3)',
                  borderRadius: '6px',
                  color: '#ef4444',
                  cursor: 'pointer',
                  padding: '6px'
                }}
              >
                <X size={16} />
              </button>
            </div>

            {!showAnswer ? (
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <button
                  onClick={() => setShowAnswer(true)}
                  style={{
                    background: 'linear-gradient(135deg, #3b82f6 0%, #10b981 100%)',
                    border: 'none',
                    borderRadius: '8px',
                    color: 'white',
                    cursor: 'pointer',
                    fontSize: '16px',
                    fontWeight: '600',
                    padding: '12px 24px'
                  }}
                >
                  Show Answer
                </button>
              </div>
            ) : (
              <div>
                {/* Quick Answer */}
                <div style={{
                  background: 'rgba(59, 130, 246, 0.1)',
                  borderLeft: '4px solid #3b82f6',
                  borderRadius: '6px',
                  padding: '16px',
                  marginBottom: '20px'
                }}>
                  <h4 style={{ fontSize: '14px', color: '#60a5fa', margin: '0 0 8px 0' }}>Quick Answer:</h4>
                  <p style={{ fontSize: '14px', color: '#e2e8f0', margin: 0, lineHeight: '1.4' }}>
                    {selectedQuestion.answer.short}
                  </p>
                </div>

                {/* Detailed Answer */}
                <div style={{ marginBottom: '20px' }}>
                  <h4 style={{ fontSize: '14px', color: '#10b981', margin: '0 0 12px 0' }}>Detailed Explanation:</h4>
                  <div style={{ 
                    fontSize: '13px', 
                    color: '#d1d5db', 
                    lineHeight: '1.6',
                    whiteSpace: 'pre-line'
                  }}>
                    {selectedQuestion.answer.detailed}
                  </div>
                </div>

                {/* Code Example */}
                {selectedQuestion.answer.codeExample && (
                  <div>
                    <h4 style={{ fontSize: '14px', color: '#f59e0b', margin: '0 0 12px 0' }}>Code Example:</h4>
                    <pre style={{
                      background: 'rgba(0, 0, 0, 0.3)',
                      borderRadius: '6px',
                      padding: '16px',
                      fontSize: '12px',
                      color: '#e2e8f0',
                      overflow: 'auto',
                      border: '1px solid rgba(75, 85, 99, 0.3)'
                    }}>
                      <code>{selectedQuestion.answer.codeExample}</code>
                    </pre>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default AuthenticationAuthorization;
import React, { useState } from 'react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { CreditCard, DollarSign, Shield, Lock, Users, AlertCircle, Layers, GitBranch, Package, Code, Activity, Database, Settings, Zap } from 'lucide-react';

const ATMachineOOP = () => {
  const [selectedConcept, setSelectedConcept] = useState('abstraction');
  const [selectedClass, setSelectedClass] = useState(null);
  const [activeTab, setActiveTab] = useState('concepts');
  const [classPositions, setClassPositions] = useState({});
  const [isDragging, setIsDragging] = useState(null);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  // OOP concepts with colors and highlighted classes
  const oopConcepts = [
    {
      id: 'abstraction',
      title: 'Abstraction',
      icon: <Shield size={20} />,
      color: '#8b5cf6',
      details: 'Abstract classes and interfaces hide implementation details while exposing necessary functionality. In ATM systems, abstraction defines common contracts for transactions, accounts, and security operations.',
      highlightClasses: ['Transaction', 'Account', 'SecurityModule', 'ATMInterface']
    },
    {
      id: 'encapsulation',
      title: 'Encapsulation',
      icon: <Lock size={20} />,
      color: '#10b981',
      details: 'Data hiding and controlled access through public interfaces. Private fields and methods protect sensitive information like account balances, PINs, and transaction details.',
      highlightClasses: ['ATMMachine', 'BankAccount', 'Card', 'CashDispenser', 'Receipt']
    },
    {
      id: 'inheritance',
      title: 'Inheritance',
      icon: <GitBranch size={20} />,
      color: '#f59e0b',
      details: 'Class hierarchy enables code reuse and specialization. Different account types inherit common functionality while adding specific behaviors for checking, savings, and credit accounts.',
      highlightClasses: ['CheckingAccount', 'SavingsAccount', 'CreditAccount', 'WithdrawalTransaction', 'DepositTransaction']
    },
    {
      id: 'polymorphism',
      title: 'Polymorphism',
      icon: <Package size={20} />,
      color: '#ef4444',
      details: 'Multiple forms of behavior through method overriding and interface implementations. Different transaction types and account operations exhibit polymorphic behavior.',
      highlightClasses: ['WithdrawalTransaction', 'DepositTransaction', 'BalanceInquiry', 'CheckingAccount', 'SavingsAccount']
    }
  ];

  // UML Classes for ATM Machine System
  const umlClasses = [
    {
      id: 'ATMInterface',
      name: 'ATMInterface',
      type: 'interface',
      position: { x: 80, y: 30 },
      methods: ['+authenticateUser()', '+processTransaction()', '+dispenseCash()'],
      attributes: [],
      color: '#8b5cf6'
    },
    {
      id: 'Transactable',
      name: 'Transactable',
      type: 'interface',
      position: { x: 350, y: 30 },
      methods: ['+execute()', '+validate()', '+getAmount()'],
      attributes: [],
      color: '#8b5cf6'
    },
    {
      id: 'ATMMachine',
      name: 'ATMMachine',
      type: 'class',
      position: { x: 200, y: 200 },
      methods: ['+authenticateUser()', '+processTransaction()', '+dispenseCash()'],
      attributes: ['- machineId: String', '- location: String', '- cashBalance: double'],
      color: '#10b981'
    },
    {
      id: 'Transaction',
      name: 'Transaction',
      type: 'abstract',
      position: { x: 30, y: 400 },
      methods: ['+execute()', '+validate()', '+generateReceipt()'],
      attributes: ['# transactionId: String', '# amount: double', '# timestamp: Date'],
      color: '#8b5cf6'
    },
    {
      id: 'WithdrawalTransaction',
      name: 'WithdrawalTransaction',
      type: 'class',
      position: { x: 200, y: 400 },
      methods: ['+execute()', '+checkSufficientFunds()'],
      attributes: ['- dailyLimit: double'],
      color: '#f59e0b'
    },
    {
      id: 'DepositTransaction',
      name: 'DepositTransaction',
      type: 'class',
      position: { x: 370, y: 400 },
      methods: ['+execute()', '+validateDeposit()'],
      attributes: ['- depositType: String'],
      color: '#f59e0b'
    },
    {
      id: 'BalanceInquiry',
      name: 'BalanceInquiry',
      type: 'class',
      position: { x: 540, y: 400 },
      methods: ['+execute()', '+getBalance()'],
      attributes: [],
      color: '#ef4444'
    },
    {
      id: 'Account',
      name: 'Account',
      type: 'abstract',
      position: { x: 700, y: 200 },
      methods: ['+withdraw()', '+deposit()', '+getBalance()'],
      attributes: ['# accountNumber: String', '# balance: double', '# ownerId: String'],
      color: '#8b5cf6'
    },
    {
      id: 'CheckingAccount',
      name: 'CheckingAccount',
      type: 'class',
      position: { x: 550, y: 580 },
      methods: ['+withdraw()', '+checkOverdraft()'],
      attributes: ['- overdraftLimit: double'],
      color: '#f59e0b'
    },
    {
      id: 'SavingsAccount',
      name: 'SavingsAccount',
      type: 'class',
      position: { x: 700, y: 580 },
      methods: ['+withdraw()', '+calculateInterest()'],
      attributes: ['- interestRate: double', '- withdrawalLimit: int'],
      color: '#f59e0b'
    },
    {
      id: 'CreditAccount',
      name: 'CreditAccount',
      type: 'class',
      position: { x: 850, y: 580 },
      methods: ['+withdraw()', '+checkCreditLimit()'],
      attributes: ['- creditLimit: double', '- apr: double'],
      color: '#f59e0b'
    },
    {
      id: 'Card',
      name: 'Card',
      type: 'class',
      position: { x: 950, y: 200 },
      methods: ['+validate()', '+isExpired()', '+authenticate()'],
      attributes: ['- cardNumber: String', '- pin: String', '- expiryDate: Date'],
      color: '#10b981'
    },
    {
      id: 'CashDispenser',
      name: 'CashDispenser',
      type: 'class',
      position: { x: 80, y: 580 },
      methods: ['+dispenseCash()', '+checkAvailability()'],
      attributes: ['- availableCash: double', '- denominationCounts: Map'],
      color: '#10b981'
    },
    {
      id: 'Receipt',
      name: 'Receipt',
      type: 'class',
      position: { x: 300, y: 580 },
      methods: ['+print()', '+generateContent()'],
      attributes: ['- transactionDetails: String', '- timestamp: Date'],
      color: '#10b981'
    },
    {
      id: 'SecurityModule',
      name: 'SecurityModule',
      type: 'class',
      position: { x: 1000, y: 400 },
      methods: ['+encryptPIN()', '+validateUser()', '+logActivity()'],
      attributes: ['- encryptionKey: String', '- auditLog: List'],
      color: '#8b5cf6'
    }
  ];

  // Function to check if a class should be highlighted
  const isClassHighlighted = (classId) => {
    const concept = oopConcepts.find(c => c.id === selectedConcept);
    return concept && concept.highlightClasses.includes(classId);
  };

  // Get position for a class (either from state or default)
  const getClassPosition = (classId) => {
    const cls = umlClasses.find(c => c.id === classId);
    return cls ? cls.position : { x: 0, y: 0 };
  };

  // Handle dragging
  const handleMouseDown = (e, classId) => {
    e.preventDefault();
    setIsDragging(classId);
    const rect = e.currentTarget.getBoundingClientRect();
    setDragStart({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    
    const rect = e.currentTarget.getBoundingClientRect();
    const newX = e.clientX - rect.left - dragStart.x;
    const newY = e.clientY - rect.top - dragStart.y;
    
    setClassPositions(prev => ({
      ...prev,
      [isDragging]: { x: Math.max(0, newX), y: Math.max(0, newY) }
    }));
  };

  const handleMouseUp = () => {
    setIsDragging(null);
  };

  return (
    <div style={{ 
      padding: '20px',
      backgroundColor: '#0f172a',
      minHeight: '100vh',
      color: 'white'
    }}>
      {/* Header with concept tabs */}
      <div style={{ 
        display: 'flex', 
        gap: '12px', 
        marginBottom: '20px',
        padding: '16px',
        backgroundColor: 'rgba(30, 41, 59, 0.5)',
        borderRadius: '12px',
        border: '1px solid #334155'
      }}>
        {oopConcepts.map((concept) => (
          <div
            key={concept.id}
            onClick={() => setSelectedConcept(concept.id)}
            style={{
              padding: '12px 20px',
              backgroundColor: selectedConcept === concept.id 
                ? `${concept.color}33` 
                : 'rgba(31, 41, 55, 0.3)',
              border: `2px solid ${selectedConcept === concept.id ? concept.color : '#374151'}`,
              borderRadius: '8px',
              cursor: 'pointer',
              transition: 'all 0.3s',
              minWidth: '140px',
              textAlign: 'center',
              boxShadow: selectedConcept === concept.id 
                ? `0 0 20px ${concept.color}66`
                : 'none'
            }}
          >
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center', 
              gap: '8px',
              marginBottom: '4px'
            }}>
              <div style={{ color: concept.color }}>{concept.icon}</div>
              <span style={{ 
                fontSize: '14px', 
                fontWeight: '600',
                color: selectedConcept === concept.id ? concept.color : '#e2e8f0'
              }}>
                {concept.title}
              </span>
            </div>
            <p style={{ 
              fontSize: '11px', 
              color: '#94a3b8', 
              margin: 0,
              lineHeight: '1.3'
            }}>
              {concept.title === 'Abstraction' && 'Hide complexity'}
              {concept.title === 'Encapsulation' && 'Data protection'}
              {concept.title === 'Inheritance' && 'Code reuse'}
              {concept.title === 'Polymorphism' && 'Multiple forms'}
            </p>
          </div>
        ))}
      </div>

      {/* Main content area */}
      <div style={{ display: 'flex', gap: '20px', height: 'calc(100vh - 140px)' }}>
        {/* Left: UML Diagram */}
        <div style={{ 
          flex: '1 1 60%',
          backgroundColor: '#1e293b',
          borderRadius: '12px',
          padding: '20px',
          border: '1px solid #374151'
        }}>
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            marginBottom: '20px'
          }}>
            <h2 style={{ 
              color: '#10b981', 
              fontSize: '20px', 
              margin: 0,
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <CreditCard size={24} />
              ATM Machine System - UML Class Diagram
            </h2>
          </div>
          
          <svg 
            width="900" 
            height="600" 
            style={{ 
              backgroundColor: '#0f172a', 
              borderRadius: '8px',
              border: '1px solid #374151',
              width: '100%',
              cursor: isDragging ? 'grabbing' : 'default'
            }}
            viewBox="0 0 1200 700"
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
          >
            {/* Define arrow markers */}
            <defs>
              <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                      refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
              </marker>
              <marker id="inheritance-arrow" markerWidth="12" markerHeight="8" 
                      refX="11" refY="4" orient="auto">
                <polygon points="0 0, 0 8, 12 4" fill="none" stroke="#f59e0b" strokeWidth="2"/>
              </marker>
            </defs>

            {/* Draw relationships */}
            {/* ATMMachine implements ATMInterface */}
            <line x1="200" y1="200" x2="150" y2="100" stroke="#8b5cf6" strokeWidth="2" strokeDasharray="5,5" markerEnd="url(#arrowhead)"/>
            
            {/* Transaction hierarchy */}
            <line x1="200" y1="400" x2="100" y2="450" stroke="#f59e0b" strokeWidth="2" markerEnd="url(#inheritance-arrow)"/>
            <line x1="370" y1="400" x2="100" y2="450" stroke="#f59e0b" strokeWidth="2" markerEnd="url(#inheritance-arrow)"/>
            <line x1="540" y1="400" x2="100" y2="450" stroke="#f59e0b" strokeWidth="2" markerEnd="url(#inheritance-arrow)"/>
            
            {/* Account hierarchy */}
            <line x1="550" y1="580" x2="750" y2="300" stroke="#f59e0b" strokeWidth="2" markerEnd="url(#inheritance-arrow)"/>
            <line x1="700" y1="580" x2="750" y2="300" stroke="#f59e0b" strokeWidth="2" markerEnd="url(#inheritance-arrow)"/>
            <line x1="850" y1="580" x2="750" y2="300" stroke="#f59e0b" strokeWidth="2" markerEnd="url(#inheritance-arrow)"/>
            
            {/* ATMMachine uses components */}
            <line x1="250" y1="280" x2="150" y2="580" stroke="#10b981" strokeWidth="1.5" strokeDasharray="3,3" markerEnd="url(#arrowhead)"/>
            <line x1="280" y1="280" x2="350" y2="580" stroke="#10b981" strokeWidth="1.5" strokeDasharray="3,3" markerEnd="url(#arrowhead)"/>
            
            {/* Card to Account relationship */}
            <line x1="950" y1="250" x2="800" y2="250" stroke="#6b7280" strokeWidth="1.5" markerEnd="url(#arrowhead)"/>

            {/* Draw UML Classes */}
            {umlClasses.map((cls) => {
              const isHighlighted = isClassHighlighted(cls.id);
              const strokeColor = isHighlighted ? 
                oopConcepts.find(c => c.id === selectedConcept)?.color : 
                '#374151';
              const fillColor = isHighlighted ? 
                `${oopConcepts.find(c => c.id === selectedConcept)?.color}22` : 
                '#1e293b';
              const position = getClassPosition(cls.id);
              
              return (
                <g 
                  key={cls.id}
                  transform={`translate(${position.x}, ${position.y})`}
                  onMouseDown={(e) => handleMouseDown(e, cls.id)}
                  onClick={() => setSelectedClass(cls)}
                  style={{ cursor: isDragging === cls.id ? 'grabbing' : 'grab' }}
                >
                  <rect
                    width="160"
                    height="100"
                    fill={fillColor}
                    stroke={strokeColor}
                    strokeWidth={isHighlighted ? "3" : "2"}
                    rx="6"
                  />
                  
                  {/* Class stereotype */}
                  <text x="80" y="15" textAnchor="middle" fontSize="10" fill="#94a3b8">
                    &lt;&lt;{cls.type}&gt;&gt;
                  </text>
                  
                  {/* Class name */}
                  <text x="80" y="30" textAnchor="middle" fontSize="12" fontWeight="bold" fill={strokeColor}>
                    {cls.name}
                  </text>
                  
                  {/* Separator line */}
                  <line x1="8" y1="35" x2="152" y2="35" stroke={strokeColor} strokeWidth="1"/>
                  
                  {/* Attributes */}
                  {cls.attributes.slice(0, 1).map((attr, index) => (
                    <text key={index} x="12" y={48 + index * 10} fontSize="9" fill="#94a3b8">
                      {attr.length > 20 ? attr.substring(0, 20) + '...' : attr}
                    </text>
                  ))}
                  
                  {/* Separator line */}
                  <line x1="8" y1="58" x2="152" y2="58" stroke={strokeColor} strokeWidth="1"/>
                  
                  {/* Methods */}
                  {cls.methods.slice(0, 3).map((method, index) => (
                    <text key={index} x="12" y={71 + index * 10} fontSize="9" fill="#94a3b8">
                      {method.length > 20 ? method.substring(0, 20) + '...' : method}
                    </text>
                  ))}
                </g>
              );
            })}
          </svg>
        </div>

        {/* Right: Information Panel */}
        <div style={{ 
          flex: '1 1 40%',
          backgroundColor: '#1e293b',
          borderRadius: '12px',
          border: '1px solid #374151',
          display: 'flex',
          flexDirection: 'column'
        }}>
          {/* Tab headers */}
          <div style={{ 
            display: 'flex',
            borderBottom: '1px solid #374151',
            backgroundColor: 'rgba(51, 65, 81, 0.5)',
            borderRadius: '12px 12px 0 0'
          }}>
            {['concepts', 'code', 'class'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                style={{
                  flex: 1,
                  padding: '12px',
                  backgroundColor: activeTab === tab ? '#374151' : 'transparent',
                  color: activeTab === tab ? '#10b981' : '#94a3b8',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '500',
                  transition: 'all 0.2s',
                  borderRadius: tab === 'concepts' ? '12px 0 0 0' : tab === 'class' ? '0 12px 0 0' : '0'
                }}
              >
                {tab === 'concepts' && 'OOP Concepts'}
                {tab === 'code' && 'Java Code'}
                {tab === 'class' && 'Class Details'}
              </button>
            ))}
          </div>
          
          <div style={{ flex: 1, padding: '24px', overflowY: 'auto' }}>
            {activeTab === 'concepts' && (
              <>
                <div style={{ marginBottom: '20px' }}>
                  <h3 style={{ 
                    color: oopConcepts.find(c => c.id === selectedConcept)?.color,
                    fontSize: '20px',
                    marginBottom: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}>
                    {oopConcepts.find(c => c.id === selectedConcept)?.icon}
                    {oopConcepts.find(c => c.id === selectedConcept)?.title}
                  </h3>
                  <p style={{ color: '#e2e8f0', fontSize: '14px', lineHeight: '1.6' }}>
                    {oopConcepts.find(c => c.id === selectedConcept)?.details}
                  </p>
                </div>
                
                {/* Code Example */}
                <div>
                  <h4 style={{ color: '#10b981', fontSize: '16px', marginBottom: '12px' }}>
                    Code Example
                  </h4>
                  <SyntaxHighlighter
                    language="java"
                    style={oneDark}
                    customStyle={{
                      borderRadius: '8px',
                      fontSize: '12px',
                      maxHeight: '400px'
                    }}
                  >
                    {selectedConcept === 'abstraction' && `// Abstract Transaction class
public abstract class Transaction {
    protected String transactionId;
    protected double amount;
    protected Date timestamp;
    
    // Abstract methods
    public abstract boolean execute();
    public abstract boolean validate();
    
    // Concrete method
    public Receipt generateReceipt() {
        return new Receipt(transactionId, amount, timestamp);
    }
}

// ATM Interface
public interface ATMInterface {
    boolean authenticateUser(String cardNumber, String pin);
    Transaction processTransaction(String transactionType);
    boolean dispenseCash(double amount);
}`}
                    {selectedConcept === 'encapsulation' && `// ATM Machine with encapsulated data
public class ATMMachine implements ATMInterface {
    // Private fields - encapsulated data
    private String machineId;
    private String location;
    private double cashBalance;
    private SecurityModule security;
    private CashDispenser dispenser;
    
    // Public interface methods
    public boolean authenticateUser(String cardNumber, String pin) {
        return security.validateUser(cardNumber, pin);
    }
    
    public boolean dispenseCash(double amount) {
        if (hasSufficientCash(amount)) {
            return dispenser.dispenseCash(amount);
        }
        return false;
    }
    
    // Private helper methods
    private boolean hasSufficientCash(double amount) {
        return cashBalance >= amount;
    }
}`}
                    {selectedConcept === 'inheritance' && `// Inheritance hierarchy for Accounts
public abstract class Account {
    protected String accountNumber;
    protected double balance;
    protected String ownerId;
    
    public abstract boolean withdraw(double amount);
    
    public boolean deposit(double amount) {
        balance += amount;
        return true;
    }
    
    public double getBalance() {
        return balance;
    }
}

public class CheckingAccount extends Account {
    private double overdraftLimit;
    
    @Override
    public boolean withdraw(double amount) {
        if (balance + overdraftLimit >= amount) {
            balance -= amount;
            return true;
        }
        return false;
    }
    
    public boolean checkOverdraft() {
        return balance < 0;
    }
}

public class SavingsAccount extends Account {
    private double interestRate;
    private int withdrawalLimit;
    
    @Override
    public boolean withdraw(double amount) {
        if (balance >= amount && withdrawalLimit > 0) {
            balance -= amount;
            withdrawalLimit--;
            return true;
        }
        return false;
    }
    
    public double calculateInterest() {
        return balance * interestRate / 100;
    }
}`}
                    {selectedConcept === 'polymorphism' && `// Polymorphic behavior in ATM system
public class ATMController {
    private List<Account> accounts;
    private List<Transaction> transactions;
    
    public void processTransactions() {
        for (Transaction transaction : transactions) {
            // Polymorphic method call - different implementations
            boolean success = transaction.execute();
            
            if (success) {
                transaction.generateReceipt();
            }
        }
    }
    
    public void manageAccounts() {
        for (Account account : accounts) {
            // Polymorphic behavior - each account type implements differently
            if (account instanceof CheckingAccount) {
                ((CheckingAccount) account).checkOverdraft();
            } else if (account instanceof SavingsAccount) {
                double interest = ((SavingsAccount) account).calculateInterest();
                account.deposit(interest);
            }
            
            // Common interface - polymorphic method
            double balance = account.getBalance();
            System.out.println("Account balance: " + balance);
        }
    }
}`}
                  </SyntaxHighlighter>
                </div>
              </>
            )}

            {activeTab === 'code' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '18px', marginBottom: '16px' }}>
                  Complete ATM System Implementation
                </h3>
                <SyntaxHighlighter
                  language="java"
                  style={oneDark}
                  customStyle={{
                    borderRadius: '8px',
                    fontSize: '11px',
                    maxHeight: '500px'
                  }}
                >
{`// ATM Machine System - Complete Implementation

import java.util.*;
import java.time.LocalDateTime;
import java.security.MessageDigest;

// Abstract base class for all transactions
public abstract class Transaction {
    protected String transactionId;
    protected double amount;
    protected LocalDateTime timestamp;
    protected Account account;
    
    public Transaction(String transactionId, double amount, Account account) {
        this.transactionId = transactionId;
        this.amount = amount;
        this.account = account;
        this.timestamp = LocalDateTime.now();
    }
    
    public abstract boolean execute();
    public abstract boolean validate();
    
    public Receipt generateReceipt() {
        return new Receipt(transactionId, amount, timestamp, getTransactionType());
    }
    
    public abstract String getTransactionType();
    
    // Getters
    public String getTransactionId() { return transactionId; }
    public double getAmount() { return amount; }
    public LocalDateTime getTimestamp() { return timestamp; }
}

// Concrete transaction implementations
public class WithdrawalTransaction extends Transaction {
    private double dailyLimit = 1000.0;
    private static Map<String, Double> dailyWithdrawals = new HashMap<>();
    
    public WithdrawalTransaction(String transactionId, double amount, Account account) {
        super(transactionId, amount, account);
    }
    
    @Override
    public boolean execute() {
        if (!validate()) return false;
        
        if (account.withdraw(amount)) {
            updateDailyWithdrawal();
            return true;
        }
        return false;
    }
    
    @Override
    public boolean validate() {
        return checkSufficientFunds() && checkDailyLimit() && amount > 0;
    }
    
    @Override
    public String getTransactionType() {
        return "WITHDRAWAL";
    }
    
    private boolean checkSufficientFunds() {
        return account.getBalance() >= amount;
    }
    
    private boolean checkDailyLimit() {
        String accountKey = account.getAccountNumber();
        double todayWithdrawal = dailyWithdrawals.getOrDefault(accountKey, 0.0);
        return (todayWithdrawal + amount) <= dailyLimit;
    }
    
    private void updateDailyWithdrawal() {
        String accountKey = account.getAccountNumber();
        double current = dailyWithdrawals.getOrDefault(accountKey, 0.0);
        dailyWithdrawals.put(accountKey, current + amount);
    }
}

public class DepositTransaction extends Transaction {
    private String depositType; // "CASH", "CHECK"
    
    public DepositTransaction(String transactionId, double amount, Account account, String depositType) {
        super(transactionId, amount, account);
        this.depositType = depositType;
    }
    
    @Override
    public boolean execute() {
        if (!validate()) return false;
        return account.deposit(amount);
    }
    
    @Override
    public boolean validate() {
        return validateDeposit() && amount > 0;
    }
    
    @Override
    public String getTransactionType() {
        return "DEPOSIT";
    }
    
    private boolean validateDeposit() {
        // Validate deposit based on type
        if ("CHECK".equals(depositType)) {
            return amount <= 10000.0; // Check deposit limit
        }
        return true; // Cash deposits always valid
    }
}

public class BalanceInquiry extends Transaction {
    
    public BalanceInquiry(String transactionId, Account account) {
        super(transactionId, 0.0, account);
    }
    
    @Override
    public boolean execute() {
        return true; // Balance inquiry always succeeds
    }
    
    @Override
    public boolean validate() {
        return account != null;
    }
    
    @Override
    public String getTransactionType() {
        return "BALANCE_INQUIRY";
    }
    
    public double getBalance() {
        return account.getBalance();
    }
}

// Abstract Account base class
public abstract class Account {
    protected String accountNumber;
    protected double balance;
    protected String ownerId;
    protected List<Transaction> transactionHistory;
    
    public Account(String accountNumber, String ownerId, double initialBalance) {
        this.accountNumber = accountNumber;
        this.ownerId = ownerId;
        this.balance = initialBalance;
        this.transactionHistory = new ArrayList<>();
    }
    
    public abstract boolean withdraw(double amount);
    
    public boolean deposit(double amount) {
        if (amount > 0) {
            balance += amount;
            return true;
        }
        return false;
    }
    
    public double getBalance() {
        return balance;
    }
    
    public String getAccountNumber() {
        return accountNumber;
    }
    
    public void addTransaction(Transaction transaction) {
        transactionHistory.add(transaction);
    }
    
    public List<Transaction> getTransactionHistory() {
        return new ArrayList<>(transactionHistory);
    }
}

// Account implementations
public class CheckingAccount extends Account {
    private double overdraftLimit;
    
    public CheckingAccount(String accountNumber, String ownerId, double initialBalance, double overdraftLimit) {
        super(accountNumber, ownerId, initialBalance);
        this.overdraftLimit = overdraftLimit;
    }
    
    @Override
    public boolean withdraw(double amount) {
        if (balance + overdraftLimit >= amount) {
            balance -= amount;
            return true;
        }
        return false;
    }
    
    public boolean checkOverdraft() {
        return balance < 0;
    }
    
    public double getAvailableCredit() {
        return balance + overdraftLimit;
    }
}

public class SavingsAccount extends Account {
    private double interestRate;
    private int monthlyWithdrawalLimit;
    private int withdrawalsThisMonth;
    
    public SavingsAccount(String accountNumber, String ownerId, double initialBalance, double interestRate) {
        super(accountNumber, ownerId, initialBalance);
        this.interestRate = interestRate;
        this.monthlyWithdrawalLimit = 6;
        this.withdrawalsThisMonth = 0;
    }
    
    @Override
    public boolean withdraw(double amount) {
        if (balance >= amount && withdrawalsThisMonth < monthlyWithdrawalLimit) {
            balance -= amount;
            withdrawalsThisMonth++;
            return true;
        }
        return false;
    }
    
    public double calculateInterest() {
        return balance * interestRate / 100 / 12; // Monthly interest
    }
    
    public void applyMonthlyInterest() {
        deposit(calculateInterest());
    }
    
    public void resetMonthlyWithdrawals() {
        withdrawalsThisMonth = 0;
    }
}

public class CreditAccount extends Account {
    private double creditLimit;
    private double apr;
    private double minimumPayment;
    
    public CreditAccount(String accountNumber, String ownerId, double creditLimit, double apr) {
        super(accountNumber, ownerId, 0.0); // Credit accounts start at 0
        this.creditLimit = creditLimit;
        this.apr = apr;
        this.minimumPayment = 0.02; // 2% of balance
    }
    
    @Override
    public boolean withdraw(double amount) {
        if (Math.abs(balance - amount) <= creditLimit) {
            balance -= amount;
            return true;
        }
        return false;
    }
    
    public boolean checkCreditLimit(double amount) {
        return Math.abs(balance - amount) <= creditLimit;
    }
    
    public double getAvailableCredit() {
        return creditLimit + balance; // balance is negative for credit
    }
    
    public double calculateMinimumPayment() {
        return Math.abs(balance) * minimumPayment;
    }
}

// ATM Card class
public class Card {
    private String cardNumber;
    private String encryptedPin;
    private Date expiryDate;
    private String accountNumber;
    private boolean isBlocked;
    
    public Card(String cardNumber, String pin, Date expiryDate, String accountNumber) {
        this.cardNumber = cardNumber;
        this.encryptedPin = encryptPin(pin);
        this.expiryDate = expiryDate;
        this.accountNumber = accountNumber;
        this.isBlocked = false;
    }
    
    public boolean validate(String pin) {
        return !isExpired() && !isBlocked && encryptedPin.equals(encryptPin(pin));
    }
    
    public boolean isExpired() {
        return new Date().after(expiryDate);
    }
    
    public void block() {
        isBlocked = true;
    }
    
    public void unblock() {
        isBlocked = false;
    }
    
    private String encryptPin(String pin) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(pin.getBytes("UTF-8"));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            return pin; // Fallback - in real system, should handle properly
        }
    }
    
    // Getters
    public String getCardNumber() { return cardNumber; }
    public String getAccountNumber() { return accountNumber; }
    public boolean isBlocked() { return isBlocked; }
}

// Cash Dispenser
public class CashDispenser {
    private double availableCash;
    private Map<Integer, Integer> denominationCounts; // denomination -> count
    
    public CashDispenser(double initialCash) {
        this.availableCash = initialCash;
        this.denominationCounts = new HashMap<>();
        // Initialize with standard denominations
        denominationCounts.put(100, 100); // 100 x $100 bills
        denominationCounts.put(50, 200);  // 200 x $50 bills
        denominationCounts.put(20, 500);  // 500 x $20 bills
        denominationCounts.put(10, 300);  // 300 x $10 bills
    }
    
    public boolean dispenseCash(double amount) {
        if (!checkAvailability(amount)) {
            return false;
        }
        
        Map<Integer, Integer> dispensed = calculateDispensePattern(amount);
        if (dispensed != null) {
            updateCounts(dispensed);
            availableCash -= amount;
            return true;
        }
        return false;
    }
    
    public boolean checkAvailability(double amount) {
        return availableCash >= amount && amount > 0;
    }
    
    private Map<Integer, Integer> calculateDispensePattern(double amount) {
        Map<Integer, Integer> pattern = new HashMap<>();
        int remaining = (int) amount;
        
        // Try to dispense using largest denominations first
        int[] denominations = {100, 50, 20, 10};
        
        for (int denom : denominations) {
            int available = denominationCounts.get(denom);
            int needed = remaining / denom;
            int dispense = Math.min(needed, available);
            
            if (dispense > 0) {
                pattern.put(denom, dispense);
                remaining -= (dispense * denom);
            }
        }
        
        return remaining == 0 ? pattern : null;
    }
    
    private void updateCounts(Map<Integer, Integer> dispensed) {
        for (Map.Entry<Integer, Integer> entry : dispensed.entrySet()) {
            int denom = entry.getKey();
            int count = entry.getValue();
            denominationCounts.put(denom, denominationCounts.get(denom) - count);
        }
    }
}

// Receipt class
public class Receipt {
    private String transactionId;
    private double amount;
    private LocalDateTime timestamp;
    private String transactionType;
    private String receiptContent;
    
    public Receipt(String transactionId, double amount, LocalDateTime timestamp, String transactionType) {
        this.transactionId = transactionId;
        this.amount = amount;
        this.timestamp = timestamp;
        this.transactionType = transactionType;
        this.receiptContent = generateContent();
    }
    
    public void print() {
        System.out.println(receiptContent);
    }
    
    private String generateContent() {
        StringBuilder content = new StringBuilder();
        content.append("=== ATM RECEIPT ===\\n");
        content.append("Transaction ID: ").append(transactionId).append("\\n");
        content.append("Type: ").append(transactionType).append("\\n");
        content.append("Amount: $").append(String.format("%.2f", amount)).append("\\n");
        content.append("Date: ").append(timestamp.toString()).append("\\n");
        content.append("Thank you for using our ATM\\n");
        content.append("==================\\n");
        return content.toString();
    }
    
    public String getReceiptContent() {
        return receiptContent;
    }
}

// Security Module
public class SecurityModule {
    private String encryptionKey;
    private List<String> auditLog;
    private int maxPinAttempts;
    private Map<String, Integer> pinAttempts;
    
    public SecurityModule(String encryptionKey) {
        this.encryptionKey = encryptionKey;
        this.auditLog = new ArrayList<>();
        this.maxPinAttempts = 3;
        this.pinAttempts = new HashMap<>();
    }
    
    public boolean validateUser(String cardNumber, String pin) {
        logActivity("PIN validation attempt for card: " + cardNumber);
        
        if (isCardBlocked(cardNumber)) {
            logActivity("Card blocked: " + cardNumber);
            return false;
        }
        
        // In real implementation, would validate against secure database
        // For demo, assume validation logic here
        boolean valid = true; // Placeholder
        
        if (!valid) {
            incrementPinAttempts(cardNumber);
            if (pinAttempts.get(cardNumber) >= maxPinAttempts) {
                blockCard(cardNumber);
                logActivity("Card blocked due to multiple failed attempts: " + cardNumber);
            }
            return false;
        }
        
        resetPinAttempts(cardNumber);
        logActivity("Successful authentication for card: " + cardNumber);
        return true;
    }
    
    public String encryptPIN(String pin) {
        // Simple encryption - in real system would use proper encryption
        return pin + encryptionKey;
    }
    
    public void logActivity(String activity) {
        String logEntry = LocalDateTime.now() + ": " + activity;
        auditLog.add(logEntry);
        System.out.println("[SECURITY LOG] " + logEntry);
    }
    
    private boolean isCardBlocked(String cardNumber) {
        return pinAttempts.getOrDefault(cardNumber, 0) >= maxPinAttempts;
    }
    
    private void incrementPinAttempts(String cardNumber) {
        pinAttempts.put(cardNumber, pinAttempts.getOrDefault(cardNumber, 0) + 1);
    }
    
    private void resetPinAttempts(String cardNumber) {
        pinAttempts.remove(cardNumber);
    }
    
    private void blockCard(String cardNumber) {
        // In real system, would update database to block card
        logActivity("Card permanently blocked: " + cardNumber);
    }
    
    public List<String> getAuditLog() {
        return new ArrayList<>(auditLog);
    }
}

// Main ATM Machine class
public class ATMMachine implements ATMInterface {
    private String machineId;
    private String location;
    private CashDispenser cashDispenser;
    private SecurityModule securityModule;
    private Map<String, Account> accounts;
    private List<Card> validCards;
    
    public ATMMachine(String machineId, String location) {
        this.machineId = machineId;
        this.location = location;
        this.cashDispenser = new CashDispenser(50000.0); // $50,000 initial cash
        this.securityModule = new SecurityModule("ATM_SECRET_KEY_2024");
        this.accounts = new HashMap<>();
        this.validCards = new ArrayList<>();
        
        // Initialize some test data
        initializeTestData();
    }
    
    @Override
    public boolean authenticateUser(String cardNumber, String pin) {
        Card card = findCard(cardNumber);
        if (card == null) {
            securityModule.logActivity("Invalid card number: " + cardNumber);
            return false;
        }
        
        return card.validate(pin) && securityModule.validateUser(cardNumber, pin);
    }
    
    @Override
    public Transaction processTransaction(String transactionType, String cardNumber, double amount) {
        Card card = findCard(cardNumber);
        if (card == null) return null;
        
        Account account = accounts.get(card.getAccountNumber());
        if (account == null) return null;
        
        String transactionId = generateTransactionId();
        Transaction transaction = null;
        
        if ("WITHDRAWAL".equals(transactionType)) {
            transaction = new WithdrawalTransaction(transactionId, amount, account);
        } else if ("DEPOSIT".equals(transactionType)) {
            transaction = new DepositTransaction(transactionId, amount, account, "CASH");
        } else if ("BALANCE_INQUIRY".equals(transactionType)) {
            transaction = new BalanceInquiry(transactionId, account);
        }
        
        if (transaction != null && transaction.execute()) {
            account.addTransaction(transaction);
            securityModule.logActivity("Transaction completed: " + transactionId);
            return transaction;
        }
        
        securityModule.logActivity("Transaction failed: " + transactionId);
        return null;
    }
    
    @Override
    public boolean dispenseCash(double amount) {
        return cashDispenser.dispenseCash(amount);
    }
    
    private Card findCard(String cardNumber) {
        return validCards.stream()
                .filter(card -> card.getCardNumber().equals(cardNumber))
                .findFirst()
                .orElse(null);
    }
    
    private String generateTransactionId() {
        return "TXN" + System.currentTimeMillis();
    }
    
    private void initializeTestData() {
        // Create test accounts
        CheckingAccount checking = new CheckingAccount("CHK001", "USER001", 5000.0, 500.0);
        SavingsAccount savings = new SavingsAccount("SAV001", "USER001", 10000.0, 2.5);
        accounts.put("CHK001", checking);
        accounts.put("SAV001", savings);
        
        // Create test cards
        validCards.add(new Card("****************", "1234", new Date(System.currentTimeMillis() + 365L*24*60*60*1000), "CHK001"));
        validCards.add(new Card("****************", "5678", new Date(System.currentTimeMillis() + 365L*24*60*60*1000), "SAV001"));
    }
    
    // Getters
    public String getMachineId() { return machineId; }
    public String getLocation() { return location; }
    public double getAvailableCash() { return cashDispenser.availableCash; }
}

// ATM Interface
interface ATMInterface {
    boolean authenticateUser(String cardNumber, String pin);
    Transaction processTransaction(String transactionType, String cardNumber, double amount);
    boolean dispenseCash(double amount);
}`}
                </SyntaxHighlighter>
              </div>
            )}

            {activeTab === 'class' && selectedClass && (
              <div>
                <div style={{ 
                  padding: '16px',
                  backgroundColor: 'rgba(139, 92, 246, 0.1)',
                  borderRadius: '8px',
                  border: '1px solid #8b5cf6',
                  marginBottom: '20px'
                }}>
                  <h4 style={{ 
                    color: '#8b5cf6', 
                    margin: '0 0 8px 0',
                    fontSize: '18px',
                    fontWeight: '600'
                  }}>
                    {selectedClass.name}
                  </h4>
                  <p style={{ color: '#e2e8f0', fontSize: '12px' }}>
                    Type: {selectedClass.type}
                  </p>
                  <p style={{ color: '#94a3b8', fontSize: '11px', marginTop: '8px' }}>
                    This class demonstrates {selectedConcept} through its structure and relationships.
                  </p>
                </div>
                
                {selectedClass.attributes && selectedClass.attributes.length > 0 && (
                  <div style={{ marginBottom: '20px' }}>
                    <h5 style={{ color: '#10b981', fontSize: '14px', marginBottom: '8px' }}>
                      Attributes:
                    </h5>
                    <ul style={{ color: '#e2e8f0', fontSize: '12px', paddingLeft: '16px' }}>
                      {selectedClass.attributes.map((attr, index) => (
                        <li key={index} style={{ marginBottom: '4px' }}>{attr}</li>
                      ))}
                    </ul>
                  </div>
                )}
                
                <div>
                  <h5 style={{ color: '#10b981', fontSize: '14px', marginBottom: '8px' }}>
                    Methods:
                  </h5>
                  <ul style={{ color: '#e2e8f0', fontSize: '12px', paddingLeft: '16px' }}>
                    {selectedClass.methods.map((method, index) => (
                      <li key={index} style={{ marginBottom: '4px' }}>{method}</li>
                    ))}
                  </ul>
                </div>
              </div>
            )}

            {activeTab === 'class' && !selectedClass && (
              <div style={{ 
                textAlign: 'center', 
                color: '#94a3b8', 
                fontSize: '14px',
                marginTop: '40px'
              }}>
                <Activity size={48} style={{ margin: '0 auto 16px', opacity: 0.5 }} />
                <p>Click on a UML class in the diagram to view its details</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ATMachineOOP;
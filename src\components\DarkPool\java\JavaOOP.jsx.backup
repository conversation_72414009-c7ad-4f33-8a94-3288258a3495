import React, { useState } from 'react';
import { Prism as Synta<PERSON><PERSON><PERSON>lighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Code, Layers, Lock, GitBranch, Package, Sparkles } from 'lucide-react';

const JavaOOP = () => {
  const [selectedConcept, setSelectedConcept] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [detailsTab, setDetailsTab] = useState('definition');

  const oopConcepts = [
    {
      id: 'encapsulation',
      name: 'Encapsulation',
      icon: <Lock size={20} />,
      description: 'Data hiding and access control',
      color: '#ef4444'
    },
    {
      id: 'inheritance',
      name: 'Inheritance',
      icon: <GitBranch size={20} />,
      description: 'Class hierarchy and code reuse',
      color: '#3b82f6'
    },
    {
      id: 'polymorphism',
      name: 'Polymorphism',
      icon: <Layers size={20} />,
      description: 'Method overloading and overriding',
      color: '#10b981'
    },
    {
      id: 'abstraction',
      name: 'Abstraction',
      icon: <Package size={20} />,
      description: 'Abstract classes and interfaces',
      color: '#f59e0b'
    },
    {
      id: 'composition',
      name: 'Composition',
      icon: <Sparkles size={20} />,
      description: 'Object relationships and design',
      color: '#8b5cf6'
    },
    {
      id: 'abstract-vs-interface',
      name: 'Abstract Classes vs Interfaces',
      icon: <Layers size={20} />,
      description: 'Choosing between abstraction mechanisms',
      color: '#ec4899'
    },
    {
      id: 'overloading-vs-overriding',
      name: 'Overloading vs Overriding',
      icon: <GitBranch size={20} />,
      description: 'Method polymorphism techniques',
      color: '#06b6d4'
    },
    {
      id: 'access-modifiers',
      name: 'Access Modifiers',
      icon: <Lock size={20} />,
      description: 'Controlling visibility and access',
      color: '#f97316'
    },
    {
      id: 'super-this',
      name: 'super and this Keywords',
      icon: <Code size={20} />,
      description: 'Referencing current and parent objects',
      color: '#84cc16'
    }
  ];

  const codeExamples = {
    encapsulation: `public class TradingAccount {
    private double balance;
    private String accountId;
    private List<Trade> trades;
    
    public TradingAccount(String accountId) {
        this.accountId = accountId;
        this.balance = 0.0;
        this.trades = new ArrayList<>();
    }
    
    public double getBalance() {
        return balance;
    }
    
    public void deposit(double amount) {
        if (amount > 0) {
            balance += amount;
        }
    }
    
    public boolean executeTrade(Trade trade) {
        if (trade.getValue() <= balance) {
            balance -= trade.getValue();
            trades.add(trade);
            return true;
        }
        return false;
    }
}`,
    inheritance: `public abstract class Order {
    protected String orderId;
    protected double price;
    protected int quantity;
    
    public abstract void execute();
    public abstract double calculateValue();
}

public class MarketOrder extends Order {
    @Override
    public void execute() {
        // Execute at current market price
        System.out.println("Executing market order");
    }
    
    @Override
    public double calculateValue() {
        return price * quantity;
    }
}

public class LimitOrder extends Order {
    private double limitPrice;
    
    @Override
    public void execute() {
        if (price <= limitPrice) {
            System.out.println("Executing limit order");
        }
    }
    
    @Override
    public double calculateValue() {
        return limitPrice * quantity;
    }
}`,
    polymorphism: `public interface TradingStrategy {
    double calculateRisk(Portfolio portfolio);
    List<Order> generateOrders(MarketData data);
}

public class ConservativeStrategy implements TradingStrategy {
    @Override
    public double calculateRisk(Portfolio portfolio) {
        return portfolio.getValue() * 0.02; // 2% risk
    }
    
    @Override
    public List<Order> generateOrders(MarketData data) {
        // Conservative order generation logic
        return Arrays.asList(new LimitOrder(...));
    }
}

public class AggressiveStrategy implements TradingStrategy {
    @Override
    public double calculateRisk(Portfolio portfolio) {
        return portfolio.getValue() * 0.10; // 10% risk
    }
    
    @Override
    public List<Order> generateOrders(MarketData data) {
        // Aggressive order generation logic
        return Arrays.asList(new MarketOrder(...));
    }
}`,
    abstraction: `public interface RiskCalculator {
    double calculateVaR(Portfolio portfolio);
    double calculateCVaR(Portfolio portfolio);
    RiskMetrics analyzeRisk(List<Position> positions);
}

public abstract class BaseRiskCalculator implements RiskCalculator {
    protected double confidenceLevel = 0.95;
    
    protected abstract double computeHistoricalVaR(List<Double> returns);
    
    @Override
    public double calculateVaR(Portfolio portfolio) {
        List<Double> returns = portfolio.getHistoricalReturns();
        return computeHistoricalVaR(returns);
    }
}

public class MonteCarloRiskCalculator extends BaseRiskCalculator {
    private int simulations = 10000;
    
    @Override
    protected double computeHistoricalVaR(List<Double> returns) {
        // Monte Carlo simulation logic
        return simulateVaR(returns, simulations);
    }
}`,
    composition: `public class TradingSystem {
    private final OrderGateway gateway;
    private final RiskEngine riskEngine;
    private final MatchingEngine matchingEngine;
    private final PositionManager positionManager;
    
    public TradingSystem(TradingSystemConfig config) {
        this.gateway = new OrderGateway(config.getGatewayConfig());
        this.riskEngine = new RiskEngine(config.getRiskConfig());
        this.matchingEngine = new MatchingEngine(config.getMatchingConfig());
        this.positionManager = new PositionManager();
    }
    
    public OrderResponse submitOrder(Order order) {
        // Validate order
        if (!gateway.validate(order)) {
            return OrderResponse.rejected("Validation failed");
        }
        
        // Check risk
        RiskCheck riskCheck = riskEngine.evaluate(order);
        if (!riskCheck.isPassed()) {
            return OrderResponse.rejected(riskCheck.getReason());
        }
        
        // Match order
        MatchResult result = matchingEngine.match(order);
        
        // Update positions
        positionManager.updatePosition(result);
        
        return OrderResponse.success(result);
    }
}`,
    'abstract-vs-interface': `// Abstract Classes vs Interfaces in Trading Systems

// Abstract Class - Provides partial implementation
public abstract class BaseTradingStrategy {
    protected String name;
    protected double riskTolerance;
    
    // Constructor for common initialization
    public BaseTradingStrategy(String name, double riskTolerance) {
        this.name = name;
        this.riskTolerance = riskTolerance;
    }
    
    // Concrete method - shared behavior
    public boolean isWithinRiskLimit(double positionSize) {
        return positionSize <= riskTolerance;
    }
    
    // Abstract method - must be implemented by subclasses
    public abstract Decision makeDecision(MarketData data);
    public abstract double calculatePositionSize(double portfolioValue);
}

// Interface - Contract only, no implementation
public interface RiskCalculator {
    // All methods are implicitly public and abstract
    double calculateVaR(Portfolio portfolio, double confidence);
    double calculateCVaR(Portfolio portfolio, double confidence);
    RiskMetrics analyzePortfolioRisk(List<Position> positions);
    
    // Default methods (Java 8+) - can provide implementation
    default boolean isHighRisk(double var, double threshold) {
        return var > threshold;
    }
    
    // Static methods (Java 8+) - utility methods
    static double convertToPercent(double decimal) {
        return decimal * 100;
    }
}

// When to use Abstract Classes:
// - Need to share code among related classes
// - Want to provide common fields and methods
// - Need constructors for initialization
// - Have closely related classes with some common implementation

public class MomentumStrategy extends BaseTradingStrategy {
    public MomentumStrategy(double riskTolerance) {
        super("Momentum", riskTolerance);
    }
    
    @Override
    public Decision makeDecision(MarketData data) {
        double momentum = data.calculateMomentum();
        return momentum > 0.05 ? Decision.BUY : Decision.SELL;
    }
    
    @Override
    public double calculatePositionSize(double portfolioValue) {
        return portfolioValue * riskTolerance;
    }
}

// When to use Interfaces:
// - Define a contract for unrelated classes
// - Support multiple inheritance
// - Want to specify behavior without implementation
// - Need to make classes interchangeable

public class MonteCarloRiskCalculator implements RiskCalculator {
    private int simulations = 10000;
    
    @Override
    public double calculateVaR(Portfolio portfolio, double confidence) {
        // Monte Carlo implementation
        return runSimulations(portfolio, confidence);
    }
    
    @Override
    public double calculateCVaR(Portfolio portfolio, double confidence) {
        // Conditional VaR calculation
        return calculateExpectedShortfall(portfolio, confidence);
    }
    
    @Override
    public RiskMetrics analyzePortfolioRisk(List<Position> positions) {
        // Portfolio risk analysis
        return new RiskMetrics(positions);
    }
}`,
    'overloading-vs-overriding': `// Method Overloading vs Overriding in Trading Systems

// METHOD OVERLOADING - Same method name, different parameters
public class OrderProcessor {
    
    // Overloaded methods - different signatures
    public OrderResult processOrder(Order order) {
        return processOrder(order, ExecutionType.IMMEDIATE);
    }
    
    public OrderResult processOrder(Order order, ExecutionType type) {
        return processOrder(order, type, Priority.NORMAL);
    }
    
    public OrderResult processOrder(Order order, ExecutionType type, Priority priority) {
        // Main implementation
        return executeWithPriority(order, type, priority);
    }
    
    // Overloaded with different parameter types
    public void logTrade(String tradeId) {
        log.info("Trade executed: {}", tradeId);
    }
    
    public void logTrade(Trade trade) {
        log.info("Trade executed: {} - {} shares at {}", 
            trade.getId(), trade.getQuantity(), trade.getPrice());
    }
    
    public void logTrade(Trade trade, String additionalInfo) {
        log.info("Trade executed: {} - {} shares at {} - {}", 
            trade.getId(), trade.getQuantity(), trade.getPrice(), additionalInfo);
    }
}

// METHOD OVERRIDING - Same signature, different implementation
public abstract class Order {
    protected String id;
    protected String symbol;
    protected double price;
    protected int quantity;
    
    // Method to be overridden
    public abstract double calculateExecutionPrice(MarketData marketData);
    public abstract boolean isExecutable(MarketData marketData);
    
    // Template method that uses overridden methods
    public final OrderResult execute(MarketData marketData) {
        if (isExecutable(marketData)) {
            double executionPrice = calculateExecutionPrice(marketData);
            return new OrderResult(id, executionPrice, quantity);
        }
        return OrderResult.rejected("Order not executable");
    }
}

// Overriding in MarketOrder
public class MarketOrder extends Order {
    
    @Override
    public double calculateExecutionPrice(MarketData marketData) {
        // Market orders execute at current market price
        return isBuy() ? marketData.getAskPrice() : marketData.getBidPrice();
    }
    
    @Override
    public boolean isExecutable(MarketData marketData) {
        // Market orders are always executable during market hours
        return marketData.isMarketOpen();
    }
}

// Overriding in LimitOrder
public class LimitOrder extends Order {
    private double limitPrice;
    
    @Override
    public double calculateExecutionPrice(MarketData marketData) {
        // Limit orders execute at limit price or better
        double marketPrice = isBuy() ? marketData.getAskPrice() : marketData.getBidPrice();
        return isBuy() ? 
            Math.min(limitPrice, marketPrice) : 
            Math.max(limitPrice, marketPrice);
    }
    
    @Override
    public boolean isExecutable(MarketData marketData) {
        // Limit orders execute only when price condition is met
        double marketPrice = isBuy() ? marketData.getAskPrice() : marketData.getBidPrice();
        return isBuy() ? 
            marketPrice <= limitPrice : 
            marketPrice >= limitPrice;
    }
}

// Key Differences:
// Overloading: Compile-time polymorphism, same class, different parameters
// Overriding: Runtime polymorphism, inheritance hierarchy, same signature`,
    'access-modifiers': `// Access Modifiers in Trading Systems

public class TradingSystem {
    
    // PUBLIC - Accessible from anywhere
    public static final String SYSTEM_NAME = "DarkPool Trading System";
    public String getSystemStatus() {
        return "ACTIVE";
    }
    
    // PRIVATE - Only accessible within this class
    private double totalPnL = 0.0;
    private Map<String, Position> positions = new HashMap<>();
    private void updateInternalMetrics() {
        // Internal calculation logic
    }
    
    // PROTECTED - Accessible within package and by subclasses
    protected Logger logger = LoggerFactory.getLogger(TradingSystem.class);
    protected void logActivity(String message) {
        logger.info(message);
    }
    
    // PACKAGE-PRIVATE (no modifier) - Accessible within same package only
    String configFile = "trading.properties";
    void loadConfiguration() {
        // Package-level utility method
    }
    
    // Demonstrating access control
    public OrderResult submitOrder(Order order) {
        // PUBLIC method - external API
        validateOrder(order);  // Call private method
        logActivity("Order submitted: " + order.getId()); // Call protected method
        updateInternalMetrics(); // Call private method
        return executeOrder(order);
    }
    
    private void validateOrder(Order order) {
        // PRIVATE - only this class can validate orders
        if (order.getQuantity() <= 0) {
            throw new IllegalArgumentException("Invalid quantity");
        }
        if (!positions.containsKey(order.getSymbol())) {
            initializePosition(order.getSymbol());
        }
    }
    
    private void initializePosition(String symbol) {
        // PRIVATE helper method
        positions.put(symbol, new Position(symbol));
    }
}

// Subclass demonstrating protected access
public class AdvancedTradingSystem extends TradingSystem {
    
    public void generateReport() {
        // Can access protected members from parent
        logger.info("Generating advanced trading report");
        logActivity("Report generation started");
        
        // Cannot access private members from parent
        // totalPnL = 1000; // COMPILE ERROR - private in parent
        // updateInternalMetrics(); // COMPILE ERROR - private in parent
    }
    
    @Override
    protected void logActivity(String message) {
        // Can override protected methods
        super.logActivity("[ADVANCED] " + message);
    }
}

// Package-private helper class (same package)
class TradingUtilities {
    
    void processSystemConfiguration(TradingSystem system) {
        // Can access package-private members
        String config = system.configFile; // OK - package-private
        system.loadConfiguration(); // OK - package-private
        
        // Cannot access private members
        // system.totalPnL = 100; // COMPILE ERROR - private
        
        // Can access public members
        String status = system.getSystemStatus(); // OK - public
    }
}

// External class (different package)
public class ExternalMonitor {
    
    public void monitorSystem(TradingSystem system) {
        // Can only access public members
        String status = system.getSystemStatus(); // OK - public
        String name = TradingSystem.SYSTEM_NAME; // OK - public static
        
        // Cannot access other modifiers
        // system.configFile; // COMPILE ERROR - package-private
        // system.logActivity("test"); // COMPILE ERROR - protected
        // system.updateInternalMetrics(); // COMPILE ERROR - private
    }
}

// Access Level Summary:
// public: Anywhere
// protected: Same package + subclasses  
// package-private: Same package only
// private: Same class only`,
    'super-this': `// super and this Keywords in Trading Systems

public class BaseOrder {
    protected String id;
    protected String symbol;
    protected double price;
    protected int quantity;
    
    public BaseOrder(String id, String symbol, double price, int quantity) {
        this.id = id;           // 'this' refers to current instance
        this.symbol = symbol;
        this.price = price;
        this.quantity = quantity;
    }
    
    public void execute() {
        System.out.println("Executing base order: " + this.id);
        this.logExecution(); // 'this' is optional here, but clarifies intent
    }
    
    protected void logExecution() {
        System.out.println("Base order logged");
    }
    
    public BaseOrder getThis() {
        return this; // Return current instance
    }
    
    // Method with parameter shadowing field
    public void updatePrice(double price) {
        this.price = price; // 'this.price' is field, 'price' is parameter
    }
}

public class LimitOrder extends BaseOrder {
    private double limitPrice;
    private boolean isValid;
    
    public LimitOrder(String id, String symbol, double price, int quantity, double limitPrice) {
        super(id, symbol, price, quantity); // Call parent constructor
        this.limitPrice = limitPrice;
        this.isValid = true;
    }
    
    // Alternative constructor using this()
    public LimitOrder(String id, String symbol, double limitPrice) {
        this(id, symbol, limitPrice, 100, limitPrice); // Call another constructor in same class
    }
    
    @Override
    public void execute() {
        System.out.println("Executing limit order: " + this.id);
        
        // Call parent method first
        super.execute(); // Calls BaseOrder.execute()
        
        // Then add specific logic
        if (this.isExecutable()) {
            this.processLimitOrder();
        }
    }
    
    @Override
    protected void logExecution() {
        super.logExecution(); // Call parent's logging first
        System.out.println("Limit order specific logging"); // Add additional logging
    }
    
    private boolean isExecutable() {
        return this.isValid && this.limitPrice > 0;
    }
    
    private void processLimitOrder() {
        System.out.println("Processing limit order logic");
    }
    
    // Method demonstrating both super and this
    public void updateLimitOrder(double price, double limitPrice) {
        super.updatePrice(price); // Call parent method
        this.limitPrice = limitPrice; // Update current instance field
        this.validateOrder(); // Call current instance method
    }
    
    private void validateOrder() {
        // 'this' refers to current LimitOrder instance
        if (this.limitPrice <= 0) {
            this.isValid = false;
        }
    }
}

public class StopLossOrder extends LimitOrder {
    private double stopPrice;
    private boolean triggered = false;
    
    public StopLossOrder(String id, String symbol, double price, int quantity, 
                        double limitPrice, double stopPrice) {
        super(id, symbol, price, quantity, limitPrice); // Call parent constructor
        this.stopPrice = stopPrice;
    }
    
    @Override
    public void execute() {
        if (!this.triggered) {
            System.out.println("Stop loss not triggered yet");
            return;
        }
        
        System.out.println("Executing stop loss order: " + this.id);
        super.execute(); // Call LimitOrder.execute() which calls BaseOrder.execute()
    }
    
    public void checkTrigger(double currentPrice) {
        if (currentPrice <= this.stopPrice) {
            this.triggered = true;
            this.execute(); // Call current instance method
        }
    }
    
    // Method showing method chaining with 'this'
    public StopLossOrder setStopPrice(double stopPrice) {
        this.stopPrice = stopPrice;
        return this; // Return current instance for method chaining
    }
    
    public StopLossOrder setTriggered(boolean triggered) {
        this.triggered = triggered;
        return this; // Method chaining
    }
}

// Usage example showing method chaining
public class TradingExample {
    public void demonstrateUsage() {
        StopLossOrder order = new StopLossOrder("SL001", "AAPL", 150.0, 100, 149.0, 148.0)
            .setStopPrice(147.0)  // Method chaining using 'this'
            .setTriggered(false);
        
        // 'this' vs 'super' summary:
        // this: refers to current instance, call current class methods/constructors
        // super: refers to parent class, call parent methods/constructors
    }
}

// Key Use Cases:
// this(): Call another constructor in same class
// this.field: Distinguish field from parameter when names collide
// this.method(): Explicitly call current instance method
// super(): Call parent constructor (must be first line)
// super.method(): Call parent class method implementation`
  };

  return (
    <div style={{
      padding: '40px',
      paddingTop: '100px',
      minHeight: '100vh',
      backgroundColor: '#0f172a',
      color: 'white'
    }}>
      <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
        <div style={{ marginBottom: '40px' }}>
          <h1 style={{ fontSize: '36px', fontWeight: 'bold', marginBottom: '16px', color: '#10b981' }}>
            Object-Oriented Programming in Java
          </h1>
          <p style={{ fontSize: '18px', color: '#94a3b8' }}>
            Core OOP principles and design patterns for financial systems
          </p>
        </div>

        <div style={{ display: 'flex', gap: '20px', marginBottom: '40px' }}>
          {['overview', 'definition', 'code', 'patterns', 'best-practices'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              style={{
                padding: '12px 24px',
                backgroundColor: activeTab === tab ? '#10b981' : 'transparent',
                border: `2px solid ${activeTab === tab ? '#10b981' : '#374151'}`,
                borderRadius: '8px',
                color: activeTab === tab ? 'white' : '#9ca3af',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                textTransform: 'capitalize',
                transition: 'all 0.2s'
              }}
            >
              {tab.replace('-', ' ')}
            </button>
          ))}
        </div>

        {activeTab === 'definition' && (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            {selectedConcept ? (
              <>
                <div style={{ marginBottom: '20px' }}>
                  <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px', textTransform: 'capitalize' }}>
                    {selectedConcept.name} Definition
                  </h3>
                </div>
            
            {selectedConcept.id === 'encapsulation' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What is Encapsulation?</h4>
                <p style={{ marginBottom: '16px' }}>
                  Encapsulation is the bundling of data (attributes) and methods (functions) that operate on that data within a single unit (class). It restricts direct access to some of the object's components, which is a means of preventing accidental interference and misuse.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why is it needed in Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>Protects sensitive financial data from unauthorized access</li>
                  <li>Ensures data integrity by controlling how account balances and positions are modified</li>
                  <li>Prevents external code from putting the system in an invalid state</li>
                  <li>Makes code more maintainable and less prone to bugs</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to use it?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Declare class fields as <code style={{ color: '#60a5fa' }}>private</code></li>
                  <li>Provide <code style={{ color: '#60a5fa' }}>public</code> getter and setter methods with validation</li>
                  <li>Hide internal implementation details from external classes</li>
                  <li>Use access modifiers appropriately (private, protected, public)</li>
                </ul>
              </div>
            )}
            
            {selectedConcept.id === 'inheritance' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What is Inheritance?</h4>
                <p style={{ marginBottom: '16px' }}>
                  Inheritance is a mechanism where a new class (subclass) inherits properties and behaviors from an existing class (superclass). It enables code reuse and establishes a hierarchical relationship between classes.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why is it needed in Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>Create specialized order types (MarketOrder, LimitOrder) from a base Order class</li>
                  <li>Reduce code duplication across similar financial instruments</li>
                  <li>Enable polymorphic behavior for different trading strategies</li>
                  <li>Maintain consistency across related classes</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to use it?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Use <code style={{ color: '#60a5fa' }}>extends</code> keyword to inherit from a superclass</li>
                  <li>Override methods using <code style={{ color: '#60a5fa' }}>@Override</code> annotation</li>
                  <li>Call superclass methods with <code style={{ color: '#60a5fa' }}>super</code> keyword</li>
                  <li>Follow the Liskov Substitution Principle</li>
                </ul>
              </div>
            )}
            
            {selectedConcept.id === 'polymorphism' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What is Polymorphism?</h4>
                <p style={{ marginBottom: '16px' }}>
                  Polymorphism allows objects of different classes to be treated as objects of a common base class. It enables a single interface to represent different underlying forms (data types). In Java, this is achieved through method overloading and overriding.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why is it needed in Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>Execute different order types using the same interface</li>
                  <li>Implement multiple trading strategies with a common interface</li>
                  <li>Handle various financial instruments uniformly</li>
                  <li>Make code more flexible and extensible</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to use it?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Define common interfaces or abstract base classes</li>
                  <li>Override methods in subclasses with specific implementations</li>
                  <li>Use interface references to point to concrete implementations</li>
                  <li>Leverage runtime method resolution for dynamic behavior</li>
                </ul>
              </div>
            )}
            
            {selectedConcept.id === 'abstraction' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What is Abstraction?</h4>
                <p style={{ marginBottom: '16px' }}>
                  Abstraction is the process of hiding implementation details while showing only essential features of an object. It focuses on what an object does rather than how it does it, achieved through abstract classes and interfaces.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why is it needed in Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>Define contracts for risk calculation without exposing implementation</li>
                  <li>Create pluggable components for different market data feeds</li>
                  <li>Standardize interfaces across different exchanges</li>
                  <li>Enable easier testing with mock implementations</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to use it?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Define interfaces with method signatures but no implementation</li>
                  <li>Create abstract classes with some implemented and some abstract methods</li>
                  <li>Implement interfaces in concrete classes</li>
                  <li>Program to interfaces, not implementations</li>
                </ul>
              </div>
            )}
            
            {selectedConcept.id === 'composition' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What is Composition?</h4>
                <p style={{ marginBottom: '16px' }}>
                  Composition is a design principle where classes are composed of other classes rather than inheriting from them. It represents a "has-a" relationship where one class contains instances of other classes as member variables.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why is it needed in Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>Build complex trading systems from smaller, focused components</li>
                  <li>Avoid deep inheritance hierarchies that become hard to maintain</li>
                  <li>Enable better code reuse and flexibility</li>
                  <li>Make systems more modular and testable</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to use it?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Include instances of other classes as member variables</li>
                  <li>Delegate functionality to composed objects</li>
                  <li>Favor composition over inheritance when modeling "has-a" relationships</li>
                  <li>Use dependency injection to manage composed objects</li>
                </ul>
              </div>
            )}
            
            {selectedConcept.id === 'abstract-vs-interface' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What are Abstract Classes vs Interfaces?</h4>
                <p style={{ marginBottom: '16px' }}>
                  Abstract classes provide partial implementation and can have constructors, fields, and concrete methods. Interfaces define contracts with method signatures (and default/static methods in Java 8+) but no instance fields or constructors.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>When to use each in Trading Systems?</h4>
                <div style={{ marginBottom: '16px' }}>
                  <strong style={{ color: '#60a5fa' }}>Abstract Classes:</strong>
                  <ul style={{ paddingLeft: '20px', marginTop: '8px' }}>
                    <li>Base trading strategies with common risk management logic</li>
                    <li>Order types that share validation and execution patterns</li>
                    <li>When you need constructors for initialization</li>
                    <li>Classes that are closely related in the inheritance hierarchy</li>
                  </ul>
                  <strong style={{ color: '#f59e0b', marginTop: '12px', display: 'block' }}>Interfaces:</strong>
                  <ul style={{ paddingLeft: '20px', marginTop: '8px' }}>
                    <li>Risk calculators that can have multiple implementations</li>
                    <li>Market data providers from different exchanges</li>
                    <li>When you need multiple inheritance capability</li>
                    <li>Defining contracts for plugin-style architectures</li>
                  </ul>
                </div>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to choose between them?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Use abstract classes for "is-a" relationships with shared implementation</li>
                  <li>Use interfaces for "can-do" contracts and multiple inheritance</li>
                  <li>Consider using both: interface for contract, abstract class for base implementation</li>
                  <li>Prefer interfaces for better flexibility and testability</li>
                </ul>
              </div>
            )}
            
            {selectedConcept.id === 'overloading-vs-overriding' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What are Method Overloading and Overriding?</h4>
                <p style={{ marginBottom: '16px' }}>
                  Method overloading provides multiple methods with the same name but different parameters (compile-time polymorphism). Method overriding allows subclasses to provide specific implementations of parent class methods (runtime polymorphism).
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why are they useful in Trading Systems?</h4>
                <div style={{ marginBottom: '16px' }}>
                  <strong style={{ color: '#60a5fa' }}>Overloading:</strong>
                  <ul style={{ paddingLeft: '20px', marginTop: '8px' }}>
                    <li>Process orders with different parameter combinations</li>
                    <li>Provide convenient API methods with default values</li>
                    <li>Handle different data types (trade ID, trade object, etc.)</li>
                    <li>Create flexible constructors for order creation</li>
                  </ul>
                  <strong style={{ color: '#f59e0b', marginTop: '12px', display: 'block' }}>Overriding:</strong>
                  <ul style={{ paddingLeft: '20px', marginTop: '8px' }}>
                    <li>Different execution logic for market vs limit orders</li>
                    <li>Specialized risk calculations for different instruments</li>
                    <li>Custom validation rules for different order types</li>
                    <li>Specific logging or auditing for different components</li>
                  </ul>
                </div>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to implement them correctly?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Overloading: Vary parameter types, number, or order</li>
                  <li>Overriding: Use @Override annotation and maintain method signature</li>
                  <li>Follow Liskov Substitution Principle for overriding</li>
                  <li>Use overloading for convenience, overriding for behavior specialization</li>
                </ul>
              </div>
            )}
            
            {selectedConcept.id === 'access-modifiers' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What are Access Modifiers?</h4>
                <p style={{ marginBottom: '16px' }}>
                  Access modifiers control the visibility and accessibility of classes, methods, and fields. Java provides four levels: public (everywhere), protected (package + subclasses), package-private (same package), and private (same class only).
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why are they crucial for Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>Protect sensitive financial data from unauthorized access</li>
                  <li>Prevent external modification of critical trading algorithms</li>
                  <li>Create clean APIs by hiding implementation details</li>
                  <li>Enable safe inheritance hierarchies</li>
                  <li>Support modular design and maintainability</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to use them effectively?</h4>
                <div style={{ paddingLeft: '20px' }}>
                  <div style={{ marginBottom: '8px' }}><strong style={{ color: '#ef4444' }}>private:</strong> Internal implementation, sensitive data</div>
                  <div style={{ marginBottom: '8px' }}><strong style={{ color: '#f59e0b' }}>package-private:</strong> Helper classes, internal utilities</div>
                  <div style={{ marginBottom: '8px' }}><strong style={{ color: '#3b82f6' }}>protected:</strong> Extensible framework methods</div>
                  <div><strong style={{ color: '#10b981' }}>public:</strong> External API, service interfaces</div>
                </div>
              </div>
            )}
            
            {selectedConcept.id === 'super-this' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What are super and this Keywords?</h4>
                <p style={{ marginBottom: '16px' }}>
                  The 'this' keyword refers to the current instance and is used to access instance variables and methods or call other constructors in the same class. The 'super' keyword refers to the parent class and is used to access parent methods, constructors, and variables.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why are they important in Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>Distinguish between parameters and instance variables</li>
                  <li>Chain constructors for different order initialization patterns</li>
                  <li>Call parent class logic while adding specialized behavior</li>
                  <li>Enable method chaining for fluent APIs</li>
                  <li>Maintain clear inheritance relationships in complex hierarchies</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to use them properly?</h4>
                <div style={{ paddingLeft: '20px' }}>
                  <div style={{ marginBottom: '8px' }}><strong style={{ color: '#60a5fa' }}>this():</strong> Call other constructors in same class</div>
                  <div style={{ marginBottom: '8px' }}><strong style={{ color: '#60a5fa' }}>this.field:</strong> Access instance variable when parameter has same name</div>
                  <div style={{ marginBottom: '8px' }}><strong style={{ color: '#f59e0b' }}>super():</strong> Call parent constructor (must be first line)</div>
                  <div><strong style={{ color: '#f59e0b' }}>super.method():</strong> Call parent implementation before/after custom logic</div>
                </div>
              </div>
            )}
              </>
            ) : (
              <div style={{ textAlign: 'center', color: '#9ca3af', padding: '40px' }}>
                <p>Select an OOP concept from the overview to see its definition</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'overview' && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
            {/* Top Horizontal Bar with OOP Concepts */}
            <div style={{ 
              padding: '16px',
              backgroundColor: 'rgba(31, 41, 55, 0.5)',
              borderRadius: '12px',
              border: '1px solid #374151'
            }}>
              <div style={{ display: 'flex', gap: '12px', alignItems: 'center', justifyContent: 'center' }}>
                {oopConcepts.slice(0, 5).map((concept) => (
                  <div
                    key={concept.id}
                    onClick={() => {
                      console.log('Selecting concept:', concept);
                      setSelectedConcept(concept);
                    }}
                    style={{
                      padding: '10px 16px',
                      backgroundColor: selectedConcept?.id === concept.id ? 
                        'rgba(16, 185, 129, 0.2)' : 'rgba(31, 41, 55, 0.3)',
                      border: `2px solid ${selectedConcept?.id === concept.id ? concept.color : '#374151'}`,
                      borderRadius: '8px',
                      cursor: 'pointer',
                      transition: 'all 0.2s',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      minWidth: '150px'
                    }}
                    onMouseEnter={(e) => {
                      if (selectedConcept?.id !== concept.id) {
                        e.currentTarget.style.backgroundColor = 'rgba(31, 41, 55, 0.7)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (selectedConcept?.id !== concept.id) {
                        e.currentTarget.style.backgroundColor = 'rgba(31, 41, 55, 0.3)';
                      }
                    }}
                  >
                    <div style={{ color: concept.color }}>{concept.icon}</div>
                    <div style={{ fontSize: '13px', fontWeight: '600', color: selectedConcept?.id === concept.id ? concept.color : '#e2e8f0' }}>
                      {concept.name}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Main UML Diagram */}
            <div style={{ display: 'flex', justifyContent: 'flex-start', padding: '0 20px' }}>
              <div style={{ 
              backgroundColor: 'rgba(31, 41, 55, 0.5)',
              borderRadius: '12px',
              padding: '40px',
              border: '1px solid #374151',
              maxWidth: '1000px',
              width: '100%'
            }}>
              <h3 style={{ 
                fontSize: '24px', 
                fontWeight: 'bold', 
                marginBottom: '30px', 
                color: '#10b981',
                textAlign: 'center' 
              }}>
                Dark Pool Trading System - UML Class Diagram
              </h3>
              
              {/* UML Diagram */}
              <div style={{ 
                backgroundColor: '#1e293b',
                borderRadius: '8px',
                padding: '30px',
                border: '1px solid #374151',
                fontFamily: 'monospace',
                fontSize: '12px',
                color: '#e2e8f0',
                overflowX: 'auto',
                minHeight: '500px',
                position: 'relative'
              }}>
                
                {/* TradingAccount Class - Encapsulation Example */}
                <div 
                  onClick={() => setSelectedConcept(oopConcepts.find(c => c.id === 'encapsulation'))}
                  style={{
                    position: 'absolute',
                    top: '30px',
                    left: '50px',
                    width: '220px',
                    height: '140px',
                    border: `3px solid ${selectedConcept?.id === 'encapsulation' ? '#ef4444' : '#374151'}`,
                    backgroundColor: selectedConcept?.id === 'encapsulation' ? 'rgba(239, 68, 68, 0.25)' : 'rgba(55, 65, 81, 0.3)',
                    borderRadius: '6px',
                    padding: '10px',
                    cursor: 'pointer',
                    transition: 'all 0.2s',
                    boxShadow: selectedConcept?.id === 'encapsulation' ? '0 0 20px rgba(239, 68, 68, 0.5)' : 'none'
                  }}
                >
                  <div style={{ fontWeight: 'bold', color: '#ef4444', marginBottom: '8px' }}>TradingAccount</div>
                  <div style={{ borderBottom: '1px solid #374151', marginBottom: '8px' }}></div>
                  <div style={{ fontSize: '10px', color: '#9ca3af', marginBottom: '6px' }}>- balance: double</div>
                  <div style={{ fontSize: '10px', color: '#9ca3af', marginBottom: '6px' }}>- accountId: String</div>
                  <div style={{ fontSize: '10px', color: '#9ca3af', marginBottom: '6px' }}>- trades: List&lt;Trade&gt;</div>
                  <div style={{ borderBottom: '1px solid #374151', marginBottom: '8px' }}></div>
                  <div style={{ fontSize: '10px', color: '#60a5fa', marginBottom: '4px' }}>+ getBalance(): double</div>
                  <div style={{ fontSize: '10px', color: '#60a5fa', marginBottom: '4px' }}>+ deposit(amount: double)</div>
                  <div style={{ fontSize: '10px', color: '#60a5fa' }}>+ executeTrade(trade: Trade)</div>
                </div>

                {/* Order Abstract Class - Abstraction & Inheritance */}
                <div 
                  onClick={() => setSelectedConcept(oopConcepts.find(c => c.id === 'abstraction'))}
                  style={{
                    position: 'absolute',
                    top: '30px',
                    left: '320px',
                    width: '200px',
                    height: '120px',
                    border: `3px solid ${(selectedConcept?.id === 'abstraction' || selectedConcept?.id === 'inheritance') ? '#f59e0b' : '#374151'}`,
                    backgroundColor: (selectedConcept?.id === 'abstraction' || selectedConcept?.id === 'inheritance') ? 'rgba(245, 158, 11, 0.25)' : 'rgba(55, 65, 81, 0.3)',
                    borderRadius: '6px',
                    padding: '10px',
                    cursor: 'pointer',
                    transition: 'all 0.2s',
                    boxShadow: (selectedConcept?.id === 'abstraction' || selectedConcept?.id === 'inheritance') ? '0 0 20px rgba(245, 158, 11, 0.5)' : 'none'
                  }}
                >
                  <div style={{ fontWeight: 'bold', color: '#f59e0b', marginBottom: '8px', fontStyle: 'italic' }}>«abstract» Order</div>
                  <div style={{ borderBottom: '1px solid #374151', marginBottom: '8px' }}></div>
                  <div style={{ fontSize: '10px', color: '#9ca3af', marginBottom: '6px' }}># orderId: String</div>
                  <div style={{ fontSize: '10px', color: '#9ca3af', marginBottom: '6px' }}># price: double</div>
                  <div style={{ fontSize: '10px', color: '#9ca3af', marginBottom: '6px' }}># quantity: int</div>
                  <div style={{ borderBottom: '1px solid #374151', marginBottom: '8px' }}></div>
                  <div style={{ fontSize: '10px', color: '#f59e0b', marginBottom: '4px', fontStyle: 'italic' }}>+ execute(): void</div>
                  <div style={{ fontSize: '10px', color: '#f59e0b', fontStyle: 'italic' }}>+ calculateValue(): double</div>
                </div>

                {/* MarketOrder - Inheritance */}
                <div 
                  onClick={() => setSelectedConcept(oopConcepts.find(c => c.id === 'inheritance'))}
                  style={{
                    position: 'absolute',
                    top: '200px',
                    left: '250px',
                    width: '160px',
                    height: '80px',
                    border: `3px solid ${selectedConcept?.id === 'inheritance' ? '#3b82f6' : '#374151'}`,
                    backgroundColor: selectedConcept?.id === 'inheritance' ? 'rgba(59, 130, 246, 0.25)' : 'rgba(55, 65, 81, 0.3)',
                    borderRadius: '6px',
                    padding: '10px',
                    cursor: 'pointer',
                    transition: 'all 0.2s',
                    boxShadow: selectedConcept?.id === 'inheritance' ? '0 0 20px rgba(59, 130, 246, 0.5)' : 'none'
                  }}
                >
                  <div style={{ fontWeight: 'bold', color: '#3b82f6', marginBottom: '8px' }}>MarketOrder</div>
                  <div style={{ borderBottom: '1px solid #374151', marginBottom: '8px' }}></div>
                  <div style={{ fontSize: '10px', color: '#60a5fa', marginBottom: '4px' }}>+ execute(): void</div>
                  <div style={{ fontSize: '10px', color: '#60a5fa' }}>+ calculateValue(): double</div>
                </div>

                {/* LimitOrder - Inheritance */}
                <div 
                  onClick={() => setSelectedConcept(oopConcepts.find(c => c.id === 'inheritance'))}
                  style={{
                    position: 'absolute',
                    top: '200px',
                    left: '430px',
                    width: '160px',
                    height: '100px',
                    border: `3px solid ${selectedConcept?.id === 'inheritance' ? '#3b82f6' : '#374151'}`,
                    backgroundColor: selectedConcept?.id === 'inheritance' ? 'rgba(59, 130, 246, 0.25)' : 'rgba(55, 65, 81, 0.3)',
                    borderRadius: '6px',
                    padding: '10px',
                    cursor: 'pointer',
                    transition: 'all 0.2s',
                    boxShadow: selectedConcept?.id === 'inheritance' ? '0 0 20px rgba(59, 130, 246, 0.5)' : 'none'
                  }}
                >
                  <div style={{ fontWeight: 'bold', color: '#3b82f6', marginBottom: '8px' }}>LimitOrder</div>
                  <div style={{ borderBottom: '1px solid #374151', marginBottom: '8px' }}></div>
                  <div style={{ fontSize: '10px', color: '#9ca3af', marginBottom: '6px' }}>- limitPrice: double</div>
                  <div style={{ borderBottom: '1px solid #374151', marginBottom: '8px' }}></div>
                  <div style={{ fontSize: '10px', color: '#60a5fa', marginBottom: '4px' }}>+ execute(): void</div>
                  <div style={{ fontSize: '10px', color: '#60a5fa' }}>+ calculateValue(): double</div>
                </div>

                {/* TradingStrategy Interface - Polymorphism */}
                <div 
                  onClick={() => setSelectedConcept(oopConcepts.find(c => c.id === 'polymorphism'))}
                  style={{
                    position: 'absolute',
                    top: '30px',
                    left: '650px',
                    width: '220px',
                    height: '100px',
                    border: `3px solid ${selectedConcept?.id === 'polymorphism' ? '#10b981' : '#374151'}`,
                    backgroundColor: selectedConcept?.id === 'polymorphism' ? 'rgba(16, 185, 129, 0.25)' : 'rgba(55, 65, 81, 0.3)',
                    borderRadius: '6px',
                    padding: '10px',
                    cursor: 'pointer',
                    transition: 'all 0.2s',
                    boxShadow: selectedConcept?.id === 'polymorphism' ? '0 0 20px rgba(16, 185, 129, 0.5)' : 'none'
                  }}
                >
                  <div style={{ fontWeight: 'bold', color: '#10b981', marginBottom: '8px' }}>«interface» TradingStrategy</div>
                  <div style={{ borderBottom: '1px solid #374151', marginBottom: '8px' }}></div>
                  <div style={{ fontSize: '10px', color: '#10b981', marginBottom: '4px' }}>+ calculateRisk(portfolio): double</div>
                  <div style={{ fontSize: '10px', color: '#10b981' }}>+ generateOrders(data): List&lt;Order&gt;</div>
                </div>

                {/* ConservativeStrategy - Polymorphism Implementation */}
                <div 
                  onClick={() => setSelectedConcept(oopConcepts.find(c => c.id === 'polymorphism'))}
                  style={{
                    position: 'absolute',
                    top: '170px',
                    left: '620px',
                    width: '140px',
                    height: '80px',
                    border: `3px solid ${selectedConcept?.id === 'polymorphism' ? '#10b981' : '#374151'}`,
                    backgroundColor: selectedConcept?.id === 'polymorphism' ? 'rgba(16, 185, 129, 0.25)' : 'rgba(55, 65, 81, 0.3)',
                    borderRadius: '6px',
                    padding: '10px',
                    cursor: 'pointer',
                    transition: 'all 0.2s',
                    boxShadow: selectedConcept?.id === 'polymorphism' ? '0 0 20px rgba(16, 185, 129, 0.5)' : 'none'
                  }}
                >
                  <div style={{ fontWeight: 'bold', color: '#10b981', marginBottom: '8px' }}>ConservativeStrategy</div>
                  <div style={{ borderBottom: '1px solid #374151', marginBottom: '8px' }}></div>
                  <div style={{ fontSize: '9px', color: '#60a5fa', marginBottom: '4px' }}>+ calculateRisk(): double</div>
                  <div style={{ fontSize: '9px', color: '#60a5fa' }}>+ generateOrders(): List</div>
                </div>

                {/* AggressiveStrategy - Polymorphism Implementation */}
                <div 
                  onClick={() => setSelectedConcept(oopConcepts.find(c => c.id === 'polymorphism'))}
                  style={{
                    position: 'absolute',
                    top: '170px',
                    left: '780px',
                    width: '140px',
                    height: '80px',
                    border: `3px solid ${selectedConcept?.id === 'polymorphism' ? '#10b981' : '#374151'}`,
                    backgroundColor: selectedConcept?.id === 'polymorphism' ? 'rgba(16, 185, 129, 0.25)' : 'rgba(55, 65, 81, 0.3)',
                    borderRadius: '6px',
                    padding: '10px',
                    cursor: 'pointer',
                    transition: 'all 0.2s',
                    boxShadow: selectedConcept?.id === 'polymorphism' ? '0 0 20px rgba(16, 185, 129, 0.5)' : 'none'
                  }}
                >
                  <div style={{ fontWeight: 'bold', color: '#10b981', marginBottom: '8px' }}>AggressiveStrategy</div>
                  <div style={{ borderBottom: '1px solid #374151', marginBottom: '8px' }}></div>
                  <div style={{ fontSize: '9px', color: '#60a5fa', marginBottom: '4px' }}>+ calculateRisk(): double</div>
                  <div style={{ fontSize: '9px', color: '#60a5fa' }}>+ generateOrders(): List</div>
                </div>

                {/* TradingSystem - Composition */}
                <div 
                  onClick={() => setSelectedConcept(oopConcepts.find(c => c.id === 'composition'))}
                  style={{
                    position: 'absolute',
                    top: '330px',
                    left: '50px',
                    width: '280px',
                    height: '140px',
                    border: `3px solid ${selectedConcept?.id === 'composition' ? '#8b5cf6' : '#374151'}`,
                    backgroundColor: selectedConcept?.id === 'composition' ? 'rgba(139, 92, 246, 0.25)' : 'rgba(55, 65, 81, 0.3)',
                    borderRadius: '6px',
                    padding: '10px',
                    cursor: 'pointer',
                    transition: 'all 0.2s',
                    boxShadow: selectedConcept?.id === 'composition' ? '0 0 20px rgba(139, 92, 246, 0.5)' : 'none'
                  }}
                >
                  <div style={{ fontWeight: 'bold', color: '#8b5cf6', marginBottom: '8px' }}>TradingSystem</div>
                  <div style={{ borderBottom: '1px solid #374151', marginBottom: '8px' }}></div>
                  <div style={{ fontSize: '10px', color: '#9ca3af', marginBottom: '4px' }}>- gateway: OrderGateway</div>
                  <div style={{ fontSize: '10px', color: '#9ca3af', marginBottom: '4px' }}>- riskEngine: RiskEngine</div>
                  <div style={{ fontSize: '10px', color: '#9ca3af', marginBottom: '4px' }}>- matchingEngine: MatchingEngine</div>
                  <div style={{ fontSize: '10px', color: '#9ca3af', marginBottom: '6px' }}>- positionManager: PositionManager</div>
                  <div style={{ borderBottom: '1px solid #374151', marginBottom: '8px' }}></div>
                  <div style={{ fontSize: '10px', color: '#60a5fa' }}>+ submitOrder(order): OrderResponse</div>
                </div>

                {/* Inheritance Arrows */}
                {selectedConcept?.id === 'inheritance' && (
                  <>
                    {/* Arrow from Order to MarketOrder */}
                    <div style={{
                      position: 'absolute',
                      top: '150px',
                      left: '380px',
                      width: '2px',
                      height: '50px',
                      backgroundColor: '#3b82f6',
                      transform: 'rotate(-20deg)',
                      transformOrigin: 'top'
                    }}></div>
                    <div style={{
                      position: 'absolute',
                      top: '195px',
                      left: '326px',
                      width: '0',
                      height: '0',
                      borderLeft: '8px solid transparent',
                      borderRight: '8px solid transparent',
                      borderBottom: '12px solid #3b82f6',
                      transform: 'rotate(-20deg)'
                    }}></div>

                    {/* Arrow from Order to LimitOrder */}
                    <div style={{
                      position: 'absolute',
                      top: '150px',
                      left: '460px',
                      width: '2px',
                      height: '50px',
                      backgroundColor: '#3b82f6',
                      transform: 'rotate(20deg)',
                      transformOrigin: 'top'
                    }}></div>
                    <div style={{
                      position: 'absolute',
                      top: '195px',
                      left: '506px',
                      width: '0',
                      height: '0',
                      borderLeft: '8px solid transparent',
                      borderRight: '8px solid transparent',
                      borderBottom: '12px solid #3b82f6',
                      transform: 'rotate(20deg)'
                    }}></div>
                  </>
                )}

                {/* Implementation Arrows for Polymorphism */}
                {selectedConcept?.id === 'polymorphism' && (
                  <>
                    {/* Dashed arrow from TradingStrategy to ConservativeStrategy */}
                    <div style={{
                      position: 'absolute',
                      top: '130px',
                      left: '700px',
                      width: '2px',
                      height: '40px',
                      backgroundColor: '#10b981',
                      borderLeft: '2px dashed #10b981',
                      transform: 'rotate(-20deg)',
                      transformOrigin: 'top'
                    }}></div>

                    {/* Dashed arrow from TradingStrategy to AggressiveStrategy */}
                    <div style={{
                      position: 'absolute',
                      top: '130px',
                      left: '820px',
                      width: '2px',
                      height: '40px',
                      backgroundColor: '#10b981',
                      borderLeft: '2px dashed #10b981',
                      transform: 'rotate(20deg)',
                      transformOrigin: 'top'
                    }}></div>
                  </>
                )}

                {/* Composition Arrows */}
                {selectedConcept?.id === 'composition' && (
                  <>
                    <div style={{
                      position: 'absolute',
                      top: '400px',
                      left: '330px',
                      fontSize: '10px',
                      color: '#8b5cf6'
                    }}>
                      ◆ has-a relationships
                    </div>
                  </>
                )}

                {/* Legend */}
                <div style={{
                  position: 'absolute',
                  top: '500px',
                  left: '50px',
                  fontSize: '10px',
                  color: '#9ca3af'
                }}>
                  <div style={{ marginBottom: '4px', color: '#10b981' }}>💡 Click on any class to explore the OOP concept it demonstrates</div>
                  <div style={{ marginBottom: '4px' }}>- : private fields</div>
                  <div style={{ marginBottom: '4px' }}># : protected fields</div>
                  <div style={{ marginBottom: '4px' }}>+ : public methods</div>
                  <div>«interface» : interface, «abstract» : abstract class</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'code' && (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            {selectedConcept ? (
              <>
                <div style={{ marginBottom: '20px' }}>
                  <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px', textTransform: 'capitalize' }}>
                    {selectedConcept.id.replace('-', ' ')} Example
                  </h3>
                </div>
                <SyntaxHighlighter
                  language="java"
                  style={oneDark}
                  customStyle={{
                    backgroundColor: '#1e293b',
                    padding: '20px',
                    borderRadius: '8px',
                    fontSize: '14px',
                    lineHeight: '1.6'
                  }}
                >
                  {codeExamples[selectedConcept.id]}
                </SyntaxHighlighter>
              </>
            ) : (
              <div style={{ textAlign: 'center', color: '#9ca3af', padding: '40px' }}>
                <p>Select an OOP concept from the overview to see code examples</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'patterns' && (
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
            <div style={{
              padding: '24px',
              backgroundColor: 'rgba(31, 41, 55, 0.5)',
              borderRadius: '12px',
              border: '1px solid #374151'
            }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px', color: '#10b981' }}>
                SOLID Principles
              </h3>
              <ul style={{ listStyle: 'none', padding: 0 }}>
                {[
                  'Single Responsibility Principle',
                  'Open/Closed Principle',
                  'Liskov Substitution Principle',
                  'Interface Segregation Principle',
                  'Dependency Inversion Principle'
                ].map((principle, index) => (
                  <li key={index} style={{ padding: '8px 0', color: '#94a3b8', fontSize: '14px' }}>
                    <span style={{ color: '#10b981', marginRight: '8px' }}>•</span>
                    {principle}
                  </li>
                ))}
              </ul>
            </div>
            <div style={{
              padding: '24px',
              backgroundColor: 'rgba(31, 41, 55, 0.5)',
              borderRadius: '12px',
              border: '1px solid #374151'
            }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px', color: '#3b82f6' }}>
                Common Design Patterns
              </h3>
              <ul style={{ listStyle: 'none', padding: 0 }}>
                {[
                  'Singleton - Order Management System',
                  'Factory - Order Type Creation',
                  'Observer - Market Data Updates',
                  'Strategy - Trading Algorithms',
                  'Builder - Complex Order Construction'
                ].map((pattern, index) => (
                  <li key={index} style={{ padding: '8px 0', color: '#94a3b8', fontSize: '14px' }}>
                    <span style={{ color: '#3b82f6', marginRight: '8px' }}>•</span>
                    {pattern}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {activeTab === 'best-practices' && (
          <div style={{
            padding: '24px',
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            border: '1px solid #374151'
          }}>
            <h3 style={{ fontSize: '24px', fontWeight: '600', marginBottom: '20px', color: '#10b981' }}>
              OOP Best Practices for Trading Systems
            </h3>
            <div style={{ display: 'grid', gap: '16px' }}>
              {[
                {
                  title: 'Favor Composition Over Inheritance',
                  description: 'Use composition to build flexible trading strategies rather than deep inheritance hierarchies'
                },
                {
                  title: 'Program to Interfaces',
                  description: 'Define interfaces for key components like RiskCalculator, OrderRouter, and PriceEngine'
                },
                {
                  title: 'Immutable Objects for Thread Safety',
                  description: 'Make Order, Trade, and Position objects immutable to ensure thread safety in concurrent systems'
                },
                {
                  title: 'Domain-Driven Design',
                  description: 'Model your classes based on trading domain concepts like Order, Execution, Position, and Portfolio'
                },
                {
                  title: 'Separation of Concerns',
                  description: 'Keep business logic, data access, and presentation layers separate for maintainability'
                }
              ].map((practice, index) => (
                <div key={index} style={{
                  padding: '16px',
                  backgroundColor: '#1e293b',
                  borderRadius: '8px',
                  borderLeft: '4px solid #10b981'
                }}>
                  <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '8px' }}>
                    {practice.title}
                  </h4>
                  <p style={{ color: '#94a3b8', fontSize: '14px' }}>
                    {practice.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      
      {/* Details Panel - Matching Dark Pool Style */}
      {selectedConcept && (
        <div style={{
          position: 'fixed',
          right: 0,
          top: 0,
          width: '500px',
          height: '100vh',
          backgroundColor: 'rgba(17, 24, 39, 0.95)',
          backdropFilter: 'blur(12px)',
          borderLeft: '1px solid #374151',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          zIndex: 1000
        }}>
          {/* Panel Header */}
          <div style={{
            padding: '20px',
            borderBottom: '1px solid #374151',
            background: 'linear-gradient(135deg, #1f2937 0%, #111827 100%)'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start' }}>
              <div>
                <h2 style={{ fontSize: '18px', fontWeight: 'bold', color: '#fbbf24', margin: '0 0 8px 0' }}>
                  {selectedConcept.name}
                </h2>
                <div style={{ fontSize: '12px', color: '#9ca3af' }}>
                  {selectedConcept.description}
                </div>
              </div>
              <button
                onClick={() => setSelectedConcept(null)}
                style={{
                  background: 'transparent',
                  border: 'none',
                  color: '#9ca3af',
                  fontSize: '24px',
                  cursor: 'pointer',
                  padding: 0,
                  lineHeight: 1
                }}
              >
                ×
              </button>
            </div>
          </div>

          {/* Tabs */}
          <div style={{ display: 'flex', borderBottom: '1px solid #374151' }}>
            {['definition', 'code', 'usage', 'best-practices'].map(tab => (
              <button
                key={tab}
                onClick={() => setDetailsTab(tab)}
                style={{
                  flex: 1,
                  padding: '12px',
                  background: detailsTab === tab ? '#1f2937' : 'transparent',
                  border: 'none',
                  color: detailsTab === tab ? '#fbbf24' : '#9ca3af',
                  fontSize: '12px',
                  fontWeight: '600',
                  textTransform: 'capitalize',
                  cursor: 'pointer'
                }}
              >
                {tab.replace('-', ' ')}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div style={{ flex: 1, padding: '20px', overflowY: 'auto' }}>
            {detailsTab === 'definition' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  What is {selectedConcept.name}?
                </h3>
                <div style={{ color: '#d1d5db', fontSize: '13px', lineHeight: '1.8' }}>
                  {selectedConcept.id === 'encapsulation' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Encapsulation is the bundling of data (attributes) and methods (functions) that operate on that data within a single unit (class). It restricts direct access to some of the object's components, which is a means of preventing accidental interference and misuse.
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Key Benefits:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>Protects sensitive financial data from unauthorized access</li>
                        <li>Ensures data integrity by controlling how account balances and positions are modified</li>
                        <li>Prevents external code from putting the system in an invalid state</li>
                        <li>Makes code more maintainable and less prone to bugs</li>
                      </ul>
                    </>
                  )}
                  {selectedConcept.id === 'inheritance' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Inheritance is a mechanism where a new class (subclass) inherits properties and behaviors from an existing class (superclass). It enables code reuse and establishes a hierarchical relationship between classes.
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Key Benefits:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>Create specialized order types (MarketOrder, LimitOrder) from a base Order class</li>
                        <li>Reduce code duplication across similar financial instruments</li>
                        <li>Enable polymorphic behavior for different trading strategies</li>
                        <li>Maintain consistency across related classes</li>
                      </ul>
                    </>
                  )}
                  {selectedConcept.id === 'polymorphism' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Polymorphism allows objects of different classes to be treated as objects of a common base class. It enables a single interface to represent different underlying forms (data types).
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Key Benefits:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>Execute different order types using the same interface</li>
                        <li>Implement multiple trading strategies with a common interface</li>
                        <li>Handle various financial instruments uniformly</li>
                        <li>Make code more flexible and extensible</li>
                      </ul>
                    </>
                  )}
                  {selectedConcept.id === 'abstraction' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Abstraction is the process of hiding implementation details while showing only essential features of an object. It focuses on what an object does rather than how it does it.
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Key Benefits:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>Define contracts for risk calculation without exposing implementation</li>
                        <li>Create pluggable components for different market data feeds</li>
                        <li>Standardize interfaces across different exchanges</li>
                        <li>Enable easier testing with mock implementations</li>
                      </ul>
                    </>
                  )}
                </div>
              </div>
            )}

            {detailsTab === 'code' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  Implementation Example
                </h3>
                <div style={{
                  background: '#0f172a',
                  padding: '16px',
                  borderRadius: '8px',
                  maxHeight: '600px',
                  overflow: 'auto'
                }}>
                  <SyntaxHighlighter
                    language="java"
                    style={oneDark}
                    customStyle={{
                      background: 'transparent',
                      padding: 0,
                      margin: 0,
                      fontSize: '12px',
                      lineHeight: '1.5'
                    }}
                  >
                    {codeExamples[selectedConcept.id] || '// Code example not available'}
                  </SyntaxHighlighter>
                </div>
              </div>
            )}

            {detailsTab === 'usage' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  How to Use in Trading Systems
                </h3>
                <div style={{ color: '#d1d5db', fontSize: '13px', lineHeight: '1.8' }}>
                  {selectedConcept.id === 'encapsulation' && (
                    <ul style={{ paddingLeft: '20px' }}>
                      <li>Declare class fields as private</li>
                      <li>Provide public getter and setter methods with validation</li>
                      <li>Hide internal implementation details from external classes</li>
                      <li>Use access modifiers appropriately (private, protected, public)</li>
                    </ul>
                  )}
                  {selectedConcept.id === 'inheritance' && (
                    <ul style={{ paddingLeft: '20px' }}>
                      <li>Use extends keyword to inherit from a superclass</li>
                      <li>Override methods using @Override annotation</li>
                      <li>Call superclass methods with super keyword</li>
                      <li>Follow the Liskov Substitution Principle</li>
                    </ul>
                  )}
                  {selectedConcept.id === 'polymorphism' && (
                    <ul style={{ paddingLeft: '20px' }}>
                      <li>Define common interfaces or abstract base classes</li>
                      <li>Override methods in subclasses with specific implementations</li>
                      <li>Use interface references to point to concrete implementations</li>
                      <li>Leverage runtime method resolution for dynamic behavior</li>
                    </ul>
                  )}
                  {selectedConcept.id === 'abstraction' && (
                    <ul style={{ paddingLeft: '20px' }}>
                      <li>Define interfaces with method signatures but no implementation</li>
                      <li>Create abstract classes with some implemented and some abstract methods</li>
                      <li>Implement interfaces in concrete classes</li>
                      <li>Program to interfaces, not implementations</li>
                    </ul>
                  )}
                </div>
              </div>
            )}

            {detailsTab === 'best-practices' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  Best Practices
                </h3>
                <div style={{ display: 'grid', gap: '12px' }}>
                  {[
                    'Follow SOLID principles for maintainable code',
                    'Use meaningful names for classes and methods',
                    'Keep classes focused on a single responsibility',
                    'Favor composition over inheritance when appropriate',
                    'Write unit tests for all public methods',
                    'Document complex business logic',
                    'Use design patterns where they add value'
                  ].map((practice, idx) => (
                    <div key={idx} style={{
                      padding: '12px',
                      background: '#1f2937',
                      borderRadius: '6px',
                      borderLeft: '3px solid #10b981',
                      color: '#d1d5db',
                      fontSize: '13px'
                    }}>
                      {practice}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default JavaOOP;
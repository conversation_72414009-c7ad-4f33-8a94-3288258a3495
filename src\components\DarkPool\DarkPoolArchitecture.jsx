import React, { useState, useRef } from 'react';
import { Cpu, Database, Server, GitBranch, Activity, Shield, BarChart3 } from 'lucide-react';
import SearchComponent from './SearchComponent';
import ExportControls from './ExportControls';
import ZoomControls from './ZoomControls';
import { components, connections, componentDetails } from '../../data/componentData';

const DarkPoolArchitecture = () => {
  const [selectedComponent, setSelectedComponent] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [zoomLevel, setZoomLevel] = useState(1);
  const [panPosition, setPanPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  
  const containerRef = useRef(null);

  const icons = {
    'order-gateway': <Shield className="w-6 h-6" />,
    'matching-engine': <Cpu className="w-6 h-6" />,
    'settlement-hub': <GitBranch className="w-6 h-6" />,
    'market-data': <BarChart3 className="w-6 h-6" />,
    'risk-engine': <Activity className="w-6 h-6" />,
    'trade-reporting': <Server className="w-6 h-6" />,
    'order-book': <Database className="w-6 h-6" />,
    'kafka-cluster': <Server className="w-6 h-6" />,
    'redis-cluster': <Database className="w-6 h-6" />,
    'postgresql': <Database className="w-6 h-6" />,
    'influxdb': <Database className="w-6 h-6" />,
    'monitoring': <Activity className="w-6 h-6" />,
    'kubernetes': <Server className="w-6 h-6" />
  };

  const filteredComponents = components.filter(component => {
    if (!searchQuery) return true;
    const query = searchQuery.toLowerCase();
    return (
      component.name.toLowerCase().includes(query) ||
      component.tech.some(t => t.toLowerCase().includes(query)) ||
      component.keywords.some(k => k.includes(query))
    );
  });

  const handleZoom = (delta) => {
    setZoomLevel(prev => Math.max(0.5, Math.min(2, prev + delta)));
  };

  const resetView = () => {
    setZoomLevel(1);
    setPanPosition({ x: 0, y: 0 });
  };

  const handleMouseDown = (e) => {
    if (e.target.closest('.component')) return;
    setIsDragging(true);
    setDragStart({ x: e.clientX - panPosition.x, y: e.clientY - panPosition.y });
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    setPanPosition({
      x: e.clientX - dragStart.x,
      y: e.clientY - dragStart.y
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const getCategoryColor = (category) => {
    const colors = {
      processing: 'border-green-500 bg-gradient-to-br from-green-900 to-green-800',
      data: 'border-blue-500 bg-gradient-to-br from-blue-900 to-blue-800',
      storage: 'border-purple-500 bg-gradient-to-br from-purple-900 to-purple-800',
      infrastructure: 'border-orange-500 bg-gradient-to-br from-orange-900 to-orange-800'
    };
    return colors[category] || colors.processing;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 overflow-hidden">
      {/* Header */}
      <div className="relative z-20 bg-black bg-opacity-30 backdrop-blur-md border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 py-4 flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">Dark Pool Matching Engine</h1>
            <p className="text-gray-400 text-sm mt-1">Interactive System Architecture</p>
          </div>
          
          <div className="flex items-center gap-4">
            <SearchComponent
              components={components}
              onSearch={setSearchQuery}
              onSelectComponent={setSelectedComponent}
            />
            <ExportControls containerRef={containerRef} />
          </div>
        </div>
      </div>

      {/* Zoom Controls */}
      <ZoomControls 
        zoomLevel={zoomLevel}
        onZoom={handleZoom}
        onReset={resetView}
      />

      {/* Main Architecture */}
      <div 
        ref={containerRef}
        className="relative h-screen cursor-move"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
      >
        <div
          className="absolute inset-0 flex items-center justify-center"
          style={{
            transform: `translate(${panPosition.x}px, ${panPosition.y}px) scale(${zoomLevel})`,
            transformOrigin: 'center',
            transition: isDragging ? 'none' : 'transform 0.3s ease'
          }}
        >
          <div className="relative w-[800px] h-[800px]">
            {/* Components */}
            {components.map((component) => {
              const isFiltered = filteredComponents.includes(component);
              const isSelected = selectedComponent?.id === component.id;
              
              return (
                <div
                  key={component.id}
                  className={`component absolute rounded-lg p-4 cursor-pointer 
                             border-2 transition-all duration-300 min-w-[180px] text-center
                             ${getCategoryColor(component.category)}
                             ${isSelected ? 'ring-4 ring-yellow-400 shadow-yellow-400/30' : 'hover:shadow-lg'}
                             ${!isFiltered ? 'opacity-20' : 'hover:scale-105'}`}
                  style={{ 
                    top: `${component.position.top}px`, 
                    left: `${component.position.left}px`,
                    zIndex: isSelected ? 10 : 2
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedComponent(component);
                  }}
                >
                  <div className="flex items-center justify-center mb-2 text-green-400">
                    {icons[component.id]}
                  </div>
                  <h3 className="text-sm font-semibold text-white mb-2">
                    {component.name}
                  </h3>
                  <div className="text-xs text-gray-300 space-y-1">
                    {component.tech.map((t, i) => (
                      <div key={i}>{t}</div>
                    ))}
                  </div>
                  {component.latency && (
                    <div className="absolute -top-2 -right-2 bg-red-600 text-white text-xs px-2 py-1 rounded-full">
                      {component.latency}
                    </div>
                  )}
                  {component.throughput && (
                    <div className="absolute -bottom-2 -right-2 bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
                      {component.throughput}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Detail Panel */}
      {selectedComponent && componentDetails[selectedComponent.id] && (
        <div className="fixed right-8 top-24 w-[500px] max-h-[80vh] bg-gray-900 bg-opacity-95 backdrop-blur-md rounded-xl shadow-2xl overflow-hidden z-30">
          <div className="bg-gray-800 p-4 flex items-center justify-between">
            <h3 className="text-xl font-bold text-yellow-400">
              {componentDetails[selectedComponent.id].details.title}
            </h3>
            <button
              onClick={() => setSelectedComponent(null)}
              className="text-gray-400 hover:text-white transition-colors"
            >
              ×
            </button>
          </div>
          <div className="p-6 overflow-y-auto">
            <h4 className="text-green-400 font-semibold mb-3">Primary Functions</h4>
            <ul className="list-disc list-inside text-gray-300 text-sm">
              {componentDetails[selectedComponent.id].details.functions.map((func, i) => (
                <li key={i}>{func}</li>
              ))}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default DarkPoolArchitecture;
import React, { useState } from 'react';
import { Shield, <PERSON>pu, AlertTriangle, BarChart3, Lock, Download } from 'lucide-react';
import SidebarNavigation from '../ui-components/SidebarNavigation';
import PortfolioManagement from './PortfolioManagement';
import VarCvarRiskAnalyticsInteractive from './VarCvarRiskAnalyticsInteractive';
import JavaOOP from '../java/JavaOOP';
import JavaMemoryManagement from '../java/JavaMemoryManagement';
import JavaCollections from '../java/JavaCollections';
import JavaConcurrency from '../java/JavaConcurrency';
import Java8Plus from '../java/Java8Plus';
import JavaExceptions from '../java/JavaExceptions';
import JavaJVM from '../java/JavaJVM';
import JavaAdvancedOOP from '../java/JavaAdvancedOOP';
import JavaSpring from '../java/JavaSpring';
import JavaSpringBoot from '../java/JavaSpringBoot';
import SQLBasics from '../sql/SQLBasics';
import SQLJoins from '../sql/SQLJoins';
import SQLAdvanced from '../sql/SQLAdvanced';
import SQLOptimization from '../sql/SQLOptimization';
import SQLTransactions from '../sql/SQLTransactions';
import SQLTradingPatterns from '../sql/SQLTradingPatterns';
import Kafka from '../messaging/Kafka';
import RabbitMQ from '../messaging/RabbitMQ';
import Solace from '../messaging/Solace';
// Security Components
import AuthenticationAuthorization from '../security/AuthenticationAuthorization';
import CryptographyEncryption from '../security/CryptographyEncryption';
import NetworkSecurity from '../security/NetworkSecurity';
import SecurityMonitoring from '../security/SecurityMonitoring';
import ThreatDetection from '../security/ThreatDetection';
import ParkingLotOOP from '../oop-designs/ParkingLotSystem';
import LibraryManagementOOP from '../oop-designs/LibraryManagementSystem';
import ElevatorSystemOOP from '../oop-designs/ElevatorControlSystem';
import ATMachineOOP from '../oop-designs/ATMachineSystem';
import DesignPatterns from '../patterns/DesignPatterns';
import MicroserviceDesignPatterns from '../patterns/MicroservicePatterns';
import { enhancedComponentDetails } from '../../../data/enhancedComponentData';

const DarkPoolMatchingEngine = () => {
  const [currentView, setCurrentView] = useState('darkpool');
  console.log('Current view:', currentView);
  const [selectedComponent, setSelectedComponent] = useState(null);
  console.log('Selected component:', selectedComponent);
  const [activeTab, setActiveTab] = useState('details');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  const handleViewChange = (view) => {
    setCurrentView(view);
  };

  const handleSidebarCollapse = (collapsed) => {
    setIsSidebarCollapsed(collapsed);
  };

  // Dark pool components with Order Validator
  const darkpoolComponents = [
    {
      id: 'order-gateway',
      name: 'Order Gateway',
      icon: <Shield size={20} />,
      position: { top: 50, left: 100 },
      category: 'api',
      description: 'FIX Protocol + Rate Limiting'
    },
    {
      id: 'order-validator',
      name: 'Order Validator',
      icon: <Shield size={20} />,
      position: { top: 50, left: 350 },
      category: 'processing',
      description: 'Multi-Layer Validation Engine'
    },
    {
      id: 'matching-engine',
      name: 'Matching Engine',
      icon: <Cpu size={20} />,
      position: { top: 50, left: 600 },
      category: 'core',
      description: 'Ultra-Low Latency Matching'
    },
    {
      id: 'risk-engine',
      name: 'Risk Engine',
      icon: <AlertTriangle size={20} />,
      position: { top: 200, left: 100 },
      category: 'processing',
      description: 'Real-Time Risk Management'
    },
    {
      id: 'market-data',
      name: 'Market Data Feed',
      icon: <BarChart3 size={20} />,
      position: { top: 200, left: 350 },
      category: 'data',
      description: 'Ultra-Fast Distribution'
    },
    {
      id: 'settlement-hub',
      name: 'Settlement Hub',
      icon: <Lock size={20} />,
      position: { top: 200, left: 600 },
      category: 'processing',
      description: 'Trade Settlement & Clearing'
    }
  ];

  const getCategoryStyle = (category) => {
    const styles = {
      api: { color: '#8b5cf6', bg: 'rgba(139, 92, 246, 0.1)' },
      core: { color: '#ef4444', bg: 'rgba(239, 68, 68, 0.1)' },
      processing: { color: '#10b981', bg: 'rgba(16, 185, 129, 0.1)' },
      data: { color: '#3b82f6', bg: 'rgba(59, 130, 246, 0.1)' },
      compliance: { color: '#f59e0b', bg: 'rgba(245, 158, 11, 0.1)' }
    };
    return styles[category] || styles.processing;
  };

  if (currentView === 'portfolio') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <PortfolioManagement />
        </div>
      </div>
    );
  }

  if (currentView === 'varcvar') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <ParkingLotOOP />
        </div>
      </div>
    );
  }

  if (currentView === 'library-management') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <LibraryManagementOOP />
        </div>
      </div>
    );
  }
  
  if (currentView === 'elevator-system') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <ElevatorSystemOOP />
        </div>
      </div>
    );
  }
  
  if (currentView === 'atm-machine') {
    console.log('Rendering ATM machine view');
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <ATMachineOOP />
        </div>
      </div>
    );
  }

  if (currentView === 'compliance') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <VarCvarRiskAnalyticsInteractive />
        </div>
      </div>
    );
  }

  // Java subcategory routing
  if (currentView === 'java-oop') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <JavaOOP />
        </div>
      </div>
    );
  }

  if (currentView === 'java-memory') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <JavaMemoryManagement />
        </div>
      </div>
    );
  }

  if (currentView === 'java-collections') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <JavaCollections />
        </div>
      </div>
    );
  }

  if (currentView === 'java-concurrency') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <JavaConcurrency />
        </div>
      </div>
    );
  }

  if (currentView === 'java-8plus') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <Java8Plus />
        </div>
      </div>
    );
  }

  if (currentView === 'java-exceptions') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <JavaExceptions />
        </div>
      </div>
    );
  }

  if (currentView === 'java-jvm') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <JavaJVM />
        </div>
      </div>
    );
  }

  if (currentView === 'java-advanced-oop') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <JavaAdvancedOOP />
        </div>
      </div>
    );
  }

  if (currentView === 'java-spring') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <JavaSpring />
        </div>
      </div>
    );
  }

  if (currentView === 'java-spring-boot') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <JavaSpringBoot />
        </div>
      </div>
    );
  }

  // SQL subcategory routing
  if (currentView === 'sql-basics') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <SQLBasics />
        </div>
      </div>
    );
  }

  if (currentView === 'sql-joins') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <SQLJoins />
        </div>
      </div>
    );
  }

  if (currentView === 'sql-advanced') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <SQLAdvanced />
        </div>
      </div>
    );
  }

  if (currentView === 'sql-optimization') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <SQLOptimization />
        </div>
      </div>
    );
  }

  if (currentView === 'sql-transactions') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <SQLTransactions />
        </div>
      </div>
    );
  }

  if (currentView === 'sql-trading') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <SQLTradingPatterns />
        </div>
      </div>
    );
  }

  // Messaging subcategory routing
  if (currentView === 'kafka') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <Kafka />
        </div>
      </div>
    );
  }

  if (currentView === 'rabbitmq') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <RabbitMQ />
        </div>
      </div>
    );
  }

  if (currentView === 'solace') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <Solace />
        </div>
      </div>
    );
  }

  // Security Components
  if (currentView === 'auth-authorization') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <AuthenticationAuthorization />
        </div>
      </div>
    );
  }

  if (currentView === 'cryptography') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <CryptographyEncryption />
        </div>
      </div>
    );
  }

  if (currentView === 'network-security') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <NetworkSecurity />
        </div>
      </div>
    );
  }

  if (currentView === 'security-monitoring') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <SecurityMonitoring />
        </div>
      </div>
    );
  }

  if (currentView === 'threat-detection') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <ThreatDetection />
        </div>
      </div>
    );
  }

  if (currentView === 'patterns') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <DesignPatterns />
        </div>
      </div>
    );
  }

  if (currentView === 'microservice-patterns') {
    return (
      <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
        <SidebarNavigation 
          currentView={currentView}
          onViewChange={handleViewChange}
          onCollapseChange={handleSidebarCollapse}
        />
        <div style={{ 
          marginLeft: isSidebarCollapsed ? '60px' : '280px',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          transition: 'margin-left 0.3s ease'
        }}>
          <MicroserviceDesignPatterns />
        </div>
      </div>
    );
  }

  return (
    <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%)', overflow: 'hidden' }}>
      <SidebarNavigation 
        currentView={currentView}
        onViewChange={handleViewChange}
        onCollapseChange={handleSidebarCollapse}
      />
      
      {/* Main Content */}
      <div style={{ 
        display: 'flex', 
        marginLeft: isSidebarCollapsed ? '60px' : '280px', 
        height: '100vh',
        overflowY: 'auto',
        overflowX: 'hidden',
        transition: 'margin-left 0.3s ease'
      }}>
        {/* Architecture Diagram */}
        <div style={{ flex: 1, position: 'relative', padding: '40px' }}>
          <h1 style={{ color: 'white', marginBottom: '32px', fontSize: '28px' }}>
            Enhanced Dark Pool Trading Architecture
          </h1>
          
          {/* Components Grid */}
          <div style={{ position: 'relative', width: '800px', height: '400px' }}>
            {darkpoolComponents.map(component => {
              const categoryStyle = getCategoryStyle(component.category);
              const isSelected = selectedComponent?.id === component.id;
              
              return (
                <div
                  key={component.id}
                  onClick={() => setSelectedComponent(component)}
                  style={{
                    position: 'absolute',
                    top: component.position.top,
                    left: component.position.left,
                    width: '200px',
                    padding: '16px',
                    background: isSelected ? categoryStyle.color : categoryStyle.bg,
                    border: `2px solid ${categoryStyle.color}`,
                    borderRadius: '12px',
                    cursor: 'pointer',
                    transition: 'all 0.3s',
                    transform: isSelected ? 'scale(1.05)' : 'scale(1)'
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '8px' }}>
                    <div style={{ color: isSelected ? 'white' : categoryStyle.color }}>
                      {component.icon}
                    </div>
                    <h3 style={{ 
                      fontSize: '14px', 
                      fontWeight: 'bold', 
                      color: isSelected ? 'white' : 'white',
                      margin: 0
                    }}>
                      {component.name}
                    </h3>
                  </div>
                  <div style={{ fontSize: '12px', color: isSelected ? '#e5e7eb' : '#9ca3af' }}>
                    {component.description}
                  </div>
                </div>
              );
            })}
            
            {/* Connection Lines */}
            <svg style={{ position: 'absolute', inset: 0, pointerEvents: 'none' }}>
              <defs>
                <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                  <polygon points="0 0, 10 3.5, 0 7" fill="#10b981" />
                </marker>
              </defs>
              {/* Order flow lines */}
              <line x1="200" y1="70" x2="350" y2="70" stroke="#10b981" strokeWidth="2" markerEnd="url(#arrowhead)" />
              <line x1="450" y1="70" x2="600" y2="70" stroke="#10b981" strokeWidth="2" markerEnd="url(#arrowhead)" />
              <line x1="350" y1="100" x2="200" y2="180" stroke="#f59e0b" strokeWidth="2" strokeDasharray="5,5" markerEnd="url(#arrowhead)" />
            </svg>
          </div>
        </div>

        {/* Details Panel */}
        {selectedComponent && enhancedComponentDetails[selectedComponent.id] && (
          <div style={{
            width: '500px',
            backgroundColor: 'rgba(17, 24, 39, 0.95)',
            backdropFilter: 'blur(12px)',
            borderLeft: '1px solid #374151',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column'
          }}>
            {/* Panel Header */}
            <div style={{
              padding: '20px',
              borderBottom: '1px solid #374151',
              background: 'linear-gradient(135deg, #1f2937 0%, #111827 100%)'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start' }}>
                <div>
                  <h2 style={{ fontSize: '18px', fontWeight: 'bold', color: '#fbbf24', margin: '0 0 8px 0' }}>
                    {enhancedComponentDetails[selectedComponent.id].details.title}
                  </h2>
                  <div style={{ fontSize: '12px', color: '#9ca3af' }}>
                    {selectedComponent.description}
                  </div>
                </div>
                <button
                  onClick={() => setSelectedComponent(null)}
                  style={{
                    background: 'transparent',
                    border: 'none',
                    color: '#9ca3af',
                    fontSize: '24px',
                    cursor: 'pointer'
                  }}
                >
                  ×
                </button>
              </div>
            </div>

            {/* Tabs */}
            <div style={{ display: 'flex', borderBottom: '1px solid #374151' }}>
              {['details', 'workflow', 'code', 'uml', 'performance'].map(tab => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  style={{
                    flex: 1,
                    padding: '12px',
                    background: activeTab === tab ? '#1f2937' : 'transparent',
                    border: 'none',
                    color: activeTab === tab ? '#fbbf24' : '#9ca3af',
                    fontSize: '12px',
                    fontWeight: '600',
                    textTransform: 'capitalize',
                    cursor: 'pointer'
                  }}
                >
                  {tab}
                </button>
              ))}
            </div>

            {/* Tab Content */}
            <div style={{ flex: 1, padding: '20px', overflowY: 'auto' }}>
              {activeTab === 'details' && (
                <div>
                  <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                    Key Functions
                  </h3>
                  <ul style={{ margin: 0, paddingLeft: '20px', lineHeight: '1.6' }}>
                    {enhancedComponentDetails[selectedComponent.id].details.functions.map((func, idx) => (
                      <li key={idx} style={{ color: '#d1d5db', fontSize: '13px', marginBottom: '8px' }}>
                        {func}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {activeTab === 'workflow' && (
                <div>
                  <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                    Process Flow
                  </h3>
                  <pre style={{
                    color: '#d1d5db',
                    fontSize: '12px',
                    whiteSpace: 'pre-wrap',
                    fontFamily: 'monospace',
                    background: '#0f172a',
                    padding: '16px',
                    borderRadius: '8px',
                    lineHeight: '1.5'
                  }}>
                    {enhancedComponentDetails[selectedComponent.id].workflow}
                  </pre>
                </div>
              )}

              {activeTab === 'code' && (
                <div>
                  <div style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    marginBottom: '16px'
                  }}>
                    <h3 style={{ color: '#10b981', fontSize: '14px', margin: 0 }}>
                      Implementation Example
                    </h3>
                    <button
                      onClick={() => {
                        const code = enhancedComponentDetails[selectedComponent.id].code;
                        const blob = new Blob([code], { type: 'text/plain' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `${selectedComponent.id.replace(/-/g, '_')}_implementation.java`;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);
                      }}
                      style={{
                        padding: '6px 12px',
                        backgroundColor: '#10b981',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        fontSize: '12px',
                        fontWeight: '500',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '6px',
                        transition: 'background-color 0.2s'
                      }}
                      onMouseEnter={(e) => e.target.style.backgroundColor = '#059669'}
                      onMouseLeave={(e) => e.target.style.backgroundColor = '#10b981'}
                    >
                      <Download size={14} />
                      Download Code
                    </button>
                  </div>
                  <pre style={{
                    color: '#d1d5db',
                    fontSize: '10px',
                    whiteSpace: 'pre-wrap',
                    fontFamily: 'Consolas, Monaco, monospace',
                    background: '#0f172a',
                    padding: '16px',
                    borderRadius: '8px',
                    overflow: 'auto',
                    lineHeight: '1.4',
                    maxHeight: '400px'
                  }}>
                    {enhancedComponentDetails[selectedComponent.id].code}
                  </pre>
                </div>
              )}

              {activeTab === 'uml' && (
                <div>
                  <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                    System Architecture UML
                  </h3>
                  <div style={{ marginBottom: '20px' }}>
                    <svg width="440" height="320" style={{ background: '#0f172a', borderRadius: '8px', border: '1px solid #374151' }}>
                      {/* Order Gateway */}
                      <rect x="20" y="20" width="120" height="80" fill="#1e293b" stroke="#8b5cf6" strokeWidth="2" rx="4"/>
                      <text x="80" y="35" textAnchor="middle" fill="#8b5cf6" fontSize="11" fontWeight="bold">OrderGateway</text>
                      <line x1="20" y1="45" x2="140" y2="45" stroke="#8b5cf6"/>
                      <text x="25" y="58" fill="#d1d5db" fontSize="9">validateOrder()</text>
                      <text x="25" y="70" fill="#d1d5db" fontSize="9">processRequest()</text>
                      <text x="25" y="82" fill="#d1d5db" fontSize="9">handleFIX()</text>
                      <text x="25" y="94" fill="#d1d5db" fontSize="9">rateLimit()</text>

                      {/* Order Validator */}
                      <rect x="160" y="20" width="120" height="80" fill="#1e293b" stroke="#10b981" strokeWidth="2" rx="4"/>
                      <text x="220" y="35" textAnchor="middle" fill="#10b981" fontSize="11" fontWeight="bold">OrderValidator</text>
                      <line x1="160" y1="45" x2="280" y2="45" stroke="#10b981"/>
                      <text x="165" y="58" fill="#d1d5db" fontSize="9">validateSyntax()</text>
                      <text x="165" y="70" fill="#d1d5db" fontSize="9">checkBusinessRules()</text>
                      <text x="165" y="82" fill="#d1d5db" fontSize="9">verifyClient()</text>
                      <text x="165" y="94" fill="#d1d5db" fontSize="9">enrichOrder()</text>

                      {/* Matching Engine */}
                      <rect x="300" y="20" width="120" height="80" fill="#1e293b" stroke="#ef4444" strokeWidth="2" rx="4"/>
                      <text x="360" y="35" textAnchor="middle" fill="#ef4444" fontSize="11" fontWeight="bold">MatchingEngine</text>
                      <line x1="300" y1="45" x2="420" y2="45" stroke="#ef4444"/>
                      <text x="305" y="58" fill="#d1d5db" fontSize="9">matchOrders()</text>
                      <text x="305" y="70" fill="#d1d5db" fontSize="9">updateOrderBook()</text>
                      <text x="305" y="82" fill="#d1d5db" fontSize="9">generateTrades()</text>
                      <text x="305" y="94" fill="#d1d5db" fontSize="9">priorityQueue()</text>

                      {/* Risk Engine */}
                      <rect x="20" y="120" width="120" height="80" fill="#1e293b" stroke="#10b981" strokeWidth="2" rx="4"/>
                      <text x="80" y="135" textAnchor="middle" fill="#10b981" fontSize="11" fontWeight="bold">RiskEngine</text>
                      <line x1="20" y1="145" x2="140" y2="145" stroke="#10b981"/>
                      <text x="25" y="158" fill="#d1d5db" fontSize="9">calculateRisk()</text>
                      <text x="25" y="170" fill="#d1d5db" fontSize="9">checkLimits()</text>
                      <text x="25" y="182" fill="#d1d5db" fontSize="9">validateExposure()</text>
                      <text x="25" y="194" fill="#d1d5db" fontSize="9">monitorVaR()</text>

                      {/* Market Data Feed */}
                      <rect x="160" y="120" width="120" height="80" fill="#1e293b" stroke="#3b82f6" strokeWidth="2" rx="4"/>
                      <text x="220" y="135" textAnchor="middle" fill="#3b82f6" fontSize="11" fontWeight="bold">MarketDataFeed</text>
                      <line x1="160" y1="145" x2="280" y2="145" stroke="#3b82f6"/>
                      <text x="165" y="158" fill="#d1d5db" fontSize="9">publishPrices()</text>
                      <text x="165" y="170" fill="#d1d5db" fontSize="9">distributeFeed()</text>
                      <text x="165" y="182" fill="#d1d5db" fontSize="9">handleSubscriptions()</text>
                      <text x="165" y="194" fill="#d1d5db" fontSize="9">normalizeData()</text>

                      {/* Settlement Hub */}
                      <rect x="300" y="120" width="120" height="80" fill="#1e293b" stroke="#10b981" strokeWidth="2" rx="4"/>
                      <text x="360" y="135" textAnchor="middle" fill="#10b981" fontSize="11" fontWeight="bold">SettlementHub</text>
                      <line x1="300" y1="145" x2="420" y2="145" stroke="#10b981"/>
                      <text x="305" y="158" fill="#d1d5db" fontSize="9">processSettlement()</text>
                      <text x="305" y="170" fill="#d1d5db" fontSize="9">clearTrades()</text>
                      <text x="305" y="182" fill="#d1d5db" fontSize="9">updatePositions()</text>
                      <text x="305" y="194" fill="#d1d5db" fontSize="9">generateReports()</text>

                      {/* Relationship arrows */}
                      <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                          <polygon points="0 0, 10 3.5, 0 7" fill="#10b981"/>
                        </marker>
                      </defs>
                      
                      {/* Order flow */}
                      <line x1="140" y1="60" x2="160" y2="60" stroke="#10b981" strokeWidth="2" markerEnd="url(#arrowhead)"/>
                      <line x1="280" y1="60" x2="300" y2="60" stroke="#10b981" strokeWidth="2" markerEnd="url(#arrowhead)"/>
                      
                      {/* Risk validation */}
                      <line x1="220" y1="100" x2="80" y2="120" stroke="#f59e0b" strokeWidth="2" strokeDasharray="3,3"/>
                      
                      {/* Market data flow */}
                      <line x1="220" y1="120" x2="360" y2="100" stroke="#3b82f6" strokeWidth="2" markerEnd="url(#arrowhead)"/>
                      
                      {/* Settlement flow */}
                      <line x1="360" y1="100" x2="360" y2="120" stroke="#10b981" strokeWidth="2" markerEnd="url(#arrowhead)"/>
                      
                      {/* Legend */}
                      <text x="20" y="230" fill="#9ca3af" fontSize="10" fontWeight="bold">Legend:</text>
                      <line x1="20" y1="245" x2="40" y2="245" stroke="#10b981" strokeWidth="2" markerEnd="url(#arrowhead)"/>
                      <text x="45" y="249" fill="#d1d5db" fontSize="9">Order Flow</text>
                      <line x1="120" y1="245" x2="140" y2="245" stroke="#f59e0b" strokeWidth="2" strokeDasharray="3,3"/>
                      <text x="145" y="249" fill="#d1d5db" fontSize="9">Risk Check</text>
                      <line x1="220" y1="245" x2="240" y2="245" stroke="#3b82f6" strokeWidth="2" markerEnd="url(#arrowhead)"/>
                      <text x="245" y="249" fill="#d1d5db" fontSize="9">Data Flow</text>
                    </svg>
                  </div>
                  <div style={{ marginTop: '16px' }}>
                    <h4 style={{ color: '#fbbf24', fontSize: '12px', marginBottom: '8px' }}>
                      Architecture Overview
                    </h4>
                    <p style={{ color: '#d1d5db', fontSize: '11px', lineHeight: '1.5' }}>
                      The Dark Pool architecture follows a layered approach with clear separation of concerns. 
                      Orders flow from the Gateway through validation and risk checks before reaching the core 
                      matching engine. Real-time market data feeds pricing information while the settlement 
                      hub ensures proper trade clearing and position updates.
                    </p>
                  </div>
                </div>
              )}

              {activeTab === 'performance' && enhancedComponentDetails[selectedComponent.id].performance && (
                <div>
                  <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                    Performance Metrics
                  </h3>
                  {Object.entries(enhancedComponentDetails[selectedComponent.id].performance).map(([key, value]) => (
                    <div key={key} style={{ marginBottom: '16px' }}>
                      <div style={{ color: '#9ca3af', fontSize: '11px', textTransform: 'uppercase', marginBottom: '4px' }}>
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </div>
                      <div style={{ color: '#fbbf24', fontSize: '16px', fontWeight: 'bold' }}>
                        {value}
                      </div>
                    </div>
                  ))}
                  
                  {enhancedComponentDetails[selectedComponent.id].technologies && (
                    <div style={{ marginTop: '24px' }}>
                      <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '12px' }}>
                        Technology Stack
                      </h3>
                      {Object.entries(enhancedComponentDetails[selectedComponent.id].technologies).map(([key, value]) => (
                        <div key={key} style={{ marginBottom: '8px' }}>
                          <span style={{ color: '#9ca3af', fontSize: '12px' }}>{key}: </span>
                          <span style={{ color: '#d1d5db', fontSize: '12px' }}>{value}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Interactive UML Diagram at Bottom */}
        {selectedComponent && (
          <div style={{
            position: 'fixed',
            bottom: 0,
            left: isSidebarCollapsed ? '60px' : '280px',
            right: 0,
            height: '350px',
            backgroundColor: 'rgba(15, 23, 42, 0.98)',
            backdropFilter: 'blur(16px)',
            border: '1px solid rgba(51, 65, 85, 0.8)',
            borderBottom: 'none',
            borderRadius: '16px 16px 0 0',
            padding: '20px',
            transition: 'all 0.4s ease',
            zIndex: 1000,
            boxShadow: '0 -10px 40px rgba(0, 0, 0, 0.3)'
          }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              marginBottom: '16px',
              borderBottom: '1px solid rgba(51, 65, 85, 0.5)',
              paddingBottom: '12px'
            }}>
              <h3 style={{ 
                color: '#fbbf24', 
                margin: 0, 
                fontSize: '18px',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                <div style={{ color: selectedComponent ? getCategoryStyle(selectedComponent.category).color : '#10b981' }}>
                  {selectedComponent ? selectedComponent.icon : '🔧'}
                </div>
                Interactive UML: {selectedComponent ? selectedComponent.name : 'Test Component'}
              </h3>
              <button
                onClick={() => setSelectedComponent(null)}
                style={{
                  background: 'transparent',
                  border: '1px solid rgba(51, 65, 85, 0.5)',
                  borderRadius: '6px',
                  color: '#9ca3af',
                  padding: '6px 12px',
                  cursor: 'pointer',
                  fontSize: '12px',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  e.target.style.background = 'rgba(239, 68, 68, 0.1)';
                  e.target.style.borderColor = '#ef4444';
                  e.target.style.color = '#ef4444';
                }}
                onMouseLeave={(e) => {
                  e.target.style.background = 'transparent';
                  e.target.style.borderColor = 'rgba(51, 65, 85, 0.5)';
                  e.target.style.color = '#9ca3af';
                }}
              >
                Close UML
              </button>
            </div>

            <div style={{ 
              height: '280px', 
              overflowY: 'auto',
              overflowX: 'auto'
            }}>
              <svg 
                width="100%" 
                height="600" 
                style={{ 
                  background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%)', 
                  borderRadius: '12px',
                  border: '1px solid rgba(51, 65, 85, 0.3)'
                }}
              >
                {/* Enhanced Interactive UML based on selected component */}
                {selectedComponent && selectedComponent.id === 'order-gateway' && (
                  <g>
                    {/* Order Gateway Class */}
                    <rect x="50" y="40" width="200" height="120" fill="#1e293b" stroke="#8b5cf6" strokeWidth="2" rx="8"/>
                    <text x="150" y="60" textAnchor="middle" fill="#8b5cf6" fontSize="14" fontWeight="bold">OrderGateway</text>
                    <line x1="50" y1="70" x2="250" y2="70" stroke="#8b5cf6"/>
                    <text x="60" y="90" fill="#d1d5db" fontSize="11">- fixProtocol: FIXProtocol</text>
                    <text x="60" y="105" fill="#d1d5db" fontSize="11">- rateLimiter: RateLimiter</text>
                    <text x="60" y="120" fill="#d1d5db" fontSize="11">- validator: OrderValidator</text>
                    <line x1="50" y1="130" x2="250" y2="130" stroke="#8b5cf6"/>
                    <text x="60" y="145" fill="#10b981" fontSize="11">+ receiveOrder()</text>
                    <text x="60" y="160" fill="#10b981" fontSize="11">+ validateRate()</text>

                    {/* FIX Protocol */}
                    <rect x="320" y="40" width="180" height="80" fill="#1e293b" stroke="#3b82f6" strokeWidth="2" rx="8"/>
                    <text x="410" y="60" textAnchor="middle" fill="#3b82f6" fontSize="14" fontWeight="bold">FIXProtocol</text>
                    <line x1="320" y1="70" x2="500" y2="70" stroke="#3b82f6"/>
                    <text x="330" y="90" fill="#d1d5db" fontSize="11">- version: String</text>
                    <text x="330" y="105" fill="#d1d5db" fontSize="11">+ parseMessage()</text>

                    {/* Rate Limiter */}
                    <rect x="320" y="140" width="180" height="80" fill="#1e293b" stroke="#f59e0b" strokeWidth="2" rx="8"/>
                    <text x="410" y="160" textAnchor="middle" fill="#f59e0b" fontSize="14" fontWeight="bold">RateLimiter</text>
                    <line x1="320" y1="170" x2="500" y2="170" stroke="#f59e0b"/>
                    <text x="330" y="190" fill="#d1d5db" fontSize="11">- tokensPerSecond: int</text>
                    <text x="330" y="205" fill="#d1d5db" fontSize="11">+ checkLimit()</text>

                    {/* Order Validator */}
                    <rect x="50" y="200" width="200" height="100" fill="#1e293b" stroke="#10b981" strokeWidth="2" rx="8"/>
                    <text x="150" y="220" textAnchor="middle" fill="#10b981" fontSize="14" fontWeight="bold">OrderValidator</text>
                    <line x1="50" y1="230" x2="250" y2="230" stroke="#10b981"/>
                    <text x="60" y="250" fill="#d1d5db" fontSize="11">- rules: ValidationRules</text>
                    <text x="60" y="265" fill="#d1d5db" fontSize="11">+ validateSyntax()</text>
                    <text x="60" y="280" fill="#d1d5db" fontSize="11">+ validateBusiness()</text>
                    <text x="60" y="295" fill="#d1d5db" fontSize="11">+ validateRisk()</text>

                    {/* Relationships */}
                    <defs>
                      <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#8b5cf6"/>
                      </marker>
                    </defs>
                    
                    {/* Gateway to FIX */}
                    <line x1="250" y1="80" x2="320" y2="80" stroke="#8b5cf6" strokeWidth="2" markerEnd="url(#arrowhead)"/>
                    <text x="280" y="75" fill="#8b5cf6" fontSize="10">uses</text>
                    
                    {/* Gateway to RateLimiter */}
                    <line x1="250" y1="120" x2="320" y2="160" stroke="#8b5cf6" strokeWidth="2" markerEnd="url(#arrowhead)"/>
                    <text x="280" y="140" fill="#8b5cf6" fontSize="10">uses</text>
                    
                    {/* Gateway to Validator */}
                    <line x1="150" y1="160" x2="150" y2="200" stroke="#8b5cf6" strokeWidth="2" markerEnd="url(#arrowhead)"/>
                    <text x="155" y="185" fill="#8b5cf6" fontSize="10">delegates</text>

                    {/* Data Flow Annotations */}
                    <text x="60" y="350" fill="#fbbf24" fontSize="12" fontWeight="bold">Data Flow:</text>
                    <text x="60" y="370" fill="#d1d5db" fontSize="11">1. Client Order → FIX Protocol Parsing</text>
                    <text x="60" y="385" fill="#d1d5db" fontSize="11">2. Rate Limiting Check → Validation Pipeline</text>
                    <text x="60" y="400" fill="#d1d5db" fontSize="11">3. Multi-layer validation → Forward to Matching Engine</text>

                    {/* Performance Metrics Box */}
                    <rect x="320" y="340" width="200" height="80" fill="rgba(16, 185, 129, 0.1)" stroke="#10b981" strokeWidth="1" rx="6"/>
                    <text x="420" y="355" textAnchor="middle" fill="#10b981" fontSize="12" fontWeight="bold">Performance</text>
                    <text x="330" y="375" fill="#d1d5db" fontSize="10">Latency: &lt; 10μs</text>
                    <text x="330" y="390" fill="#d1d5db" fontSize="10">Throughput: 1M orders/sec</text>
                    <text x="330" y="405" fill="#d1d5db" fontSize="10">CPU Usage: 15%</text>
                  </g>
                )}

                {selectedComponent && selectedComponent.id === 'matching-engine' && (
                  <g>
                    {/* Matching Engine Core */}
                    <rect x="200" y="40" width="220" height="140" fill="#1e293b" stroke="#ef4444" strokeWidth="2" rx="8"/>
                    <text x="310" y="60" textAnchor="middle" fill="#ef4444" fontSize="14" fontWeight="bold">MatchingEngine</text>
                    <line x1="200" y1="70" x2="420" y2="70" stroke="#ef4444"/>
                    <text x="210" y="90" fill="#d1d5db" fontSize="11">- orderBook: OrderBook</text>
                    <text x="210" y="105" fill="#d1d5db" fontSize="11">- priceTimeQueue: PriorityQueue</text>
                    <text x="210" y="120" fill="#d1d5db" fontSize="11">- matchingAlgorithm: Algorithm</text>
                    <line x1="200" y1="130" x2="420" y2="130" stroke="#ef4444"/>
                    <text x="210" y="145" fill="#10b981" fontSize="11">+ processOrder(Order)</text>
                    <text x="210" y="160" fill="#10b981" fontSize="11">+ matchOrders()</text>
                    <text x="210" y="175" fill="#10b981" fontSize="11">+ updateOrderBook()</text>

                    {/* Order Book */}
                    <rect x="50" y="220" width="180" height="120" fill="#1e293b" stroke="#3b82f6" strokeWidth="2" rx="8"/>
                    <text x="140" y="240" textAnchor="middle" fill="#3b82f6" fontSize="14" fontWeight="bold">OrderBook</text>
                    <line x1="50" y1="250" x2="230" y2="250" stroke="#3b82f6"/>
                    <text x="60" y="270" fill="#d1d5db" fontSize="11">- bidSide: TreeMap</text>
                    <text x="60" y="285" fill="#d1d5db" fontSize="11">- askSide: TreeMap</text>
                    <text x="60" y="300" fill="#d1d5db" fontSize="11">+ getBestBid()</text>
                    <text x="60" y="315" fill="#d1d5db" fontSize="11">+ getBestAsk()</text>
                    <text x="60" y="330" fill="#d1d5db" fontSize="11">+ addOrder()</text>

                    {/* Price Level */}
                    <rect x="280" y="220" width="180" height="100" fill="#1e293b" stroke="#10b981" strokeWidth="2" rx="8"/>
                    <text x="370" y="240" textAnchor="middle" fill="#10b981" fontSize="14" fontWeight="bold">PriceLevel</text>
                    <line x1="280" y1="250" x2="460" y2="250" stroke="#10b981"/>
                    <text x="290" y="270" fill="#d1d5db" fontSize="11">- price: BigDecimal</text>
                    <text x="290" y="285" fill="#d1d5db" fontSize="11">- orders: LinkedList</text>
                    <text x="290" y="300" fill="#d1d5db" fontSize="11">+ addOrder()</text>
                    <text x="290" y="315" fill="#d1d5db" fontSize="11">+ removeOrder()</text>

                    {/* Matching Algorithm */}
                    <rect x="480" y="40" width="160" height="100" fill="#1e293b" stroke="#f59e0b" strokeWidth="2" rx="8"/>
                    <text x="560" y="60" textAnchor="middle" fill="#f59e0b" fontSize="14" fontWeight="bold">MatchingAlgo</text>
                    <line x1="480" y1="70" x2="640" y2="70" stroke="#f59e0b"/>
                    <text x="490" y="90" fill="#d1d5db" fontSize="11">- type: FIFO/ProRata</text>
                    <text x="490" y="105" fill="#d1d5db" fontSize="11">+ match()</text>
                    <text x="490" y="120" fill="#d1d5db" fontSize="11">+ allocate()</text>

                    {/* Relationships */}
                    <defs>
                      <marker id="arrowhead-red" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#ef4444"/>
                      </marker>
                    </defs>
                    
                    {/* Engine to OrderBook */}
                    <line x1="250" y1="180" x2="180" y2="220" stroke="#ef4444" strokeWidth="2" markerEnd="url(#arrowhead-red)"/>
                    <text x="200" y="205" fill="#ef4444" fontSize="10">manages</text>
                    
                    {/* Engine to Algorithm */}
                    <line x1="420" y1="100" x2="480" y2="90" stroke="#ef4444" strokeWidth="2" markerEnd="url(#arrowhead-red)"/>
                    <text x="440" y="90" fill="#ef4444" fontSize="10">uses</text>
                    
                    {/* OrderBook to PriceLevel */}
                    <line x1="230" y1="280" x2="280" y2="270" stroke="#3b82f6" strokeWidth="2" markerEnd="url(#arrowhead-red)"/>
                    <text x="250" y="270" fill="#3b82f6" fontSize="10">contains</text>

                    {/* Performance Box */}
                    <rect x="50" y="370" width="250" height="100" fill="rgba(239, 68, 68, 0.1)" stroke="#ef4444" strokeWidth="1" rx="6"/>
                    <text x="175" y="390" textAnchor="middle" fill="#ef4444" fontSize="12" fontWeight="bold">Ultra-Low Latency Performance</text>
                    <text x="60" y="410" fill="#d1d5db" fontSize="10">Order Processing: &lt; 50ns</text>
                    <text x="60" y="425" fill="#d1d5db" fontSize="10">Memory: Lock-free data structures</text>
                    <text x="60" y="440" fill="#d1d5db" fontSize="10">Matching: FIFO with Price-Time Priority</text>
                    <text x="60" y="455" fill="#d1d5db" fontSize="10">Throughput: 10M orders/sec peak</text>

                    {/* Algorithm Details */}
                    <rect x="350" y="370" width="200" height="80" fill="rgba(245, 158, 11, 0.1)" stroke="#f59e0b" strokeWidth="1" rx="6"/>
                    <text x="450" y="390" textAnchor="middle" fill="#f59e0b" fontSize="12" fontWeight="bold">Algorithm Types</text>
                    <text x="360" y="410" fill="#d1d5db" fontSize="10">FIFO: First In, First Out</text>
                    <text x="360" y="425" fill="#d1d5db" fontSize="10">Pro-Rata: Size allocation</text>
                    <text x="360" y="440" fill="#d1d5db" fontSize="10">Price-Time Priority</text>
                  </g>
                )}

                {selectedComponent && selectedComponent.id === 'risk-engine' && (
                  <g>
                    {/* Risk Engine Core */}
                    <rect x="180" y="40" width="200" height="140" fill="#1e293b" stroke="#10b981" strokeWidth="2" rx="8"/>
                    <text x="280" y="60" textAnchor="middle" fill="#10b981" fontSize="14" fontWeight="bold">RiskEngine</text>
                    <line x1="180" y1="70" x2="380" y2="70" stroke="#10b981"/>
                    <text x="190" y="90" fill="#d1d5db" fontSize="11">- riskCalculator: VaRCalculator</text>
                    <text x="190" y="105" fill="#d1d5db" fontSize="11">- positionTracker: Portfolio</text>
                    <text x="190" y="120" fill="#d1d5db" fontSize="11">- limits: RiskLimits</text>
                    <line x1="180" y1="130" x2="380" y2="130" stroke="#10b981"/>
                    <text x="190" y="145" fill="#10b981" fontSize="11">+ checkPreTrade()</text>
                    <text x="190" y="160" fill="#10b981" fontSize="11">+ calculateVaR()</text>
                    <text x="190" y="175" fill="#10b981" fontSize="11">+ updatePosition()</text>

                    {/* VaR Calculator */}
                    <rect x="420" y="40" width="180" height="100" fill="#1e293b" stroke="#8b5cf6" strokeWidth="2" rx="8"/>
                    <text x="510" y="60" textAnchor="middle" fill="#8b5cf6" fontSize="14" fontWeight="bold">VaRCalculator</text>
                    <line x1="420" y1="70" x2="600" y2="70" stroke="#8b5cf6"/>
                    <text x="430" y="90" fill="#d1d5db" fontSize="11">- confidenceLevel: 99%</text>
                    <text x="430" y="105" fill="#d1d5db" fontSize="11">- timeHorizon: 1day</text>
                    <text x="430" y="120" fill="#d1d5db" fontSize="11">+ calculate(Portfolio)</text>

                    {/* Portfolio Tracker */}
                    <rect x="50" y="220" width="180" height="120" fill="#1e293b" stroke="#3b82f6" strokeWidth="2" rx="8"/>
                    <text x="140" y="240" textAnchor="middle" fill="#3b82f6" fontSize="14" fontWeight="bold">Portfolio</text>
                    <line x1="50" y1="250" x2="230" y2="250" stroke="#3b82f6"/>
                    <text x="60" y="270" fill="#d1d5db" fontSize="11">- positions: Map</text>
                    <text x="60" y="285" fill="#d1d5db" fontSize="11">- pnl: BigDecimal</text>
                    <text x="60" y="300" fill="#d1d5db" fontSize="11">+ getPosition()</text>
                    <text x="60" y="315" fill="#d1d5db" fontSize="11">+ updatePnL()</text>
                    <text x="60" y="330" fill="#d1d5db" fontSize="11">+ getTotalExposure()</text>

                    {/* Risk Limits */}
                    <rect x="270" y="220" width="180" height="120" fill="#1e293b" stroke="#f59e0b" strokeWidth="2" rx="8"/>
                    <text x="360" y="240" textAnchor="middle" fill="#f59e0b" fontSize="14" fontWeight="bold">RiskLimits</text>
                    <line x1="270" y1="250" x2="450" y2="250" stroke="#f59e0b"/>
                    <text x="280" y="270" fill="#d1d5db" fontSize="11">- maxVaR: BigDecimal</text>
                    <text x="280" y="285" fill="#d1d5db" fontSize="11">- maxDelta: BigDecimal</text>
                    <text x="280" y="300" fill="#d1d5db" fontSize="11">- maxNotional: BigDecimal</text>
                    <text x="280" y="315" fill="#d1d5db" fontSize="11">+ validateLimits()</text>
                    <text x="280" y="330" fill="#d1d5db" fontSize="11">+ breachAlert()</text>

                    {/* CVaR Calculator */}
                    <rect x="480" y="220" width="160" height="80" fill="#1e293b" stroke="#ec4899" strokeWidth="2" rx="8"/>
                    <text x="560" y="240" textAnchor="middle" fill="#ec4899" fontSize="14" fontWeight="bold">CVaRCalculator</text>
                    <line x1="480" y1="250" x2="640" y2="250" stroke="#ec4899"/>
                    <text x="490" y="270" fill="#d1d5db" fontSize="11">+ calculateCVaR()</text>
                    <text x="490" y="285" fill="#d1d5db" fontSize="11">+ tailRisk()</text>

                    {/* Relationships */}
                    <defs>
                      <marker id="arrowhead-green" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#10b981"/>
                      </marker>
                    </defs>
                    
                    {/* Risk Engine relationships */}
                    <line x1="380" y1="90" x2="420" y2="90" stroke="#10b981" strokeWidth="2" markerEnd="url(#arrowhead-green)"/>
                    <text x="390" y="85" fill="#10b981" fontSize="10">uses</text>
                    
                    <line x1="230" y1="150" x2="180" y2="220" stroke="#10b981" strokeWidth="2" markerEnd="url(#arrowhead-green)"/>
                    <text x="180" y="190" fill="#10b981" fontSize="10">tracks</text>
                    
                    <line x1="320" y1="180" x2="360" y2="220" stroke="#10b981" strokeWidth="2" markerEnd="url(#arrowhead-green)"/>
                    <text x="330" y="205" fill="#10b981" fontSize="10">enforces</text>

                    {/* VaR to CVaR */}
                    <line x1="560" y1="140" x2="560" y2="220" stroke="#8b5cf6" strokeWidth="2" markerEnd="url(#arrowhead-green)"/>
                    <text x="570" y="180" fill="#8b5cf6" fontSize="10">extends</text>

                    {/* Risk Metrics */}
                    <rect x="50" y="370" width="300" height="100" fill="rgba(16, 185, 129, 0.1)" stroke="#10b981" strokeWidth="1" rx="6"/>
                    <text x="200" y="390" textAnchor="middle" fill="#10b981" fontSize="12" fontWeight="bold">Risk Metrics &amp; Performance</text>
                    <text x="60" y="410" fill="#d1d5db" fontSize="10">VaR Calculation: &lt; 100μs</text>
                    <text x="60" y="425" fill="#d1d5db" fontSize="10">Pre-trade checks: &lt; 50μs</text>
                    <text x="60" y="440" fill="#d1d5db" fontSize="10">Position updates: Real-time</text>
                    <text x="60" y="455" fill="#d1d5db" fontSize="10">Breach detection: &lt; 1ms alert</text>

                    {/* Risk Types */}
                    <rect x="380" y="370" width="220" height="80" fill="rgba(245, 158, 11, 0.1)" stroke="#f59e0b" strokeWidth="1" rx="6"/>
                    <text x="490" y="390" textAnchor="middle" fill="#f59e0b" fontSize="12" fontWeight="bold">Risk Types Monitored</text>
                    <text x="390" y="410" fill="#d1d5db" fontSize="10">Market Risk (VaR/CVaR)</text>
                    <text x="390" y="425" fill="#d1d5db" fontSize="10">Credit Risk (Counterparty)</text>
                    <text x="390" y="440" fill="#d1d5db" fontSize="10">Operational Risk (Limits)</text>
                  </g>
                )}

                {/* Default UML for other components */}
                {selectedComponent && !['order-gateway', 'matching-engine', 'risk-engine'].includes(selectedComponent.id) && (
                  <g>
                    {/* Generic Component Structure */}
                    <rect x="200" y="100" width="250" height="120" fill="#1e293b" stroke={getCategoryStyle(selectedComponent.category).color} strokeWidth="2" rx="8"/>
                    <text x="325" y="125" textAnchor="middle" fill={getCategoryStyle(selectedComponent.category).color} fontSize="16" fontWeight="bold">
                      {selectedComponent.name.replace(/\s+/g, '')}
                    </text>
                    <line x1="200" y1="135" x2="450" y2="135" stroke={getCategoryStyle(selectedComponent.category).color}/>
                    <text x="210" y="155" fill="#d1d5db" fontSize="11">- configuration: Config</text>
                    <text x="210" y="170" fill="#d1d5db" fontSize="11">- status: ComponentStatus</text>
                    <text x="210" y="185" fill="#d1d5db" fontSize="11">- metrics: PerformanceMetrics</text>
                    <line x1="200" y1="195" x2="450" y2="195" stroke={getCategoryStyle(selectedComponent.category).color}/>
                    <text x="210" y="215" fill="#10b981" fontSize="11">+ initialize()</text>

                    {/* Component Category Info */}
                    <rect x="150" y="280" width="350" height="80" fill={`${getCategoryStyle(selectedComponent.category).color}20`} stroke={getCategoryStyle(selectedComponent.category).color} strokeWidth="1" rx="6"/>
                    <text x="325" y="300" textAnchor="middle" fill={getCategoryStyle(selectedComponent.category).color} fontSize="14" fontWeight="bold">
                      {selectedComponent.category.toUpperCase()} COMPONENT
                    </text>
                    <text x="160" y="320" fill="#d1d5db" fontSize="11">{selectedComponent.description}</text>
                    <text x="160" y="340" fill="#d1d5db" fontSize="11">Category: {selectedComponent.category}</text>
                    <text x="160" y="355" fill="#d1d5db" fontSize="11">Interactive UML - Click other components for detailed diagrams</text>

                    {/* Placeholder for future detailed UML */}
                    <text x="325" y="420" textAnchor="middle" fill="#9ca3af" fontSize="12" fontStyle="italic">
                      Detailed UML diagram coming soon for {selectedComponent.name}
                    </text>
                  </g>
                )}

                {/* Interactive hover effects */}
                <style jsx>{`
                  .uml-element:hover {
                    filter: brightness(1.2);
                  }
                `}</style>
              </svg>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DarkPoolMatchingEngine;
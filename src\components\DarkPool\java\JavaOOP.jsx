import React, { useState } from 'react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Code, Layers, Lock, GitBranch, Package, Sparkles } from 'lucide-react';

const JavaOOP = () => {
  const [selectedConcept, setSelectedConcept] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [detailsTab, setDetailsTab] = useState('definition');

  const generateCompleteJavaCode = () => {
    return `// DarkPoolTradingSystem.java - Complete Implementation
// Compile: javac DarkPoolTradingSystem.java
// Run: java DarkPoolTradingSystem

import java.util.*;
import java.util.concurrent.*;
import java.time.LocalDateTime;
import java.math.BigDecimal;

// ==== MAIN EXECUTABLE CLASS ====
public class DarkPoolTradingSystem {
    public static void main(String[] args) {
        System.out.println("=== Dark Pool Trading System Demo ===\\n");
        
        // Initialize the system
        DarkPoolEngine engine = new DarkPoolEngine();
        
        // Create trading accounts
        TradingAccount account1 = new TradingAccount("ACC001", 1000000.0);
        TradingAccount account2 = new TradingAccount("ACC002", 750000.0);
        
        System.out.println("Initial Balances:");
        System.out.println("Account 1: $" + account1.getBalance());
        System.out.println("Account 2: $" + account2.getBalance());
        
        // Create and submit orders
        MarketOrder buyOrder = new MarketOrder("ORD001", 150.0, 100, "BUY");
        LimitOrder sellOrder = new LimitOrder("ORD002", 149.5, 100, "SELL");
        
        System.out.println("\\nSubmitting orders to dark pool...");
        engine.processOrder(buyOrder, account1);
        engine.processOrder(sellOrder, account2);
        
        // Execute matching
        engine.matchOrders();
        
        // Display results
        System.out.println("\\nFinal account balances:");
        System.out.println("Account 1: $" + account1.getBalance());
        System.out.println("Account 2: $" + account2.getBalance());
        
        // Demonstrate polymorphism with trading strategies
        TradingStrategy conservative = new ConservativeStrategy();
        TradingStrategy aggressive = new AggressiveStrategy();
        
        System.out.println("\\n=== Trading Strategy Demonstration ===");
        System.out.println("Conservative risk tolerance: " + conservative.calculateRisk() * 100 + "%");
        System.out.println("Aggressive risk tolerance: " + aggressive.calculateRisk() * 100 + "%");
        
        // Execute strategies
        conservative.executeTrade();
        aggressive.executeTrade();
    }
}

// ==== ENCAPSULATION EXAMPLE ====
class TradingAccount {
    // Private fields - encapsulated data
    private double balance;
    private String accountId;
    private Map<String, Integer> positions;
    private List<Trade> tradeHistory;
    
    // Constructor
    public TradingAccount(String accountId, double initialBalance) {
        this.accountId = accountId;
        this.balance = initialBalance;
        this.positions = new HashMap<>();
        this.tradeHistory = new ArrayList<>();
    }
    
    // Public methods - controlled access
    public double getBalance() {
        return balance;
    }
    
    public String getAccountId() {
        return accountId;
    }
    
    public void deposit(double amount) {
        if (amount > 0) {
            balance += amount;
            logTransaction("DEPOSIT", amount);
        }
    }
    
    public boolean withdraw(double amount) {
        if (amount > 0 && balance >= amount) {
            balance -= amount;
            logTransaction("WITHDRAWAL", amount);
            return true;
        }
        return false;
    }
    
    public boolean submitOrder(Order order) {
        double requiredMargin = order.calculateRequiredMargin();
        if (balance >= requiredMargin) {
            balance -= requiredMargin;
            System.out.println("Order submitted: " + order.getOrderId());
            return true;
        }
        System.out.println("Insufficient funds for order: " + order.getOrderId());
        return false;
    }
    
    // Private helper method - implementation detail
    private void logTransaction(String type, double amount) {
        System.out.println("Transaction logged: " + type + " $" + amount + " for account " + accountId);
    }
}

// ==== ABSTRACTION EXAMPLE ====
abstract class Order {
    // Protected fields - accessible to subclasses
    protected String orderId;
    protected double price;
    protected int quantity;
    protected String side; // BUY or SELL
    protected LocalDateTime timestamp;
    
    // Constructor
    public Order(String orderId, double price, int quantity, String side) {
        this.orderId = orderId;
        this.price = price;
        this.quantity = quantity;
        this.side = side;
        this.timestamp = LocalDateTime.now();
    }
    
    // Abstract methods - must be implemented by subclasses
    public abstract void execute();
    public abstract boolean validate();
    public abstract double calculateRequiredMargin();
    
    // Concrete methods - shared implementation
    public String getOrderId() { return orderId; }
    public double getPrice() { return price; }
    public int getQuantity() { return quantity; }
    public String getSide() { return side; }
    
    public double calculateValue() {
        return price * quantity;
    }
}

// ==== INHERITANCE EXAMPLES ====
class MarketOrder extends Order {
    private double slippage;
    private String priority;
    
    public MarketOrder(String orderId, double price, int quantity, String side) {
        super(orderId, price, quantity, side);
        this.slippage = 0.01; // Default 1% slippage
        this.priority = "HIGH";
    }
    
    @Override
    public void execute() {
        double executionPrice = price * (1 + slippage);
        System.out.println("Executing Market Order " + orderId + " at price: $" + executionPrice);
    }
    
    @Override
    public boolean validate() {
        return quantity > 0 && price > 0;
    }
    
    @Override
    public double calculateRequiredMargin() {
        return price * quantity * 0.1; // 10% margin requirement
    }
}

class LimitOrder extends Order {
    private double limitPrice;
    private String timeInForce;
    
    public LimitOrder(String orderId, double limitPrice, int quantity, String side) {
        super(orderId, limitPrice, quantity, side);
        this.limitPrice = limitPrice;
        this.timeInForce = "DAY";
    }
    
    @Override
    public void execute() {
        System.out.println("Executing Limit Order " + orderId + " at limit price: $" + limitPrice);
    }
    
    @Override
    public boolean validate() {
        return quantity > 0 && limitPrice > 0;
    }
    
    @Override
    public double calculateRequiredMargin() {
        return limitPrice * quantity * 0.05; // 5% margin for limit orders
    }
}

// ==== POLYMORPHISM EXAMPLE ====
interface TradingStrategy {
    double calculateRisk();
    void executeTrade();
    void adjustPosition();
}

class ConservativeStrategy implements TradingStrategy {
    private double riskTolerance = 0.02; // 2% risk tolerance
    
    @Override
    public double calculateRisk() {
        return riskTolerance;
    }
    
    @Override
    public void executeTrade() {
        System.out.println("Executing conservative trade with 2% max risk...");
        System.out.println("Using small position sizes and tight stop losses");
    }
    
    @Override
    public void adjustPosition() {
        System.out.println("Adjusting position conservatively - reducing exposure");
    }
}

class AggressiveStrategy implements TradingStrategy {
    private double riskTolerance = 0.10; // 10% risk tolerance
    
    @Override
    public double calculateRisk() {
        return riskTolerance;
    }
    
    @Override
    public void executeTrade() {
        System.out.println("Executing aggressive trade with 10% max risk...");
        System.out.println("Using larger position sizes for higher returns");
    }
    
    @Override
    public void adjustPosition() {
        System.out.println("Adjusting position aggressively - increasing exposure");
    }
}

// ==== CORE DARK POOL COMPONENTS ====
class DarkPoolEngine {
    private OrderBook orderBook;
    private MatchingEngine matchingEngine;
    private RiskManager riskManager;
    private Set<TradingAccount> participants;
    
    public DarkPoolEngine() {
        this.orderBook = new OrderBook();
        this.matchingEngine = new MatchingEngine();
        this.riskManager = new RiskManager();
        this.participants = new HashSet<>();
    }
    
    public void processOrder(Order order, TradingAccount account) {
        System.out.println("Processing order: " + order.getOrderId());
        
        // Risk check
        if (!riskManager.checkLimits(order, account)) {
            System.out.println("Order rejected due to risk limits");
            return;
        }
        
        // Submit order to account
        if (account.submitOrder(order)) {
            orderBook.addOrder(order);
            participants.add(account);
        }
    }
    
    public void matchOrders() {
        System.out.println("\\n=== Matching Orders in Dark Pool ===");
        matchingEngine.matchOrders(orderBook);
    }
    
    public double generateMidQuote() {
        return orderBook.getMidPrice();
    }
    
    public void maintainAnonymity() {
        System.out.println("Maintaining participant anonymity...");
    }
}

class OrderBook {
    private TreeMap<Double, List<Order>> bidOrders; // Price -> Orders (descending)
    private TreeMap<Double, List<Order>> askOrders; // Price -> Orders (ascending)
    private double midPrice;
    
    public OrderBook() {
        this.bidOrders = new TreeMap<>(Collections.reverseOrder());
        this.askOrders = new TreeMap<>();
        this.midPrice = 0.0;
    }
    
    public void addOrder(Order order) {
        TreeMap<Double, List<Order>> book = order.getSide().equals("BUY") ? bidOrders : askOrders;
        book.computeIfAbsent(order.getPrice(), k -> new ArrayList<>()).add(order);
        updateMidPrice();
        System.out.println("Order added to book: " + order.getOrderId() + " at $" + order.getPrice());
    }
    
    public Order getBestBid() {
        return bidOrders.isEmpty() ? null : bidOrders.firstEntry().getValue().get(0);
    }
    
    public Order getBestAsk() {
        return askOrders.isEmpty() ? null : askOrders.firstEntry().getValue().get(0);
    }
    
    public double getMidPrice() {
        return midPrice;
    }
    
    private void updateMidPrice() {
        Order bestBid = getBestBid();
        Order bestAsk = getBestAsk();
        if (bestBid != null && bestAsk != null) {
            midPrice = (bestBid.getPrice() + bestAsk.getPrice()) / 2.0;
        }
    }
    
    public TreeMap<Double, List<Order>> getBidOrders() { return bidOrders; }
    public TreeMap<Double, List<Order>> getAskOrders() { return askOrders; }
}

class MatchingEngine {
    private String algorithm = "FIFO"; // First In, First Out
    private boolean darkIndicator = true;
    
    public void matchOrders(OrderBook orderBook) {
        System.out.println("Running " + algorithm + " matching algorithm...");
        
        // Simple crossing logic at midpoint
        crossAtMidpoint(orderBook);
        
        if (darkIndicator) {
            maintainDarkness();
        }
    }
    
    private void crossAtMidpoint(OrderBook orderBook) {
        Order bestBid = orderBook.getBestBid();
        Order bestAsk = orderBook.getBestAsk();
        
        if (bestBid != null && bestAsk != null && bestBid.getPrice() >= bestAsk.getPrice()) {
            double executionPrice = orderBook.getMidPrice();
            int executionQuantity = Math.min(bestBid.getQuantity(), bestAsk.getQuantity());
            
            System.out.println("MATCH FOUND!");
            System.out.println("Execution Price: $" + executionPrice);
            System.out.println("Execution Quantity: " + executionQuantity + " shares");
            
            // Create trade record
            Trade trade = new Trade("TRD" + System.currentTimeMillis(), executionPrice, executionQuantity);
            System.out.println("Trade executed: " + trade.getTradeId());
        } else {
            System.out.println("No matching orders found");
        }
    }
    
    private void maintainDarkness() {
        System.out.println("Maintaining order anonymity and hidden liquidity...");
    }
}

class RiskManager {
    private double maxExposure = 1000000.0;
    private Map<String, Double> positionLimits;
    
    public RiskManager() {
        this.positionLimits = new HashMap<>();
        this.positionLimits.put("DEFAULT", 100000.0);
    }
    
    public boolean checkLimits(Order order, TradingAccount account) {
        double orderValue = order.calculateValue();
        double accountBalance = account.getBalance();
        
        // Check account balance
        if (orderValue > accountBalance) {
            System.out.println("Risk check failed: Insufficient account balance");
            return false;
        }
        
        // Check exposure limits
        if (orderValue > maxExposure) {
            System.out.println("Risk check failed: Order exceeds maximum exposure");
            return false;
        }
        
        System.out.println("Risk check passed for order: " + order.getOrderId());
        return true;
    }
    
    public double calculateRisk(Order order) {
        return order.calculateValue() / maxExposure;
    }
}

class Trade {
    private String tradeId;
    private double executionPrice;
    private int quantity;
    private LocalDateTime timestamp;
    
    public Trade(String tradeId, double executionPrice, int quantity) {
        this.tradeId = tradeId;
        this.executionPrice = executionPrice;
        this.quantity = quantity;
        this.timestamp = LocalDateTime.now();
    }
    
    public String getTradeId() { return tradeId; }
    public double getExecutionPrice() { return executionPrice; }
    public int getQuantity() { return quantity; }
    
    public double calculateValue() {
        return executionPrice * quantity;
    }
}`;
  };

  const oopConcepts = [
    {
      id: 'encapsulation',
      name: 'Encapsulation',
      icon: <Lock size={20} />,
      description: 'Data hiding and access control',
      color: '#ef4444'
    },
    {
      id: 'inheritance',
      name: 'Inheritance',
      icon: <GitBranch size={20} />,
      description: 'Code reusability through class hierarchies',
      color: '#f59e0b'
    },
    {
      id: 'polymorphism',
      name: 'Polymorphism',
      icon: <Sparkles size={20} />,
      description: 'Multiple forms of behavior',
      color: '#8b5cf6'
    },
    {
      id: 'abstraction',
      name: 'Abstraction',
      icon: <Layers size={20} />,
      description: 'Simplifying complex systems',
      color: '#06b6d4'
    }
  ];

  return (
    <div style={{
      padding: '40px',
      paddingTop: '100px',
      minHeight: '100vh',
      backgroundColor: '#0f172a',
      color: 'white'
    }}>
      <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
        <div style={{ marginBottom: '40px' }}>
          <h1 style={{ fontSize: '36px', fontWeight: 'bold', marginBottom: '16px', color: '#10b981' }}>
            Object-Oriented Programming in Java
          </h1>
          <p style={{ fontSize: '18px', color: '#94a3b8' }}>
            Core OOP principles and design patterns for financial systems
          </p>
        </div>

        <div style={{ display: 'flex', gap: '20px', marginBottom: '40px' }}>
          {['overview', 'definition', 'code', 'patterns', 'best-practices'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              style={{
                padding: '12px 24px',
                backgroundColor: activeTab === tab ? '#10b981' : 'transparent',
                border: `2px solid ${activeTab === tab ? '#10b981' : '#374151'}`,
                borderRadius: '8px',
                color: activeTab === tab ? 'white' : '#9ca3af',
                cursor: 'pointer',
                fontWeight: '600',
                textTransform: 'capitalize',
                transition: 'all 0.2s'
              }}
            >
              {tab.replace('-', ' ')}
            </button>
          ))}
        </div>

        {activeTab === 'overview' && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
            {/* Top Horizontal Bar with OOP Concepts */}
            <div style={{ 
              padding: '16px',
              backgroundColor: 'rgba(31, 41, 55, 0.5)',
              borderRadius: '12px',
              border: '1px solid #374151'
            }}>
              <div style={{ display: 'flex', gap: '12px', alignItems: 'center', justifyContent: 'center' }}>
                {oopConcepts.map((concept) => (
                  <div
                    key={concept.id}
                    onClick={() => {
                      console.log('Selecting concept:', concept);
                      setSelectedConcept(concept);
                    }}
                    style={{
                      padding: '10px 16px',
                      backgroundColor: selectedConcept?.id === concept.id ? 
                        'rgba(16, 185, 129, 0.2)' : 'rgba(31, 41, 55, 0.3)',
                      border: `2px solid ${selectedConcept?.id === concept.id ? concept.color : '#374151'}`,
                      borderRadius: '8px',
                      cursor: 'pointer',
                      transition: 'all 0.2s',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      minWidth: '150px'
                    }}
                  >
                    <div style={{ color: concept.color }}>{concept.icon}</div>
                    <div style={{ fontSize: '13px', fontWeight: '600', color: selectedConcept?.id === concept.id ? concept.color : '#e2e8f0' }}>
                      {concept.name}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* UML Diagram */}
            <div style={{ display: 'flex', justifyContent: 'flex-start', padding: '0 20px' }}>
              <div style={{ 
                backgroundColor: 'rgba(31, 41, 55, 0.5)',
                borderRadius: '12px',
                padding: '40px',
                border: '1px solid #374151',
                maxWidth: '1000px',
                width: '100%'
              }}>
                <h3 style={{ 
                  fontSize: '24px', 
                  fontWeight: 'bold', 
                  marginBottom: '30px', 
                  color: '#10b981',
                  textAlign: 'center' 
                }}>
                  Dark Pool Trading System - UML Class Diagram
                </h3>
                
                <div style={{ 
                  backgroundColor: '#1e293b',
                  borderRadius: '8px',
                  padding: '30px',
                  border: '1px solid #374151',
                  fontFamily: 'monospace',
                  fontSize: '11px',
                  color: '#e2e8f0',
                  overflowX: 'auto',
                  minHeight: '700px',
                  position: 'relative',
                  width: '1200px'
                }}>
                  
                  {/* TradingAccount Class - Encapsulation Example */}
                  <div 
                    onClick={() => setSelectedConcept(oopConcepts.find(c => c.id === 'encapsulation'))}
                    style={{
                      position: 'absolute',
                      top: '30px',
                      left: '50px',
                      width: '200px',
                      height: '120px',
                      border: `3px solid ${selectedConcept?.id === 'encapsulation' ? '#ef4444' : '#374151'}`,
                      backgroundColor: selectedConcept?.id === 'encapsulation' ? 'rgba(239, 68, 68, 0.25)' : 'rgba(55, 65, 81, 0.3)',
                      borderRadius: '6px',
                      padding: '8px',
                      cursor: 'pointer',
                      transition: 'all 0.2s',
                      boxShadow: selectedConcept?.id === 'encapsulation' ? '0 0 20px rgba(239, 68, 68, 0.5)' : 'none'
                    }}
                  >
                    <div style={{ fontWeight: 'bold', color: '#ef4444', marginBottom: '6px', fontSize: '11px' }}>TradingAccount</div>
                    <div style={{ borderBottom: '1px solid #374151', marginBottom: '6px' }}></div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- balance: double</div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- accountId: String</div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- positions: Map&lt;&gt;</div>
                    <div style={{ borderBottom: '1px solid #374151', marginBottom: '6px' }}></div>
                    <div style={{ fontSize: '9px', color: '#60a5fa', marginBottom: '3px' }}>+ getBalance(): double</div>
                    <div style={{ fontSize: '9px', color: '#60a5fa', marginBottom: '3px' }}>+ deposit(amount)</div>
                    <div style={{ fontSize: '9px', color: '#60a5fa' }}>+ submitOrder(order)</div>
                  </div>

                  {/* DarkPoolEngine - Core System */}
                  <div 
                    style={{
                      position: 'absolute',
                      top: '30px',
                      left: '290px',
                      width: '220px',
                      height: '140px',
                      border: '3px solid #10b981',
                      backgroundColor: 'rgba(16, 185, 129, 0.25)',
                      borderRadius: '6px',
                      padding: '8px',
                      cursor: 'pointer',
                      transition: 'all 0.2s',
                      boxShadow: '0 0 20px rgba(16, 185, 129, 0.3)'
                    }}
                  >
                    <div style={{ fontWeight: 'bold', color: '#10b981', marginBottom: '6px', fontSize: '11px' }}>DarkPoolEngine</div>
                    <div style={{ borderBottom: '1px solid #374151', marginBottom: '6px' }}></div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- orderBook: OrderBook</div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- matchingAlgo: Matcher</div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- riskManager: RiskMgr</div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- participants: Set&lt;&gt;</div>
                    <div style={{ borderBottom: '1px solid #374151', marginBottom: '6px' }}></div>
                    <div style={{ fontSize: '9px', color: '#60a5fa', marginBottom: '3px' }}>+ processOrder(order)</div>
                    <div style={{ fontSize: '9px', color: '#60a5fa', marginBottom: '3px' }}>+ matchOrders()</div>
                    <div style={{ fontSize: '9px', color: '#60a5fa', marginBottom: '3px' }}>+ generateMidQuote()</div>
                    <div style={{ fontSize: '9px', color: '#60a5fa' }}>+ maintainAnonymity()</div>
                  </div>

                  {/* Order Abstract Class - Abstraction */}
                  <div 
                    onClick={() => setSelectedConcept(oopConcepts.find(c => c.id === 'abstraction'))}
                    style={{
                      position: 'absolute',
                      top: '30px',
                      left: '550px',
                      width: '180px',
                      height: '110px',
                      border: `3px solid ${selectedConcept?.id === 'abstraction' ? '#06b6d4' : '#374151'}`,
                      backgroundColor: selectedConcept?.id === 'abstraction' ? 'rgba(6, 182, 212, 0.25)' : 'rgba(55, 65, 81, 0.3)',
                      borderRadius: '6px',
                      padding: '8px',
                      cursor: 'pointer',
                      transition: 'all 0.2s',
                      boxShadow: selectedConcept?.id === 'abstraction' ? '0 0 20px rgba(6, 182, 212, 0.5)' : 'none'
                    }}
                  >
                    <div style={{ fontWeight: 'bold', color: '#06b6d4', marginBottom: '6px', fontStyle: 'italic', fontSize: '11px' }}>«abstract» Order</div>
                    <div style={{ borderBottom: '1px solid #374151', marginBottom: '6px' }}></div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}># orderId: String</div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}># price: double</div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}># quantity: int</div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}># timestamp: long</div>
                    <div style={{ borderBottom: '1px solid #374151', marginBottom: '6px' }}></div>
                    <div style={{ fontSize: '9px', color: '#06b6d4', marginBottom: '3px', fontStyle: 'italic' }}>+ execute(): void</div>
                    <div style={{ fontSize: '9px', color: '#06b6d4', fontStyle: 'italic' }}>+ validate(): boolean</div>
                  </div>

                  {/* TradingStrategy Interface - Polymorphism */}
                  <div 
                    onClick={() => setSelectedConcept(oopConcepts.find(c => c.id === 'polymorphism'))}
                    style={{
                      position: 'absolute',
                      top: '30px',
                      left: '770px',
                      width: '180px',
                      height: '80px',
                      border: `3px solid ${selectedConcept?.id === 'polymorphism' ? '#8b5cf6' : '#374151'}`,
                      backgroundColor: selectedConcept?.id === 'polymorphism' ? 'rgba(139, 92, 246, 0.25)' : 'rgba(55, 65, 81, 0.3)',
                      borderRadius: '6px',
                      padding: '8px',
                      cursor: 'pointer',
                      transition: 'all 0.2s',
                      boxShadow: selectedConcept?.id === 'polymorphism' ? '0 0 20px rgba(139, 92, 246, 0.5)' : 'none'
                    }}
                  >
                    <div style={{ fontWeight: 'bold', color: '#8b5cf6', marginBottom: '6px', fontStyle: 'italic', fontSize: '11px' }}>«interface» TradingStrategy</div>
                    <div style={{ borderBottom: '1px solid #374151', marginBottom: '6px' }}></div>
                    <div style={{ fontSize: '9px', color: '#8b5cf6', fontStyle: 'italic', marginBottom: '3px' }}>+ calculateRisk(): double</div>
                    <div style={{ fontSize: '9px', color: '#8b5cf6', fontStyle: 'italic', marginBottom: '3px' }}>+ executeTrade(): void</div>
                    <div style={{ fontSize: '9px', color: '#8b5cf6', fontStyle: 'italic' }}>+ adjustPosition(): void</div>
                  </div>

                  {/* MarketOrder - Inheritance */}
                  <div 
                    onClick={() => setSelectedConcept(oopConcepts.find(c => c.id === 'inheritance'))}
                    style={{
                      position: 'absolute',
                      top: '180px',
                      left: '480px',
                      width: '160px',
                      height: '90px',
                      border: `3px solid ${selectedConcept?.id === 'inheritance' ? '#f59e0b' : '#374151'}`,
                      backgroundColor: selectedConcept?.id === 'inheritance' ? 'rgba(245, 158, 11, 0.25)' : 'rgba(55, 65, 81, 0.3)',
                      borderRadius: '6px',
                      padding: '8px',
                      cursor: 'pointer',
                      transition: 'all 0.2s',
                      boxShadow: selectedConcept?.id === 'inheritance' ? '0 0 20px rgba(245, 158, 11, 0.5)' : 'none'
                    }}
                  >
                    <div style={{ fontWeight: 'bold', color: '#f59e0b', marginBottom: '6px', fontSize: '11px' }}>MarketOrder</div>
                    <div style={{ borderBottom: '1px solid #374151', marginBottom: '6px' }}></div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- slippage: double</div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- urgency: Priority</div>
                    <div style={{ borderBottom: '1px solid #374151', marginBottom: '6px' }}></div>
                    <div style={{ fontSize: '9px', color: '#60a5fa', marginBottom: '3px' }}>+ execute(): void</div>
                    <div style={{ fontSize: '9px', color: '#60a5fa' }}>+ validate(): boolean</div>
                  </div>

                  {/* LimitOrder - Inheritance */}
                  <div 
                    onClick={() => setSelectedConcept(oopConcepts.find(c => c.id === 'inheritance'))}
                    style={{
                      position: 'absolute',
                      top: '180px',
                      left: '670px',
                      width: '160px',
                      height: '90px',
                      border: `3px solid ${selectedConcept?.id === 'inheritance' ? '#f59e0b' : '#374151'}`,
                      backgroundColor: selectedConcept?.id === 'inheritance' ? 'rgba(245, 158, 11, 0.25)' : 'rgba(55, 65, 81, 0.3)',
                      borderRadius: '6px',
                      padding: '8px',
                      cursor: 'pointer',
                      transition: 'all 0.2s',
                      boxShadow: selectedConcept?.id === 'inheritance' ? '0 0 20px rgba(245, 158, 11, 0.5)' : 'none'
                    }}
                  >
                    <div style={{ fontWeight: 'bold', color: '#f59e0b', marginBottom: '6px', fontSize: '11px' }}>LimitOrder</div>
                    <div style={{ borderBottom: '1px solid #374151', marginBottom: '6px' }}></div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- limitPrice: double</div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- timeInForce: TIF</div>
                    <div style={{ borderBottom: '1px solid #374151', marginBottom: '6px' }}></div>
                    <div style={{ fontSize: '9px', color: '#60a5fa', marginBottom: '3px' }}>+ execute(): void</div>
                    <div style={{ fontSize: '9px', color: '#60a5fa' }}>+ validate(): boolean</div>
                  </div>

                  {/* OrderBook */}
                  <div 
                    style={{
                      position: 'absolute',
                      top: '310px',
                      left: '50px',
                      width: '180px',
                      height: '110px',
                      border: '2px solid #6b7280',
                      backgroundColor: 'rgba(107, 114, 128, 0.15)',
                      borderRadius: '6px',
                      padding: '8px',
                      cursor: 'pointer',
                      transition: 'all 0.2s'
                    }}
                  >
                    <div style={{ fontWeight: 'bold', color: '#6b7280', marginBottom: '6px', fontSize: '11px' }}>OrderBook</div>
                    <div style={{ borderBottom: '1px solid #374151', marginBottom: '6px' }}></div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- bidOrders: TreeMap</div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- askOrders: TreeMap</div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- midPrice: double</div>
                    <div style={{ borderBottom: '1px solid #374151', marginBottom: '6px' }}></div>
                    <div style={{ fontSize: '9px', color: '#60a5fa', marginBottom: '3px' }}>+ addOrder(order)</div>
                    <div style={{ fontSize: '9px', color: '#60a5fa', marginBottom: '3px' }}>+ removeOrder(id)</div>
                    <div style={{ fontSize: '9px', color: '#60a5fa' }}>+ getBestBid()</div>
                  </div>

                  {/* MatchingEngine */}
                  <div 
                    style={{
                      position: 'absolute',
                      top: '310px',
                      left: '270px',
                      width: '180px',
                      height: '120px',
                      border: '2px solid #6b7280',
                      backgroundColor: 'rgba(107, 114, 128, 0.15)',
                      borderRadius: '6px',
                      padding: '8px',
                      cursor: 'pointer',
                      transition: 'all 0.2s'
                    }}
                  >
                    <div style={{ fontWeight: 'bold', color: '#6b7280', marginBottom: '6px', fontSize: '11px' }}>MatchingEngine</div>
                    <div style={{ borderBottom: '1px solid #374151', marginBottom: '6px' }}></div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- algorithm: MatchAlgo</div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- crossingNetwork: CN</div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- darkIndicator: boolean</div>
                    <div style={{ borderBottom: '1px solid #374151', marginBottom: '6px' }}></div>
                    <div style={{ fontSize: '9px', color: '#60a5fa', marginBottom: '3px' }}>+ matchOrders()</div>
                    <div style={{ fontSize: '9px', color: '#60a5fa', marginBottom: '3px' }}>+ crossAtMidpoint()</div>
                    <div style={{ fontSize: '9px', color: '#60a5fa', marginBottom: '3px' }}>+ prioritizeSize()</div>
                    <div style={{ fontSize: '9px', color: '#60a5fa' }}>+ maintainDarkness()</div>
                  </div>

                  {/* RiskManager */}
                  <div 
                    onClick={() => setSelectedConcept(oopConcepts.find(c => c.id === 'encapsulation'))}
                    style={{
                      position: 'absolute',
                      top: '310px',
                      left: '490px',
                      width: '180px',
                      height: '110px',
                      border: `2px solid ${selectedConcept?.id === 'encapsulation' ? '#ef4444' : '#6b7280'}`,
                      backgroundColor: selectedConcept?.id === 'encapsulation' ? 'rgba(239, 68, 68, 0.15)' : 'rgba(107, 114, 128, 0.15)',
                      borderRadius: '6px',
                      padding: '8px',
                      cursor: 'pointer',
                      transition: 'all 0.2s'
                    }}
                  >
                    <div style={{ fontWeight: 'bold', color: '#6b7280', marginBottom: '6px', fontSize: '11px' }}>RiskManager</div>
                    <div style={{ borderBottom: '1px solid #374151', marginBottom: '6px' }}></div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- maxExposure: double</div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- positionLimits: Map</div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- varCalculator: VaR</div>
                    <div style={{ borderBottom: '1px solid #374151', marginBottom: '6px' }}></div>
                    <div style={{ fontSize: '9px', color: '#60a5fa', marginBottom: '3px' }}>+ checkLimits(order)</div>
                    <div style={{ fontSize: '9px', color: '#60a5fa', marginBottom: '3px' }}>+ calculateRisk()</div>
                    <div style={{ fontSize: '9px', color: '#60a5fa' }}>+ blockOrder(order)</div>
                  </div>

                  {/* MarketDataFeed */}
                  <div 
                    style={{
                      position: 'absolute',
                      top: '310px',
                      left: '710px',
                      width: '180px',
                      height: '100px',
                      border: '2px solid #6b7280',
                      backgroundColor: 'rgba(107, 114, 128, 0.15)',
                      borderRadius: '6px',
                      padding: '8px',
                      cursor: 'pointer',
                      transition: 'all 0.2s'
                    }}
                  >
                    <div style={{ fontWeight: 'bold', color: '#6b7280', marginBottom: '6px', fontSize: '11px' }}>MarketDataFeed</div>
                    <div style={{ borderBottom: '1px solid #374151', marginBottom: '6px' }}></div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- livePrices: Map</div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- subscribers: Set</div>
                    <div style={{ borderBottom: '1px solid #374151', marginBottom: '6px' }}></div>
                    <div style={{ fontSize: '9px', color: '#60a5fa', marginBottom: '3px' }}>+ publishPrice(symbol)</div>
                    <div style={{ fontSize: '9px', color: '#60a5fa', marginBottom: '3px' }}>+ subscribe(listener)</div>
                    <div style={{ fontSize: '9px', color: '#60a5fa' }}>+ getReferencePrice()</div>
                  </div>

                  {/* ConservativeStrategy - Polymorphism */}
                  <div 
                    onClick={() => setSelectedConcept(oopConcepts.find(c => c.id === 'polymorphism'))}
                    style={{
                      position: 'absolute',
                      top: '150px',
                      left: '770px',
                      width: '160px',
                      height: '80px',
                      border: `2px solid ${selectedConcept?.id === 'polymorphism' ? '#8b5cf6' : '#6b7280'}`,
                      backgroundColor: selectedConcept?.id === 'polymorphism' ? 'rgba(139, 92, 246, 0.15)' : 'rgba(107, 114, 128, 0.15)',
                      borderRadius: '6px',
                      padding: '8px',
                      cursor: 'pointer',
                      transition: 'all 0.2s'
                    }}
                  >
                    <div style={{ fontWeight: 'bold', color: '#6b7280', marginBottom: '6px', fontSize: '11px' }}>ConservativeStrategy</div>
                    <div style={{ borderBottom: '1px solid #374151', marginBottom: '6px' }}></div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- riskTolerance: 2%</div>
                    <div style={{ borderBottom: '1px solid #374151', marginBottom: '6px' }}></div>
                    <div style={{ fontSize: '9px', color: '#60a5fa', marginBottom: '3px' }}>+ calculateRisk()</div>
                    <div style={{ fontSize: '9px', color: '#60a5fa', marginBottom: '3px' }}>+ executeTrade()</div>
                    <div style={{ fontSize: '9px', color: '#60a5fa' }}>+ adjustPosition()</div>
                  </div>

                  {/* Trade - Result Class */}
                  <div 
                    style={{
                      position: 'absolute',
                      top: '470px',
                      left: '290px',
                      width: '160px',
                      height: '90px',
                      border: '2px solid #10b981',
                      backgroundColor: 'rgba(16, 185, 129, 0.15)',
                      borderRadius: '6px',
                      padding: '8px',
                      cursor: 'pointer',
                      transition: 'all 0.2s'
                    }}
                  >
                    <div style={{ fontWeight: 'bold', color: '#10b981', marginBottom: '6px', fontSize: '11px' }}>Trade</div>
                    <div style={{ borderBottom: '1px solid #374151', marginBottom: '6px' }}></div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- tradeId: String</div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- executionPrice: double</div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- quantity: int</div>
                    <div style={{ fontSize: '9px', color: '#9ca3af', marginBottom: '4px' }}>- timestamp: long</div>
                    <div style={{ borderBottom: '1px solid #374151', marginBottom: '6px' }}></div>
                    <div style={{ fontSize: '9px', color: '#60a5fa' }}>+ calculateValue()</div>
                  </div>

                  {/* Connection Lines */}
                  {/* Account to DarkPool */}
                  <div style={{
                    position: 'absolute',
                    top: '90px',
                    left: '250px',
                    width: '40px',
                    height: '2px',
                    backgroundColor: '#374151'
                  }}></div>

                  {/* DarkPool to Order */}
                  <div style={{
                    position: 'absolute',
                    top: '100px',
                    left: '510px',
                    width: '40px',
                    height: '2px',
                    backgroundColor: '#374151'
                  }}></div>

                  {/* Order to MarketOrder (inheritance arrow) */}
                  <div style={{
                    position: 'absolute',
                    top: '140px',
                    left: '570px',
                    width: '2px',
                    height: '40px',
                    backgroundColor: '#f59e0b'
                  }}></div>

                  {/* Order to LimitOrder (inheritance arrow) */}
                  <div style={{
                    position: 'absolute',
                    top: '140px',
                    left: '720px',
                    width: '2px',
                    height: '40px',
                    backgroundColor: '#f59e0b'
                  }}></div>

                  {/* DarkPool to components */}
                  <div style={{
                    position: 'absolute',
                    top: '170px',
                    left: '160px',
                    width: '2px',
                    height: '140px',
                    backgroundColor: '#374151'
                  }}></div>
                  <div style={{
                    position: 'absolute',
                    top: '170px',
                    left: '360px',
                    width: '2px',
                    height: '140px',
                    backgroundColor: '#374151'
                  }}></div>

                  {/* Strategy implementation line */}
                  <div style={{
                    position: 'absolute',
                    top: '110px',
                    left: '850px',
                    width: '2px',
                    height: '40px',
                    backgroundColor: '#8b5cf6',
                    borderStyle: 'dashed'
                  }}></div>

                  {/* Legend */}
                  <div style={{
                    position: 'absolute',
                    bottom: '20px',
                    right: '20px',
                    backgroundColor: 'rgba(31, 41, 55, 0.8)',
                    padding: '12px',
                    borderRadius: '6px',
                    border: '1px solid #374151',
                    fontSize: '9px'
                  }}>
                    <div style={{ fontWeight: 'bold', marginBottom: '8px', color: '#10b981' }}>Dark Pool UML Legend</div>
                    <div style={{ marginBottom: '4px' }}>- : private fields</div>
                    <div style={{ marginBottom: '4px' }}># : protected fields</div>
                    <div style={{ marginBottom: '4px' }}>+ : public methods</div>
                    <div style={{ marginBottom: '4px' }}>«interface» : interface</div>
                    <div style={{ marginBottom: '4px' }}>«abstract» : abstract class</div>
                    <div style={{ marginBottom: '4px', color: '#10b981' }}>■ : Core Engine</div>
                    <div style={{ color: '#ef4444' }}>■ : Encapsulation</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'code' && (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
              <h3 style={{ fontSize: '24px', fontWeight: '600', color: '#10b981', margin: 0 }}>
                Complete Dark Pool Trading System
              </h3>
              <button
                onClick={() => {
                  const allCode = generateCompleteJavaCode();
                  const blob = new Blob([allCode], { type: 'text/plain' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = 'DarkPoolTradingSystem.java';
                  a.click();
                  URL.revokeObjectURL(url);
                }}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#10b981',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  fontSize: '14px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}
              >
                <span>📥</span> Download Complete System
              </button>
            </div>
            
            <div style={{ marginBottom: '16px' }}>
              <p style={{ color: '#94a3b8', fontSize: '16px', lineHeight: '1.6' }}>
                Complete, runnable Java implementation of a Dark Pool Trading System demonstrating all OOP principles.
                Includes order matching, risk management, account handling, and polymorphic trading strategies.
              </p>
            </div>

            <div style={{ marginBottom: '20px', padding: '16px', backgroundColor: 'rgba(16, 185, 129, 0.1)', borderRadius: '8px', border: '1px solid #10b981' }}>
              <h4 style={{ color: '#10b981', fontSize: '14px', marginBottom: '8px' }}>🚀 Quick Start Instructions</h4>
              <div style={{ color: '#d1d5db', fontSize: '13px' }}>
                <p>1. Click the download button above to get <code style={{ background: '#1f2937', padding: '2px 6px', borderRadius: '4px' }}>DarkPoolTradingSystem.java</code></p>
                <p>2. Compile: <code style={{ background: '#1f2937', padding: '2px 6px', borderRadius: '4px' }}>javac DarkPoolTradingSystem.java</code></p>
                <p>3. Run: <code style={{ background: '#1f2937', padding: '2px 6px', borderRadius: '4px' }}>java DarkPoolTradingSystem</code></p>
              </div>
            </div>

            <SyntaxHighlighter 
              language="java" 
              style={oneDark}
              customStyle={{
                backgroundColor: '#1e293b',
                padding: '20px',
                borderRadius: '8px',
                fontSize: '12px',
                border: '1px solid #374151',
                maxHeight: '800px'
              }}
            >
{`// DarkPoolTradingSystem.java - Complete Implementation
// Compile: javac DarkPoolTradingSystem.java
// Run: java DarkPoolTradingSystem

import java.util.*;
import java.util.concurrent.*;
import java.time.LocalDateTime;
import java.math.BigDecimal;

// ==== MAIN EXECUTABLE CLASS ====
public class DarkPoolTradingSystem {
    public static void main(String[] args) {
        System.out.println("=== Dark Pool Trading System Demo ===\\n");
        
        // Initialize the system
        DarkPoolEngine engine = new DarkPoolEngine();
        
        // Create trading accounts
        TradingAccount account1 = new TradingAccount("ACC001", 1000000.0);
        TradingAccount account2 = new TradingAccount("ACC002", 750000.0);
        
        System.out.println("Initial Balances:");
        System.out.println("Account 1: $" + account1.getBalance());
        System.out.println("Account 2: $" + account2.getBalance());
        
        // Create and submit orders
        MarketOrder buyOrder = new MarketOrder("ORD001", 150.0, 100, "BUY");
        LimitOrder sellOrder = new LimitOrder("ORD002", 149.5, 100, "SELL");
        
        System.out.println("\\nSubmitting orders to dark pool...");
        engine.processOrder(buyOrder, account1);
        engine.processOrder(sellOrder, account2);
        
        // Execute matching
        engine.matchOrders();
        
        // Display results
        System.out.println("\\nFinal account balances:");
        System.out.println("Account 1: $" + account1.getBalance());
        System.out.println("Account 2: $" + account2.getBalance());
        
        // Demonstrate polymorphism with trading strategies
        TradingStrategy conservative = new ConservativeStrategy();
        TradingStrategy aggressive = new AggressiveStrategy();
        
        System.out.println("\\n=== Trading Strategy Demonstration ===");
        System.out.println("Conservative risk: " + conservative.calculateRisk() * 100 + "%");
        System.out.println("Aggressive risk: " + aggressive.calculateRisk() * 100 + "%");
        
        // Execute strategies
        conservative.executeTrade();
        aggressive.executeTrade();
    }
}

// ==== ENCAPSULATION EXAMPLE ====
class TradingAccount {
    // Private fields - encapsulated data
    private double balance;
    private String accountId;
    private Map<String, Integer> positions;
    private List<Trade> tradeHistory;
    
    // Constructor
    public TradingAccount(String accountId, double initialBalance) {
        this.accountId = accountId;
        this.balance = initialBalance;
        this.positions = new HashMap<>();
        this.tradeHistory = new ArrayList<>();
    }
    
    // Public methods - controlled access
    public double getBalance() {
        return balance;
    }
    
    public String getAccountId() {
        return accountId;
    }
    
    public void deposit(double amount) {
        if (amount > 0) {
            balance += amount;
            logTransaction("DEPOSIT", amount);
        }
    }
    
    public boolean withdraw(double amount) {
        if (amount > 0 && balance >= amount) {
            balance -= amount;
            logTransaction("WITHDRAWAL", amount);
            return true;
        }
        return false;
    }
    
    public boolean submitOrder(Order order) {
        double requiredMargin = order.calculateRequiredMargin();
        if (balance >= requiredMargin) {
            balance -= requiredMargin;
            System.out.println("Order submitted: " + order.getOrderId());
            return true;
        }
        System.out.println("Insufficient funds for order: " + order.getOrderId());
        return false;
    }
    
    // Private helper method - implementation detail
    private void logTransaction(String type, double amount) {
        System.out.println("Transaction logged: " + type + " $" + amount + " for account " + accountId);
    }
}

// ==== ABSTRACTION EXAMPLE ====
abstract class Order {
    // Protected fields - accessible to subclasses
    protected String orderId;
    protected double price;
    protected int quantity;
    protected String side; // BUY or SELL
    protected LocalDateTime timestamp;
    
    // Constructor
    public Order(String orderId, double price, int quantity, String side) {
        this.orderId = orderId;
        this.price = price;
        this.quantity = quantity;
        this.side = side;
        this.timestamp = LocalDateTime.now();
    }
    
    // Abstract methods - must be implemented by subclasses
    public abstract void execute();
    public abstract boolean validate();
    public abstract double calculateRequiredMargin();
    
    // Concrete methods - shared implementation
    public String getOrderId() { return orderId; }
    public double getPrice() { return price; }
    public int getQuantity() { return quantity; }
    public String getSide() { return side; }
    
    public double calculateValue() {
        return price * quantity;
    }
}

// ==== INHERITANCE EXAMPLES ====
class MarketOrder extends Order {
    private double slippage;
    private String priority;
    
    public MarketOrder(String orderId, double price, int quantity, String side) {
        super(orderId, price, quantity, side);
        this.slippage = 0.01; // Default 1% slippage
        this.priority = "HIGH";
    }
    
    @Override
    public void execute() {
        double executionPrice = price * (1 + slippage);
        System.out.println("Executing Market Order " + orderId + " at price: $" + executionPrice);
    }
    
    @Override
    public boolean validate() {
        return quantity > 0 && price > 0;
    }
    
    @Override
    public double calculateRequiredMargin() {
        return price * quantity * 0.1; // 10% margin requirement
    }
}

class LimitOrder extends Order {
    private double limitPrice;
    private String timeInForce;
    
    public LimitOrder(String orderId, double limitPrice, int quantity, String side) {
        super(orderId, limitPrice, quantity, side);
        this.limitPrice = limitPrice;
        this.timeInForce = "DAY";
    }
    
    @Override
    public void execute() {
        System.out.println("Executing Limit Order " + orderId + " at limit price: $" + limitPrice);
    }
    
    @Override
    public boolean validate() {
        return quantity > 0 && limitPrice > 0;
    }
    
    @Override
    public double calculateRequiredMargin() {
        return limitPrice * quantity * 0.05; // 5% margin for limit orders
    }
}

// ==== POLYMORPHISM EXAMPLE ====
interface TradingStrategy {
    double calculateRisk();
    void executeTrade();
    void adjustPosition();
}

class ConservativeStrategy implements TradingStrategy {
    private double riskTolerance = 0.02; // 2% risk tolerance
    
    @Override
    public double calculateRisk() {
        return riskTolerance;
    }
    
    @Override
    public void executeTrade() {
        System.out.println("Executing conservative trade with 2% max risk...");
        System.out.println("Using small position sizes and tight stop losses");
    }
    
    @Override
    public void adjustPosition() {
        System.out.println("Adjusting position conservatively - reducing exposure");
    }
}

class AggressiveStrategy implements TradingStrategy {
    private double riskTolerance = 0.10; // 10% risk tolerance
    
    @Override
    public double calculateRisk() {
        return riskTolerance;
    }
    
    @Override
    public void executeTrade() {
        System.out.println("Executing aggressive trade with 10% max risk...");
        System.out.println("Using larger position sizes for higher returns");
    }
    
    @Override
    public void adjustPosition() {
        System.out.println("Adjusting position aggressively - increasing exposure");
    }
}

// Download the complete file to see DarkPoolEngine, OrderBook, MatchingEngine, 
// RiskManager, and Trade classes with full implementation...`}
            </SyntaxHighlighter>
          </div>
        )}

        {/* Other tabs remain simplified for now */}
      </div>
      
        {/* Details Panel - Moved to bottom */}
      {selectedConcept && (
        <div style={{
          backgroundColor: 'rgba(31, 41, 55, 0.5)',
          borderRadius: '12px',
          border: '1px solid #374151',
          padding: '24px',
          marginTop: '32px'
                  }}>
          <div style={{
            padding: '20px',
            borderBottom: '1px solid #374151',
            background: 'linear-gradient(135deg, #1f2937 0%, #**********%)'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start' }}>
              <div>
                <h2 style={{ fontSize: '18px', fontWeight: 'bold', color: '#fbbf24', margin: '0 0 8px 0' }}>
                  {selectedConcept.name}
                </h2>
                <div style={{ fontSize: '12px', color: '#9ca3af' }}>
                  {selectedConcept.description}
                </div>
              </div>
              <button
                onClick={() => setSelectedConcept(null)}
                style={{
                  background: 'transparent',
                  border: 'none',
                  color: '#9ca3af',
                  fontSize: '24px',
                  cursor: 'pointer',
                  padding: 0,
                  lineHeight: 1
                }}
              >
                ×
              </button>
            </div>
          </div>

          <div style={{ display: 'flex', borderBottom: '1px solid #374151' }}>
            {['definition', 'code', 'usage', 'best-practices'].map(tab => (
              <button
                key={tab}
                onClick={() => setDetailsTab(tab)}
                style={{
                  flex: 1,
                  padding: '12px',
                  background: detailsTab === tab ? '#1f2937' : 'transparent',
                  border: 'none',
                  color: detailsTab === tab ? '#fbbf24' : '#9ca3af',
                  fontSize: '12px',
                  fontWeight: '600',
                  textTransform: 'capitalize',
                  cursor: 'pointer'
                }}
              >
                {tab.replace('-', ' ')}
              </button>
            ))}
          </div>

          <div style={{ flex: 1, padding: '20px', overflowY: 'auto' }}>
            {detailsTab === 'definition' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  What is {selectedConcept.name}?
                </h3>
                <div style={{ color: '#d1d5db', fontSize: '13px', lineHeight: '1.8' }}>
                  {selectedConcept.id === 'encapsulation' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Encapsulation is the bundling of data (attributes) and methods (functions) that operate on that data within a single unit (class). It restricts direct access to some of the object's components, which is a means of preventing accidental interference and misuse.
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Key Benefits:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>Protects sensitive financial data from unauthorized access</li>
                        <li>Ensures data integrity by controlling how account balances and positions are modified</li>
                        <li>Prevents external code from putting the system in an invalid state</li>
                        <li>Makes the code more maintainable by hiding implementation details</li>
                      </ul>
                    </>
                  )}
                  
                  {selectedConcept.id === 'inheritance' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Inheritance allows a new class to be based on an existing class, inheriting its attributes and methods. The new class (subclass) can extend or modify the behavior of the existing class (superclass).
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Key Benefits:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>Code reusability - common functionality is written once</li>
                        <li>Establishes an "is-a" relationship between classes</li>
                        <li>Enables polymorphic behavior through method overriding</li>
                        <li>Creates a hierarchical organization of classes</li>
                      </ul>
                    </>
                  )}
                  
                  {selectedConcept.id === 'polymorphism' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Polymorphism allows objects of different types to be treated as objects of a common base type. It enables a single interface to represent different underlying forms (data types).
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Key Benefits:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>Enables flexible and extensible code design</li>
                        <li>Supports runtime method resolution</li>
                        <li>Allows for easy addition of new types without modifying existing code</li>
                        <li>Essential for implementing design patterns like Strategy and Template Method</li>
                      </ul>
                    </>
                  )}
                  
                  {selectedConcept.id === 'abstraction' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Abstraction is the process of hiding the complex implementation details and showing only the essential features of an object. It helps in managing complexity by providing a simplified interface.
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Key Benefits:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>Reduces complexity by hiding unnecessary implementation details</li>
                        <li>Provides a clear contract for what methods must be implemented</li>
                        <li>Enables consistent interfaces across different implementations</li>
                        <li>Facilitates code maintenance and evolution</li>
                      </ul>
                    </>
                  )}
                </div>
              </div>
            )}

            {detailsTab === 'code' && (
              <div>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                  <h3 style={{ color: '#10b981', fontSize: '14px', margin: 0 }}>
                    {selectedConcept ? `${selectedConcept.name} Example` : 'Complete Dark Pool System'}
                  </h3>
                  <button
                    onClick={() => {
                      const allCode = generateCompleteJavaCode();
                      const blob = new Blob([allCode], { type: 'text/plain' });
                      const url = URL.createObjectURL(blob);
                      const a = document.createElement('a');
                      a.href = url;
                      a.download = 'DarkPoolTradingSystem.java';
                      a.click();
                      URL.revokeObjectURL(url);
                    }}
                    style={{
                      padding: '6px 12px',
                      backgroundColor: '#10b981',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      fontSize: '12px',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '6px'
                    }}
                  >
                    <span>📥</span> Download Complete System
                  </button>
                </div>
                
                <div style={{ marginBottom: '20px' }}>
                  <h4 style={{ color: '#fbbf24', fontSize: '13px', marginBottom: '12px' }}>
                    Complete Runnable Dark Pool Trading System
                  </h4>
                  <SyntaxHighlighter 
                    language="java" 
                    style={oneDark}
                    customStyle={{
                      backgroundColor: '#1e293b',
                      padding: '16px',
                      borderRadius: '8px',
                      fontSize: '11px',
                      border: '1px solid #374151',
                      maxHeight: '600px'
                    }}
                  >
{`// DarkPoolTradingSystem.java - Complete Implementation
// Compile: javac DarkPoolTradingSystem.java
// Run: java DarkPoolTradingSystem

import java.util.*;
import java.util.concurrent.*;
import java.time.LocalDateTime;
import java.math.BigDecimal;

// ==== MAIN EXECUTABLE CLASS ====
public class DarkPoolTradingSystem {
    public static void main(String[] args) {
        System.out.println("=== Dark Pool Trading System Demo ===\\n");
        
        // Initialize the system
        DarkPoolEngine engine = new DarkPoolEngine();
        
        // Create trading accounts
        TradingAccount account1 = new TradingAccount("ACC001", 1000000.0);
        TradingAccount account2 = new TradingAccount("ACC002", 750000.0);
        
        System.out.println("Initial Balances:");
        System.out.println("Account 1: $" + account1.getBalance());
        System.out.println("Account 2: $" + account2.getBalance());
        
        // Create and submit orders
        MarketOrder buyOrder = new MarketOrder("ORD001", 150.0, 100, "BUY");
        LimitOrder sellOrder = new LimitOrder("ORD002", 149.5, 100, "SELL");
        
        System.out.println("\\nSubmitting orders to dark pool...");
        engine.processOrder(buyOrder, account1);
        engine.processOrder(sellOrder, account2);
        
        // Execute matching
        engine.matchOrders();
        
        // Display results
        System.out.println("\\nFinal account balances:");
        System.out.println("Account 1: $" + account1.getBalance());
        System.out.println("Account 2: $" + account2.getBalance());
        
        // Demonstrate polymorphism with trading strategies
        TradingStrategy conservative = new ConservativeStrategy();
        TradingStrategy aggressive = new AggressiveStrategy();
        
        System.out.println("\\n=== Trading Strategy Demonstration ===");
        System.out.println("Conservative risk: " + conservative.calculateRisk() * 100 + "%");
        System.out.println("Aggressive risk: " + aggressive.calculateRisk() * 100 + "%");
        
        // Execute strategies
        conservative.executeTrade();
        aggressive.executeTrade();
    }
}

// ==== ENCAPSULATION EXAMPLE ====
class TradingAccount {
    // Private fields - encapsulated data
    private double balance;
    private String accountId;
    private Map<String, Integer> positions;
    private List<Trade> tradeHistory;
    
    // Constructor
    public TradingAccount(String accountId, double initialBalance) {
        this.accountId = accountId;
        this.balance = initialBalance;
        this.positions = new HashMap<>();
        this.tradeHistory = new ArrayList<>();
    }
    
    // Public methods - controlled access
    public double getBalance() {
        return balance;
    }
    
    public String getAccountId() {
        return accountId;
    }
    
    public void deposit(double amount) {
        if (amount > 0) {
            balance += amount;
            logTransaction("DEPOSIT", amount);
        }
    }
    
    public boolean withdraw(double amount) {
        if (amount > 0 && balance >= amount) {
            balance -= amount;
            logTransaction("WITHDRAWAL", amount);
            return true;
        }
        return false;
    }
    
    public boolean submitOrder(Order order) {
        double requiredMargin = order.calculateRequiredMargin();
        if (balance >= requiredMargin) {
            balance -= requiredMargin;
            System.out.println("Order submitted: " + order.getOrderId());
            return true;
        }
        System.out.println("Insufficient funds for order: " + order.getOrderId());
        return false;
    }
    
    // Private helper method - implementation detail
    private void logTransaction(String type, double amount) {
        System.out.println("Transaction logged: " + type + " $" + amount + " for account " + accountId);
    }
}

// ==== ABSTRACTION EXAMPLE ====
abstract class Order {
    // Protected fields - accessible to subclasses
    protected String orderId;
    protected double price;
    protected int quantity;
    protected String side; // BUY or SELL
    protected LocalDateTime timestamp;
    
    // Constructor
    public Order(String orderId, double price, int quantity, String side) {
        this.orderId = orderId;
        this.price = price;
        this.quantity = quantity;
        this.side = side;
        this.timestamp = LocalDateTime.now();
    }
    
    // Abstract methods - must be implemented by subclasses
    public abstract void execute();
    public abstract boolean validate();
    public abstract double calculateRequiredMargin();
    
    // Concrete methods - shared implementation
    public String getOrderId() { return orderId; }
    public double getPrice() { return price; }
    public int getQuantity() { return quantity; }
    public String getSide() { return side; }
    
    public double calculateValue() {
        return price * quantity;
    }
}

// ==== INHERITANCE EXAMPLES ====
class MarketOrder extends Order {
    private double slippage;
    private String priority;
    
    public MarketOrder(String orderId, double price, int quantity, String side) {
        super(orderId, price, quantity, side);
        this.slippage = 0.01; // Default 1% slippage
        this.priority = "HIGH";
    }
    
    @Override
    public void execute() {
        double executionPrice = price * (1 + slippage);
        System.out.println("Executing Market Order " + orderId + " at price: $" + executionPrice);
    }
    
    @Override
    public boolean validate() {
        return quantity > 0 && price > 0;
    }
    
    @Override
    public double calculateRequiredMargin() {
        return price * quantity * 0.1; // 10% margin requirement
    }
}

class LimitOrder extends Order {
    private double limitPrice;
    private String timeInForce;
    
    public LimitOrder(String orderId, double limitPrice, int quantity, String side) {
        super(orderId, limitPrice, quantity, side);
        this.limitPrice = limitPrice;
        this.timeInForce = "DAY";
    }
    
    @Override
    public void execute() {
        System.out.println("Executing Limit Order " + orderId + " at limit price: $" + limitPrice);
    }
    
    @Override
    public boolean validate() {
        return quantity > 0 && limitPrice > 0;
    }
    
    @Override
    public double calculateRequiredMargin() {
        return limitPrice * quantity * 0.05; // 5% margin for limit orders
    }
}

// ==== POLYMORPHISM EXAMPLE ====
interface TradingStrategy {
    double calculateRisk();
    void executeTrade();
    void adjustPosition();
}

class ConservativeStrategy implements TradingStrategy {
    private double riskTolerance = 0.02; // 2% risk tolerance
    
    @Override
    public double calculateRisk() {
        return riskTolerance;
    }
    
    @Override
    public void executeTrade() {
        System.out.println("Executing conservative trade with 2% max risk...");
        System.out.println("Using small position sizes and tight stop losses");
    }
    
    @Override
    public void adjustPosition() {
        System.out.println("Adjusting position conservatively - reducing exposure");
    }
}

class AggressiveStrategy implements TradingStrategy {
    private double riskTolerance = 0.10; // 10% risk tolerance
    
    @Override
    public double calculateRisk() {
        return riskTolerance;
    }
    
    @Override
    public void executeTrade() {
        System.out.println("Executing aggressive trade with 10% max risk...");
        System.out.println("Using larger position sizes for higher returns");
    }
    
    @Override
    public void adjustPosition() {
        System.out.println("Adjusting position aggressively - increasing exposure");
    }
}

// See download for complete DarkPoolEngine, OrderBook, MatchingEngine, RiskManager, and Trade classes...`}
                  </SyntaxHighlighter>
                </div>
                
                {selectedConcept?.id === 'encapsulation' && (
                  <SyntaxHighlighter 
                    language="java" 
                    style={oneDark}
                    customStyle={{
                      backgroundColor: '#1e293b',
                      padding: '16px',
                      borderRadius: '8px',
                      fontSize: '12px',
                      border: '1px solid #374151'
                    }}
                  >
{`public class TradingAccount {
    // Private fields - encapsulated data
    private double balance;
    private String accountId;
    private List<Trade> trades;
    
    // Constructor
    public TradingAccount(String accountId, double initialBalance) {
        this.accountId = accountId;
        this.balance = initialBalance;
        this.trades = new ArrayList<>();
    }
    
    // Public methods - controlled access
    public double getBalance() {
        return balance;
    }
    
    public void deposit(double amount) {
        if (amount > 0) {
            balance += amount;
            logTransaction("DEPOSIT", amount);
        }
    }
    
    public boolean executeTrade(Trade trade) {
        if (balance >= trade.getRequiredMargin()) {
            trades.add(trade);
            balance -= trade.getRequiredMargin();
            return true;
        }
        return false;
    }
    
    // Private helper method - implementation detail
    private void logTransaction(String type, double amount) {
        // Internal logging logic
    }
}`}
                  </SyntaxHighlighter>
                )}

                {selectedConcept.id === 'inheritance' && (
                  <SyntaxHighlighter 
                    language="java" 
                    style={oneDark}
                    customStyle={{
                      backgroundColor: '#1e293b',
                      padding: '16px',
                      borderRadius: '8px',
                      fontSize: '12px',
                      border: '1px solid #374151'
                    }}
                  >
{`// Base class
public abstract class Order {
    protected String orderId;
    protected double price;
    protected int quantity;
    
    public Order(String orderId, double price, int quantity) {
        this.orderId = orderId;
        this.price = price;
        this.quantity = quantity;
    }
    
    // Abstract methods - must be implemented by subclasses
    public abstract void execute();
    public abstract double calculateValue();
}

// Derived class
public class MarketOrder extends Order {
    private double slippage;
    
    public MarketOrder(String orderId, double price, int quantity) {
        super(orderId, price, quantity); // Call parent constructor
        this.slippage = 0.01; // Default slippage
    }
    
    @Override
    public void execute() {
        double executionPrice = price * (1 + slippage);
        System.out.println("Executing market order at: " + executionPrice);
        // Market-specific execution logic
    }
    
    @Override
    public double calculateValue() {
        return price * quantity * (1 + slippage);
    }
}`}
                  </SyntaxHighlighter>
                )}

                {selectedConcept.id === 'polymorphism' && (
                  <SyntaxHighlighter 
                    language="java" 
                    style={oneDark}
                    customStyle={{
                      backgroundColor: '#1e293b',
                      padding: '16px',
                      borderRadius: '8px',
                      fontSize: '12px',
                      border: '1px solid #374151'
                    }}
                  >
{`// Interface defining common behavior
public interface TradingStrategy {
    double calculateRisk();
    void executeTrade();
}

// Different implementations
public class ConservativeStrategy implements TradingStrategy {
    @Override
    public double calculateRisk() {
        return 0.02; // 2% max risk
    }
    
    @Override
    public void executeTrade() {
        System.out.println("Executing conservative trade...");
    }
}

public class AggressiveStrategy implements TradingStrategy {
    @Override
    public double calculateRisk() {
        return 0.10; // 10% max risk
    }
    
    @Override
    public void executeTrade() {
        System.out.println("Executing aggressive trade...");
    }
}

// Using polymorphism
public class TradingEngine {
    public void executeStrategy(TradingStrategy strategy) {
        // Same method call, different behavior
        double risk = strategy.calculateRisk();
        if (risk <= MAX_ACCEPTABLE_RISK) {
            strategy.executeTrade();
        }
    }
}`}
                  </SyntaxHighlighter>
                )}

                {selectedConcept.id === 'abstraction' && (
                  <SyntaxHighlighter 
                    language="java" 
                    style={oneDark}
                    customStyle={{
                      backgroundColor: '#1e293b',
                      padding: '16px',
                      borderRadius: '8px',
                      fontSize: '12px',
                      border: '1px solid #374151'
                    }}
                  >
{`// Abstract class providing template
public abstract class OrderProcessor {
    // Template method - defines the process
    public final void processOrder(Order order) {
        validateOrder(order);
        executeOrder(order);
        recordTransaction(order);
        notifyClient(order);
    }
    
    // Concrete method - same for all processors
    private void validateOrder(Order order) {
        // Common validation logic
    }
    
    // Abstract methods - specific implementation required
    protected abstract void executeOrder(Order order);
    protected abstract void recordTransaction(Order order);
    
    // Hook method - can be overridden
    protected void notifyClient(Order order) {
        // Default notification
    }
}

// Concrete implementation
public class DarkPoolProcessor extends OrderProcessor {
    @Override
    protected void executeOrder(Order order) {
        // Dark pool specific execution
        hiddenOrderExecution(order);
    }
    
    @Override
    protected void recordTransaction(Order order) {
        // Anonymous transaction recording
        recordAnonymously(order);
    }
    
    private void hiddenOrderExecution(Order order) {
        // Implementation details hidden
    }
}`}
                  </SyntaxHighlighter>
                )}
              </div>
            )}

            {detailsTab === 'usage' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  {selectedConcept.name} in Trading Systems
                </h3>
                <div style={{ display: 'grid', gap: '12px' }}>
                  {selectedConcept.id === 'encapsulation' && [
                    'Account balance protection - prevents direct manipulation',
                    'Position data security - controls access to sensitive trading positions',
                    'Risk calculation internals - hides complex risk algorithms',
                    'Order validation logic - encapsulates business rules',
                    'Market data access - controls how price feeds are consumed'
                  ].map((usage, idx) => (
                    <div key={idx} style={{
                      padding: '12px',
                      background: '#1f2937',
                      borderRadius: '6px',
                      borderLeft: '3px solid #ef4444',
                      color: '#d1d5db',
                      fontSize: '13px'
                    }}>
                      {usage}
                    </div>
                  ))}

                  {selectedConcept.id === 'inheritance' && [
                    'Order type hierarchy - MarketOrder, LimitOrder extend base Order',
                    'Trading strategy families - Different algorithms sharing common interface',
                    'Account types - Individual, Corporate accounts with shared functionality',
                    'Risk assessment models - Specialized calculators for different asset classes',
                    'Notification systems - Email, SMS, Push notifications with common base'
                  ].map((usage, idx) => (
                    <div key={idx} style={{
                      padding: '12px',
                      background: '#1f2937',
                      borderRadius: '6px',
                      borderLeft: '3px solid #f59e0b',
                      color: '#d1d5db',
                      fontSize: '13px'
                    }}>
                      {usage}
                    </div>
                  ))}

                  {selectedConcept.id === 'polymorphism' && [
                    'Strategy pattern - Different trading algorithms with same interface',
                    'Order execution - Various order types processed uniformly',
                    'Risk calculators - Multiple risk models used interchangeably',
                    'Market data providers - Different feeds with consistent interface',
                    'Settlement processors - Various clearing systems with common API'
                  ].map((usage, idx) => (
                    <div key={idx} style={{
                      padding: '12px',
                      background: '#1f2937',
                      borderRadius: '6px',
                      borderLeft: '3px solid #8b5cf6',
                      color: '#d1d5db',
                      fontSize: '13px'
                    }}>
                      {usage}
                    </div>
                  ))}

                  {selectedConcept.id === 'abstraction' && [
                    'Trading interface simplification - Hide complex matching algorithms',
                    'Market connection abstraction - Unified interface to different exchanges',
                    'Risk calculation templates - Standard process with custom implementations',
                    'Data access layers - Hide database complexity from business logic',
                    'API design - Simple interfaces hiding internal system complexity'
                  ].map((usage, idx) => (
                    <div key={idx} style={{
                      padding: '12px',
                      background: '#1f2937',
                      borderRadius: '6px',
                      borderLeft: '3px solid #06b6d4',
                      color: '#d1d5db',
                      fontSize: '13px'
                    }}>
                      {usage}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {detailsTab === 'best-practices' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  Best Practices
                </h3>
                <div style={{ display: 'grid', gap: '12px' }}>
                  {[
                    'Follow SOLID principles for maintainable code',
                    'Use meaningful names for classes and methods',
                    'Keep classes focused on a single responsibility',
                    'Favor composition over inheritance when appropriate',
                    'Write unit tests for all public methods',
                    'Document complex business logic',
                    'Use design patterns where they add value'
                  ].map((practice, idx) => (
                    <div key={idx} style={{
                      padding: '12px',
                      background: '#1f2937',
                      borderRadius: '6px',
                      borderLeft: '3px solid #10b981',
                      color: '#d1d5db',
                      fontSize: '13px'
                    }}>
                      {practice}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default JavaOOP;
import React, { useState } from 'react';

const ComplianceReporting = () => {
  const [selectedComponent, setSelectedComponent] = useState(null);
  const [activeTab, setActiveTab] = useState('details');

  // Using VaR/CVaR components data
  const components = [
    // Data Layer Components
    {
      id: 'market-data',
      name: 'Market Data Feed',
      position: { left: 100, top: 80 },
      category: 'data',
      tech: ['Real-time & historical prices', 'Bloomberg/Reuters feeds', 'Exchange connectivity'],
      color: '#3b82f6',
      latency: '< 1ms',
      throughput: '10M+ ticks/s'
    },
    {
      id: 'portfolio-data',
      name: 'Portfolio Data Store',
      position: { left: 350, top: 80 },
      category: 'storage',
      tech: ['Positions & instruments', 'Trade lifecycle', 'Audit trail'],
      color: '#84cc16'
    },
    {
      id: 'reference-data',
      name: 'Reference Data',
      position: { left: 600, top: 80 },
      category: 'storage',
      tech: ['Static & metadata', 'Security master', 'Corporate actions'],
      color: '#84cc16'
    },
    {
      id: 'risk-params',
      name: 'Risk Parameters',
      position: { left: 850, top: 80 },
      category: 'infrastructure',
      tech: ['Confidence levels & horizons', 'Methodology settings', 'Configuration'],
      color: '#6b7280'
    },
    // Processing Layer Components
    {
      id: 'data-validation',
      name: 'Data Validation',
      position: { left: 100, top: 240 },
      category: 'processing',
      tech: ['Quality checks & cleansing', 'Outlier detection', 'Gap filling'],
      color: '#10b981',
      latency: '< 100ms'
    },
    {
      id: 'data-enrichment',
      name: 'Data Enrichment',
      position: { left: 350, top: 240 },
      category: 'processing',
      tech: ['FX rates & mappings', 'Derived analytics', 'Interpolation'],
      color: '#10b981',
      latency: '< 50ms'
    },
    {
      id: 'scenario-gen',
      name: 'Scenario Generator',
      position: { left: 600, top: 240 },
      category: 'processing',
      tech: ['Monte Carlo & Historical', 'Correlation simulation', 'Stress scenarios'],
      color: '#10b981',
      latency: '< 2s',
      throughput: '1M scenarios/s'
    },
    {
      id: 'pricing-engine',
      name: 'Pricing Engine',
      position: { left: 850, top: 240 },
      category: 'processing',
      tech: ['Valuation models', 'Greeks calculation', 'Model calibration'],
      color: '#10b981',
      latency: '< 500ms',
      throughput: '100K valuations/s'
    },
    // Calculation Layer Components
    {
      id: 'pnl-calc',
      name: 'P&L Calculator',
      position: { left: 225, top: 400 },
      category: 'processing',
      tech: ['Scenario P&L computation', 'Attribution analysis', 'Full revaluation'],
      color: '#f59e0b',
      latency: '< 1s',
      throughput: '50K scenarios/s'
    },
    {
      id: 'var-engine',
      name: 'VaR Engine',
      position: { left: 475, top: 400 },
      category: 'processing',
      tech: ['Core VaR calculations', 'Historical/Parametric/MC', 'Component VaR'],
      color: '#ef4444',
      latency: '< 3s'
    },
    {
      id: 'cvar-engine',
      name: 'CVaR Engine',
      position: { left: 725, top: 400 },
      category: 'processing',
      tech: ['Tail risk calculations', 'Expected Shortfall', 'Coherent measures'],
      color: '#ef4444',
      latency: '< 5s'
    },
    // Output Layer Components
    {
      id: 'report-gen',
      name: 'Report Generator',
      position: { left: 100, top: 560 },
      category: 'infrastructure',
      tech: ['Risk reports & analytics', 'PDF/Excel/HTML', 'Regulatory templates'],
      color: '#f97316'
    },
    {
      id: 'api-gateway',
      name: 'API Gateway',
      position: { left: 350, top: 560 },
      category: 'infrastructure',
      tech: ['REST/gRPC endpoints', 'GraphQL queries', 'WebSocket streaming'],
      color: '#8b5cf6',
      latency: '< 10ms',
      throughput: '10K req/s'
    },
    {
      id: 'cache-layer',
      name: 'Cache Layer',
      position: { left: 600, top: 560 },
      category: 'storage',
      tech: ['Redis/Hazelcast', 'Multi-level caching', 'Pre-computation'],
      color: '#84cc16',
      latency: '< 1ms'
    },
    {
      id: 'monitoring',
      name: 'Monitoring',
      position: { left: 850, top: 560 },
      category: 'infrastructure',
      tech: ['Metrics & alerting', 'System health', 'Risk limit tracking'],
      color: '#6b7280'
    }
  ];

  const connections = [
    // Data flow connections
    { from: 'market-data', to: 'data-validation' },
    { from: 'portfolio-data', to: 'data-validation' },
    { from: 'reference-data', to: 'data-enrichment' },
    { from: 'risk-params', to: 'scenario-gen' },
    { from: 'data-validation', to: 'data-enrichment' },
    { from: 'data-enrichment', to: 'scenario-gen' },
    { from: 'scenario-gen', to: 'pricing-engine' },
    { from: 'pricing-engine', to: 'pnl-calc' },
    { from: 'pnl-calc', to: 'var-engine' },
    { from: 'var-engine', to: 'cvar-engine' },
    { from: 'cvar-engine', to: 'report-gen' },
    { from: 'cvar-engine', to: 'api-gateway' },
    { from: 'api-gateway', to: 'cache-layer' },
    { from: 'cache-layer', to: 'monitoring' }
  ];

  const handleComponentClick = (component) => {
    setSelectedComponent(component);
    setActiveTab('details');
  };

  const getComponentDetails = (componentId) => {
    const details = {
      'market-data': {
        functions: [
          'Ingests real-time and historical market data from multiple sources',
          'Multi-source data aggregation (Bloomberg, Reuters, direct exchange feeds)',
          'Real-time streaming with sub-second latency',
          'Historical data storage with time-series optimization',
          'Support for equities, FX, commodities, fixed income, derivatives',
          'Protocol support: FIX, WebSocket, REST APIs'
        ],
        workflow: '1. Connect to market data providers → 2. Stream real-time prices → 3. Validate and normalize → 4. Store in time-series DB → 5. Trigger risk calculations',
        code: `@Service
public class MarketDataService {
    private final KafkaTemplate<String, MarketData> kafkaTemplate;
    
    public void processMarketData(MarketData data) {
        if (validateData(data)) {
            MarketData normalized = normalize(data);
            timeSeriesRepo.save(normalized);
            kafkaTemplate.send("market-data", normalized);
        }
    }
}`
      },
      'var-engine': {
        functions: [
          'Core engine that calculates Value at Risk',
          'Historical VaR with various weighting schemes',
          'Parametric VaR (variance-covariance method)',
          'Monte Carlo VaR with full revaluation',
          'Component VaR and marginal VaR calculations',
          'Multiple confidence levels and time horizons'
        ],
        workflow: '1. Receive portfolio positions → 2. Apply VaR methodology → 3. Calculate returns distribution → 4. Compute VaR at confidence levels → 5. Calculate component contributions',
        code: `class VaREngine:
    def calculate_historical_var(self, returns, confidence_level=0.95):
        """Calculate Historical VaR"""
        return np.percentile(returns, (1 - confidence_level) * 100)
    
    def calculate_parametric_var(self, portfolio_value, volatility, confidence_level=0.95):
        """Calculate Parametric VaR using normal distribution"""
        from scipy.stats import norm
        z_score = norm.ppf(1 - confidence_level)
        return portfolio_value * volatility * z_score`
      },
      'cvar-engine': {
        functions: [
          'Calculates Conditional Value at Risk (Expected Shortfall)',
          'Tail risk analysis beyond VaR threshold',
          'Coherent risk measure properties',
          'Spectral risk measures for custom risk preferences',
          'Risk contribution analysis for portfolio components',
          'Extreme Value Theory for tail modeling'
        ],
        workflow: '1. Calculate VaR threshold → 2. Identify tail losses → 3. Compute expected value of tail → 4. Calculate component contributions → 5. Generate tail risk decomposition',
        code: `def calculate_cvar(returns, confidence_level=0.95):
    """Calculate Conditional Value at Risk (Expected Shortfall)"""
    var_threshold = np.percentile(returns, (1 - confidence_level) * 100)
    tail_losses = returns[returns <= var_threshold]
    cvar = np.mean(tail_losses) if len(tail_losses) > 0 else var_threshold
    
    return {
        'var': var_threshold,
        'cvar': cvar,
        'tail_ratio': cvar / var_threshold if var_threshold != 0 else 1,
        'tail_observations': len(tail_losses)
    }`
      },
      'scenario-gen': {
        functions: [
          'Generates market scenarios using various methodologies',
          'Monte Carlo simulation with multiple distributions',
          'Historical simulation with configurable lookback',
          'Stress scenarios and hypothetical shocks',
          'Correlation matrix estimation and simulation',
          'Copula models for dependency structures'
        ],
        workflow: '1. Load historical data → 2. Estimate correlation matrices → 3. Generate random scenarios → 4. Apply correlation structures → 5. Create stress scenarios',
        code: `class ScenarioGenerator:
    def __init__(self, correlation_matrix, num_scenarios=10000):
        self.corr_matrix = correlation_matrix
        self.num_scenarios = num_scenarios
        
    def generate_monte_carlo(self, means, volatilities):
        L = np.linalg.cholesky(self.corr_matrix)
        independent_normals = np.random.normal(0, 1, 
            (len(means), self.num_scenarios))
        correlated_normals = L @ independent_normals
        scenarios = means[:, np.newaxis] + volatilities[:, np.newaxis] * correlated_normals
        return scenarios.T`
      }
    };
    
    return details[componentId] || {
      functions: ['Component functionality details coming soon...'],
      workflow: 'Workflow details will be added soon.',
      code: '// Code examples coming soon...'
    };
  };

  return (
    <div style={{
      width: '100%',
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #0c0c0c 100%)',
      color: '#ffffff',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      overflow: 'auto'
    }}>
      {/* Header */}
      <div style={{
        background: 'rgba(0, 0, 0, 0.8)',
        borderBottom: '2px solid #333',
        padding: '20px',
        backdropFilter: 'blur(10px)',
        zIndex: 100,
        position: 'relative'
      }}>
        <h1 style={{
          margin: '0',
          fontSize: '28px',
          fontWeight: 'bold',
          background: 'linear-gradient(45deg, #00ff88, #00aaff)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          textAlign: 'center'
        }}>
          Compliance & Risk Reporting Architecture v2
        </h1>
        <p style={{
          margin: '10px 0 0 0',
          fontSize: '14px',
          color: '#888',
          textAlign: 'center'
        }}>
          VaR/CVaR Risk Analytics & Regulatory Reporting System
        </p>
      </div>

      {/* Main Content */}
      <div style={{ display: 'flex', minHeight: 'calc(100vh - 100px)' }}>
        {/* Architecture Diagram */}
        <div style={{
          flex: 1,
          position: 'relative',
          background: 'rgba(0, 0, 0, 0.3)',
          margin: '20px',
          borderRadius: '10px',
          overflow: 'hidden',
          minHeight: '700px'
        }}>
          {/* Grid Background */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: `
              linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px)
            `,
            backgroundSize: '30px 30px',
            zIndex: 1
          }} />

          {/* Layer Labels */}
          <div style={{
            position: 'absolute',
            left: '20px',
            top: '40px',
            fontSize: '12px',
            color: '#666',
            textTransform: 'uppercase',
            letterSpacing: '1px',
            background: 'rgba(0, 0, 0, 0.8)',
            padding: '5px 10px',
            borderRadius: '6px',
            zIndex: 15
          }}>
            Data Layer
          </div>
          <div style={{
            position: 'absolute',
            left: '20px',
            top: '200px',
            fontSize: '12px',
            color: '#666',
            textTransform: 'uppercase',
            letterSpacing: '1px',
            background: 'rgba(0, 0, 0, 0.8)',
            padding: '5px 10px',
            borderRadius: '6px',
            zIndex: 15
          }}>
            Processing Layer
          </div>
          <div style={{
            position: 'absolute',
            left: '20px',
            top: '360px',
            fontSize: '12px',
            color: '#666',
            textTransform: 'uppercase',
            letterSpacing: '1px',
            background: 'rgba(0, 0, 0, 0.8)',
            padding: '5px 10px',
            borderRadius: '6px',
            zIndex: 15
          }}>
            Calculation Layer
          </div>
          <div style={{
            position: 'absolute',
            left: '20px',
            top: '520px',
            fontSize: '12px',
            color: '#666',
            textTransform: 'uppercase',
            letterSpacing: '1px',
            background: 'rgba(0, 0, 0, 0.8)',
            padding: '5px 10px',
            borderRadius: '6px',
            zIndex: 15
          }}>
            Output Layer
          </div>

          {/* Components */}
          {components.map((component) => (
            <div
              key={component.id}
              onClick={() => handleComponentClick(component)}
              style={{
                position: 'absolute',
                left: `${component.position.left}px`,
                top: `${component.position.top}px`,
                width: '140px',
                height: '80px',
                background: selectedComponent?.id === component.id
                  ? `linear-gradient(135deg, ${component.color}, ${component.color}dd)`
                  : `linear-gradient(135deg, ${component.color}44, ${component.color}77)`,
                border: `2px solid ${component.color}`,
                borderRadius: '12px',
                cursor: 'pointer',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                padding: '8px',
                transition: 'all 0.3s ease',
                zIndex: 10,
                backdropFilter: 'blur(5px)',
                boxShadow: selectedComponent?.id === component.id
                  ? `0 0 25px ${component.color}88`
                  : `0 4px 15px rgba(0, 0, 0, 0.3)`,
                transform: selectedComponent?.id === component.id ? 'scale(1.05)' : 'scale(1)'
              }}
            >
              <div style={{
                fontSize: '13px',
                fontWeight: 'bold',
                textAlign: 'center',
                marginBottom: '4px',
                color: '#ffffff',
                lineHeight: '1.2'
              }}>
                {component.name}
              </div>
              <div style={{
                fontSize: '10px',
                color: '#cccccc',
                textAlign: 'center',
                lineHeight: '1.2'
              }}>
                {component.tech[0]}
              </div>
              {component.latency && (
                <div style={{
                  position: 'absolute',
                  top: '-8px',
                  right: '-8px',
                  backgroundColor: '#dc2626',
                  color: 'white',
                  fontSize: '9px',
                  padding: '2px 6px',
                  borderRadius: '10px',
                  fontWeight: 'bold'
                }}>
                  {component.latency}
                </div>
              )}
              {component.throughput && (
                <div style={{
                  position: 'absolute',
                  bottom: '-8px',
                  right: '-8px',
                  backgroundColor: '#2563eb',
                  color: 'white',
                  fontSize: '9px',
                  padding: '2px 6px',
                  borderRadius: '10px',
                  fontWeight: 'bold'
                }}>
                  {component.throughput}
                </div>
              )}
            </div>
          ))}

          {/* Connection Lines */}
          <svg style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            zIndex: 5,
            pointerEvents: 'none'
          }}>
            <defs>
              <marker
                id="arrowhead-compliance"
                markerWidth="10"
                markerHeight="7"
                refX="9"
                refY="3.5"
                orient="auto"
              >
                <polygon
                  points="0 0, 10 3.5, 0 7"
                  fill="#10b981"
                  opacity="0.8"
                />
              </marker>
            </defs>
            {connections.map((conn, index) => {
              const fromComp = components.find(c => c.id === conn.from);
              const toComp = components.find(c => c.id === conn.to);
              if (!fromComp || !toComp) return null;

              const fromX = fromComp.position.left + 70;
              const fromY = fromComp.position.top + 40;
              const toX = toComp.position.left + 70;
              const toY = toComp.position.top + 40;

              return (
                <line
                  key={index}
                  x1={fromX}
                  y1={fromY}
                  x2={toX}
                  y2={toY}
                  stroke="#10b981"
                  strokeWidth="2"
                  opacity="0.6"
                  markerEnd="url(#arrowhead-compliance)"
                />
              );
            })}
          </svg>
        </div>

        {/* Details Panel */}
        {selectedComponent && (
          <div style={{
            width: '400px',
            background: 'rgba(0, 0, 0, 0.9)',
            border: '1px solid #333',
            margin: '20px 20px 20px 0',
            borderRadius: '10px',
            overflow: 'hidden',
            backdropFilter: 'blur(10px)'
          }}>
            {/* Panel Header */}
            <div style={{
              background: `linear-gradient(135deg, ${selectedComponent.color}, ${selectedComponent.color}cc)`,
              padding: '20px',
              borderBottom: '1px solid #333'
            }}>
              <h3 style={{
                margin: '0 0 10px 0',
                fontSize: '20px',
                fontWeight: 'bold',
                color: '#ffffff'
              }}>
                {selectedComponent.name}
              </h3>
              <div style={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: '8px'
              }}>
                {selectedComponent.tech.map((tech, i) => (
                  <span
                    key={i}
                    style={{
                      background: 'rgba(255, 255, 255, 0.2)',
                      padding: '4px 10px',
                      borderRadius: '15px',
                      fontSize: '11px',
                      color: '#ffffff',
                      fontWeight: '500'
                    }}
                  >
                    {tech}
                  </span>
                ))}
              </div>
            </div>

            {/* Tabs */}
            <div style={{
              display: 'flex',
              borderBottom: '1px solid #333'
            }}>
              {['details', 'workflow', 'code'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  style={{
                    flex: 1,
                    padding: '12px',
                    background: activeTab === tab ? selectedComponent.color : 'transparent',
                    border: 'none',
                    color: activeTab === tab ? '#ffffff' : '#888',
                    fontSize: '12px',
                    fontWeight: 'bold',
                    textTransform: 'uppercase',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                >
                  {tab}
                </button>
              ))}
            </div>

            {/* Tab Content */}
            <div style={{
              padding: '20px',
              maxHeight: '500px',
              overflowY: 'auto'
            }}>
              {activeTab === 'details' && (
                <div>
                  <h4 style={{ margin: '0 0 15px 0', color: '#10b981', fontSize: '16px' }}>
                    Key Functions
                  </h4>
                  <ul style={{ margin: '0', padding: '0 0 0 18px', fontSize: '13px', lineHeight: '1.7' }}>
                    {getComponentDetails(selectedComponent.id).functions.map((func, i) => (
                      <li key={i} style={{ marginBottom: '10px', color: '#e0e0e0' }}>
                        {func}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {activeTab === 'workflow' && (
                <div>
                  <h4 style={{ margin: '0 0 15px 0', color: '#10b981', fontSize: '16px' }}>
                    Process Flow
                  </h4>
                  <div style={{
                    background: 'rgba(0, 0, 0, 0.6)',
                    padding: '15px',
                    borderRadius: '8px',
                    fontSize: '12px',
                    lineHeight: '1.6',
                    color: '#e5e5e5',
                    border: '1px solid #333',
                    fontFamily: 'monospace'
                  }}>
                    {getComponentDetails(selectedComponent.id).workflow}
                  </div>
                </div>
              )}

              {activeTab === 'code' && (
                <div>
                  <h4 style={{ margin: '0 0 15px 0', color: '#10b981', fontSize: '16px' }}>
                    Implementation Example
                  </h4>
                  <pre style={{
                    background: 'rgba(0, 0, 0, 0.6)',
                    padding: '15px',
                    borderRadius: '8px',
                    fontSize: '11px',
                    lineHeight: '1.5',
                    color: '#e5e5e5',
                    whiteSpace: 'pre-wrap',
                    border: '1px solid #333',
                    overflow: 'auto',
                    fontFamily: 'Consolas, Monaco, monospace'
                  }}>
                    {getComponentDetails(selectedComponent.id).code}
                  </pre>
                </div>
              )}
            </div>

            {/* Close Button */}
            <button
              onClick={() => setSelectedComponent(null)}
              style={{
                position: 'absolute',
                top: '20px',
                right: '20px',
                background: 'rgba(255, 255, 255, 0.1)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                color: '#ffffff',
                width: '30px',
                height: '30px',
                borderRadius: '50%',
                cursor: 'pointer',
                fontSize: '16px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              ×
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ComplianceReporting;
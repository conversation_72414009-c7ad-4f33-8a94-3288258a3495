import React, { useState } from 'react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Hexagon, Package, Settings, Shield, Layers, Database, Cloud, Zap, Activity, GitBranch, Cpu, Server, RefreshCw, Bell, Lock, Share2, MessageSquare, Globe } from 'lucide-react';

const JavaSpring = () => {
  const [selectedTopic, setSelectedTopic] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [detailsTab, setDetailsTab] = useState('overview');

  const topics = [
    {
      id: 'ioc-di',
      name: 'IoC & Dependency Injection',
      icon: <Package size={20} />,
      description: 'Inversion of Control and DI patterns',
      color: '#ef4444'
    },
    {
      id: 'aop',
      name: 'Aspect-Oriented Programming',
      icon: <Layers size={20} />,
      description: 'Cross-cutting concerns and aspects',
      color: '#3b82f6'
    },
    {
      id: 'spring-mvc',
      name: 'Spring MVC',
      icon: <Hexagon size={20} />,
      description: 'Web framework and REST APIs',
      color: '#10b981'
    },
    {
      id: 'data-access',
      name: 'Data Access',
      icon: <Database size={20} />,
      description: 'JDBC, JPA, and transaction management',
      color: '#f59e0b'
    },
    {
      id: 'security',
      name: 'Spring Security',
      icon: <Shield size={20} />,
      description: 'Authentication and authorization',
      color: '#8b5cf6'
    },
    {
      id: 'configuration',
      name: 'Configuration',
      icon: <Settings size={20} />,
      description: 'Java config, profiles, and properties',
      color: '#ec4899'
    },
    {
      id: 'webflux',
      name: 'Spring WebFlux',
      icon: <Zap size={20} />,
      description: 'Reactive programming and non-blocking I/O',
      color: '#06b6d4'
    },
    {
      id: 'spring-data',
      name: 'Spring Data',
      icon: <Database size={20} />,
      description: 'Data access abstraction and repositories',
      color: '#0891b2'
    },
    {
      id: 'spring-cloud',
      name: 'Spring Cloud',
      icon: <Cloud size={20} />,
      description: 'Distributed system patterns and microservices',
      color: '#0ea5e9'
    },
    {
      id: 'spring-integration',
      name: 'Spring Integration',
      icon: <GitBranch size={20} />,
      description: 'Enterprise integration patterns and messaging',
      color: '#22d3ee'
    },
    {
      id: 'spring-batch',
      name: 'Spring Batch',
      icon: <Activity size={20} />,
      description: 'Batch processing and job execution',
      color: '#67e8f9'
    },
    {
      id: 'spring-cache',
      name: 'Spring Cache',
      icon: <RefreshCw size={20} />,
      description: 'Caching abstraction and optimization',
      color: '#a7f3d0'
    },
    {
      id: 'spring-events',
      name: 'Spring Events',
      icon: <Bell size={20} />,
      description: 'Application event publishing and handling',
      color: '#86efac'
    },
    {
      id: 'spring-testing',
      name: 'Spring Testing',
      icon: <Server size={20} />,
      description: 'Testing support and test slices',
      color: '#34d399'
    },
    {
      id: 'spring-websocket',
      name: 'Spring WebSocket',
      icon: <MessageSquare size={20} />,
      description: 'Real-time bidirectional communication',
      color: '#10b981'
    },
    {
      id: 'spring-kafka',
      name: 'Spring Kafka/JMS',
      icon: <Share2 size={20} />,
      description: 'Message queue integration and streaming',
      color: '#065f46'
    },
    {
      id: 'spring-actuator',
      name: 'Spring Actuator',
      icon: <Cpu size={20} />,
      description: 'Production monitoring and management',
      color: '#059669'
    },
    {
      id: 'spring-web-services',
      name: 'Spring Web Services',
      icon: <Globe size={20} />,
      description: 'SOAP web services and XML processing',
      color: '#047857'
    }
  ];

  const codeExamples = {
    'ioc-di': `// IoC & Dependency Injection in Trading Systems
@Component
public class OrderService {
    
    private final OrderRepository orderRepository;
    private final RiskService riskService;
    private final NotificationService notificationService;
    
    // Constructor injection (recommended)
    public OrderService(OrderRepository orderRepository,
                       RiskService riskService,
                       NotificationService notificationService) {
        this.orderRepository = orderRepository;
        this.riskService = riskService;
        this.notificationService = notificationService;
    }
    
    @Transactional
    public OrderResult submitOrder(Order order) {
        // Risk validation
        RiskCheckResult riskResult = riskService.validateOrder(order);
        if (!riskResult.isPassed()) {
            return OrderResult.rejected(riskResult.getReason());
        }
        
        // Save order
        Order savedOrder = orderRepository.save(order);
        
        // Notify stakeholders
        notificationService.notifyOrderSubmitted(savedOrder);
        
        return OrderResult.success(savedOrder);
    }
}

@Repository
public class JdbcOrderRepository implements OrderRepository {
    
    private final JdbcTemplate jdbcTemplate;
    private final RowMapper<Order> orderRowMapper;
    
    public JdbcOrderRepository(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.orderRowMapper = new OrderRowMapper();
    }
    
    @Override
    public Order save(Order order) {
        if (order.getId() == null) {
            return insert(order);
        } else {
            return update(order);
        }
    }
    
    @Override
    public Optional<Order> findById(Long id) {
        try {
            Order order = jdbcTemplate.queryForObject(
                "SELECT * FROM orders WHERE id = ?",
                orderRowMapper,
                id
            );
            return Optional.of(order);
        } catch (EmptyResultDataAccessException e) {
            return Optional.empty();
        }
    }
    
    @Override
    public List<Order> findByStatus(OrderStatus status) {
        return jdbcTemplate.query(
            "SELECT * FROM orders WHERE status = ?",
            orderRowMapper,
            status.name()
        );
    }
}

@Service
public class RiskService {
    
    private final PositionService positionService;
    private final RiskParameters riskParameters;
    
    // Field injection (not recommended, but shown for completeness)
    @Autowired
    private RiskCalculator riskCalculator;
    
    public RiskService(PositionService positionService, RiskParameters riskParameters) {
        this.positionService = positionService;
        this.riskParameters = riskParameters;
    }
    
    public RiskCheckResult validateOrder(Order order) {
        // Get current positions
        Portfolio portfolio = positionService.getPortfolio(order.getAccountId());
        
        // Calculate potential risk
        double potentialRisk = riskCalculator.calculateOrderRisk(order, portfolio);
        
        // Check against limits
        if (potentialRisk > riskParameters.getMaxOrderRisk()) {
            return RiskCheckResult.failed("Order risk exceeds limit");
        }
        
        return RiskCheckResult.passed();
    }
}

// Configuration class for dependency setup
@Configuration
@EnableTransactionManagement
@ComponentScan(basePackages = "com.trading")
public class TradingSystemConfig {
    
    @Bean
    public DataSource dataSource() {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("***********************************");
        config.setUsername("trader");
        config.setPassword("secret");
        config.setMaximumPoolSize(20);
        config.setMinimumIdle(5);
        return new HikariDataSource(config);
    }
    
    @Bean
    public JdbcTemplate jdbcTemplate(DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }
    
    @Bean
    public PlatformTransactionManager transactionManager(DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
    
    @Bean
    @ConfigurationProperties(prefix = "trading.risk")
    public RiskParameters riskParameters() {
        return new RiskParameters();
    }
    
    // Conditional bean creation
    @Bean
    @ConditionalOnProperty(name = "trading.notifications.email.enabled", havingValue = "true")
    public NotificationService emailNotificationService() {
        return new EmailNotificationService();
    }
    
    @Bean
    @ConditionalOnProperty(name = "trading.notifications.slack.enabled", havingValue = "true")
    public NotificationService slackNotificationService() {
        return new SlackNotificationService();
    }
}`,
    aop: `// Aspect-Oriented Programming for Trading Systems
@Aspect
@Component
public class TradingAspects {
    
    private static final Logger logger = LoggerFactory.getLogger(TradingAspects.class);
    private final AuditService auditService;
    
    public TradingAspects(AuditService auditService) {
        this.auditService = auditService;
    }
    
    // Pointcut definitions
    @Pointcut("execution(* com.trading.service.*Service.*(..))")
    public void serviceLayer() {}
    
    @Pointcut("@annotation(com.trading.annotation.Auditable)")
    public void auditableMethod() {}
    
    @Pointcut("execution(* com.trading.service.OrderService.submitOrder(..))")
    public void orderSubmission() {}
    
    // Around advice for performance monitoring
    @Around("serviceLayer()")
    public Object monitorPerformance(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        
        try {
            Object result = joinPoint.proceed();
            long executionTime = System.currentTimeMillis() - startTime;
            
            if (executionTime > 1000) { // Log slow methods
                logger.warn("Slow method execution: {}.{} took {}ms", 
                    className, methodName, executionTime);
            }
            
            return result;
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            logger.error("Method {}.{} failed after {}ms", 
                className, methodName, executionTime, e);
            throw e;
        }
    }
    
    // Before advice for validation
    @Before("orderSubmission()")
    public void validateOrderSubmission(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        if (args.length > 0 && args[0] instanceof Order) {
            Order order = (Order) args[0];
            
            // Pre-validation checks
            if (order.getQuantity() <= 0) {
                throw new IllegalArgumentException("Order quantity must be positive");
            }
            
            if (order.getSymbol() == null || order.getSymbol().isEmpty()) {
                throw new IllegalArgumentException("Order symbol is required");
            }
            
            logger.info("Pre-validation passed for order: {}", order.getId());
        }
    }
    
    // After returning advice for audit logging
    @AfterReturning(pointcut = "auditableMethod()", returning = "result")
    public void auditMethodExecution(JoinPoint joinPoint, Object result) {
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        Object[] args = joinPoint.getArgs();
        
        AuditEvent auditEvent = AuditEvent.builder()
            .className(className)
            .methodName(methodName)
            .arguments(Arrays.toString(args))
            .result(result != null ? result.toString() : null)
            .timestamp(LocalDateTime.now())
            .userId(getCurrentUserId())
            .build();
            
        auditService.recordEvent(auditEvent);
    }
    
    // After throwing advice for error handling
    @AfterThrowing(pointcut = "serviceLayer()", throwing = "exception")
    public void handleServiceException(JoinPoint joinPoint, Exception exception) {
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        
        // Log the error
        logger.error("Service method failed: {}.{}", className, methodName, exception);
        
        // Send alert for critical errors
        if (isCriticalError(exception)) {
            alertService.sendCriticalAlert(className, methodName, exception);
        }
        
        // Create error audit entry
        ErrorAuditEvent errorEvent = ErrorAuditEvent.builder()
            .className(className)
            .methodName(methodName)
            .errorMessage(exception.getMessage())
            .errorType(exception.getClass().getSimpleName())
            .timestamp(LocalDateTime.now())
            .build();
            
        auditService.recordError(errorEvent);
    }
}

// Custom annotation for auditable methods
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Auditable {
    String description() default "";
    AuditLevel level() default AuditLevel.INFO;
}

// Security aspect for method-level authorization
@Aspect
@Component
public class SecurityAspect {
    
    @Around("@annotation(requiresPermission)")
    public Object checkPermission(ProceedingJoinPoint joinPoint, 
                                 RequiresPermission requiresPermission) throws Throwable {
        
        String currentUser = SecurityContextHolder.getContext()
            .getAuthentication().getName();
        String requiredPermission = requiresPermission.value();
        
        if (!hasPermission(currentUser, requiredPermission)) {
            throw new AccessDeniedException("Insufficient permissions: " + requiredPermission);
        }
        
        return joinPoint.proceed();
    }
    
    private boolean hasPermission(String user, String permission) {
        // Check user permissions
        return permissionService.hasPermission(user, permission);
    }
}

// Usage example with annotations
@Service
public class PortfolioService {
    
    @Auditable(description = "Portfolio value calculation", level = AuditLevel.INFO)
    @RequiresPermission("PORTFOLIO_READ")
    public BigDecimal calculatePortfolioValue(String accountId) {
        // Implementation
        return portfolioRepository.calculateValue(accountId);
    }
    
    @Auditable(description = "Portfolio rebalancing", level = AuditLevel.WARN)
    @RequiresPermission("PORTFOLIO_MODIFY")
    @Transactional
    public void rebalancePortfolio(String accountId, List<AllocationTarget> targets) {
        // Implementation
        performRebalancing(accountId, targets);
    }
}`,
    'spring-mvc': `// Spring MVC for Trading REST APIs
@RestController
@RequestMapping("/api/v1/orders")
@CrossOrigin(origins = "http://localhost:3000")
@Validated
public class OrderController {
    
    private final OrderService orderService;
    private final OrderMapper orderMapper;
    
    public OrderController(OrderService orderService, OrderMapper orderMapper) {
        this.orderService = orderService;
        this.orderMapper = orderMapper;
    }
    
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ResponseEntity<OrderResponse> submitOrder(
            @Valid @RequestBody CreateOrderRequest request,
            @RequestHeader("X-User-ID") String userId) {
        
        Order order = orderMapper.toEntity(request);
        order.setUserId(userId);
        
        OrderResult result = orderService.submitOrder(order);
        
        if (result.isSuccess()) {
            OrderResponse response = orderMapper.toResponse(result.getOrder());
            return ResponseEntity.created(
                URI.create("/api/v1/orders/" + result.getOrder().getId())
            ).body(response);
        } else {
            throw new OrderRejectionException(result.getReason());
        }
    }
    
    @GetMapping("/{orderId}")
    public ResponseEntity<OrderResponse> getOrder(
            @PathVariable @Min(1) Long orderId,
            @RequestHeader("X-User-ID") String userId) {
        
        Optional<Order> order = orderService.findById(orderId);
        
        return order
            .filter(o -> o.getUserId().equals(userId)) // Security check
            .map(orderMapper::toResponse)
            .map(ResponseEntity::ok)
            .orElse(ResponseEntity.notFound().build());
    }
    
    @GetMapping
    public ResponseEntity<PagedResponse<OrderResponse>> getOrders(
            @RequestParam(defaultValue = "0") @Min(0) int page,
            @RequestParam(defaultValue = "20") @Min(1) @Max(100) int size,
            @RequestParam(required = false) OrderStatus status,
            @RequestParam(required = false) String symbol,
            @RequestHeader("X-User-ID") String userId) {
        
        PageRequest pageRequest = PageRequest.of(page, size);
        OrderSearchCriteria criteria = OrderSearchCriteria.builder()
            .userId(userId)
            .status(status)
            .symbol(symbol)
            .build();
            
        Page<Order> orders = orderService.findOrders(criteria, pageRequest);
        
        PagedResponse<OrderResponse> response = PagedResponse.<OrderResponse>builder()
            .content(orders.getContent().stream()
                .map(orderMapper::toResponse)
                .collect(Collectors.toList()))
            .pageNumber(orders.getNumber())
            .pageSize(orders.getSize())
            .totalElements(orders.getTotalElements())
            .totalPages(orders.getTotalPages())
            .build();
            
        return ResponseEntity.ok(response);
    }
    
    @PutMapping("/{orderId}/cancel")
    public ResponseEntity<Void> cancelOrder(
            @PathVariable Long orderId,
            @RequestHeader("X-User-ID") String userId) {
        
        boolean cancelled = orderService.cancelOrder(orderId, userId);
        
        return cancelled ? 
            ResponseEntity.noContent().build() : 
            ResponseEntity.notFound().build();
    }
    
    @GetMapping("/stream")
    public SseEmitter streamOrders(@RequestHeader("X-User-ID") String userId) {
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
        
        // Subscribe to order updates for this user
        orderService.subscribeToOrderUpdates(userId, update -> {
            try {
                emitter.send(SseEmitter.event()
                    .name("order-update")
                    .data(orderMapper.toResponse(update.getOrder())));
            } catch (IOException e) {
                emitter.completeWithError(e);
            }
        });
        
        emitter.onCompletion(() -> orderService.unsubscribeFromOrderUpdates(userId));
        emitter.onError(e -> orderService.unsubscribeFromOrderUpdates(userId));
        
        return emitter;
    }
}

// Global exception handler
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    
    @ExceptionHandler(OrderRejectionException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ErrorResponse handleOrderRejection(OrderRejectionException ex) {
        return ErrorResponse.builder()
            .error("ORDER_REJECTED")
            .message(ex.getMessage())
            .timestamp(Instant.now())
            .build();
    }
    
    @ExceptionHandler(ValidationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ErrorResponse handleValidation(ValidationException ex) {
        return ErrorResponse.builder()
            .error("VALIDATION_ERROR")
            .message("Invalid request data")
            .details(ex.getViolations())
            .timestamp(Instant.now())
            .build();
    }
    
    @ExceptionHandler(AccessDeniedException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public ErrorResponse handleAccessDenied(AccessDeniedException ex) {
        return ErrorResponse.builder()
            .error("ACCESS_DENIED")
            .message("Insufficient permissions")
            .timestamp(Instant.now())
            .build();
    }
    
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ErrorResponse handleGeneral(Exception ex) {
        logger.error("Unexpected error", ex);
        return ErrorResponse.builder()
            .error("INTERNAL_ERROR")
            .message("An unexpected error occurred")
            .timestamp(Instant.now())
            .build();
    }
}

// Request/Response DTOs
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateOrderRequest {
    
    @NotNull(message = "Symbol is required")
    @Pattern(regexp = "^[A-Z]{1,10}$", message = "Invalid symbol format")
    private String symbol;
    
    @NotNull(message = "Order type is required")
    private OrderType type;
    
    @NotNull(message = "Side is required")
    private OrderSide side;
    
    @Positive(message = "Quantity must be positive")
    private Integer quantity;
    
    @Positive(message = "Price must be positive")
    private BigDecimal price;
    
    @Valid
    private TimeInForce timeInForce;
}

@Data
@Builder
public class OrderResponse {
    private Long id;
    private String symbol;
    private OrderType type;
    private OrderSide side;
    private Integer quantity;
    private BigDecimal price;
    private OrderStatus status;
    private Instant createdAt;
    private Instant updatedAt;
    private List<ExecutionReport> executions;
}

// Custom validator
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = OrderValidator.class)
public @interface ValidOrder {
    String message() default "Invalid order";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}

public class OrderValidator implements ConstraintValidator<ValidOrder, CreateOrderRequest> {
    
    @Override
    public boolean isValid(CreateOrderRequest request, ConstraintValidatorContext context) {
        if (request.getType() == OrderType.LIMIT && request.getPrice() == null) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate("Limit orders require a price")
                .addPropertyNode("price")
                .addConstraintViolation();
            return false;
        }
        
        if (request.getType() == OrderType.MARKET && request.getPrice() != null) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate("Market orders cannot have a price")
                .addPropertyNode("price")
                .addConstraintViolation();
            return false;
        }
        
        return true;
    }
}`,
    webflux: `// Spring WebFlux for Reactive Trading Systems
@RestController
@RequestMapping("/api/v2/reactive")
public class ReactiveOrderController {
    
    private final ReactiveOrderService orderService;
    private final OrderEventPublisher eventPublisher;
    
    public ReactiveOrderController(ReactiveOrderService orderService, 
                                  OrderEventPublisher eventPublisher) {
        this.orderService = orderService;
        this.eventPublisher = eventPublisher;
    }
    
    @PostMapping("/orders")
    public Mono<ResponseEntity<OrderResponse>> submitOrder(@RequestBody CreateOrderRequest request) {
        return orderService.submitOrder(request)
            .map(order -> ResponseEntity.created(URI.create("/api/v2/orders/" + order.getId()))
                .body(orderMapper.toResponse(order)))
            .doOnSuccess(response -> eventPublisher.publishOrderCreated(response.getBody()))
            .onErrorMap(ValidationException.class, 
                ex -> new ResponseStatusException(HttpStatus.BAD_REQUEST, ex.getMessage()));
    }
    
    @GetMapping("/orders/stream")
    public Flux<OrderUpdate> streamOrderUpdates(@RequestParam String userId) {
        return orderService.getOrderUpdateStream(userId)
            .map(this::toOrderUpdate)
            .onBackpressureBuffer(1000)
            .onErrorContinue((error, obj) -> 
                log.error("Error in order stream for user {}: {}", userId, error.getMessage()));
    }
    
    @GetMapping("/market-data/{symbol}")
    public Flux<MarketDataUpdate> streamMarketData(@PathVariable String symbol) {
        return marketDataService.getMarketDataStream(symbol)
            .sample(Duration.ofMillis(100)) // Throttle to 10 updates per second
            .distinctUntilChanged(MarketDataUpdate::getPrice)
            .share(); // Share among multiple subscribers
    }
    
    @GetMapping("/orders/search")
    public Flux<OrderResponse> searchOrders(@RequestParam String userId,
                                          @RequestParam(required = false) String symbol,
                                          @RequestParam(required = false) OrderStatus status) {
        return orderService.findOrdersReactive(OrderSearchCriteria.builder()
                .userId(userId)
                .symbol(symbol)
                .status(status)
                .build())
            .map(orderMapper::toResponse)
            .buffer(50) // Batch results
            .flatMap(Flux::fromIterable);
    }
}

@Service
public class ReactiveOrderService {
    
    private final ReactiveOrderRepository orderRepository;
    private final ReactiveRiskService riskService;
    private final RedisTemplate<String, Object> redisTemplate;
    
    public ReactiveOrderService(ReactiveOrderRepository orderRepository,
                               ReactiveRiskService riskService,
                               RedisTemplate<String, Object> redisTemplate) {
        this.orderRepository = orderRepository;
        this.riskService = riskService;
        this.redisTemplate = redisTemplate;
    }
    
    public Mono<Order> submitOrder(CreateOrderRequest request) {
        return validateOrder(request)
            .flatMap(this::checkRisk)
            .flatMap(orderRepository::save)
            .flatMap(this::publishToMatchingEngine)
            .onErrorMap(this::handleOrderError);
    }
    
    private Mono<Order> validateOrder(CreateOrderRequest request) {
        return Mono.fromCallable(() -> {
            if (request.getQuantity() <= 0) {
                throw new ValidationException("Quantity must be positive");
            }
            if (StringUtils.isBlank(request.getSymbol())) {
                throw new ValidationException("Symbol is required");
            }
            return orderMapper.toEntity(request);
        }).subscribeOn(Schedulers.boundedElastic());
    }
    
    private Mono<Order> checkRisk(Order order) {
        return riskService.validateOrderReactive(order)
            .filter(RiskCheckResult::isPassed)
            .map(result -> order)
            .switchIfEmpty(Mono.error(new RiskException("Order rejected by risk check")));
    }
    
    public Flux<OrderUpdate> getOrderUpdateStream(String userId) {
        return Flux.interval(Duration.ofMillis(500))
            .flatMap(tick -> orderRepository.findRecentUpdatesByUser(userId))
            .distinctUntilChanged()
            .map(this::createOrderUpdate);
    }
    
    // Reactive composition with error handling
    public Mono<PortfolioSummary> getPortfolioSummary(String userId) {
        Mono<List<Position>> positions = portfolioService.getPositionsReactive(userId);
        Mono<List<Order>> orders = orderRepository.findActiveOrdersByUser(userId);
        Mono<RiskMetrics> riskMetrics = riskService.calculateRiskMetricsReactive(userId);
        
        return Mono.zip(positions, orders, riskMetrics)
            .map(tuple -> PortfolioSummary.builder()
                .positions(tuple.getT1())
                .activeOrders(tuple.getT2())
                .riskMetrics(tuple.getT3())
                .timestamp(Instant.now())
                .build())
            .timeout(Duration.ofSeconds(5))
            .retry(3)
            .doOnError(error -> log.error("Failed to get portfolio summary for user {}", userId, error));
    }
}

// WebFlux Configuration
@Configuration
@EnableWebFlux
public class ReactiveWebConfig implements WebFluxConfigurer {
    
    @Bean
    public RouterFunction<ServerResponse> tradingRoutes(TradingHandler handler) {
        return RouterFunctions
            .route(GET("/api/v2/orders/{id}"), handler::getOrder)
            .andRoute(POST("/api/v2/orders"), handler::submitOrder)
            .andRoute(GET("/api/v2/orders/stream"), handler::streamOrders)
            .filter(this::loggingFilter)
            .filter(this::authenticationFilter);
    }
    
    private Mono<ServerResponse> loggingFilter(ServerRequest request, HandlerFunction<ServerResponse> next) {
        long startTime = System.currentTimeMillis();
        return next.handle(request)
            .doOnSuccess(response -> {
                long duration = System.currentTimeMillis() - startTime;
                log.info("Request {} {} completed in {}ms with status {}", 
                    request.method(), request.path(), duration, response.statusCode());
            });
    }
    
    @Bean
    public WebClient webClient() {
        return WebClient.builder()
            .baseUrl("http://market-data-service:8080")
            .clientConnector(new ReactorClientHttpConnector(HttpClient.create()
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000)
                .responseTimeout(Duration.ofSeconds(10))))
            .filter(ExchangeFilterFunction.ofRequestProcessor(
                clientRequest -> {
                    log.debug("Request: {} {}", clientRequest.method(), clientRequest.url());
                    return Mono.just(clientRequest);
                }))
            .build();
    }
}`,
    'spring-data': `// Spring Data for Trading System Data Access
@Entity
@Table(name = "orders", indexes = {
    @Index(name = "idx_order_symbol_status", columnList = "symbol, status"),
    @Index(name = "idx_order_user_created", columnList = "user_id, created_at"),
    @Index(name = "idx_order_status_updated", columnList = "status, updated_at")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class Order {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @EqualsAndHashCode.Include
    private Long id;
    
    @Column(nullable = false, length = 10)
    private String symbol;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private OrderType type;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private OrderSide side;
    
    @Column(nullable = false)
    private Integer quantity;
    
    @Column(precision = 19, scale = 4)
    private BigDecimal price;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private OrderStatus status;
    
    @Column(name = "user_id", nullable = false)
    private String userId;
    
    @Column(name = "created_at", nullable = false)
    @CreationTimestamp
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at", nullable = false)
    @UpdateTimestamp
    private LocalDateTime updatedAt;
    
    @OneToMany(mappedBy = "order", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Execution> executions = new ArrayList<>();
    
    @Version
    private Integer version;
}

// Repository with custom queries
@Repository
public interface OrderRepository extends JpaRepository<Order, Long>, OrderRepositoryCustom {
    
    // Query methods
    List<Order> findByUserIdOrderByCreatedAtDesc(String userId);
    
    List<Order> findBySymbolAndStatus(String symbol, OrderStatus status);
    
    @Query("SELECT o FROM Order o WHERE o.userId = :userId AND o.status IN :statuses")
    List<Order> findByUserIdAndStatusIn(@Param("userId") String userId, 
                                       @Param("statuses") List<OrderStatus> statuses);
    
    // Custom query with pagination
    @Query(value = "SELECT o FROM Order o WHERE o.createdAt >= :since AND o.status = :status")
    Page<Order> findRecentOrdersByStatus(@Param("since") LocalDateTime since,
                                        @Param("status") OrderStatus status,
                                        Pageable pageable);
    
    // Native query for performance-critical operations
    @Query(value = """
        SELECT o.symbol, COUNT(*) as order_count, SUM(o.quantity) as total_quantity
        FROM orders o 
        WHERE o.created_at >= :since AND o.status = 'FILLED'
        GROUP BY o.symbol
        ORDER BY order_count DESC
        """, nativeQuery = true)
    List<Object[]> findTradingVolumeBySymbol(@Param("since") LocalDateTime since);
    
    // Modifying query
    @Modifying
    @Query("UPDATE Order o SET o.status = :newStatus WHERE o.id IN :orderIds")
    int updateOrderStatus(@Param("orderIds") List<Long> orderIds, 
                         @Param("newStatus") OrderStatus newStatus);
    
    // Projection interface for performance
    @Query("SELECT o.id as id, o.symbol as symbol, o.quantity as quantity, o.price as price " +
           "FROM Order o WHERE o.userId = :userId")
    List<OrderSummaryProjection> findOrderSummariesByUserId(@Param("userId") String userId);
    
    // Specification-based queries
    Page<Order> findAll(Specification<Order> spec, Pageable pageable);
}

// Custom repository implementation
@Repository
public class OrderRepositoryCustomImpl implements OrderRepositoryCustom {
    
    @PersistenceContext
    private EntityManager entityManager;
    
    @Override
    public List<Order> findComplexOrders(OrderSearchCriteria criteria) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Order> query = cb.createQuery(Order.class);
        Root<Order> root = query.from(Order.class);
        
        List<Predicate> predicates = new ArrayList<>();
        
        if (criteria.getUserId() != null) {
            predicates.add(cb.equal(root.get("userId"), criteria.getUserId()));
        }
        
        if (criteria.getSymbol() != null) {
            predicates.add(cb.equal(root.get("symbol"), criteria.getSymbol()));
        }
        
        if (criteria.getDateRange() != null) {
            predicates.add(cb.between(root.get("createdAt"), 
                criteria.getDateRange().getStart(), 
                criteria.getDateRange().getEnd()));
        }
        
        if (criteria.getMinQuantity() != null) {
            predicates.add(cb.greaterThanOrEqualTo(root.get("quantity"), criteria.getMinQuantity()));
        }
        
        query.where(predicates.toArray(new Predicate[0]));
        query.orderBy(cb.desc(root.get("createdAt")));
        
        return entityManager.createQuery(query)
            .setMaxResults(1000)
            .getResultList();
    }
    
    @Override
    public BigDecimal calculateTotalVolumeBySymbol(String symbol, LocalDateTime since) {
        return entityManager.createQuery(
            "SELECT COALESCE(SUM(e.quantity * e.price), 0) " +
            "FROM Execution e JOIN e.order o " +
            "WHERE o.symbol = :symbol AND e.executedAt >= :since", 
            BigDecimal.class)
            .setParameter("symbol", symbol)
            .setParameter("since", since)
            .getSingleResult();
    }
}

// Specifications for dynamic queries
public class OrderSpecifications {
    
    public static Specification<Order> hasUserId(String userId) {
        return (root, query, cb) -> userId == null ? null : cb.equal(root.get("userId"), userId);
    }
    
    public static Specification<Order> hasSymbol(String symbol) {
        return (root, query, cb) -> symbol == null ? null : cb.equal(root.get("symbol"), symbol);
    }
    
    public static Specification<Order> hasStatus(OrderStatus status) {
        return (root, query, cb) -> status == null ? null : cb.equal(root.get("status"), status);
    }
    
    public static Specification<Order> createdAfter(LocalDateTime date) {
        return (root, query, cb) -> date == null ? null : cb.greaterThanOrEqualTo(root.get("createdAt"), date);
    }
    
    public static Specification<Order> hasMinQuantity(Integer minQuantity) {
        return (root, query, cb) -> minQuantity == null ? null : cb.greaterThanOrEqualTo(root.get("quantity"), minQuantity);
    }
    
    // Composite specifications
    public static Specification<Order> activeOrdersForUser(String userId) {
        return Specification.where(hasUserId(userId))
            .and(hasStatus(OrderStatus.NEW).or(hasStatus(OrderStatus.PARTIALLY_FILLED)));
    }
    
    public static Specification<Order> recentLargeOrders(LocalDateTime since, Integer minQuantity) {
        return Specification.where(createdAfter(since))
            .and(hasMinQuantity(minQuantity));
    }
}

// Service layer using Spring Data
@Service
@Transactional
public class OrderService {
    
    private final OrderRepository orderRepository;
    private final OrderSpecifications orderSpecs;
    
    public OrderService(OrderRepository orderRepository) {
        this.orderRepository = orderRepository;
    }
    
    public Page<Order> findOrders(OrderSearchCriteria criteria, Pageable pageable) {
        Specification<Order> spec = Specification.where(null);
        
        if (criteria.getUserId() != null) {
            spec = spec.and(OrderSpecifications.hasUserId(criteria.getUserId()));
        }
        
        if (criteria.getSymbol() != null) {
            spec = spec.and(OrderSpecifications.hasSymbol(criteria.getSymbol()));
        }
        
        if (criteria.getStatus() != null) {
            spec = spec.and(OrderSpecifications.hasStatus(criteria.getStatus()));
        }
        
        return orderRepository.findAll(spec, pageable);
    }
    
    @Transactional(readOnly = true)
    public List<OrderSummaryProjection> getUserOrderSummaries(String userId) {
        return orderRepository.findOrderSummariesByUserId(userId);
    }
    
    public int cancelOrders(List<Long> orderIds) {
        return orderRepository.updateOrderStatus(orderIds, OrderStatus.CANCELLED);
    }
}

// Data JPA Configuration
@Configuration
@EnableJpaRepositories(basePackages = "com.trading.repository")
@EnableJpaAuditing
public class JpaConfig {
    
    @Bean
    public AuditorAware<String> auditorProvider() {
        return () -> Optional.ofNullable(SecurityContextHolder.getContext())
            .map(SecurityContext::getAuthentication)
            .filter(Authentication::isAuthenticated)
            .map(Authentication::getName);
    }
}`,
    'spring-cloud': `// Spring Cloud for Trading Microservices
// Service Discovery with Eureka
@SpringBootApplication
@EnableEurekaClient
@EnableCircuitBreaker
public class OrderServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(OrderServiceApplication.class, args);
    }
}

// Configuration Server
@RestController
@RefreshScope
public class TradingConfigController {
    
    @Value("\${trading.risk.max-position-size}")
    private BigDecimal maxPositionSize;
    
    @Value("\${trading.risk.daily-var-limit}")
    private BigDecimal dailyVarLimit;
    
    @Value("\${trading.market.hours}")
    private String marketHours;
    
    @GetMapping("/config")
    public TradingConfiguration getConfiguration() {
        return TradingConfiguration.builder()
            .maxPositionSize(maxPositionSize)
            .dailyVarLimit(dailyVarLimit)
            .marketHours(marketHours)
            .lastUpdated(Instant.now())
            .build();
    }
}

// Circuit Breaker with Hystrix/Resilience4j
@Component
public class MarketDataClient {
    
    private final WebClient webClient;
    private final CircuitBreaker circuitBreaker;
    
    public MarketDataClient(WebClient webClient) {
        this.webClient = webClient;
        this.circuitBreaker = CircuitBreaker.ofDefaults("marketDataService");
    }
    
    @CircuitBreaker(name = "market-data", fallbackMethod = "getMarketDataFallback")
    @Retry(name = "market-data")
    @TimeLimiter(name = "market-data")
    public CompletableFuture<MarketData> getMarketData(String symbol) {
        return webClient.get()
            .uri("/market-data/{symbol}", symbol)
            .retrieve()
            .bodyToMono(MarketData.class)
            .timeout(Duration.ofSeconds(2))
            .toFuture();
    }
    
    public CompletableFuture<MarketData> getMarketDataFallback(String symbol, Exception ex) {
        log.warn("Market data service unavailable for symbol {}: {}", symbol, ex.getMessage());
        return CompletableFuture.completedFuture(
            MarketData.builder()
                .symbol(symbol)
                .price(BigDecimal.ZERO)
                .timestamp(Instant.now())
                .stale(true)
                .build()
        );
    }
}

// Service-to-Service Communication with Feign
@FeignClient(name = "risk-service", fallback = RiskServiceFallback.class)
public interface RiskServiceClient {
    
    @PostMapping("/api/risk/validate-order")
    RiskCheckResult validateOrder(@RequestBody Order order);
    
    @GetMapping("/api/risk/portfolio/{userId}")
    PortfolioRisk getPortfolioRisk(@PathVariable String userId);
    
    @PostMapping("/api/risk/calculate-var")
    VaRCalculationResult calculateVaR(@RequestBody VaRRequest request);
}

@Component
public class RiskServiceFallback implements RiskServiceClient {
    
    @Override
    public RiskCheckResult validateOrder(Order order) {
        log.warn("Risk service unavailable, allowing order with conservative limits");
        return RiskCheckResult.builder()
            .passed(order.getQuantity() <= 100) // Conservative fallback
            .reason("Risk service unavailable - conservative check applied")
            .build();
    }
    
    @Override
    public PortfolioRisk getPortfolioRisk(String userId) {
        return PortfolioRisk.builder()
            .userId(userId)
            .totalRisk(BigDecimal.ZERO)
            .available(false)
            .build();
    }
    
    @Override
    public VaRCalculationResult calculateVaR(VaRRequest request) {
        return VaRCalculationResult.unavailable("Risk service down");
    }
}

// API Gateway with Spring Cloud Gateway
@Configuration
public class GatewayConfig {
    
    @Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
        return builder.routes()
            // Order Service Routes
            .route("order-service", r -> r.path("/api/orders/**")
                .filters(f -> f
                    .circuitBreaker(c -> c.setName("order-service")
                        .setFallbackUri("forward:/fallback/orders"))
                    .retry(3)
                    .addRequestHeader("X-Gateway", "trading-gateway")
                    .requestRateLimiter(c -> c
                        .setRateLimiter(redisRateLimiter())
                        .setKeyResolver(userKeyResolver())))
                .uri("lb://order-service"))
            
            // Market Data Service Routes
            .route("market-data-service", r -> r.path("/api/market-data/**")
                .filters(f -> f
                    .circuitBreaker(c -> c.setName("market-data-service"))
                    .cache(Duration.ofSeconds(1))) // Cache for 1 second
                .uri("lb://market-data-service"))
            
            // Portfolio Service Routes  
            .route("portfolio-service", r -> r.path("/api/portfolio/**")
                .filters(f -> f
                    .circuitBreaker(c -> c.setName("portfolio-service"))
                    .addRequestHeader("X-Service", "portfolio"))
                .uri("lb://portfolio-service"))
            
            .build();
    }
    
    @Bean
    public RedisRateLimiter redisRateLimiter() {
        return new RedisRateLimiter(100, 200, 1); // 100 requests per second, burst 200
    }
    
    @Bean
    public KeyResolver userKeyResolver() {
        return exchange -> exchange.getRequest().getHeaders()
            .getFirst("X-User-ID")
            .map(Mono::just)
            .orElse(Mono.just("anonymous"));
    }
}`,
    'spring-integration': `// Spring Integration for Trading Message Processing
@Configuration
@EnableIntegration
public class TradingIntegrationConfig {
    
    @Bean
    public MessageChannel orderChannel() {
        return MessageChannels.direct().get();
    }
    
    @Bean
    public MessageChannel riskCheckChannel() {
        return MessageChannels.executor(Executors.newCachedThreadPool()).get();
    }
    
    @Bean
    public MessageChannel executionChannel() {
        return MessageChannels.publishSubscribe().get();
    }
    
    @Bean
    public IntegrationFlow orderProcessingFlow() {
        return IntegrationFlows
            .from(orderChannel())
            .filter(Message.class, m -> m.getPayload() instanceof Order)
            .transform(Order.class, this::enrichOrderWithMetadata)
            .route(Order.class, order -> order.getType(),
                mapping -> mapping
                    .channelMapping(OrderType.MARKET, "marketOrderChannel")
                    .channelMapping(OrderType.LIMIT, "limitOrderChannel")
                    .defaultOutputChannel("defaultOrderChannel"))
            .get();
    }
    
    @Bean
    public IntegrationFlow riskCheckFlow() {
        return IntegrationFlows
            .from(riskCheckChannel())
            .handle(Order.class, (payload, headers) -> {
                RiskCheckResult result = riskService.validateOrder(payload);
                if (!result.isPassed()) {
                    throw new RiskException("Risk check failed: " + result.getReason());
                }
                return payload;
            })
            .channel("executionChannel")
            .get();
    }
    
    @Bean
    public IntegrationFlow marketDataFlow() {
        return IntegrationFlows
            .from(Jms.messageDrivenChannelAdapter(connectionFactory())
                .destination("market.data.topic")
                .configureListenerContainer(c -> c.concurrency("5-10")))
            .transform(String.class, this::parseMarketDataMessage)
            .filter(MarketData.class, data -> data.getPrice().compareTo(BigDecimal.ZERO) > 0)
            .publishSubscribeChannel(c -> c
                .subscribe(f -> f.channel("priceUpdateChannel"))
                .subscribe(f -> f.channel("volumeAnalysisChannel"))
                .subscribe(f -> f.channel("riskRecalcChannel")))
            .get();
    }
    
    @Bean
    public IntegrationFlow executionReportFlow() {
        return IntegrationFlows
            .from("executionChannel")
            .aggregate(a -> a
                .correlationStrategy(m -> ((Order) m.getPayload()).getSymbol())
                .releaseStrategy(g -> g.size() >= 10 || g.getTimestamp() < System.currentTimeMillis() - 5000)
                .sendPartialResultOnExpiry(true))
            .transform(List.class, this::createBatchExecutionReport)
            .handle(Kafka.outboundChannelAdapter(kafkaTemplate())
                .topic("execution.reports")
                .messageKey(m -> ((BatchExecutionReport) m.getPayload()).getSymbol()))
            .get();
    }
    
    @ServiceActivator(inputChannel = "errorChannel")
    public void handleError(ErrorMessage errorMessage) {
        Throwable cause = errorMessage.getPayload().getCause();
        Message<?> failedMessage = errorMessage.getPayload().getFailedMessage();
        
        if (cause instanceof RiskException) {
            // Handle risk rejection
            orderService.rejectOrder((Order) failedMessage.getPayload(), cause.getMessage());
        } else {
            // Log and potentially retry
            log.error("Integration error", cause);
            deadLetterService.handleFailedMessage(failedMessage, cause);
        }
    }
}`
  };

  return (
    <div style={{
      padding: '40px',
      paddingTop: '100px',
      minHeight: '100vh',
      backgroundColor: '#0f172a',
      color: 'white'
    }}>
      <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
        <div style={{ marginBottom: '40px' }}>
          <h1 style={{ fontSize: '36px', fontWeight: 'bold', marginBottom: '16px', color: '#10b981' }}>
            Spring Framework
          </h1>
          <p style={{ fontSize: '18px', color: '#94a3b8' }}>
            Enterprise Java framework for dependency injection, AOP, and web applications
          </p>
        </div>

        <div style={{ display: 'flex', gap: '20px', marginBottom: '40px' }}>
          {['overview', 'definition', 'code', 'architecture', 'best-practices'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              style={{
                padding: '12px 24px',
                backgroundColor: activeTab === tab ? '#10b981' : 'transparent',
                border: `2px solid ${activeTab === tab ? '#10b981' : '#374151'}`,
                borderRadius: '8px',
                color: activeTab === tab ? 'white' : '#9ca3af',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                textTransform: 'capitalize',
                transition: 'all 0.2s'
              }}
            >
              {tab.replace('-', ' ')}
            </button>
          ))}
        </div>

        {activeTab === 'definition' && (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <div style={{ marginBottom: '20px' }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px' }}>
                {selectedTopic === 'ioc-di' ? 'IoC & Dependency Injection' :
                 selectedTopic === 'aop' ? 'Aspect-Oriented Programming' :
                 selectedTopic === 'spring-mvc' ? 'Spring MVC' :
                 selectedTopic === 'data-access' ? 'Data Access' :
                 selectedTopic === 'security' ? 'Spring Security' :
                 'Configuration'} Definition
              </h3>
            </div>
            
            {selectedTopic === 'ioc-di' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What is IoC & Dependency Injection?</h4>
                <p style={{ marginBottom: '16px' }}>
                  Inversion of Control (IoC) is a principle where object creation and dependency management is handled by an external framework rather than the objects themselves. Dependency Injection is the implementation technique where dependencies are provided to objects rather than created by them.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why is it essential for Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>Loose coupling between trading components (services, repositories, calculators)</li>
                  <li>Easy testing with mock dependencies for risk and pricing services</li>
                  <li>Flexible configuration for different market environments</li>
                  <li>Better maintainability and extensibility of trading logic</li>
                  <li>Centralized dependency management for complex systems</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to implement it effectively?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Prefer constructor injection for mandatory dependencies</li>
                  <li>Use @Autowired sparingly, favor explicit configuration</li>
                  <li>Define clear interfaces for trading services</li>
                  <li>Configure beans with proper scopes (singleton, prototype)</li>
                </ul>
              </div>
            )}
            
            {selectedTopic === 'spring-mvc' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What is Spring MVC?</h4>
                <p style={{ marginBottom: '16px' }}>
                  Spring MVC is a web framework that follows the Model-View-Controller pattern. It provides a comprehensive infrastructure for building web applications and RESTful APIs with features like request mapping, data binding, validation, and exception handling.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why is it valuable for Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>Build RESTful APIs for order management and portfolio services</li>
                  <li>Handle real-time trading data with WebSocket support</li>
                  <li>Implement secure trading dashboards and admin interfaces</li>
                  <li>Provide validation and error handling for trading requests</li>
                  <li>Support for various data formats (JSON, XML) and content negotiation</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to build robust APIs?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Use proper HTTP status codes for trading operations</li>
                  <li>Implement comprehensive input validation</li>
                  <li>Design consistent error response formats</li>
                  <li>Add request/response logging and monitoring</li>
                </ul>
              </div>
            )}
          </div>
        )}

        {activeTab === 'overview' && (
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>
            {topics.map((topic) => (
              <div
                key={topic.id}
                onClick={() => setSelectedTopic(topic)}
                style={{
                  padding: '24px',
                  backgroundColor: selectedTopic?.id === topic.id ? 'rgba(16, 185, 129, 0.1)' : 'rgba(31, 41, 55, 0.5)',
                  border: `2px solid ${selectedTopic?.id === topic.id ? '#10b981' : 'transparent'}`,
                  borderRadius: '12px',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '12px' }}>
                  <div style={{ color: topic.color }}>{topic.icon}</div>
                  <h3 style={{ fontSize: '20px', fontWeight: '600' }}>{topic.name}</h3>
                </div>
                <p style={{ color: '#94a3b8', fontSize: '14px' }}>{topic.description}</p>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'code' && (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <div style={{ marginBottom: '20px' }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px', textTransform: 'capitalize' }}>
                {selectedTopic ? selectedTopic.replace('-', ' ') : 'Spring Framework'} Example
              </h3>
            </div>
            <SyntaxHighlighter
              language="java"
              style={oneDark}
              customStyle={{
                backgroundColor: '#1e293b',
                padding: '20px',
                borderRadius: '8px',
                fontSize: '14px',
                lineHeight: '1.6'
              }}
            >
              {selectedTopic ? codeExamples[selectedTopic] : '// Select a topic to view code examples'}
            </SyntaxHighlighter>
          </div>
        )}

        {activeTab === 'architecture' && (
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
            <div style={{
              padding: '24px',
              backgroundColor: 'rgba(31, 41, 55, 0.5)',
              borderRadius: '12px',
              border: '1px solid #374151'
            }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px', color: '#10b981' }}>
                Spring Core Architecture
              </h3>
              <ul style={{ listStyle: 'none', padding: 0 }}>
                {[
                  'IoC Container - Bean management and DI',
                  'Application Context - Runtime environment',
                  'Bean Factory - Core bean creation',
                  'Aspect Weaving - AOP implementation',
                  'Resource Loading - Configuration management',
                  'Event System - Decoupled communication'
                ].map((feature, index) => (
                  <li key={index} style={{ padding: '8px 0', color: '#94a3b8', fontSize: '14px' }}>
                    <span style={{ color: '#10b981', marginRight: '8px' }}>•</span>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
            <div style={{
              padding: '24px',
              backgroundColor: 'rgba(31, 41, 55, 0.5)',
              borderRadius: '12px',
              border: '1px solid #374151'
            }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px', color: '#3b82f6' }}>
                Trading System Integration
              </h3>
              <ul style={{ listStyle: 'none', padding: 0 }}>
                {[
                  'Service Layer - Business logic components',
                  'Repository Layer - Data access abstraction',
                  'Controller Layer - API endpoints',
                  'Security Layer - Authentication/authorization',
                  'Transaction Layer - ACID compliance',
                  'Monitoring Layer - Metrics and health checks'
                ].map((layer, index) => (
                  <li key={index} style={{ padding: '8px 0', color: '#94a3b8', fontSize: '14px' }}>
                    <span style={{ color: '#3b82f6', marginRight: '8px' }}>•</span>
                    {layer}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {activeTab === 'best-practices' && (
          <div style={{
            padding: '24px',
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            border: '1px solid #374151'
          }}>
            <h3 style={{ fontSize: '24px', fontWeight: '600', marginBottom: '20px', color: '#10b981' }}>
              Spring Framework Best Practices
            </h3>
            <div style={{ display: 'grid', gap: '16px' }}>
              {[
                {
                  title: 'Constructor Injection',
                  description: 'Use constructor injection for required dependencies to ensure immutability'
                },
                {
                  title: 'Interface-Based Design',
                  description: 'Program to interfaces, not implementations for better testability'
                },
                {
                  title: 'Proper Bean Scopes',
                  description: 'Choose appropriate scopes: singleton for stateless, prototype for stateful'
                },
                {
                  title: 'Aspect Separation',
                  description: 'Use AOP for cross-cutting concerns like logging, security, transactions'
                },
                {
                  title: 'Configuration Management',
                  description: 'Externalize configuration and use profiles for different environments'
                }
              ].map((practice, index) => (
                <div key={index} style={{
                  padding: '16px',
                  backgroundColor: '#1e293b',
                  borderRadius: '8px',
                  borderLeft: '4px solid #10b981'
                }}>
                  <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '8px' }}>
                    {practice.title}
                  </h4>
                  <p style={{ color: '#94a3b8', fontSize: '14px' }}>
                    {practice.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      
        {/* Details Panel - Moved to bottom */}
      {selectedTopic && (
        <div style={{
          backgroundColor: 'rgba(31, 41, 55, 0.5)',
          borderRadius: '12px',
          border: '1px solid #374151',
          padding: '24px',
          marginTop: '32px'
                  }}>
          {/* Panel Header */}
          <div style={{
            padding: '20px',
            borderBottom: '1px solid #374151',
            background: 'linear-gradient(135deg, #1f2937 0%, #111827 100%)'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start' }}>
              <div>
                <h2 style={{ fontSize: '18px', fontWeight: 'bold', color: '#fbbf24', margin: '0 0 8px 0' }}>
                  {selectedTopic.name}
                </h2>
                <div style={{ fontSize: '12px', color: '#9ca3af' }}>
                  {selectedTopic.description}
                </div>
              </div>
              <button
                onClick={() => setSelectedTopic(null)}
                style={{
                  background: 'transparent',
                  border: 'none',
                  color: '#9ca3af',
                  fontSize: '24px',
                  cursor: 'pointer',
                  padding: 0,
                  lineHeight: 1
                }}
              >
                ×
              </button>
            </div>
          </div>

          {/* Spring-Specific Tabs */}
          <div style={{ display: 'flex', borderBottom: '1px solid #374151' }}>
            {['overview', 'annotations', 'configuration', 'architecture'].map(tab => (
              <button
                key={tab}
                onClick={() => setDetailsTab(tab)}
                style={{
                  flex: 1,
                  padding: '12px',
                  background: detailsTab === tab ? '#1f2937' : 'transparent',
                  border: 'none',
                  color: detailsTab === tab ? '#fbbf24' : '#9ca3af',
                  fontSize: '12px',
                  fontWeight: '600',
                  textTransform: 'capitalize',
                  cursor: 'pointer'
                }}
              >
                {tab}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div style={{ flex: 1, padding: '20px', overflowY: 'auto' }}>
            {detailsTab === 'overview' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  Spring {selectedTopic.name} Overview
                </h3>
                <div style={{ color: '#d1d5db', fontSize: '13px', lineHeight: '1.8' }}>
                  {selectedTopic.id === 'ioc-di' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Inversion of Control (IoC) and Dependency Injection (DI) are core principles that make Spring ideal for trading systems by promoting loose coupling and testability.
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Key Benefits for Trading Systems:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>Loose coupling between order processing components</li>
                        <li>Easy mocking and testing of risk management services</li>
                        <li>Configuration-based component wiring</li>
                        <li>Seamless integration with market data feeds</li>
                      </ul>
                    </>
                  )}
                  {selectedTopic.id === 'aop' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Aspect-Oriented Programming (AOP) handles cross-cutting concerns like logging, security, and transaction management without cluttering business logic.
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Trading System Applications:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>Audit logging for all trade operations</li>
                        <li>Performance monitoring and metrics collection</li>
                        <li>Security enforcement on sensitive operations</li>
                        <li>Transaction boundary management</li>
                      </ul>
                    </>
                  )}
                  {selectedTopic.id === 'spring-mvc' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Spring MVC provides the web framework for building RESTful APIs and web interfaces for trading platforms.
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Trading Platform Features:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>REST APIs for order submission and status</li>
                        <li>Real-time market data endpoints</li>
                        <li>Portfolio and position management interfaces</li>
                        <li>Risk reporting and analytics dashboards</li>
                      </ul>
                    </>
                  )}
                  {selectedTopic.id === 'data-access' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Spring's data access layer provides consistent abstractions over JDBC, JPA, and transaction management for trading data persistence.
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Trading Data Management:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>High-performance order and trade persistence</li>
                        <li>Complex portfolio and position queries</li>
                        <li>Transaction management for trade settlement</li>
                        <li>Market data archival and retrieval</li>
                      </ul>
                    </>
                  )}
                  {selectedTopic.id === 'security' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Spring Security provides comprehensive authentication and authorization for trading systems with stringent security requirements.
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Security Features:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>Role-based access control for trading functions</li>
                        <li>OAuth2 integration for external systems</li>
                        <li>Session management for trader authentication</li>
                        <li>Method-level security for sensitive operations</li>
                      </ul>
                    </>
                  )}
                  {selectedTopic.id === 'configuration' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Spring's configuration management enables flexible deployment across different trading environments and market conditions.
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Configuration Benefits:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>Environment-specific trading parameters</li>
                        <li>Market-specific configuration profiles</li>
                        <li>External configuration for risk limits</li>
                        <li>Feature toggles for trading strategies</li>
                      </ul>
                    </>
                  )}
                </div>
              </div>
            )}

            {detailsTab === 'annotations' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  Key Spring Annotations
                </h3>
                <div style={{ display: 'grid', gap: '12px' }}>
                  {[
                    { annotation: '@Component', purpose: 'Generic Spring-managed component' },
                    { annotation: '@Service', purpose: 'Business logic layer (OrderService, RiskService)' },
                    { annotation: '@Repository', purpose: 'Data access layer (OrderRepository, TradeRepository)' },
                    { annotation: '@Controller', purpose: 'Web layer for trading APIs' },
                    { annotation: '@Autowired', purpose: 'Dependency injection' },
                    { annotation: '@Transactional', purpose: 'Transaction boundary management' },
                    { annotation: '@Value', purpose: 'Property injection from configuration' },
                    { annotation: '@Profile', purpose: 'Environment-specific beans (dev, prod, test)' },
                    { annotation: '@Aspect', purpose: 'AOP aspect definition' },
                    { annotation: '@Scheduled', purpose: 'Periodic tasks (risk calculations, settlements)' }
                  ].map((item, idx) => (
                    <div key={idx} style={{
                      padding: '12px',
                      background: '#1f2937',
                      borderRadius: '6px',
                      borderLeft: `3px solid ${selectedTopic.color}`
                    }}>
                      <div style={{ color: '#fbbf24', fontSize: '13px', fontWeight: 'bold', fontFamily: 'monospace' }}>
                        {item.annotation}
                      </div>
                      <div style={{ color: '#d1d5db', fontSize: '12px', marginTop: '4px' }}>
                        {item.purpose}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {detailsTab === 'configuration' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  Trading System Configuration
                </h3>
                <div style={{
                  background: '#0f172a',
                  padding: '16px',
                  borderRadius: '8px',
                  maxHeight: '600px',
                  overflow: 'auto'
                }}>
                  <SyntaxHighlighter
                    language="java"
                    style={oneDark}
                    customStyle={{
                      background: 'transparent',
                      padding: 0,
                      margin: 0,
                      fontSize: '12px',
                      lineHeight: '1.5'
                    }}
                  >
                    {`// Trading System Configuration
@Configuration
@EnableTransactionManagement
@EnableScheduling
@EnableAspectJAutoProxy
public class TradingSystemConfig {

    @Bean
    @Primary
    public DataSource tradingDataSource() {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("*********************************");
        config.setUsername("trading_user");
        config.setPassword("secure_password");
        config.setMaximumPoolSize(50);
        config.setMinimumIdle(10);
        config.setConnectionTimeout(30000);
        return new HikariDataSource(config);
    }

    @Bean
    public PlatformTransactionManager transactionManager(DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean
    @ConfigurationProperties("trading.risk")
    public RiskParameters riskParameters() {
        return new RiskParameters();
    }

    @Bean
    public OrderExecutionService orderExecutionService() {
        return new OrderExecutionService();
    }
}

@ConfigurationProperties("trading")
@Data
public class TradingConfig {
    private Risk risk = new Risk();
    private Market market = new Market();
    private Orders orders = new Orders();
    
    @Data
    public static class Risk {
        private BigDecimal maxPositionSize = new BigDecimal("1000000");
        private BigDecimal dailyVarLimit = new BigDecimal("100000");
        private boolean enableRealTimeChecks = true;
    }
    
    @Data
    public static class Market {
        private List<String> supportedSymbols = Arrays.asList("AAPL", "GOOGL", "MSFT");
        private String primaryExchange = "NASDAQ";
        private Duration marketHours = Duration.ofHours(8);
    }
}`}
                  </SyntaxHighlighter>
                </div>
              </div>
            )}

            {detailsTab === 'architecture' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  Spring Trading System Architecture
                </h3>
                <div style={{ display: 'grid', gap: '12px' }}>
                  <div style={{
                    padding: '16px',
                    background: '#1f2937',
                    borderRadius: '8px',
                    borderLeft: '4px solid #10b981'
                  }}>
                    <h4 style={{ color: '#fbbf24', fontSize: '14px', marginBottom: '8px' }}>
                      Layered Architecture
                    </h4>
                    <ul style={{ color: '#d1d5db', fontSize: '12px', paddingLeft: '16px' }}>
                      <li>Controller Layer → REST APIs for trading operations</li>
                      <li>Service Layer → Business logic (OrderService, RiskService)</li>
                      <li>Repository Layer → Data access (JPA/JDBC)</li>
                      <li>Domain Layer → Trading entities (Order, Trade, Position)</li>
                    </ul>
                  </div>
                  
                  <div style={{
                    padding: '16px',
                    background: '#1f2937',
                    borderRadius: '8px',
                    borderLeft: '4px solid #3b82f6'
                  }}>
                    <h4 style={{ color: '#fbbf24', fontSize: '14px', marginBottom: '8px' }}>
                      Cross-Cutting Concerns (AOP)
                    </h4>
                    <ul style={{ color: '#d1d5db', fontSize: '12px', paddingLeft: '16px' }}>
                      <li>Audit Logging → Track all trading operations</li>
                      <li>Performance Monitoring → Measure execution times</li>
                      <li>Security → Role-based access control</li>
                      <li>Transaction Management → ACID compliance</li>
                    </ul>
                  </div>
                  
                  <div style={{
                    padding: '16px',
                    background: '#1f2937',
                    borderRadius: '8px',
                    borderLeft: '4px solid #f59e0b'
                  }}>
                    <h4 style={{ color: '#fbbf24', fontSize: '14px', marginBottom: '8px' }}>
                      Integration Points
                    </h4>
                    <ul style={{ color: '#d1d5db', fontSize: '12px', paddingLeft: '16px' }}>
                      <li>Market Data Feeds → Real-time price updates</li>
                      <li>Risk Management → Pre-trade and post-trade checks</li>
                      <li>Settlement Systems → Trade confirmation and clearing</li>
                      <li>Regulatory Reporting → Compliance data submission</li>
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default JavaSpring;
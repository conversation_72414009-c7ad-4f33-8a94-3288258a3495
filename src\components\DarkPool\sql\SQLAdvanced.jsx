import React, { useState } from 'react';
import { Prism as Synta<PERSON><PERSON><PERSON>lighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Settings, BarChart3, Calculator, Filter, Layers, TrendingUp } from 'lucide-react';

const SQLAdvanced = () => {
  const [selectedTopic, setSelectedTopic] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [detailsTab, setDetailsTab] = useState('definition');

  const topics = [
    {
      id: 'window-functions',
      name: 'Window Functions',
      icon: <BarChart3 size={20} />,
      description: 'Analytical functions over data sets'
    },
    {
      id: 'cte',
      name: 'Common Table Expressions',
      icon: <Layers size={20} />,
      description: 'WITH clauses and recursive queries'
    },
    {
      id: 'subqueries',
      name: 'Advanced Subqueries',
      icon: <Filter size={20} />,
      description: 'Correlated and nested subqueries'
    },
    {
      id: 'pivot',
      name: 'PIVOT & UNPIVOT',
      icon: <Settings size={20} />,
      description: 'Data transformation operations'
    },
    {
      id: 'analytical',
      name: 'Analytical Functions',
      icon: <Calculator size={20} />,
      description: 'Statistical and ranking functions'
    },
    {
      id: 'temporal',
      name: 'Temporal Queries',
      icon: <TrendingUp size={20} />,
      description: 'Time-series and date analysis'
    }
  ];

  const definitions = {
    'window-functions': `Window functions perform calculations across a set of table rows that are related to the current row. Unlike aggregate functions, window functions don't group rows into a single output row. In trading systems, they're essential for calculating moving averages, running totals, and ranking trades.`,
    'cte': `Common Table Expressions (CTEs) are temporary result sets that exist within the scope of a single SQL statement. They improve query readability and enable recursive operations. In trading, CTEs are used for complex hierarchical data analysis and multi-step calculations.`,
    'subqueries': `Advanced subqueries include correlated subqueries that reference columns from the outer query, and nested subqueries for complex filtering. They're crucial for trading systems when you need to filter based on calculated values or compare against dynamic criteria.`,
    'pivot': `PIVOT transforms rows into columns, while UNPIVOT does the reverse. These operations are valuable in trading systems for creating cross-tabulation reports, transforming time-series data, and preparing data for analysis dashboards.`,
    'analytical': `Analytical functions include ranking, statistical, and distribution functions that provide insights into data patterns. In trading, these functions help identify outliers, calculate percentiles, and rank performance across different dimensions.`,
    'temporal': `Temporal queries handle time-series data, date ranges, and time-based calculations. They're fundamental in trading systems for analyzing market trends, calculating time-weighted returns, and handling trading session logic.`
  };

  const codeExamples = {
    'window-functions': `-- Window functions for trading analysis
-- Calculate running total and moving averages
SELECT 
    symbol,
    trade_date,
    close_price,
    volume,
    -- Running total of volume
    SUM(volume) OVER (
        PARTITION BY symbol 
        ORDER BY trade_date
    ) as running_volume,
    -- 20-day moving average
    AVG(close_price) OVER (
        PARTITION BY symbol 
        ORDER BY trade_date 
        ROWS BETWEEN 19 PRECEDING AND CURRENT ROW
    ) as ma_20,
    -- Price change vs previous day
    LAG(close_price, 1) OVER (
        PARTITION BY symbol 
        ORDER BY trade_date
    ) as prev_close,
    close_price - LAG(close_price, 1) OVER (
        PARTITION BY symbol 
        ORDER BY trade_date
    ) as daily_change
FROM market_data
WHERE trade_date >= '2024-01-01';

-- Ranking and percentile functions
SELECT 
    trader_id,
    symbol,
    quantity * price as trade_value,
    -- Rank within symbol
    ROW_NUMBER() OVER (
        PARTITION BY symbol 
        ORDER BY quantity * price DESC
    ) as value_rank,
    -- Dense rank for ties
    DENSE_RANK() OVER (
        PARTITION BY symbol 
        ORDER BY quantity * price DESC
    ) as dense_rank,
    -- Percentile within trader's trades
    PERCENT_RANK() OVER (
        PARTITION BY trader_id 
        ORDER BY quantity * price
    ) as trade_percentile,
    -- First and last values
    FIRST_VALUE(price) OVER (
        PARTITION BY symbol 
        ORDER BY trade_time 
        ROWS UNBOUNDED PRECEDING
    ) as session_open,
    LAST_VALUE(price) OVER (
        PARTITION BY symbol 
        ORDER BY trade_time 
        ROWS BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING
    ) as session_close
FROM trades
WHERE trade_date = CURRENT_DATE;

-- Lead/Lag for trend analysis
SELECT 
    symbol,
    trade_time,
    price,
    -- Next 3 prices for trend analysis
    LEAD(price, 1) OVER (PARTITION BY symbol ORDER BY trade_time) as next_1,
    LEAD(price, 2) OVER (PARTITION BY symbol ORDER BY trade_time) as next_2,
    LEAD(price, 3) OVER (PARTITION BY symbol ORDER BY trade_time) as next_3,
    -- Determine trend direction
    CASE 
        WHEN price < LEAD(price, 1) OVER (PARTITION BY symbol ORDER BY trade_time) 
         AND LEAD(price, 1) OVER (PARTITION BY symbol ORDER BY trade_time) < LEAD(price, 2) OVER (PARTITION BY symbol ORDER BY trade_time)
        THEN 'UPTREND'
        WHEN price > LEAD(price, 1) OVER (PARTITION BY symbol ORDER BY trade_time)
         AND LEAD(price, 1) OVER (PARTITION BY symbol ORDER BY trade_time) > LEAD(price, 2) OVER (PARTITION BY symbol ORDER BY trade_time)
        THEN 'DOWNTREND'
        ELSE 'SIDEWAYS'
    END as trend_direction
FROM trades
WHERE symbol = 'AAPL' AND trade_date = CURRENT_DATE;`,

    'cte': `-- Common Table Expressions for complex trading analysis
-- Recursive CTE for organizational hierarchy
WITH RECURSIVE trader_hierarchy AS (
    -- Base case: top-level managers
    SELECT 
        trader_id,
        trader_name,
        reports_to,
        0 as level,
        CAST(trader_name AS VARCHAR(1000)) as hierarchy_path
    FROM traders 
    WHERE reports_to IS NULL
    
    UNION ALL
    
    -- Recursive case: subordinates
    SELECT 
        t.trader_id,
        t.trader_name,
        t.reports_to,
        th.level + 1,
        CONCAT(th.hierarchy_path, ' -> ', t.trader_name)
    FROM traders t
    JOIN trader_hierarchy th ON t.reports_to = th.trader_id
    WHERE th.level < 10  -- Prevent infinite recursion
)
SELECT 
    trader_id,
    trader_name,
    level,
    hierarchy_path,
    -- Calculate team size for each manager
    (SELECT COUNT(*) FROM trader_hierarchy sub WHERE sub.hierarchy_path LIKE CONCAT(th.hierarchy_path, '%')) - 1 as team_size
FROM trader_hierarchy th
ORDER BY level, hierarchy_path;

-- Multiple CTEs for complex portfolio analysis
WITH daily_pnl AS (
    SELECT 
        trader_id,
        symbol,
        trade_date,
        SUM(CASE WHEN side = 'BUY' THEN -quantity * price ELSE quantity * price END) as daily_pnl
    FROM trades
    WHERE trade_date >= CURRENT_DATE - INTERVAL '30 days'
    GROUP BY trader_id, symbol, trade_date
),
running_pnl AS (
    SELECT 
        trader_id,
        symbol,
        trade_date,
        daily_pnl,
        SUM(daily_pnl) OVER (
            PARTITION BY trader_id, symbol 
            ORDER BY trade_date
        ) as cumulative_pnl
    FROM daily_pnl
),
risk_metrics AS (
    SELECT 
        trader_id,
        symbol,
        AVG(daily_pnl) as avg_daily_pnl,
        STDDEV(daily_pnl) as daily_volatility,
        COUNT(*) as trading_days,
        MAX(cumulative_pnl) as max_cumulative_pnl,
        MIN(cumulative_pnl) as min_cumulative_pnl
    FROM running_pnl
    GROUP BY trader_id, symbol
)
SELECT 
    r.trader_id,
    r.symbol,
    r.avg_daily_pnl,
    r.daily_volatility,
    r.trading_days,
    -- Sharpe ratio approximation (assuming 0% risk-free rate)
    CASE 
        WHEN r.daily_volatility > 0 THEN r.avg_daily_pnl / r.daily_volatility 
        ELSE 0 
    END as sharpe_ratio,
    -- Maximum drawdown
    r.max_cumulative_pnl - r.min_cumulative_pnl as max_drawdown,
    -- Get current position
    COALESCE(p.quantity, 0) as current_position
FROM risk_metrics r
LEFT JOIN portfolio_positions p ON r.trader_id = p.trader_id AND r.symbol = p.symbol
WHERE r.trading_days >= 5;

-- CTE for market correlation analysis
WITH price_changes AS (
    SELECT 
        symbol,
        trade_date,
        close_price,
        LAG(close_price) OVER (PARTITION BY symbol ORDER BY trade_date) as prev_close,
        (close_price - LAG(close_price) OVER (PARTITION BY symbol ORDER BY trade_date)) / 
        LAG(close_price) OVER (PARTITION BY symbol ORDER BY trade_date) as return_pct
    FROM market_data
    WHERE trade_date >= CURRENT_DATE - INTERVAL '252 days'
),
correlations AS (
    SELECT 
        a.symbol as symbol_a,
        b.symbol as symbol_b,
        COUNT(*) as observations,
        CORR(a.return_pct, b.return_pct) as correlation
    FROM price_changes a
    JOIN price_changes b ON a.trade_date = b.trade_date
    WHERE a.symbol < b.symbol  -- Avoid duplicates
      AND a.return_pct IS NOT NULL 
      AND b.return_pct IS NOT NULL
    GROUP BY a.symbol, b.symbol
    HAVING COUNT(*) >= 50
)
SELECT 
    symbol_a,
    symbol_b,
    correlation,
    CASE 
        WHEN ABS(correlation) >= 0.8 THEN 'HIGHLY_CORRELATED'
        WHEN ABS(correlation) >= 0.5 THEN 'MODERATELY_CORRELATED'
        WHEN ABS(correlation) >= 0.3 THEN 'WEAKLY_CORRELATED'
        ELSE 'UNCORRELATED'
    END as correlation_strength
FROM correlations
ORDER BY ABS(correlation) DESC;`,

    'subqueries': `-- Advanced subqueries for trading systems
-- Correlated subquery for relative performance
SELECT 
    t.trader_id,
    t.trader_name,
    t.desk,
    -- Trader's YTD P&L
    (SELECT SUM(pnl) FROM trades WHERE trader_id = t.trader_id AND YEAR(trade_date) = YEAR(CURRENT_DATE)) as ytd_pnl,
    -- Desk average P&L
    (SELECT AVG(desk_pnl) FROM (
        SELECT trader_id, SUM(pnl) as desk_pnl 
        FROM trades 
        WHERE YEAR(trade_date) = YEAR(CURRENT_DATE)
        GROUP BY trader_id
    ) desk_traders 
    JOIN traders dt ON desk_traders.trader_id = dt.trader_id 
    WHERE dt.desk = t.desk) as desk_avg_pnl,
    -- Rank within desk
    (SELECT COUNT(*) + 1 FROM traders t2 WHERE t2.desk = t.desk 
     AND (SELECT COALESCE(SUM(pnl), 0) FROM trades WHERE trader_id = t2.trader_id AND YEAR(trade_date) = YEAR(CURRENT_DATE)) >
         (SELECT COALESCE(SUM(pnl), 0) FROM trades WHERE trader_id = t.trader_id AND YEAR(trade_date) = YEAR(CURRENT_DATE))) as desk_rank
FROM traders t
WHERE t.status = 'ACTIVE';

-- EXISTS and NOT EXISTS for complex filtering
-- Find traders who have traded every symbol in the S&P 500
SELECT DISTINCT t.trader_id, t.trader_name
FROM traders t
WHERE NOT EXISTS (
    SELECT 1 FROM sp500_symbols s
    WHERE NOT EXISTS (
        SELECT 1 FROM trades tr
        WHERE tr.trader_id = t.trader_id 
          AND tr.symbol = s.symbol
          AND tr.trade_date >= CURRENT_DATE - INTERVAL '30 days'
    )
);

-- Find symbols with unusual volume (subquery in HAVING)
SELECT 
    symbol,
    trade_date,
    SUM(volume) as daily_volume
FROM trades
WHERE trade_date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY symbol, trade_date
HAVING SUM(volume) > (
    SELECT AVG(daily_vol) * 2
    FROM (
        SELECT symbol, trade_date, SUM(volume) as daily_vol
        FROM trades
        WHERE trade_date >= CURRENT_DATE - INTERVAL '90 days'
        GROUP BY symbol, trade_date
    ) avg_calc
    WHERE avg_calc.symbol = trades.symbol
);

-- Nested subqueries for top performers in each category
SELECT 
    category,
    symbol,
    performance_metric,
    rank_in_category
FROM (
    SELECT 
        i.sector as category,
        p.symbol,
        p.ytd_return as performance_metric,
        ROW_NUMBER() OVER (PARTITION BY i.sector ORDER BY p.ytd_return DESC) as rank_in_category
    FROM (
        SELECT 
            symbol,
            ((MAX(close_price) - MIN(close_price)) / MIN(close_price)) * 100 as ytd_return
        FROM market_data
        WHERE YEAR(trade_date) = YEAR(CURRENT_DATE)
        GROUP BY symbol
    ) p
    JOIN instruments i ON p.symbol = i.symbol
) ranked
WHERE rank_in_category <= 3;

-- Scalar subqueries for calculated fields
SELECT 
    o.order_id,
    o.trader_id,
    o.symbol,
    o.quantity,
    o.price,
    -- Compare to average order size for this trader
    o.quantity / (
        SELECT AVG(quantity) 
        FROM orders 
        WHERE trader_id = o.trader_id 
          AND created_at >= CURRENT_DATE - INTERVAL '30 days'
    ) as vs_avg_size,
    -- Compare to current market price
    o.price / (
        SELECT price 
        FROM market_data 
        WHERE symbol = o.symbol 
        ORDER BY timestamp DESC 
        LIMIT 1
    ) as vs_market_price,
    -- Risk as percentage of trader's limit
    (o.quantity * o.price) / (
        SELECT daily_limit 
        FROM trader_limits 
        WHERE trader_id = o.trader_id
    ) * 100 as pct_of_limit
FROM orders o
WHERE o.status = 'NEW'
  AND o.created_at >= CURRENT_DATE;`,

    'pivot': `-- PIVOT and UNPIVOT for trading data transformation
-- Create a cross-tab of daily P&L by trader and symbol (MySQL/PostgreSQL approach)
SELECT 
    trade_date,
    SUM(CASE WHEN symbol = 'AAPL' THEN pnl ELSE 0 END) as AAPL_PNL,
    SUM(CASE WHEN symbol = 'GOOGL' THEN pnl ELSE 0 END) as GOOGL_PNL,
    SUM(CASE WHEN symbol = 'MSFT' THEN pnl ELSE 0 END) as MSFT_PNL,
    SUM(CASE WHEN symbol = 'TSLA' THEN pnl ELSE 0 END) as TSLA_PNL,
    SUM(pnl) as TOTAL_PNL
FROM trades
WHERE trade_date >= CURRENT_DATE - INTERVAL '30 days'
  AND symbol IN ('AAPL', 'GOOGL', 'MSFT', 'TSLA')
GROUP BY trade_date
ORDER BY trade_date;

-- Dynamic pivot for all symbols (using GROUP_CONCAT)
SELECT 
    CONCAT('SELECT trade_date, ',
           GROUP_CONCAT(
               DISTINCT CONCAT('SUM(CASE WHEN symbol = ''', symbol, ''' THEN pnl ELSE 0 END) as ', symbol, '_PNL')
               ORDER BY symbol
               SEPARATOR ', '
           ),
           ', SUM(pnl) as TOTAL_PNL FROM trades WHERE trade_date >= CURRENT_DATE - INTERVAL ''30 days'' GROUP BY trade_date ORDER BY trade_date'
    ) as pivot_query
FROM (SELECT DISTINCT symbol FROM trades WHERE trade_date >= CURRENT_DATE - INTERVAL '30 days' LIMIT 20) symbols;

-- Time-based pivot: hourly trading volumes
SELECT 
    symbol,
    SUM(CASE WHEN HOUR(trade_time) = 9 THEN volume ELSE 0 END) as H09,
    SUM(CASE WHEN HOUR(trade_time) = 10 THEN volume ELSE 0 END) as H10,
    SUM(CASE WHEN HOUR(trade_time) = 11 THEN volume ELSE 0 END) as H11,
    SUM(CASE WHEN HOUR(trade_time) = 12 THEN volume ELSE 0 END) as H12,
    SUM(CASE WHEN HOUR(trade_time) = 13 THEN volume ELSE 0 END) as H13,
    SUM(CASE WHEN HOUR(trade_time) = 14 THEN volume ELSE 0 END) as H14,
    SUM(CASE WHEN HOUR(trade_time) = 15 THEN volume ELSE 0 END) as H15,
    SUM(CASE WHEN HOUR(trade_time) = 16 THEN volume ELSE 0 END) as H16,
    SUM(volume) as TOTAL_VOLUME
FROM trades
WHERE trade_date = CURRENT_DATE
  AND HOUR(trade_time) BETWEEN 9 AND 16
GROUP BY symbol;

-- UNPIVOT simulation: Transform columns to rows
-- Original table has columns: Q1_PNL, Q2_PNL, Q3_PNL, Q4_PNL
SELECT 
    trader_id,
    'Q1' as quarter,
    Q1_PNL as pnl
FROM quarterly_pnl
WHERE Q1_PNL IS NOT NULL

UNION ALL

SELECT 
    trader_id,
    'Q2' as quarter,
    Q2_PNL as pnl
FROM quarterly_pnl
WHERE Q2_PNL IS NOT NULL

UNION ALL

SELECT 
    trader_id,
    'Q3' as quarter,
    Q3_PNL as pnl
FROM quarterly_pnl
WHERE Q3_PNL IS NOT NULL

UNION ALL

SELECT 
    trader_id,
    'Q4' as quarter,
    Q4_PNL as pnl
FROM quarterly_pnl
WHERE Q4_PNL IS NOT NULL;

-- Risk exposure by asset class pivot
SELECT 
    trader_id,
    trader_name,
    SUM(CASE WHEN i.asset_class = 'EQUITY' THEN ABS(p.market_value) ELSE 0 END) as EQUITY_EXPOSURE,
    SUM(CASE WHEN i.asset_class = 'BOND' THEN ABS(p.market_value) ELSE 0 END) as BOND_EXPOSURE,
    SUM(CASE WHEN i.asset_class = 'COMMODITY' THEN ABS(p.market_value) ELSE 0 END) as COMMODITY_EXPOSURE,
    SUM(CASE WHEN i.asset_class = 'CURRENCY' THEN ABS(p.market_value) ELSE 0 END) as CURRENCY_EXPOSURE,
    SUM(ABS(p.market_value)) as TOTAL_EXPOSURE
FROM portfolio_positions p
JOIN instruments i ON p.symbol = i.symbol
JOIN traders t ON p.trader_id = t.trader_id
WHERE p.quantity != 0
GROUP BY trader_id, trader_name;`,

    'analytical': `-- Analytical functions for trading insights
-- Statistical distribution analysis
SELECT 
    symbol,
    trade_date,
    close_price,
    -- Basic statistics
    AVG(close_price) OVER (PARTITION BY symbol) as mean_price,
    STDDEV(close_price) OVER (PARTITION BY symbol) as price_stddev,
    VARIANCE(close_price) OVER (PARTITION BY symbol) as price_variance,
    -- Percentiles and quartiles
    PERCENTILE_CONT(0.25) WITHIN GROUP (ORDER BY close_price) OVER (PARTITION BY symbol) as q1,
    PERCENTILE_CONT(0.50) WITHIN GROUP (ORDER BY close_price) OVER (PARTITION BY symbol) as median,
    PERCENTILE_CONT(0.75) WITHIN GROUP (ORDER BY close_price) OVER (PARTITION BY symbol) as q3,
    PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY close_price) OVER (PARTITION BY symbol) as p95,
    -- Z-score for outlier detection
    (close_price - AVG(close_price) OVER (PARTITION BY symbol)) / 
    STDDEV(close_price) OVER (PARTITION BY symbol) as z_score
FROM market_data
WHERE trade_date >= CURRENT_DATE - INTERVAL '252 days';

-- Ranking and distribution functions
SELECT 
    trader_id,
    symbol,
    pnl,
    -- Different ranking methods
    ROW_NUMBER() OVER (PARTITION BY symbol ORDER BY pnl DESC) as row_num,
    RANK() OVER (PARTITION BY symbol ORDER BY pnl DESC) as rank_with_gaps,
    DENSE_RANK() OVER (PARTITION BY symbol ORDER BY pnl DESC) as dense_rank,
    -- Percentile rankings
    PERCENT_RANK() OVER (PARTITION BY symbol ORDER BY pnl) as percentile_rank,
    CUME_DIST() OVER (PARTITION BY symbol ORDER BY pnl) as cumulative_distribution,
    -- Ntile for quartiles/deciles
    NTILE(4) OVER (PARTITION BY symbol ORDER BY pnl) as quartile,
    NTILE(10) OVER (PARTITION BY symbol ORDER BY pnl) as decile,
    -- Performance vs median
    pnl - PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY pnl) OVER (PARTITION BY symbol) as vs_median
FROM (
    SELECT 
        trader_id,
        symbol,
        SUM(pnl) as pnl
    FROM trades
    WHERE trade_date >= CURRENT_DATE - INTERVAL '90 days'
    GROUP BY trader_id, symbol
) trader_performance;

-- Moving statistics and trends
SELECT 
    symbol,
    trade_date,
    close_price,
    volume,
    -- Moving averages of different windows
    AVG(close_price) OVER (
        PARTITION BY symbol 
        ORDER BY trade_date 
        ROWS BETWEEN 4 PRECEDING AND CURRENT ROW
    ) as ma_5,
    AVG(close_price) OVER (
        PARTITION BY symbol 
        ORDER BY trade_date 
        ROWS BETWEEN 19 PRECEDING AND CURRENT ROW
    ) as ma_20,
    AVG(close_price) OVER (
        PARTITION BY symbol 
        ORDER BY trade_date 
        ROWS BETWEEN 49 PRECEDING AND CURRENT ROW
    ) as ma_50,
    -- Moving standard deviation for volatility
    STDDEV(close_price) OVER (
        PARTITION BY symbol 
        ORDER BY trade_date 
        ROWS BETWEEN 19 PRECEDING AND CURRENT ROW
    ) as volatility_20d,
    -- Bollinger bands
    AVG(close_price) OVER (
        PARTITION BY symbol 
        ORDER BY trade_date 
        ROWS BETWEEN 19 PRECEDING AND CURRENT ROW
    ) + 2 * STDDEV(close_price) OVER (
        PARTITION BY symbol 
        ORDER BY trade_date 
        ROWS BETWEEN 19 PRECEDING AND CURRENT ROW
    ) as upper_bollinger,
    AVG(close_price) OVER (
        PARTITION BY symbol 
        ORDER BY trade_date 
        ROWS BETWEEN 19 PRECEDING AND CURRENT ROW
    ) - 2 * STDDEV(close_price) OVER (
        PARTITION BY symbol 
        ORDER BY trade_date 
        ROWS BETWEEN 19 PRECEDING AND CURRENT ROW
    ) as lower_bollinger
FROM market_data
WHERE symbol IN ('AAPL', 'GOOGL', 'MSFT')
  AND trade_date >= CURRENT_DATE - INTERVAL '100 days';

-- Advanced analytical patterns
SELECT 
    trader_id,
    trade_date,
    symbol,
    pnl,
    -- Cumulative sum and running statistics
    SUM(pnl) OVER (
        PARTITION BY trader_id 
        ORDER BY trade_date
    ) as cumulative_pnl,
    -- Consecutive winning/losing days
    ROW_NUMBER() OVER (
        PARTITION BY trader_id, 
                    CASE WHEN pnl > 0 THEN 'WIN' ELSE 'LOSS' END,
                    trade_date - ROW_NUMBER() OVER (PARTITION BY trader_id ORDER BY trade_date)
        ORDER BY trade_date
    ) as streak_length,
    -- Maximum and minimum in rolling window
    MAX(pnl) OVER (
        PARTITION BY trader_id 
        ORDER BY trade_date 
        ROWS BETWEEN 29 PRECEDING AND CURRENT ROW
    ) as max_30d,
    MIN(pnl) OVER (
        PARTITION BY trader_id 
        ORDER BY trade_date 
        ROWS BETWEEN 29 PRECEDING AND CURRENT ROW
    ) as min_30d,
    -- Ratio to best and worst performance
    pnl / NULLIF(MAX(pnl) OVER (PARTITION BY trader_id), 0) as ratio_to_best,
    pnl / NULLIF(MIN(pnl) OVER (PARTITION BY trader_id), 0) as ratio_to_worst
FROM (
    SELECT 
        trader_id,
        trade_date,
        symbol,
        SUM(pnl) as pnl
    FROM trades
    WHERE trade_date >= CURRENT_DATE - INTERVAL '180 days'
    GROUP BY trader_id, trade_date, symbol
) daily_pnl;`,

    'temporal': `-- Temporal queries for time-series trading analysis
-- Time-based aggregations and periods
SELECT 
    symbol,
    -- Different time groupings
    DATE(trade_time) as trade_date,
    HOUR(trade_time) as trade_hour,
    DAYOFWEEK(trade_time) as day_of_week,
    WEEK(trade_time) as week_number,
    MONTH(trade_time) as month,
    QUARTER(trade_time) as quarter,
    -- Time-based calculations
    COUNT(*) as trade_count,
    SUM(quantity) as total_volume,
    AVG(price) as avg_price,
    STDDEV(price) as price_volatility,
    -- First and last trades of the period
    MIN(trade_time) as first_trade,
    MAX(trade_time) as last_trade,
    -- Price range
    MIN(price) as low,
    MAX(price) as high,
    -- OHLC construction
    FIRST_VALUE(price) OVER (
        PARTITION BY symbol, DATE(trade_time) 
        ORDER BY trade_time
    ) as open_price,
    LAST_VALUE(price) OVER (
        PARTITION BY symbol, DATE(trade_time) 
        ORDER BY trade_time
        ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
    ) as close_price
FROM trades
WHERE trade_time >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY symbol, DATE(trade_time), HOUR(trade_time), 
         DAYOFWEEK(trade_time), WEEK(trade_time), MONTH(trade_time), QUARTER(trade_time);

-- Time intervals and gaps analysis
SELECT 
    symbol,
    trade_time,
    price,
    -- Time differences
    LAG(trade_time) OVER (PARTITION BY symbol ORDER BY trade_time) as prev_trade_time,
    TIMESTAMPDIFF(SECOND, 
        LAG(trade_time) OVER (PARTITION BY symbol ORDER BY trade_time), 
        trade_time
    ) as seconds_since_last,
    -- Identify gaps in trading
    CASE 
        WHEN TIMESTAMPDIFF(MINUTE, 
                LAG(trade_time) OVER (PARTITION BY symbol ORDER BY trade_time), 
                trade_time) > 10 
        THEN 'TRADING_GAP'
        ELSE 'NORMAL'
    END as gap_status,
    -- Business hours classification
    CASE 
        WHEN HOUR(trade_time) BETWEEN 9 AND 16 
         AND DAYOFWEEK(trade_time) BETWEEN 2 AND 6 
        THEN 'MARKET_HOURS'
        ELSE 'AFTER_HOURS'
    END as session_type
FROM trades
WHERE symbol = 'AAPL' 
  AND trade_time >= CURRENT_DATE - INTERVAL '7 days';

-- Rolling time window calculations
SELECT 
    symbol,
    trade_date,
    close_price,
    -- Returns over different periods
    (close_price / LAG(close_price, 1) OVER (PARTITION BY symbol ORDER BY trade_date) - 1) * 100 as daily_return,
    (close_price / LAG(close_price, 5) OVER (PARTITION BY symbol ORDER BY trade_date) - 1) * 100 as weekly_return,
    (close_price / LAG(close_price, 21) OVER (PARTITION BY symbol ORDER BY trade_date) - 1) * 100 as monthly_return,
    -- Rolling volatility (annualized)
    STDDEV(
        LN(close_price / LAG(close_price, 1) OVER (PARTITION BY symbol ORDER BY trade_date))
    ) OVER (
        PARTITION BY symbol 
        ORDER BY trade_date 
        ROWS BETWEEN 19 PRECEDING AND CURRENT ROW
    ) * SQRT(252) * 100 as rolling_volatility_20d,
    -- Maximum drawdown in rolling window
    (close_price - MAX(close_price) OVER (
        PARTITION BY symbol 
        ORDER BY trade_date 
        ROWS BETWEEN 252 PRECEDING AND CURRENT ROW
    )) / MAX(close_price) OVER (
        PARTITION BY symbol 
        ORDER BY trade_date 
        ROWS BETWEEN 252 PRECEDING AND CURRENT ROW
    ) * 100 as max_drawdown_1y
FROM market_data
WHERE trade_date >= CURRENT_DATE - INTERVAL '500 days';

-- Seasonal analysis and patterns
SELECT 
    symbol,
    MONTH(trade_date) as month,
    DAYNAME(trade_date) as day_name,
    HOUR(trade_time) as hour,
    -- Average performance by time periods
    AVG((close_price - open_price) / open_price * 100) as avg_intraday_return,
    COUNT(*) as observation_count,
    STDDEV((close_price - open_price) / open_price * 100) as return_volatility,
    -- Best and worst performances
    MAX((close_price - open_price) / open_price * 100) as best_return,
    MIN((close_price - open_price) / open_price * 100) as worst_return,
    -- Win rate
    SUM(CASE WHEN close_price > open_price THEN 1 ELSE 0 END) / COUNT(*) * 100 as win_rate
FROM market_data
WHERE trade_date >= CURRENT_DATE - INTERVAL '2 years'
  AND DAYOFWEEK(trade_date) BETWEEN 2 AND 6  -- Weekdays only
GROUP BY symbol, MONTH(trade_date), DAYNAME(trade_date), HOUR(trade_time)
HAVING COUNT(*) >= 10  -- Minimum observations for statistical significance;

-- Time-weighted calculations for portfolio performance
SELECT 
    portfolio_id,
    measurement_date,
    market_value,
    -- Time-weighted return calculation
    LAG(market_value) OVER (PARTITION BY portfolio_id ORDER BY measurement_date) as prev_value,
    LAG(measurement_date) OVER (PARTITION BY portfolio_id ORDER BY measurement_date) as prev_date,
    DATEDIFF(
        measurement_date, 
        LAG(measurement_date) OVER (PARTITION BY portfolio_id ORDER BY measurement_date)
    ) as days_elapsed,
    -- Period return
    (market_value / LAG(market_value) OVER (PARTITION BY portfolio_id ORDER BY measurement_date) - 1) * 100 as period_return,
    -- Annualized return
    POWER(
        market_value / LAG(market_value) OVER (PARTITION BY portfolio_id ORDER BY measurement_date),
        365.0 / DATEDIFF(
            measurement_date, 
            LAG(measurement_date) OVER (PARTITION BY portfolio_id ORDER BY measurement_date)
        )
    ) - 1 as annualized_return
FROM portfolio_valuations
WHERE measurement_date >= CURRENT_DATE - INTERVAL '1 year'
ORDER BY portfolio_id, measurement_date;`
  };

  return (
    <div style={{
      padding: '40px',
      fontFamily: 'Inter, system-ui, sans-serif',
      backgroundColor: '#0f172a',
      color: 'white',
      minHeight: '100vh'
    }}>
      <div style={{ marginBottom: '40px' }}>
        <h1 style={{ 
          fontSize: '36px', 
          fontWeight: '700', 
          marginBottom: '16px',
          background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent'
        }}>
          Advanced SQL Queries
        </h1>
        <p style={{ fontSize: '18px', color: '#94a3b8', lineHeight: '1.6' }}>
          Master complex SQL patterns for sophisticated trading system analysis
        </p>
      </div>

      <div style={{ display: 'flex', gap: '40px' }}>
        <div style={{ flex: '1', maxWidth: '300px' }}>
          <h2 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '24px', color: '#10b981' }}>
            Advanced Topics
          </h2>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {topics.map((topic) => (
              <button
                key={topic.id}
                onClick={() => setSelectedTopic(topic)}
                style={{
                  padding: '16px',
                  backgroundColor: selectedTopic?.id === topic.id ? 'rgba(16, 185, 129, 0.2)' : 'rgba(30, 41, 59, 0.5)',
                  border: `2px solid ${selectedTopic?.id === topic.id ? '#10b981' : 'transparent'}`,
                  borderRadius: '12px',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  textAlign: 'left',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px'
                }}
              >
                <div style={{ color: selectedTopic === topic.id ? '#10b981' : '#64748b' }}>
                  {topic.icon}
                </div>
                <div>
                  <div style={{ 
                    fontWeight: '600', 
                    color: selectedTopic === topic.id ? '#10b981' : 'white',
                    marginBottom: '4px'
                  }}>
                    {topic.name}
                  </div>
                  <div style={{ fontSize: '14px', color: '#94a3b8' }}>
                    {topic.description}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        <div style={{ flex: '2' }}>
          <div style={{ marginBottom: '24px' }}>
            <div style={{ display: 'flex', gap: '8px', marginBottom: '24px' }}>
              {['overview', 'definition', 'code'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  style={{
                    padding: '12px 24px',
                    backgroundColor: activeTab === tab ? '#10b981' : 'transparent',
                    border: `2px solid ${activeTab === tab ? '#10b981' : '#374151'}`,
                    borderRadius: '8px',
                    color: activeTab === tab ? 'white' : '#94a3b8',
                    cursor: 'pointer',
                    fontWeight: '600',
                    textTransform: 'capitalize',
                    transition: 'all 0.2s'
                  }}
                >
                  {tab}
                </button>
              ))}
            </div>
          </div>

          {activeTab === 'overview' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <h2 style={{ fontSize: '28px', fontWeight: '600', marginBottom: '24px', color: '#10b981' }}>
                Advanced SQL Overview
              </h2>
              <div style={{ fontSize: '16px', lineHeight: '1.8', color: '#e2e8f0' }}>
                <p style={{ marginBottom: '20px' }}>
                  Advanced SQL techniques enable sophisticated analysis of trading data through window functions, 
                  Common Table Expressions, complex subqueries, and analytical functions that are essential 
                  for modern quantitative trading systems.
                </p>
                <div style={{ marginBottom: '24px' }}>
                  <h3 style={{ color: '#10b981', fontSize: '20px', marginBottom: '16px' }}>Key Advanced Features:</h3>
                  <ul style={{ marginLeft: '24px', lineHeight: '1.8' }}>
                    <li><strong>Window Functions:</strong> Moving averages, rankings, and running totals</li>
                    <li><strong>CTEs:</strong> Complex hierarchical queries and recursive operations</li>
                    <li><strong>Analytical Functions:</strong> Statistical analysis and performance metrics</li>
                    <li><strong>Temporal Queries:</strong> Time-series analysis and seasonal patterns</li>
                  </ul>
                </div>
                <div style={{ 
                  backgroundColor: 'rgba(16, 185, 129, 0.1)', 
                  padding: '20px', 
                  borderRadius: '8px',
                  border: '1px solid #10b981'
                }}>
                  <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Trading Applications:</h4>
                  <p>
                    These advanced SQL patterns are crucial for risk analytics, performance attribution, 
                    algorithmic trading signals, and regulatory reporting in institutional trading environments.
                  </p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'definition' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <h2 style={{ fontSize: '24px', fontWeight: '600', marginBottom: '20px', color: '#10b981' }}>
                {topics.find(t => t.id === selectedTopic)?.name} Definition
              </h2>
              <p style={{ fontSize: '16px', lineHeight: '1.8', color: '#e2e8f0' }}>
                {definitions[selectedTopic]}
              </p>
            </div>
          )}

          {activeTab === 'code' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <div style={{ marginBottom: '20px' }}>
                <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px' }}>
                  {topics.find(t => t.id === selectedTopic)?.name} Examples
                </h3>
              </div>
              <SyntaxHighlighter
                language="sql"
                style={oneDark}
                customStyle={{
                  backgroundColor: '#1e293b',
                  padding: '20px',
                  borderRadius: '8px',
                  fontSize: '14px',
                  lineHeight: '1.6'
                }}
              >
                {codeExamples[selectedTopic]}
              </SyntaxHighlighter>
            </div>
          )}
        </div>
      </div>
      
      {/* Details Panel - Matching Dark Pool Style */}
      {selectedTopic && (
        <div style={{
          position: 'fixed',
          right: 0,
          top: 0,
          width: '500px',
          height: '100vh',
          backgroundColor: 'rgba(17, 24, 39, 0.95)',
          backdropFilter: 'blur(12px)',
          borderLeft: '1px solid #374151',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          zIndex: 1000
        }}>
          {/* Panel Header */}
          <div style={{
            padding: '20px',
            borderBottom: '1px solid #374151',
            background: 'linear-gradient(135deg, #1f2937 0%, #**********%)'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start' }}>
              <div>
                <h2 style={{ fontSize: '18px', fontWeight: 'bold', color: '#fbbf24', margin: '0 0 8px 0' }}>
                  {selectedTopic.name}
                </h2>
                <div style={{ fontSize: '12px', color: '#9ca3af' }}>
                  {selectedTopic.description}
                </div>
              </div>
              <button
                onClick={() => setSelectedTopic(null)}
                style={{
                  background: 'transparent',
                  border: 'none',
                  color: '#9ca3af',
                  fontSize: '24px',
                  cursor: 'pointer',
                  padding: 0,
                  lineHeight: 1
                }}
              >
                ×
              </button>
            </div>
          </div>

          {/* Tabs */}
          <div style={{ display: 'flex', borderBottom: '1px solid #374151' }}>
            {['definition', 'code', 'use-cases', 'best-practices'].map(tab => (
              <button
                key={tab}
                onClick={() => setDetailsTab(tab)}
                style={{
                  flex: 1,
                  padding: '12px',
                  background: detailsTab === tab ? '#1f2937' : 'transparent',
                  border: 'none',
                  color: detailsTab === tab ? '#fbbf24' : '#9ca3af',
                  fontSize: '12px',
                  fontWeight: '600',
                  textTransform: 'capitalize',
                  cursor: 'pointer'
                }}
              >
                {tab.replace('-', ' ')}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div style={{ flex: 1, padding: '20px', overflowY: 'auto' }}>
            {detailsTab === 'definition' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  What are {selectedTopic.name}?
                </h3>
                <div style={{ color: '#d1d5db', fontSize: '13px', lineHeight: '1.8' }}>
                  <p style={{ marginBottom: '16px' }}>
                    {definitions[selectedTopic.id]}
                  </p>
                  
                  <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Key Benefits in Trading Systems:</h4>
                  <ul style={{ paddingLeft: '20px' }}>
                    {selectedTopic.id === 'window-functions' && (
                      <>
                        <li>Calculate moving averages and technical indicators</li>
                        <li>Rank trades and performance metrics</li>
                        <li>Compare current values with historical data</li>
                        <li>Calculate running totals and cumulative metrics</li>
                      </>
                    )}
                    {selectedTopic.id === 'cte' && (
                      <>
                        <li>Simplify complex multi-step calculations</li>
                        <li>Handle recursive portfolio hierarchies</li>
                        <li>Improve query readability and maintenance</li>
                        <li>Enable step-by-step data transformations</li>
                      </>
                    )}
                    {selectedTopic.id === 'subqueries' && (
                      <>
                        <li>Filter trades based on calculated criteria</li>
                        <li>Compare against dynamic benchmarks</li>
                        <li>Handle complex conditional logic</li>
                        <li>Perform existence checks efficiently</li>
                      </>
                    )}
                  </ul>
                </div>
              </div>
            )}

            {detailsTab === 'code' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  Implementation Example
                </h3>
                <div style={{
                  background: '#0f172a',
                  padding: '16px',
                  borderRadius: '8px',
                  maxHeight: '600px',
                  overflow: 'auto'
                }}>
                  <SyntaxHighlighter
                    language="sql"
                    style={oneDark}
                    customStyle={{
                      background: 'transparent',
                      padding: 0,
                      margin: 0,
                      fontSize: '12px',
                      lineHeight: '1.5'
                    }}
                  >
                    {codeExamples[selectedTopic.id] || '-- Code example not available'}
                  </SyntaxHighlighter>
                </div>
              </div>
            )}

            {detailsTab === 'use-cases' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  Trading System Use Cases
                </h3>
                <div style={{ display: 'grid', gap: '12px' }}>
                  {[
                    'Real-time P&L calculation with running totals',
                    'Risk metrics calculation using window functions',
                    'Portfolio performance ranking and percentiles',
                    'Time-series analysis for market trends',
                    'Complex trade settlement calculations',
                    'Regulatory reporting with aggregated data'
                  ].map((useCase, idx) => (
                    <div key={idx} style={{
                      padding: '12px',
                      background: '#1f2937',
                      borderRadius: '6px',
                      borderLeft: '3px solid #10b981',
                      color: '#d1d5db',
                      fontSize: '13px'
                    }}>
                      {useCase}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {detailsTab === 'best-practices' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  Best Practices
                </h3>
                <div style={{ display: 'grid', gap: '12px' }}>
                  {[
                    'Use appropriate window frames for calculations',
                    'Index partition columns for better performance',
                    'Avoid unnecessary sorting in window functions',
                    'Use CTEs for complex multi-step queries',
                    'Test subquery performance vs joins',
                    'Consider materialized views for repeated calculations',
                    'Monitor query execution plans regularly'
                  ].map((practice, idx) => (
                    <div key={idx} style={{
                      padding: '12px',
                      background: '#1f2937',
                      borderRadius: '6px',
                      borderLeft: '3px solid #10b981',
                      color: '#d1d5db',
                      fontSize: '13px'
                    }}>
                      {practice}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SQLAdvanced;
import React, { useState } from 'react';
import { <PERSON>rism as <PERSON>ynta<PERSON><PERSON><PERSON>lighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Database, Table, Search, Plus, Edit, Trash2 } from 'lucide-react';

const SQLBasics = () => {
  const [selectedTopic, setSelectedTopic] = useState('select');
  const [activeTab, setActiveTab] = useState('overview');

  const topics = [
    {
      id: 'select',
      name: 'SELECT Queries',
      icon: <Search size={20} />,
      description: 'Data retrieval and filtering'
    },
    {
      id: 'insert',
      name: 'INSERT Operations',
      icon: <Plus size={20} />,
      description: 'Adding new data records'
    },
    {
      id: 'update',
      name: 'UPDATE Statements',
      icon: <Edit size={20} />,
      description: 'Modifying existing data'
    },
    {
      id: 'delete',
      name: 'DELETE Operations',
      icon: <Trash2 size={20} />,
      description: 'Removing data records'
    },
    {
      id: 'ddl',
      name: 'DDL Operations',
      icon: <Table size={20} />,
      description: 'CREATE, ALTER, DROP statements'
    },
    {
      id: 'functions',
      name: 'Built-in Functions',
      icon: <Database size={20} />,
      description: 'Aggregate and scalar functions'
    }
  ];

  const definitions = {
    'select': `SELECT statements are used to retrieve data from database tables. They form the foundation of SQL querying and allow you to filter, sort, and transform data. In trading systems, SELECT queries are essential for retrieving market data, order information, and portfolio positions.`,
    'insert': `INSERT statements add new records to database tables. In trading systems, INSERT operations are used to record new orders, trades, market data updates, and transaction logs. Proper INSERT handling is crucial for maintaining data integrity.`,
    'update': `UPDATE statements modify existing records in database tables. Trading systems use UPDATE operations to change order statuses, update positions, modify risk parameters, and maintain current market prices.`,
    'delete': `DELETE statements remove records from database tables. In trading systems, DELETE operations are used carefully to remove cancelled orders, clean up old data, or purge test records while maintaining audit trails.`,
    'ddl': `Data Definition Language (DDL) operations create, modify, and remove database objects like tables, indexes, and constraints. In trading systems, DDL is used to set up order tables, market data schemas, and performance indexes.`,
    'functions': `Built-in SQL functions perform calculations, transformations, and aggregations on data. Trading systems extensively use functions for calculating returns, aggregating volumes, computing moving averages, and formatting financial data.`
  };

  const codeExamples = {
    'select': `-- Basic SELECT queries for trading data
-- Retrieve all orders for a specific symbol
SELECT order_id, symbol, side, quantity, price, status, created_at
FROM orders 
WHERE symbol = 'AAPL' 
  AND status IN ('NEW', 'PARTIALLY_FILLED');

-- Get daily trading volume by symbol
SELECT symbol, 
       SUM(quantity) as total_volume,
       COUNT(*) as trade_count,
       AVG(price) as avg_price
FROM trades 
WHERE trade_date = CURRENT_DATE
GROUP BY symbol
ORDER BY total_volume DESC;

-- Find top performing stocks
SELECT symbol,
       (close_price - open_price) / open_price * 100 as pct_change
FROM market_data 
WHERE trade_date = CURRENT_DATE
  AND close_price IS NOT NULL
ORDER BY pct_change DESC
LIMIT 10;

-- Complex filtering with subqueries
SELECT o.order_id, o.symbol, o.quantity, o.price
FROM orders o
WHERE o.trader_id IN (
    SELECT trader_id 
    FROM traders 
    WHERE risk_level = 'HIGH'
)
AND o.created_at >= CURRENT_DATE - INTERVAL '1 day';`,

    'insert': `-- INSERT operations for trading systems
-- Add new order
INSERT INTO orders (
    order_id, trader_id, symbol, side, order_type,
    quantity, price, status, created_at
) VALUES (
    'ORD-001', 'TRD-123', 'AAPL', 'BUY', 'LIMIT',
    100, 150.25, 'NEW', NOW()
);

-- Bulk insert market data
INSERT INTO market_data (symbol, price, volume, timestamp)
VALUES 
    ('AAPL', 150.25, 1000, NOW()),
    ('GOOGL', 2800.50, 500, NOW()),
    ('MSFT', 300.75, 750, NOW());

-- Insert with calculation
INSERT INTO portfolio_positions (
    trader_id, symbol, quantity, avg_price, market_value
)
SELECT 
    trader_id,
    symbol,
    SUM(CASE WHEN side = 'BUY' THEN quantity ELSE -quantity END) as net_quantity,
    AVG(price) as avg_price,
    SUM(CASE WHEN side = 'BUY' THEN quantity ELSE -quantity END) * AVG(price) as market_value
FROM executed_trades
WHERE execution_date = CURRENT_DATE
GROUP BY trader_id, symbol
HAVING SUM(CASE WHEN side = 'BUY' THEN quantity ELSE -quantity END) != 0;`,

    'update': `-- UPDATE operations for trading systems
-- Update order status after execution
UPDATE orders 
SET status = 'FILLED',
    filled_quantity = quantity,
    avg_fill_price = 150.30,
    updated_at = NOW()
WHERE order_id = 'ORD-001';

-- Update portfolio positions after trade
UPDATE portfolio_positions 
SET quantity = quantity + 100,
    market_value = (quantity + 100) * 150.30,
    last_updated = NOW()
WHERE trader_id = 'TRD-123' 
  AND symbol = 'AAPL';

-- Conditional update with risk check
UPDATE orders 
SET status = 'CANCELLED',
    cancel_reason = 'RISK_LIMIT_EXCEEDED',
    updated_at = NOW()
WHERE trader_id = 'TRD-123'
  AND status = 'NEW'
  AND quantity * price > (
    SELECT daily_limit 
    FROM trader_limits 
    WHERE trader_id = 'TRD-123'
  );

-- Update market prices with percentage change
UPDATE market_data 
SET previous_close = close_price,
    pct_change = (close_price - previous_close) / previous_close * 100
WHERE trade_date = CURRENT_DATE;`,

    'delete': `-- DELETE operations for trading systems
-- Cancel and remove pending orders (with audit trail)
-- First insert into audit table
INSERT INTO order_audit 
SELECT *, 'DELETED', NOW(), 'SYSTEM_CLEANUP'
FROM orders 
WHERE status = 'NEW' 
  AND created_at < NOW() - INTERVAL '1 day';

-- Then delete the orders
DELETE FROM orders 
WHERE status = 'NEW' 
  AND created_at < NOW() - INTERVAL '1 day';

-- Remove old market data (keeping recent data)
DELETE FROM market_data_minute 
WHERE timestamp < CURRENT_DATE - INTERVAL '30 days';

-- Conditional delete with join
DELETE o FROM orders o
JOIN traders t ON o.trader_id = t.trader_id
WHERE t.status = 'INACTIVE'
  AND o.status IN ('CANCELLED', 'REJECTED');

-- Safe delete with limit (for large tables)
DELETE FROM trade_logs 
WHERE log_date < CURRENT_DATE - INTERVAL '90 days'
LIMIT 1000;`,

    'ddl': `-- DDL operations for trading systems
-- Create orders table
CREATE TABLE orders (
    order_id VARCHAR(50) PRIMARY KEY,
    trader_id VARCHAR(20) NOT NULL,
    symbol VARCHAR(10) NOT NULL,
    side ENUM('BUY', 'SELL') NOT NULL,
    order_type ENUM('MARKET', 'LIMIT', 'STOP') NOT NULL,
    quantity DECIMAL(15,2) NOT NULL,
    price DECIMAL(15,4),
    status ENUM('NEW', 'PARTIALLY_FILLED', 'FILLED', 'CANCELLED') DEFAULT 'NEW',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_trader_symbol (trader_id, symbol),
    INDEX idx_status_created (status, created_at)
);

-- Create market data table with partitioning
CREATE TABLE market_data (
    symbol VARCHAR(10) NOT NULL,
    price DECIMAL(15,4) NOT NULL,
    volume BIGINT NOT NULL,
    bid DECIMAL(15,4),
    ask DECIMAL(15,4),
    timestamp TIMESTAMP NOT NULL,
    PRIMARY KEY (symbol, timestamp)
) PARTITION BY RANGE (UNIX_TIMESTAMP(timestamp)) (
    PARTITION p_2024_01 VALUES LESS THAN (UNIX_TIMESTAMP('2024-02-01')),
    PARTITION p_2024_02 VALUES LESS THAN (UNIX_TIMESTAMP('2024-03-01'))
);

-- Add constraints for data integrity
ALTER TABLE orders 
ADD CONSTRAINT chk_positive_quantity CHECK (quantity > 0),
ADD CONSTRAINT chk_positive_price CHECK (price IS NULL OR price > 0);

-- Create indexes for performance
CREATE INDEX idx_orders_symbol_time ON orders (symbol, created_at);
CREATE INDEX idx_market_data_time ON market_data (timestamp);`,

    'functions': `-- Built-in SQL functions for trading systems
-- Date and time functions
SELECT 
    symbol,
    DATE(created_at) as trade_date,
    TIME(created_at) as trade_time,
    YEAR(created_at) as trade_year,
    MONTH(created_at) as trade_month,
    DAYOFWEEK(created_at) as day_of_week
FROM trades;

-- Aggregate functions for portfolio analysis
SELECT 
    trader_id,
    COUNT(*) as total_trades,
    SUM(quantity * price) as total_value,
    AVG(price) as avg_price,
    MIN(price) as min_price,
    MAX(price) as max_price,
    STDDEV(price) as price_volatility
FROM trades
GROUP BY trader_id;

-- String functions for symbol manipulation
SELECT 
    symbol,
    LENGTH(symbol) as symbol_length,
    LEFT(symbol, 3) as symbol_prefix,
    CONCAT(symbol, '_', DATE_FORMAT(NOW(), '%Y%m%d')) as daily_symbol,
    UPPER(symbol) as upper_symbol,
    REPLACE(symbol, '.', '_') as clean_symbol
FROM market_data;

-- Mathematical functions for financial calculations
SELECT 
    symbol,
    price,
    ROUND(price, 2) as rounded_price,
    ABS(price - prev_price) as abs_change,
    POWER((price / prev_price), 252) as annualized_return,
    LOG(price / prev_price) as log_return,
    SQRT(variance) as volatility
FROM daily_returns;

-- Conditional functions
SELECT 
    order_id,
    symbol,
    quantity,
    price,
    CASE 
        WHEN price * quantity > 10000 THEN 'LARGE'
        WHEN price * quantity > 1000 THEN 'MEDIUM'
        ELSE 'SMALL'
    END as order_size,
    IF(status = 'FILLED', 'COMPLETE', 'PENDING') as simple_status,
    COALESCE(fill_price, price, 0) as effective_price
FROM orders;`
  };

  return (
    <div style={{
      padding: '40px',
      fontFamily: 'Inter, system-ui, sans-serif',
      backgroundColor: '#0f172a',
      color: 'white',
      minHeight: '100vh'
    }}>
      <div style={{ marginBottom: '40px' }}>
        <h1 style={{ 
          fontSize: '36px', 
          fontWeight: '700', 
          marginBottom: '16px',
          background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent'
        }}>
          SQL Fundamentals
        </h1>
        <p style={{ fontSize: '18px', color: '#94a3b8', lineHeight: '1.6' }}>
          Master the core SQL operations essential for trading system data management
        </p>
      </div>

      <div style={{ display: 'flex', gap: '40px' }}>
        <div style={{ flex: '1', maxWidth: '300px' }}>
          <h2 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '24px', color: '#10b981' }}>
            SQL Topics
          </h2>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {topics.map((topic) => (
              <button
                key={topic.id}
                onClick={() => setSelectedTopic(topic.id)}
                style={{
                  padding: '16px',
                  backgroundColor: selectedTopic === topic.id ? 'rgba(16, 185, 129, 0.2)' : 'rgba(30, 41, 59, 0.5)',
                  border: `2px solid ${selectedTopic === topic.id ? '#10b981' : 'transparent'}`,
                  borderRadius: '12px',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  textAlign: 'left',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px'
                }}
              >
                <div style={{ color: selectedTopic === topic.id ? '#10b981' : '#64748b' }}>
                  {topic.icon}
                </div>
                <div>
                  <div style={{ 
                    fontWeight: '600', 
                    color: selectedTopic === topic.id ? '#10b981' : 'white',
                    marginBottom: '4px'
                  }}>
                    {topic.name}
                  </div>
                  <div style={{ fontSize: '14px', color: '#94a3b8' }}>
                    {topic.description}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        <div style={{ flex: '2' }}>
          <div style={{ marginBottom: '24px' }}>
            <div style={{ display: 'flex', gap: '8px', marginBottom: '24px' }}>
              {['overview', 'definition', 'code'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  style={{
                    padding: '12px 24px',
                    backgroundColor: activeTab === tab ? '#10b981' : 'transparent',
                    border: `2px solid ${activeTab === tab ? '#10b981' : '#374151'}`,
                    borderRadius: '8px',
                    color: activeTab === tab ? 'white' : '#94a3b8',
                    cursor: 'pointer',
                    fontWeight: '600',
                    textTransform: 'capitalize',
                    transition: 'all 0.2s'
                  }}
                >
                  {tab}
                </button>
              ))}
            </div>
          </div>

          {activeTab === 'overview' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <h2 style={{ fontSize: '28px', fontWeight: '600', marginBottom: '24px', color: '#10b981' }}>
                SQL Fundamentals Overview
              </h2>
              <div style={{ fontSize: '16px', lineHeight: '1.8', color: '#e2e8f0' }}>
                <p style={{ marginBottom: '20px' }}>
                  SQL (Structured Query Language) is the foundation of data management in trading systems. 
                  Understanding SQL fundamentals is crucial for working with order management systems, 
                  market data processing, and portfolio management.
                </p>
                <div style={{ marginBottom: '24px' }}>
                  <h3 style={{ color: '#10b981', fontSize: '20px', marginBottom: '16px' }}>Key Areas:</h3>
                  <ul style={{ marginLeft: '24px', lineHeight: '1.8' }}>
                    <li><strong>Data Retrieval:</strong> SELECT queries for orders, trades, and market data</li>
                    <li><strong>Data Modification:</strong> INSERT, UPDATE, DELETE for maintaining trading records</li>
                    <li><strong>Schema Design:</strong> CREATE, ALTER, DROP for database structure</li>
                    <li><strong>Functions:</strong> Built-in functions for calculations and transformations</li>
                  </ul>
                </div>
                <div style={{ 
                  backgroundColor: 'rgba(16, 185, 129, 0.1)', 
                  padding: '20px', 
                  borderRadius: '8px',
                  border: '1px solid #10b981'
                }}>
                  <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Trading System Applications:</h4>
                  <p>
                    SQL is essential for order management, trade settlement, risk calculation, 
                    market data analysis, and regulatory reporting in modern trading platforms.
                  </p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'definition' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <h2 style={{ fontSize: '24px', fontWeight: '600', marginBottom: '20px', color: '#10b981' }}>
                {topics.find(t => t.id === selectedTopic)?.name} Definition
              </h2>
              <p style={{ fontSize: '16px', lineHeight: '1.8', color: '#e2e8f0' }}>
                {definitions[selectedTopic]}
              </p>
            </div>
          )}

          {activeTab === 'code' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <div style={{ marginBottom: '20px' }}>
                <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px', textTransform: 'capitalize' }}>
                  {selectedTopic ? selectedTopic.replace('-', ' ') : 'SQL Basics'} Examples
                </h3>
              </div>
              <SyntaxHighlighter
                language="sql"
                style={oneDark}
                customStyle={{
                  backgroundColor: '#1e293b',
                  padding: '20px',
                  borderRadius: '8px',
                  fontSize: '14px',
                  lineHeight: '1.6'
                }}
              >
                {selectedTopic ? codeExamples[selectedTopic] : '// Select a topic to view code examples'}
              </SyntaxHighlighter>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SQLBasics;
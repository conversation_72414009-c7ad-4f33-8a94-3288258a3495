import React, { useState } from 'react';
import { Cpu, TrendingUp, BarChart, Shield, Database, Activity, Coffee, Layers, MessageSquare, Server, HardDrive, Terminal, ChevronDown, ChevronRight, ChevronLeft, Cloud, Lock, Zap, Code, Brain, Package, GitBranch, Sparkles, AlertCircle, Gauge, Settings, Hexagon, Box, Menu, CreditCard, AlertTriangle } from 'lucide-react';

const SidebarNavigation = ({ currentView, onViewChange, onCollapseChange }) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  
  const handleCollapse = () => {
    const newState = !isCollapsed;
    setIsCollapsed(newState);
    if (onCollapseChange) {
      onCollapseChange(newState);
    }
  };
  const [expandedSections, setExpandedSections] = useState({
    'Architecture Views': true,
    'Java': true,
    'Design Patterns': true,
    'Messaging': true,
    'SQL': true,
    'NoSQL': true,
    'DevOps': true,
    'Cloud Services': true,
    'Security': true,
    'Performance': true
  });

  const toggleSection = (sectionTitle) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionTitle]: !prev[sectionTitle]
    }));
  };

  const navigationSections = [
    {
      title: 'Architecture Views',
      items: [
        {
          id: 'darkpool',
          name: 'Dark Pool Matching Engine Basic',
          icon: <Cpu size={20} />,
          description: 'High-frequency trading system'
        },
        {
          id: 'varcvar',
          name: 'Parking Lot OOP',
          icon: <TrendingUp size={20} />,
          description: 'Object-oriented parking system'
        },
        {
          id: 'library-management',
          name: 'Library Management OOP',
          icon: <Coffee size={20} />,
          description: 'Object-oriented library system',
          disabled: false
        },
        {
          id: 'elevator-system',
          name: 'Elevator System OOP',
          icon: <Box size={20} />,
          description: 'Object-oriented elevator control system',
          disabled: false
        },
        {
          id: 'atm-machine',
          name: 'ATM Machine OOP',
          icon: <CreditCard size={20} />,
          description: 'Object-oriented ATM banking system',
          disabled: false
        },
        {
          id: 'portfolio',
          name: 'Dark Pool Matching Engine Advanced',
          icon: <BarChart size={20} />,
          description: 'Advanced matching engine features',
          disabled: false
        },
        {
          id: 'compliance',
          name: 'VaR/CVaR System',
          icon: <Shield size={20} />,
          description: 'Value at Risk analytics system',
          disabled: false
        },
        {
          id: 'data',
          name: 'Data Infrastructure',
          icon: <Database size={20} />,
          description: 'Coming soon',
          disabled: true
        }
      ]
    },
    {
      title: 'Java',
      items: [
        {
          id: 'java-oop',
          name: 'OOP',
          icon: <Code size={20} />,
          description: 'Object-Oriented Programming concepts',
          disabled: false
        },
        {
          id: 'java-memory',
          name: 'Memory Management',
          icon: <Brain size={20} />,
          description: 'Heap, stack, and garbage collection',
          disabled: false
        },
        {
          id: 'java-collections',
          name: 'Collections',
          icon: <Package size={20} />,
          description: 'Lists, sets, maps, and queues',
          disabled: false
        },
        {
          id: 'java-concurrency',
          name: 'Concurrency',
          icon: <GitBranch size={20} />,
          description: 'Threads, executors, and synchronization',
          disabled: false
        },
        {
          id: 'java-8plus',
          name: 'Java 8+',
          icon: <Sparkles size={20} />,
          description: 'Lambdas, streams, and modern features',
          disabled: false
        },
        {
          id: 'java-exceptions',
          name: 'Exception Handling',
          icon: <AlertCircle size={20} />,
          description: 'Try-catch, custom exceptions, best practices',
          disabled: false
        },
        {
          id: 'java-jvm',
          name: 'JVM and Performance',
          icon: <Gauge size={20} />,
          description: 'JVM internals, tuning, and optimization',
          disabled: false
        },
        {
          id: 'java-advanced-oop',
          name: 'Advanced OOP',
          icon: <Settings size={20} />,
          description: 'Advanced object-oriented concepts and patterns',
          disabled: false
        },
        {
          id: 'java-spring',
          name: 'Spring Framework',
          icon: <Hexagon size={20} />,
          description: 'Spring Core, DI, AOP, and enterprise features',
          disabled: false
        },
        {
          id: 'java-spring-boot',
          name: 'Spring Boot',
          icon: <Box size={20} />,
          description: 'Auto-configuration, microservices, and production features',
          disabled: false
        }
      ]
    },
    {
      title: 'Design Patterns',
      items: [
        {
          id: 'patterns',
          name: 'Design Patterns',
          icon: <Layers size={20} />,
          description: 'Architecture patterns and best practices',
          disabled: false
        },
        {
          id: 'microservice-patterns',
          name: 'Microservice Patterns',
          icon: <Server size={20} />,
          description: 'Microservice architecture patterns',
          disabled: false
        }
      ]
    },
    {
      title: 'Messaging',
      items: [
        {
          id: 'kafka',
          name: 'Apache Kafka',
          icon: <MessageSquare size={20} />,
          description: 'Distributed event streaming platform',
          disabled: false
        },
        {
          id: 'rabbitmq',
          name: 'RabbitMQ',
          icon: <Server size={20} />,
          description: 'Message broker with advanced routing',
          disabled: false
        },
        {
          id: 'solace',
          name: 'Solace PubSub+',
          icon: <Cloud size={20} />,
          description: 'Enterprise messaging platform',
          disabled: false
        }
      ]
    },
    {
      title: 'SQL',
      items: [
        {
          id: 'sql-basics',
          name: 'SQL Fundamentals',
          icon: <Database size={20} />,
          description: 'Basic SQL queries and operations',
          disabled: false
        },
        {
          id: 'sql-joins',
          name: 'Joins & Relationships',
          icon: <GitBranch size={20} />,
          description: 'JOIN operations and table relationships',
          disabled: false
        },
        {
          id: 'sql-advanced',
          name: 'Advanced Queries',
          icon: <Settings size={20} />,
          description: 'Window functions, CTEs, and complex queries',
          disabled: false
        },
        {
          id: 'sql-optimization',
          name: 'Query Optimization',
          icon: <Gauge size={20} />,
          description: 'Indexing, execution plans, and performance',
          disabled: false
        },
        {
          id: 'sql-transactions',
          name: 'Transactions & ACID',
          icon: <Lock size={20} />,
          description: 'Transaction management and consistency',
          disabled: false
        },
        {
          id: 'sql-trading',
          name: 'Trading SQL Patterns',
          icon: <TrendingUp size={20} />,
          description: 'SQL patterns for financial data',
          disabled: false
        }
      ]
    },
    {
      title: 'NoSQL',
      items: []
    },
    {
      title: 'DevOps',
      items: []
    },
    {
      title: 'Cloud Services',
      items: []
    },
    {
      title: 'Security',
      items: [
        {
          id: 'auth-authorization',
          name: 'Authentication & Authorization',
          icon: <Shield size={20} />,
          description: 'Identity management and access control systems',
          disabled: false
        },
        {
          id: 'cryptography',
          name: 'Cryptography & Encryption',
          icon: <Lock size={20} />,
          description: 'Advanced encryption algorithms and key management',
          disabled: false
        },
        {
          id: 'network-security',
          name: 'Network Security',
          icon: <Zap size={20} />,
          description: 'Firewall, VPN, and network protection layers',
          disabled: false
        },
        {
          id: 'security-monitoring',
          name: 'Security Monitoring',
          icon: <Activity size={20} />,
          description: 'SIEM, logging, and real-time threat monitoring',
          disabled: false
        },
        {
          id: 'threat-detection',
          name: 'Threat Detection',
          icon: <AlertTriangle size={20} />,
          description: 'AI-powered threat detection and incident response',
          disabled: false
        }
      ]
    },
    {
      title: 'Performance',
      items: []
    }
  ];

  const buttonStyle = (isActive, isDisabled) => ({
    width: '100%',
    padding: '12px',
    marginBottom: '8px',
    backgroundColor: isActive ? 'rgba(16, 185, 129, 0.2)' : 'transparent',
    border: isActive ? '2px solid #10b981' : '2px solid transparent',
    borderRadius: '8px',
    cursor: isDisabled ? 'not-allowed' : 'pointer',
    opacity: isDisabled ? 0.4 : 1,
    transition: 'all 0.2s',
    textAlign: 'left',
    display: 'flex',
    flexDirection: 'column',
    gap: '4px'
  });

  return (
    <div style={{
      position: 'fixed',
      left: 0,
      top: 0,
      width: isCollapsed ? '60px' : '280px',
      height: '100vh',
      backgroundColor: 'rgba(17, 24, 39, 0.95)',
      backdropFilter: 'blur(12px)',
      borderRight: '1px solid #374151',
      padding: isCollapsed ? '12px' : '20px',
      paddingTop: '20px',
      overflowY: 'auto',
      zIndex: 15,
      transition: 'all 0.3s ease'
    }}>
      <button
        onClick={handleCollapse}
        style={{
          width: '100%',
          padding: '8px',
          marginBottom: '20px',
          backgroundColor: 'rgba(55, 65, 81, 0.5)',
          border: '1px solid #374151',
          borderRadius: '6px',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '8px',
          color: '#9ca3af',
          transition: 'all 0.2s'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = 'rgba(16, 185, 129, 0.2)';
          e.currentTarget.style.borderColor = '#10b981';
          e.currentTarget.style.color = '#10b981';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = 'rgba(55, 65, 81, 0.5)';
          e.currentTarget.style.borderColor = '#374151';
          e.currentTarget.style.color = '#9ca3af';
        }}
      >
        {isCollapsed ? (
          <ChevronRight size={20} />
        ) : (
          <>
            <ChevronLeft size={20} />
            <span style={{ fontSize: '14px', fontWeight: '500' }}>Collapse</span>
          </>
        )}
      </button>

      {!isCollapsed && navigationSections.map((section, sectionIndex) => (
        <div key={section.title} style={{ marginBottom: sectionIndex < navigationSections.length - 1 ? '30px' : '0px' }}>
          <div 
            onClick={() => toggleSection(section.title)}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              cursor: 'pointer',
              marginBottom: expandedSections[section.title] ? '20px' : '0px',
              padding: '8px',
              borderRadius: '6px',
              transition: 'background-color 0.2s',
              backgroundColor: 'transparent'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(55, 65, 81, 0.5)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            <div style={{ color: '#9ca3af' }}>
              {expandedSections[section.title] ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
            </div>
            <h2 style={{
              fontSize: '14px',
              fontWeight: '600',
              color: '#9ca3af',
              textTransform: 'uppercase',
              letterSpacing: '0.05em',
              margin: '0'
            }}>
              {section.title}
            </h2>
            {section.items.length === 0 && (
              <span style={{
                fontSize: '10px',
                color: '#6b7280',
                marginLeft: 'auto',
                fontStyle: 'italic'
              }}>
                empty
              </span>
            )}
          </div>

          {expandedSections[section.title] && section.items.map((item) => (
            <button
              key={item.id}
              onClick={() => !item.disabled && onViewChange(item.id)}
              style={buttonStyle(currentView === item.id, item.disabled)}
              onMouseEnter={(e) => {
                if (!item.disabled && currentView !== item.id) {
                  e.currentTarget.style.backgroundColor = 'rgba(16, 185, 129, 0.1)';
                  e.currentTarget.style.borderColor = '#10b981';
                }
              }}
              onMouseLeave={(e) => {
                if (!item.disabled && currentView !== item.id) {
                  e.currentTarget.style.backgroundColor = 'transparent';
                  e.currentTarget.style.borderColor = 'transparent';
                }
              }}
              disabled={item.disabled}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <div style={{ color: currentView === item.id ? '#10b981' : '#9ca3af' }}>
                  {item.icon}
                </div>
                <div style={{ flex: 1 }}>
                  <div style={{ 
                    fontSize: '14px', 
                    fontWeight: '500', 
                    color: currentView === item.id ? '#10b981' : 'white',
                    marginBottom: '2px'
                  }}>
                    {item.name}
                  </div>
                  <div style={{ 
                    fontSize: '12px', 
                    color: '#6b7280',
                    fontStyle: item.disabled ? 'italic' : 'normal'
                  }}>
                    {item.description}
                  </div>
                </div>
              </div>
            </button>
          ))}
        </div>
      ))}

      {!isCollapsed && (
        <div style={{
          marginTop: '40px',
          padding: '16px',
          backgroundColor: 'rgba(31, 41, 55, 0.5)',
          borderRadius: '8px',
          border: '1px solid #374151'
        }}>
          <h3 style={{
            fontSize: '12px',
            fontWeight: '600',
            color: '#10b981',
            marginBottom: '8px'
          }}>
            Quick Stats
          </h3>
          <div style={{ fontSize: '11px', color: '#9ca3af', lineHeight: '1.6' }}>
            <div>Components: {currentView === 'darkpool' ? '13' : '13'}</div>
            <div>Connections: {currentView === 'darkpool' ? '12' : '0'}</div>
            <div>Categories: 4</div>
          </div>
        </div>
      )}

      {isCollapsed && (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', marginTop: '10px' }}>
          {navigationSections.flatMap(section => 
            section.items.filter(item => !item.disabled).map(item => (
              <button
                key={item.id}
                onClick={() => onViewChange(item.id)}
                style={{
                  width: '36px',
                  height: '36px',
                  backgroundColor: currentView === item.id ? 'rgba(16, 185, 129, 0.2)' : 'transparent',
                  border: currentView === item.id ? '2px solid #10b981' : '2px solid transparent',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: currentView === item.id ? '#10b981' : '#9ca3af',
                  transition: 'all 0.2s',
                  position: 'relative'
                }}
                onMouseEnter={(e) => {
                  if (currentView !== item.id) {
                    e.currentTarget.style.backgroundColor = 'rgba(16, 185, 129, 0.1)';
                    e.currentTarget.style.borderColor = '#10b981';
                  }
                  const tooltip = e.currentTarget.querySelector('.tooltip');
                  if (tooltip) tooltip.style.opacity = '1';
                }}
                onMouseLeave={(e) => {
                  if (currentView !== item.id) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.borderColor = 'transparent';
                  }
                  const tooltip = e.currentTarget.querySelector('.tooltip');
                  if (tooltip) tooltip.style.opacity = '0';
                }}
                title={item.name}
              >
                {item.icon}
                <div 
                  className="tooltip"
                  style={{
                    position: 'absolute',
                    left: '45px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    backgroundColor: 'rgba(17, 24, 39, 0.95)',
                    color: 'white',
                    padding: '6px 12px',
                    borderRadius: '6px',
                    border: '1px solid #374151',
                    fontSize: '12px',
                    whiteSpace: 'nowrap',
                    opacity: '0',
                    pointerEvents: 'none',
                    transition: 'opacity 0.2s',
                    zIndex: 100
                  }}
                >
                  {item.name}
                </div>
              </button>
            ))
          )}
        </div>
      )}

    </div>
  );
};

export default SidebarNavigation;
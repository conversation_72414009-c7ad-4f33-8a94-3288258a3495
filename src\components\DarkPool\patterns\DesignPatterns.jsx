import React, { useState } from 'react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Layers, Package, Settings, Cpu, Database, MessageCircle, Shield, Zap, Clock, Activity } from 'lucide-react';

const DesignPatterns = () => {
  const [selectedPattern, setSelectedPattern] = useState(null);
  const [activeTab, setActiveTab] = useState('details');

  const patterns = [
    // Creational Patterns (Column 1)
    {
      id: 'singleton',
      name: 'Singleton Pattern',
      position: { left: 100, top: 150 },
      category: 'creational',
      tech: ['Thread Safety', 'Lazy Loading', 'Instance Control'],
      color: '#3b82f6'
    },
    {
      id: 'factory',
      name: 'Factory Pattern',
      position: { left: 100, top: 330 },
      category: 'creational',
      tech: ['Object Creation', 'Type Safety', 'Flexibility'],
      color: '#3b82f6'
    },
    {
      id: 'builder',
      name: 'Builder Pattern',
      position: { left: 100, top: 510 },
      category: 'creational',
      tech: ['Complex Objects', 'Method Chaining', 'Immutability'],
      color: '#3b82f6'
    },
    {
      id: 'prototype',
      name: 'Prototype Pattern',
      position: { left: 100, top: 690 },
      category: 'creational',
      tech: ['Object Cloning', 'Dynamic Creation', 'Performance'],
      color: '#3b82f6'
    },
    // Structural Patterns (Column 2)
    {
      id: 'adapter',
      name: 'Adapter Pattern',
      position: { left: 400, top: 150 },
      category: 'structural',
      tech: ['Interface Compatibility', 'Legacy Integration', 'Wrapper'],
      color: '#10b981'
    },
    {
      id: 'decorator',
      name: 'Decorator Pattern',
      position: { left: 400, top: 330 },
      category: 'structural',
      tech: ['Dynamic Behavior', 'Composition', 'Extensibility'],
      color: '#10b981'
    },
    {
      id: 'facade',
      name: 'Facade Pattern',
      position: { left: 400, top: 510 },
      category: 'structural',
      tech: ['Simplified Interface', 'Subsystem Hiding', 'Unified API'],
      color: '#10b981'
    },
    // Behavioral Patterns (Column 3)
    {
      id: 'observer',
      name: 'Observer Pattern',
      position: { left: 700, top: 150 },
      category: 'behavioral',
      tech: ['Event Handling', 'Loose Coupling', 'Notifications'],
      color: '#ef4444'
    },
    {
      id: 'strategy',
      name: 'Strategy Pattern',
      position: { left: 700, top: 330 },
      category: 'behavioral',
      tech: ['Algorithm Selection', 'Runtime Switching', 'Polymorphism'],
      color: '#ef4444'
    },
    {
      id: 'command',
      name: 'Command Pattern',
      position: { left: 700, top: 510 },
      category: 'behavioral',
      tech: ['Action Encapsulation', 'Undo/Redo', 'Queuing'],
      color: '#ef4444'
    },
    {
      id: 'state',
      name: 'State Pattern',
      position: { left: 700, top: 690 },
      category: 'behavioral',
      tech: ['State Management', 'Behavior Switching', 'Context Delegation'],
      color: '#ef4444'
    },
    // Architectural Patterns (Column 4)
    {
      id: 'mvc',
      name: 'MVC Pattern',
      position: { left: 1000, top: 150 },
      category: 'architectural',
      tech: ['Separation of Concerns', 'Maintainability', 'Testability'],
      color: '#f59e0b'
    }
  ];

  const getPatternUML = (patternId) => {
    const umlDiagrams = {
      'singleton': {
        viewBox: "0 0 800 400",
        diagram: (
          <>
            {/* Singleton Class */}
            <rect x="250" y="50" width="300" height="180" fill="#1e293b" stroke="#3b82f6" strokeWidth="2" rx="4"/>
            <text x="400" y="80" textAnchor="middle" fill="#3b82f6" fontSize="14" fontWeight="bold">«singleton»</text>
            <text x="400" y="100" textAnchor="middle" fill="#fff" fontSize="16" fontWeight="bold">DatabaseConnectionManager</text>
            <line x1="250" y1="110" x2="550" y2="110" stroke="#374151" strokeWidth="1"/>
            
            {/* Attributes */}
            <text x="260" y="130" fill="#94a3b8" fontSize="12">- static instance: DatabaseConnectionManager</text>
            <text x="260" y="150" fill="#94a3b8" fontSize="12">- connection: Connection</text>
            <text x="260" y="170" fill="#94a3b8" fontSize="12">- dataSource: HikariDataSource</text>
            <line x1="250" y1="180" x2="550" y2="180" stroke="#374151" strokeWidth="1"/>
            
            {/* Methods */}
            <text x="260" y="200" fill="#60a5fa" fontSize="12">+ getInstance(): DatabaseConnectionManager</text>
            <text x="260" y="220" fill="#60a5fa" fontSize="12">+ getConnection(): Connection</text>
            
            {/* Client */}
            <rect x="50" y="120" width="150" height="60" fill="#1e293b" stroke="#9ca3af" strokeWidth="1" rx="4"/>
            <text x="125" y="145" textAnchor="middle" fill="#fff" fontSize="14">Client</text>
            <text x="125" y="165" textAnchor="middle" fill="#94a3b8" fontSize="12">Application Code</text>
            
            {/* Arrow from Client to Singleton */}
            <defs>
              <marker id="arrow-singleton" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="#9ca3af"/>
              </marker>
            </defs>
            <line x1="200" y1="150" x2="250" y2="150" stroke="#9ca3af" strokeWidth="1" markerEnd="url(#arrow-singleton)"/>
            
            {/* Note */}
            <rect x="300" y="280" width="200" height="60" fill="rgba(251, 191, 36, 0.1)" stroke="#fbbf24" strokeWidth="1" strokeDasharray="5,5" rx="4"/>
            <text x="400" y="305" textAnchor="middle" fill="#fbbf24" fontSize="12">Thread-Safe Implementation</text>
            <text x="400" y="325" textAnchor="middle" fill="#d1d5db" fontSize="11">Double-checked locking</text>
          </>
        )
      },
      'factory': {
        viewBox: "0 0 900 500",
        diagram: (
          <>
            {/* Abstract Factory */}
            <rect x="350" y="20" width="200" height="100" fill="#1e293b" stroke="#3b82f6" strokeWidth="2" rx="4"/>
            <text x="450" y="45" textAnchor="middle" fill="#3b82f6" fontSize="14" fontStyle="italic">«abstract»</text>
            <text x="450" y="65" textAnchor="middle" fill="#fff" fontSize="14" fontWeight="bold">InstrumentFactory</text>
            <line x1="350" y1="75" x2="550" y2="75" stroke="#374151" strokeWidth="1"/>
            <text x="360" y="95" fill="#60a5fa" fontSize="11">+ createInstrument(): Instrument</text>
            <text x="360" y="110" fill="#60a5fa" fontSize="11">+ validateInstrument(): boolean</text>
            
            {/* Concrete Factories */}
            <rect x="150" y="180" width="180" height="80" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="240" y="205" textAnchor="middle" fill="#fff" fontSize="13">EquityFactory</text>
            <line x1="150" y1="215" x2="330" y2="215" stroke="#374151" strokeWidth="1"/>
            <text x="160" y="235" fill="#60a5fa" fontSize="11">+ createInstrument()</text>
            <text x="160" y="250" fill="#60a5fa" fontSize="11">+ createStock()</text>
            
            <rect x="370" y="180" width="180" height="80" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="460" y="205" textAnchor="middle" fill="#fff" fontSize="13">DerivativeFactory</text>
            <line x1="370" y1="215" x2="550" y2="215" stroke="#374151" strokeWidth="1"/>
            <text x="380" y="235" fill="#60a5fa" fontSize="11">+ createInstrument()</text>
            <text x="380" y="250" fill="#60a5fa" fontSize="11">+ createOption()</text>
            
            {/* Products */}
            <rect x="50" y="320" width="120" height="60" fill="#1e293b" stroke="#ef4444" strokeWidth="1" rx="4"/>
            <text x="110" y="345" textAnchor="middle" fill="#fff" fontSize="12">Stock</text>
            <text x="110" y="365" textAnchor="middle" fill="#94a3b8" fontSize="10">+ trade()</text>
            
            <rect x="200" y="320" width="120" height="60" fill="#1e293b" stroke="#ef4444" strokeWidth="1" rx="4"/>
            <text x="260" y="345" textAnchor="middle" fill="#fff" fontSize="12">ETF</text>
            <text x="260" y="365" textAnchor="middle" fill="#94a3b8" fontSize="10">+ trade()</text>
            
            <rect x="380" y="320" width="120" height="60" fill="#1e293b" stroke="#ef4444" strokeWidth="1" rx="4"/>
            <text x="440" y="345" textAnchor="middle" fill="#fff" fontSize="12">Option</text>
            <text x="440" y="365" textAnchor="middle" fill="#94a3b8" fontSize="10">+ exercise()</text>
            
            <rect x="530" y="320" width="120" height="60" fill="#1e293b" stroke="#ef4444" strokeWidth="1" rx="4"/>
            <text x="590" y="345" textAnchor="middle" fill="#fff" fontSize="12">Future</text>
            <text x="590" y="365" textAnchor="middle" fill="#94a3b8" fontSize="10">+ settle()</text>
            
            {/* Inheritance arrows */}
            <defs>
              <marker id="arrow-factory" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="none" stroke="#9ca3af"/>
              </marker>
            </defs>
            <line x1="240" y1="180" x2="400" y2="120" stroke="#9ca3af" strokeWidth="1" markerEnd="url(#arrow-factory)"/>
            <line x1="460" y1="180" x2="450" y2="120" stroke="#9ca3af" strokeWidth="1" markerEnd="url(#arrow-factory)"/>
            
            {/* Creation arrows */}
            <line x1="110" y1="260" x2="110" y2="320" stroke="#9ca3af" strokeWidth="1" strokeDasharray="3,3" markerEnd="url(#arrow-factory)"/>
            <line x1="260" y1="260" x2="260" y2="320" stroke="#9ca3af" strokeWidth="1" strokeDasharray="3,3" markerEnd="url(#arrow-factory)"/>
            <line x1="440" y1="260" x2="440" y2="320" stroke="#9ca3af" strokeWidth="1" strokeDasharray="3,3" markerEnd="url(#arrow-factory)"/>
            <line x1="590" y1="260" x2="590" y2="320" stroke="#9ca3af" strokeWidth="1" strokeDasharray="3,3" markerEnd="url(#arrow-factory)"/>
          </>
        )
      },
      'builder': {
        viewBox: "0 0 800 500",
        diagram: (
          <>
            {/* Director */}
            <rect x="50" y="50" width="150" height="80" fill="#1e293b" stroke="#9ca3af" strokeWidth="1" rx="4"/>
            <text x="125" y="75" textAnchor="middle" fill="#fff" fontSize="14">OrderDirector</text>
            <line x1="50" y1="85" x2="200" y2="85" stroke="#374151" strokeWidth="1"/>
            <text x="60" y="105" fill="#60a5fa" fontSize="11">+ construct()</text>
            <text x="60" y="120" fill="#60a5fa" fontSize="11">+ builder: Builder</text>
            
            {/* Builder Interface */}
            <rect x="300" y="30" width="200" height="120" fill="#1e293b" stroke="#3b82f6" strokeWidth="2" rx="4"/>
            <text x="400" y="55" textAnchor="middle" fill="#3b82f6" fontSize="14">«interface»</text>
            <text x="400" y="75" textAnchor="middle" fill="#fff" fontSize="14" fontWeight="bold">OrderBuilder</text>
            <line x1="300" y1="85" x2="500" y2="85" stroke="#374151" strokeWidth="1"/>
            <text x="310" y="105" fill="#60a5fa" fontSize="11">+ setSymbol(symbol)</text>
            <text x="310" y="120" fill="#60a5fa" fontSize="11">+ setQuantity(qty)</text>
            <text x="310" y="135" fill="#60a5fa" fontSize="11">+ build(): Order</text>
            
            {/* Concrete Builder */}
            <rect x="300" y="200" width="200" height="140" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="400" y="225" textAnchor="middle" fill="#fff" fontSize="14">TradingOrderBuilder</text>
            <line x1="300" y1="235" x2="500" y2="235" stroke="#374151" strokeWidth="1"/>
            <text x="310" y="255" fill="#94a3b8" fontSize="11">- order: TradingOrder</text>
            <line x1="300" y1="265" x2="500" y2="265" stroke="#374151" strokeWidth="1"/>
            <text x="310" y="285" fill="#60a5fa" fontSize="11">+ setSymbol(symbol)</text>
            <text x="310" y="300" fill="#60a5fa" fontSize="11">+ setQuantity(qty)</text>
            <text x="310" y="315" fill="#60a5fa" fontSize="11">+ setPrice(price)</text>
            <text x="310" y="330" fill="#60a5fa" fontSize="11">+ build(): TradingOrder</text>
            
            {/* Product */}
            <rect x="550" y="220" width="180" height="100" fill="#1e293b" stroke="#ef4444" strokeWidth="1" rx="4"/>
            <text x="640" y="245" textAnchor="middle" fill="#fff" fontSize="14">TradingOrder</text>
            <line x1="550" y1="255" x2="730" y2="255" stroke="#374151" strokeWidth="1"/>
            <text x="560" y="275" fill="#94a3b8" fontSize="11">- symbol: String</text>
            <text x="560" y="290" fill="#94a3b8" fontSize="11">- quantity: BigDecimal</text>
            <text x="560" y="305" fill="#94a3b8" fontSize="11">- price: BigDecimal</text>
            
            {/* Relationships */}
            <line x1="200" y1="90" x2="300" y2="90" stroke="#9ca3af" strokeWidth="1" markerEnd="url(#arrow-factory)"/>
            <line x1="400" y1="150" x2="400" y2="200" stroke="#9ca3af" strokeWidth="1" strokeDasharray="5,5" markerEnd="url(#arrow-factory)"/>
            <line x1="500" y1="270" x2="550" y2="270" stroke="#9ca3af" strokeWidth="1" strokeDasharray="3,3" markerEnd="url(#arrow-factory)"/>
            
            {/* Labels */}
            <text x="230" y="85" fill="#9ca3af" fontSize="10">uses</text>
            <text x="410" y="175" fill="#9ca3af" fontSize="10">implements</text>
            <text x="510" y="265" fill="#9ca3af" fontSize="10">creates</text>
          </>
        )
      },
      'observer': {
        viewBox: "0 0 900 600",
        diagram: (
          <>
            {/* Subject Interface */}
            <rect x="350" y="20" width="200" height="100" fill="#1e293b" stroke="#ef4444" strokeWidth="2" rx="4"/>
            <text x="450" y="45" textAnchor="middle" fill="#ef4444" fontSize="14">«interface»</text>
            <text x="450" y="65" textAnchor="middle" fill="#fff" fontSize="14" fontWeight="bold">MarketDataSubject</text>
            <line x1="350" y1="75" x2="550" y2="75" stroke="#374151" strokeWidth="1"/>
            <text x="360" y="95" fill="#60a5fa" fontSize="11">+ addObserver(observer)</text>
            <text x="360" y="110" fill="#60a5fa" fontSize="11">+ removeObserver(observer)</text>
            
            {/* Concrete Subject */}
            <rect x="320" y="170" width="260" height="120" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="450" y="195" textAnchor="middle" fill="#fff" fontSize="14">MarketDataFeed</text>
            <line x1="320" y1="205" x2="580" y2="205" stroke="#374151" strokeWidth="1"/>
            <text x="330" y="225" fill="#94a3b8" fontSize="11">- observers: List&lt;Observer&gt;</text>
            <text x="330" y="240" fill="#94a3b8" fontSize="11">- currentPrice: BigDecimal</text>
            <line x1="320" y1="250" x2="580" y2="250" stroke="#374151" strokeWidth="1"/>
            <text x="330" y="270" fill="#60a5fa" fontSize="11">+ notifyObservers()</text>
            <text x="330" y="285" fill="#60a5fa" fontSize="11">+ updatePrice(price)</text>
            
            {/* Observer Interface */}
            <rect x="50" y="80" width="200" height="80" fill="#1e293b" stroke="#ef4444" strokeWidth="2" rx="4"/>
            <text x="150" y="105" textAnchor="middle" fill="#ef4444" fontSize="14">«interface»</text>
            <text x="150" y="125" textAnchor="middle" fill="#fff" fontSize="14" fontWeight="bold">MarketDataObserver</text>
            <line x1="50" y1="135" x2="250" y2="135" stroke="#374151" strokeWidth="1"/>
            <text x="60" y="155" fill="#60a5fa" fontSize="11">+ onPriceUpdate(data)</text>
            
            {/* Concrete Observers */}
            <rect x="20" y="220" width="180" height="80" fill="#1e293b" stroke="#8b5cf6" strokeWidth="1" rx="4"/>
            <text x="110" y="245" textAnchor="middle" fill="#fff" fontSize="13">TradingEngine</text>
            <line x1="20" y1="255" x2="200" y2="255" stroke="#374151" strokeWidth="1"/>
            <text x="30" y="275" fill="#60a5fa" fontSize="11">+ onPriceUpdate(data)</text>
            <text x="30" y="290" fill="#60a5fa" fontSize="11">+ executeTrades()</text>
            
            <rect x="20" y="320" width="180" height="80" fill="#1e293b" stroke="#8b5cf6" strokeWidth="1" rx="4"/>
            <text x="110" y="345" textAnchor="middle" fill="#fff" fontSize="13">RiskManager</text>
            <line x1="20" y1="355" x2="200" y2="355" stroke="#374151" strokeWidth="1"/>
            <text x="30" y="375" fill="#60a5fa" fontSize="11">+ onPriceUpdate(data)</text>
            <text x="30" y="390" fill="#60a5fa" fontSize="11">+ assessRisk()</text>
            
            <rect x="20" y="420" width="180" height="80" fill="#1e293b" stroke="#8b5cf6" strokeWidth="1" rx="4"/>
            <text x="110" y="445" textAnchor="middle" fill="#fff" fontSize="13">Portfolio</text>
            <line x1="20" y1="455" x2="200" y2="455" stroke="#374151" strokeWidth="1"/>
            <text x="30" y="475" fill="#60a5fa" fontSize="11">+ onPriceUpdate(data)</text>
            <text x="30" y="490" fill="#60a5fa" fontSize="11">+ updatePositions()</text>
            
            {/* Client */}
            <rect x="650" y="200" width="150" height="60" fill="#1e293b" stroke="#9ca3af" strokeWidth="1" rx="4"/>
            <text x="725" y="225" textAnchor="middle" fill="#fff" fontSize="14">TradingApp</text>
            <text x="725" y="245" textAnchor="middle" fill="#94a3b8" fontSize="12">Main Application</text>
            
            {/* Relationships */}
            <defs>
              <marker id="arrow-observer" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="none" stroke="#9ca3af"/>
              </marker>
            </defs>
            
            {/* Implements relationships */}
            <line x1="450" y1="170" x2="450" y2="120" stroke="#9ca3af" strokeWidth="1" strokeDasharray="5,5" markerEnd="url(#arrow-observer)"/>
            <line x1="110" y1="220" x2="150" y2="160" stroke="#9ca3af" strokeWidth="1" strokeDasharray="5,5" markerEnd="url(#arrow-observer)"/>
            <line x1="110" y1="320" x2="150" y2="160" stroke="#9ca3af" strokeWidth="1" strokeDasharray="5,5" markerEnd="url(#arrow-observer)"/>
            <line x1="110" y1="420" x2="150" y2="160" stroke="#9ca3af" strokeWidth="1" strokeDasharray="5,5" markerEnd="url(#arrow-observer)"/>
            
            {/* Observer relationship */}
            <line x1="320" y1="230" x2="250" y2="120" stroke="#9ca3af" strokeWidth="1" markerEnd="url(#arrow-observer)"/>
            
            {/* Client uses subject */}
            <line x1="650" y1="230" x2="580" y2="230" stroke="#9ca3af" strokeWidth="1" markerEnd="url(#arrow-observer)"/>
            
            {/* Labels */}
            <text x="270" y="175" fill="#9ca3af" fontSize="10">notifies</text>
            <text x="590" y="225" fill="#9ca3af" fontSize="10">uses</text>
          </>
        )
      },
      'strategy': {
        viewBox: "0 0 900 500",
        diagram: (
          <>
            {/* Context */}
            <rect x="50" y="150" width="200" height="120" fill="#1e293b" stroke="#9ca3af" strokeWidth="1" rx="4"/>
            <text x="150" y="180" textAnchor="middle" fill="#fff" fontSize="14">OrderExecutionEngine</text>
            <line x1="50" y1="190" x2="250" y2="190" stroke="#374151" strokeWidth="1"/>
            <text x="60" y="210" fill="#94a3b8" fontSize="11">- strategy: ExecutionStrategy</text>
            <line x1="50" y1="220" x2="250" y2="220" stroke="#374151" strokeWidth="1"/>
            <text x="60" y="240" fill="#60a5fa" fontSize="11">+ executeOrder(order)</text>
            <text x="60" y="255" fill="#60a5fa" fontSize="11">+ setStrategy(strategy)</text>
            
            {/* Strategy Interface */}
            <rect x="350" y="20" width="200" height="100" fill="#1e293b" stroke="#ef4444" strokeWidth="2" rx="4"/>
            <text x="450" y="45" textAnchor="middle" fill="#ef4444" fontSize="14">«interface»</text>
            <text x="450" y="65" textAnchor="middle" fill="#fff" fontSize="14" fontWeight="bold">ExecutionStrategy</text>
            <line x1="350" y1="75" x2="550" y2="75" stroke="#374151" strokeWidth="1"/>
            <text x="360" y="95" fill="#60a5fa" fontSize="11">+ execute(order): Result</text>
            <text x="360" y="110" fill="#60a5fa" fontSize="11">+ getStrategyName(): String</text>
            
            {/* Concrete Strategies */}
            <rect x="200" y="200" width="180" height="80" fill="#1e293b" stroke="#8b5cf6" strokeWidth="1" rx="4"/>
            <text x="290" y="225" textAnchor="middle" fill="#fff" fontSize="13">MarketStrategy</text>
            <line x1="200" y1="235" x2="380" y2="235" stroke="#374151" strokeWidth="1"/>
            <text x="210" y="255" fill="#60a5fa" fontSize="11">+ execute(order)</text>
            <text x="210" y="270" fill="#60a5fa" fontSize="11">+ executeFastOrder()</text>
            
            <rect x="400" y="200" width="180" height="80" fill="#1e293b" stroke="#8b5cf6" strokeWidth="1" rx="4"/>
            <text x="490" y="225" textAnchor="middle" fill="#fff" fontSize="13">TWAPStrategy</text>
            <line x1="400" y1="235" x2="580" y2="235" stroke="#374151" strokeWidth="1"/>
            <text x="410" y="255" fill="#60a5fa" fontSize="11">+ execute(order)</text>
            <text x="410" y="270" fill="#60a5fa" fontSize="11">+ scheduleSlices()</text>
            
            <rect x="600" y="200" width="180" height="80" fill="#1e293b" stroke="#8b5cf6" strokeWidth="1" rx="4"/>
            <text x="690" y="225" textAnchor="middle" fill="#fff" fontSize="13">VWAPStrategy</text>
            <line x1="600" y1="235" x2="780" y2="235" stroke="#374151" strokeWidth="1"/>
            <text x="610" y="255" fill="#60a5fa" fontSize="11">+ execute(order)</text>
            <text x="610" y="270" fill="#60a5fa" fontSize="11">+ analyzeVolume()</text>
            
            <rect x="300" y="320" width="180" height="80" fill="#1e293b" stroke="#8b5cf6" strokeWidth="1" rx="4"/>
            <text x="390" y="345" textAnchor="middle" fill="#fff" fontSize="13">IcebergStrategy</text>
            <line x1="300" y1="355" x2="480" y2="355" stroke="#374151" strokeWidth="1"/>
            <text x="310" y="375" fill="#60a5fa" fontSize="11">+ execute(order)</text>
            <text x="310" y="390" fill="#60a5fa" fontSize="11">+ hideQuantity()</text>
            
            {/* Client */}
            <rect x="50" y="50" width="150" height="60" fill="#1e293b" stroke="#9ca3af" strokeWidth="1" rx="4"/>
            <text x="125" y="75" textAnchor="middle" fill="#fff" fontSize="14">TradingSystem</text>
            <text x="125" y="95" textAnchor="middle" fill="#94a3b8" fontSize="12">Client Code</text>
            
            {/* Relationships */}
            <defs>
              <marker id="arrow-strategy" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="none" stroke="#9ca3af"/>
              </marker>
            </defs>
            
            {/* Client uses Context */}
            <line x1="150" y1="110" x2="150" y2="150" stroke="#9ca3af" strokeWidth="1" markerEnd="url(#arrow-strategy)"/>
            
            {/* Context uses Strategy */}
            <line x1="250" y1="180" x2="350" y2="90" stroke="#9ca3af" strokeWidth="1" markerEnd="url(#arrow-strategy)"/>
            
            {/* Implementations */}
            <line x1="290" y1="200" x2="400" y2="120" stroke="#9ca3af" strokeWidth="1" strokeDasharray="5,5" markerEnd="url(#arrow-strategy)"/>
            <line x1="490" y1="200" x2="450" y2="120" stroke="#9ca3af" strokeWidth="1" strokeDasharray="5,5" markerEnd="url(#arrow-strategy)"/>
            <line x1="690" y1="200" x2="500" y2="120" stroke="#9ca3af" strokeWidth="1" strokeDasharray="5,5" markerEnd="url(#arrow-strategy)"/>
            <line x1="390" y1="320" x2="450" y2="120" stroke="#9ca3af" strokeWidth="1" strokeDasharray="5,5" markerEnd="url(#arrow-strategy)"/>
            
            {/* Labels */}
            <text x="270" y="140" fill="#9ca3af" fontSize="10">uses</text>
            <text x="120" y="135" fill="#9ca3af" fontSize="10">uses</text>
          </>
        )
      },
      'adapter': {
        viewBox: "0 0 900 400",
        diagram: (
          <>
            {/* Target Interface */}
            <rect x="50" y="50" width="200" height="100" fill="#1e293b" stroke="#10b981" strokeWidth="2" rx="4"/>
            <text x="150" y="75" textAnchor="middle" fill="#10b981" fontSize="14">«interface»</text>
            <text x="150" y="95" textAnchor="middle" fill="#fff" fontSize="14" fontWeight="bold">MarketDataFeed</text>
            <line x1="50" y1="105" x2="250" y2="105" stroke="#374151" strokeWidth="1"/>
            <text x="60" y="125" fill="#60a5fa" fontSize="11">+ getLatestPrice(symbol)</text>
            <text x="60" y="140" fill="#60a5fa" fontSize="11">+ subscribe(symbol)</text>
            
            {/* Adapter */}
            <rect x="350" y="50" width="240" height="120" fill="#1e293b" stroke="#f59e0b" strokeWidth="1" rx="4"/>
            <text x="470" y="80" textAnchor="middle" fill="#fff" fontSize="14">BloombergDataAdapter</text>
            <line x1="350" y1="90" x2="590" y2="90" stroke="#374151" strokeWidth="1"/>
            <text x="360" y="110" fill="#94a3b8" fontSize="11">- bloombergAPI: BloombergAPI</text>
            <text x="360" y="125" fill="#94a3b8" fontSize="11">- symbolMapper: SymbolMapper</text>
            <line x1="350" y1="135" x2="590" y2="135" stroke="#374151" strokeWidth="1"/>
            <text x="360" y="155" fill="#60a5fa" fontSize="11">+ getLatestPrice(symbol)</text>
            <text x="360" y="170" fill="#60a5fa" fontSize="11">+ subscribe(symbol)</text>
            
            {/* Adaptee */}
            <rect x="650" y="80" width="200" height="140" fill="#1e293b" stroke="#ec4899" strokeWidth="1" rx="4"/>
            <text x="750" y="110" textAnchor="middle" fill="#fff" fontSize="14">BloombergAPI</text>
            <text x="750" y="130" textAnchor="middle" fill="#94a3b8" fontSize="12">(Third-party)</text>
            <line x1="650" y1="140" x2="850" y2="140" stroke="#374151" strokeWidth="1"/>
            <text x="660" y="160" fill="#60a5fa" fontSize="11">+ getCurrentQuote(ticker)</text>
            <text x="660" y="175" fill="#60a5fa" fontSize="11">+ getHistory(ticker)</text>
            <text x="660" y="190" fill="#60a5fa" fontSize="11">+ registerCallback(ticker)</text>
            <text x="660" y="205" fill="#60a5fa" fontSize="11">+ parseBloombergFormat()</text>
            
            {/* Client */}
            <rect x="50" y="220" width="180" height="80" fill="#1e293b" stroke="#9ca3af" strokeWidth="1" rx="4"/>
            <text x="140" y="250" textAnchor="middle" fill="#fff" fontSize="14">TradingSystem</text>
            <line x1="50" y1="260" x2="230" y2="260" stroke="#374151" strokeWidth="1"/>
            <text x="60" y="280" fill="#60a5fa" fontSize="11">+ analyzePrices()</text>
            <text x="60" y="295" fill="#60a5fa" fontSize="11">+ executeTrades()</text>
            
            {/* Helper Classes */}
            <rect x="350" y="220" width="120" height="60" fill="#1e293b" stroke="#06b6d4" strokeWidth="1" rx="4"/>
            <text x="410" y="245" textAnchor="middle" fill="#fff" fontSize="12">SymbolMapper</text>
            <line x1="350" y1="255" x2="470" y2="255" stroke="#374151" strokeWidth="1"/>
            <text x="360" y="275" fill="#60a5fa" fontSize="10">+ toBloomberg()</text>
            
            <rect x="490" y="220" width="120" height="60" fill="#1e293b" stroke="#06b6d4" strokeWidth="1" rx="4"/>
            <text x="550" y="245" textAnchor="middle" fill="#fff" fontSize="12">DataConverter</text>
            <line x1="490" y1="255" x2="610" y2="255" stroke="#374151" strokeWidth="1"/>
            <text x="500" y="275" fill="#60a5fa" fontSize="10">+ convertQuote()</text>
            
            {/* Relationships */}
            <defs>
              <marker id="arrow-adapter" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="none" stroke="#9ca3af"/>
              </marker>
            </defs>
            
            {/* Adapter implements Target */}
            <line x1="350" y1="100" x2="250" y2="100" stroke="#9ca3af" strokeWidth="1" strokeDasharray="5,5" markerEnd="url(#arrow-adapter)"/>
            
            {/* Adapter uses Adaptee */}
            <line x1="590" y1="120" x2="650" y2="120" stroke="#9ca3af" strokeWidth="1" markerEnd="url(#arrow-adapter)"/>
            
            {/* Client uses Target */}
            <line x1="140" y1="220" x2="150" y2="150" stroke="#9ca3af" strokeWidth="1" markerEnd="url(#arrow-adapter)"/>
            
            {/* Adapter uses helpers */}
            <line x1="410" y1="170" x2="410" y2="220" stroke="#9ca3af" strokeWidth="1" strokeDasharray="3,3" markerEnd="url(#arrow-adapter)"/>
            <line x1="550" y1="170" x2="550" y2="220" stroke="#9ca3af" strokeWidth="1" strokeDasharray="3,3" markerEnd="url(#arrow-adapter)"/>
            
            {/* Labels */}
            <text x="280" y="95" fill="#9ca3af" fontSize="10">implements</text>
            <text x="610" y="115" fill="#9ca3af" fontSize="10">adapts</text>
            <text x="110" y="185" fill="#9ca3af" fontSize="10">uses</text>
            
            {/* Note */}
            <rect x="350" y="320" width="250" height="40" fill="rgba(251, 191, 36, 0.1)" stroke="#fbbf24" strokeWidth="1" strokeDasharray="5,5" rx="4"/>
            <text x="475" y="340" textAnchor="middle" fill="#fbbf24" fontSize="12">Converts Bloomberg API calls</text>
            <text x="475" y="355" textAnchor="middle" fill="#d1d5db" fontSize="11">to standard MarketDataFeed interface</text>
          </>
        )
      },
      'decorator': {
        viewBox: "0 0 900 600",
        diagram: (
          <>
            {/* Component Interface */}
            <rect x="350" y="20" width="200" height="80" fill="#1e293b" stroke="#10b981" strokeWidth="2" rx="4"/>
            <text x="450" y="45" textAnchor="middle" fill="#10b981" fontSize="14">«interface»</text>
            <text x="450" y="65" textAnchor="middle" fill="#fff" fontSize="14" fontWeight="bold">TradeProcessor</text>
            <line x1="350" y1="75" x2="550" y2="75" stroke="#374151" strokeWidth="1"/>
            <text x="360" y="95" fill="#60a5fa" fontSize="11">+ process(trade): Result</text>
            
            {/* Concrete Component */}
            <rect x="150" y="150" width="200" height="100" fill="#1e293b" stroke="#06b6d4" strokeWidth="1" rx="4"/>
            <text x="250" y="180" textAnchor="middle" fill="#fff" fontSize="14">BasicTradeProcessor</text>
            <line x1="150" y1="190" x2="350" y2="190" stroke="#374151" strokeWidth="1"/>
            <text x="160" y="210" fill="#94a3b8" fontSize="11">- tradeRepository</text>
            <text x="160" y="225" fill="#94a3b8" fontSize="11">- positionService</text>
            <line x1="150" y1="235" x2="350" y2="235" stroke="#374151" strokeWidth="1"/>
            <text x="160" y="250" fill="#60a5fa" fontSize="11">+ process(trade)</text>
            
            {/* Base Decorator */}
            <rect x="500" y="150" width="220" height="100" fill="#1e293b" stroke="#f59e0b" strokeWidth="1" rx="4"/>
            <text x="610" y="180" textAnchor="middle" fill="#fff" fontSize="14">TradeProcessorDecorator</text>
            <line x1="500" y1="190" x2="720" y2="190" stroke="#374151" strokeWidth="1"/>
            <text x="510" y="210" fill="#94a3b8" fontSize="11"># component: TradeProcessor</text>
            <line x1="500" y1="220" x2="720" y2="220" stroke="#374151" strokeWidth="1"/>
            <text x="510" y="240" fill="#60a5fa" fontSize="11">+ process(trade)</text>
            
            {/* Concrete Decorators */}
            <rect x="50" y="320" width="180" height="100" fill="#1e293b" stroke="#8b5cf6" strokeWidth="1" rx="4"/>
            <text x="140" y="350" textAnchor="middle" fill="#fff" fontSize="13">RiskValidationDecorator</text>
            <line x1="50" y1="360" x2="230" y2="360" stroke="#374151" strokeWidth="1"/>
            <text x="60" y="380" fill="#94a3b8" fontSize="10">- riskManager</text>
            <text x="60" y="395" fill="#60a5fa" fontSize="10">+ process(trade)</text>
            <text x="60" y="410" fill="#60a5fa" fontSize="10">+ validateRisk()</text>
            
            <rect x="250" y="320" width="180" height="100" fill="#1e293b" stroke="#8b5cf6" strokeWidth="1" rx="4"/>
            <text x="340" y="350" textAnchor="middle" fill="#fff" fontSize="13">ComplianceDecorator</text>
            <line x1="250" y1="360" x2="430" y2="360" stroke="#374151" strokeWidth="1"/>
            <text x="260" y="380" fill="#94a3b8" fontSize="10">- complianceEngine</text>
            <text x="260" y="395" fill="#60a5fa" fontSize="10">+ process(trade)</text>
            <text x="260" y="410" fill="#60a5fa" fontSize="10">+ checkCompliance()</text>
            
            <rect x="450" y="320" width="180" height="100" fill="#1e293b" stroke="#8b5cf6" strokeWidth="1" rx="4"/>
            <text x="540" y="350" textAnchor="middle" fill="#fff" fontSize="13">MonitoringDecorator</text>
            <line x1="450" y1="360" x2="630" y2="360" stroke="#374151" strokeWidth="1"/>
            <text x="460" y="380" fill="#94a3b8" fontSize="10">- metricsRegistry</text>
            <text x="460" y="395" fill="#60a5fa" fontSize="10">+ process(trade)</text>
            <text x="460" y="410" fill="#60a5fa" fontSize="10">+ recordMetrics()</text>
            
            <rect x="650" y="320" width="180" height="100" fill="#1e293b" stroke="#8b5cf6" strokeWidth="1" rx="4"/>
            <text x="740" y="350" textAnchor="middle" fill="#fff" fontSize="13">CachingDecorator</text>
            <line x1="650" y1="360" x2="830" y2="360" stroke="#374151" strokeWidth="1"/>
            <text x="660" y="380" fill="#94a3b8" fontSize="10">- cache</text>
            <text x="660" y="395" fill="#60a5fa" fontSize="10">+ process(trade)</text>
            <text x="660" y="410" fill="#60a5fa" fontSize="10">+ cacheResult()</text>
            
            {/* Client */}
            <rect x="350" y="480" width="200" height="60" fill="#1e293b" stroke="#9ca3af" strokeWidth="1" rx="4"/>
            <text x="450" y="505" textAnchor="middle" fill="#fff" fontSize="14">TradingApplication</text>
            <text x="450" y="525" textAnchor="middle" fill="#94a3b8" fontSize="12">Client Code</text>
            
            {/* Relationships */}
            <defs>
              <marker id="arrow-decorator" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="none" stroke="#9ca3af"/>
              </marker>
            </defs>
            
            {/* Component implementations */}
            <line x1="250" y1="150" x2="400" y2="100" stroke="#9ca3af" strokeWidth="1" strokeDasharray="5,5" markerEnd="url(#arrow-decorator)"/>
            <line x1="610" y1="150" x2="500" y2="100" stroke="#9ca3af" strokeWidth="1" strokeDasharray="5,5" markerEnd="url(#arrow-decorator)"/>
            
            {/* Decorator inheritance */}
            <line x1="140" y1="320" x2="560" y2="250" stroke="#9ca3af" strokeWidth="1" markerEnd="url(#arrow-decorator)"/>
            <line x1="340" y1="320" x2="580" y2="250" stroke="#9ca3af" strokeWidth="1" markerEnd="url(#arrow-decorator)"/>
            <line x1="540" y1="320" x2="600" y2="250" stroke="#9ca3af" strokeWidth="1" markerEnd="url(#arrow-decorator)"/>
            <line x1="740" y1="320" x2="640" y2="250" stroke="#9ca3af" strokeWidth="1" markerEnd="url(#arrow-decorator)"/>
            
            {/* Composition relationship */}
            <line x1="500" y1="200" x2="450" y2="100" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-decorator)"/>
            <circle cx="480" cy="150" r="3" fill="#9ca3af"/>
            
            {/* Client uses component */}
            <line x1="450" y1="480" x2="450" y2="100" stroke="#9ca3af" strokeWidth="1" strokeDasharray="3,3" markerEnd="url(#arrow-decorator)"/>
            
            {/* Labels */}
            <text x="320" y="125" fill="#9ca3af" fontSize="10">implements</text>
            <text x="530" y="125" fill="#9ca3af" fontSize="10">implements</text>
            <text x="430" y="125" fill="#9ca3af" fontSize="10">wraps</text>
            
            {/* Composition note */}
            <rect x="650" y="480" width="220" height="60" fill="rgba(251, 191, 36, 0.1)" stroke="#fbbf24" strokeWidth="1" strokeDasharray="5,5" rx="4"/>
            <text x="760" y="505" textAnchor="middle" fill="#fbbf24" fontSize="12">Decorators can be chained</text>
            <text x="760" y="525" textAnchor="middle" fill="#d1d5db" fontSize="11">Risk → Compliance → Monitoring</text>
          </>
        )
      },
      'command': {
        viewBox: "0 0 900 600",
        diagram: (
          <>
            {/* Command Interface */}
            <rect x="350" y="20" width="200" height="100" fill="#1e293b" stroke="#84cc16" strokeWidth="2" rx="4"/>
            <text x="450" y="45" textAnchor="middle" fill="#84cc16" fontSize="14">«interface»</text>
            <text x="450" y="65" textAnchor="middle" fill="#fff" fontSize="14" fontWeight="bold">OrderCommand</text>
            <line x1="350" y1="75" x2="550" y2="75" stroke="#374151" strokeWidth="1"/>
            <text x="360" y="95" fill="#60a5fa" fontSize="11">+ execute(): Result</text>
            <text x="360" y="110" fill="#60a5fa" fontSize="11">+ undo(): Result</text>
            
            {/* Concrete Commands */}
            <rect x="50" y="180" width="180" height="120" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="140" y="210" textAnchor="middle" fill="#fff" fontSize="13">CreateOrderCommand</text>
            <line x1="50" y1="220" x2="230" y2="220" stroke="#374151" strokeWidth="1"/>
            <text x="60" y="240" fill="#94a3b8" fontSize="10">- orderService</text>
            <text x="60" y="255" fill="#94a3b8" fontSize="10">- order: TradingOrder</text>
            <line x1="50" y1="265" x2="230" y2="265" stroke="#374151" strokeWidth="1"/>
            <text x="60" y="285" fill="#60a5fa" fontSize="10">+ execute()</text>
            <text x="60" y="295" fill="#60a5fa" fontSize="10">+ undo()</text>
            
            <rect x="250" y="180" width="180" height="120" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="340" y="210" textAnchor="middle" fill="#fff" fontSize="13">CancelOrderCommand</text>
            <line x1="250" y1="220" x2="430" y2="220" stroke="#374151" strokeWidth="1"/>
            <text x="260" y="240" fill="#94a3b8" fontSize="10">- orderService</text>
            <text x="260" y="255" fill="#94a3b8" fontSize="10">- orderId: String</text>
            <line x1="250" y1="265" x2="430" y2="265" stroke="#374151" strokeWidth="1"/>
            <text x="260" y="285" fill="#60a5fa" fontSize="10">+ execute()</text>
            <text x="260" y="295" fill="#60a5fa" fontSize="10">+ undo()</text>
            
            <rect x="450" y="180" width="180" height="120" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="540" y="210" textAnchor="middle" fill="#fff" fontSize="13">ModifyOrderCommand</text>
            <line x1="450" y1="220" x2="630" y2="220" stroke="#374151" strokeWidth="1"/>
            <text x="460" y="240" fill="#94a3b8" fontSize="10">- orderService</text>
            <text x="460" y="255" fill="#94a3b8" fontSize="10">- modification</text>
            <line x1="450" y1="265" x2="630" y2="265" stroke="#374151" strokeWidth="1"/>
            <text x="460" y="285" fill="#60a5fa" fontSize="10">+ execute()</text>
            <text x="460" y="295" fill="#60a5fa" fontSize="10">+ undo()</text>
            
            <rect x="650" y="180" width="180" height="120" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="740" y="210" textAnchor="middle" fill="#fff" fontSize="13">BatchOrderCommand</text>
            <line x1="650" y1="220" x2="830" y2="220" stroke="#374151" strokeWidth="1"/>
            <text x="660" y="240" fill="#94a3b8" fontSize="10">- commands: List</text>
            <text x="660" y="255" fill="#94a3b8" fontSize="10">- results: List</text>
            <line x1="650" y1="265" x2="830" y2="265" stroke="#374151" strokeWidth="1"/>
            <text x="660" y="285" fill="#60a5fa" fontSize="10">+ execute()</text>
            <text x="660" y="295" fill="#60a5fa" fontSize="10">+ undo()</text>
            
            {/* Invoker */}
            <rect x="50" y="360" width="220" height="120" fill="#1e293b" stroke="#ef4444" strokeWidth="1" rx="4"/>
            <text x="160" y="390" textAnchor="middle" fill="#fff" fontSize="14">OrderCommandProcessor</text>
            <line x1="50" y1="400" x2="270" y2="400" stroke="#374151" strokeWidth="1"/>
            <text x="60" y="420" fill="#94a3b8" fontSize="11">- commandQueue</text>
            <text x="60" y="435" fill="#94a3b8" fontSize="11">- commandHistory</text>
            <line x1="50" y1="445" x2="270" y2="445" stroke="#374151" strokeWidth="1"/>
            <text x="60" y="465" fill="#60a5fa" fontSize="11">+ executeCommand(cmd)</text>
            <text x="60" y="475" fill="#60a5fa" fontSize="11">+ undoLastCommand()</text>
            
            {/* Receiver */}
            <rect x="350" y="360" width="200" height="120" fill="#1e293b" stroke="#8b5cf6" strokeWidth="1" rx="4"/>
            <text x="450" y="390" textAnchor="middle" fill="#fff" fontSize="14">OrderService</text>
            <line x1="350" y1="400" x2="550" y2="400" stroke="#374151" strokeWidth="1"/>
            <text x="360" y="420" fill="#94a3b8" fontSize="11">- orderRepository</text>
            <text x="360" y="435" fill="#94a3b8" fontSize="11">- tradeEngine</text>
            <line x1="350" y1="445" x2="550" y2="445" stroke="#374151" strokeWidth="1"/>
            <text x="360" y="465" fill="#60a5fa" fontSize="11">+ createOrder(order)</text>
            <text x="360" y="475" fill="#60a5fa" fontSize="11">+ cancelOrder(id)</text>
            
            {/* Client */}
            <rect x="600" y="360" width="180" height="80" fill="#1e293b" stroke="#9ca3af" strokeWidth="1" rx="4"/>
            <text x="690" y="390" textAnchor="middle" fill="#fff" fontSize="14">TradingApplication</text>
            <line x1="600" y1="400" x2="780" y2="400" stroke="#374151" strokeWidth="1"/>
            <text x="610" y="420" fill="#60a5fa" fontSize="11">+ submitOrder()</text>
            <text x="610" y="435" fill="#60a5fa" fontSize="11">+ cancelOrder()</text>
            
            {/* Relationships */}
            <defs>
              <marker id="arrow-command" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="none" stroke="#9ca3af"/>
              </marker>
            </defs>
            
            {/* Commands implement interface */}
            <line x1="140" y1="180" x2="400" y2="120" stroke="#9ca3af" strokeWidth="1" strokeDasharray="5,5" markerEnd="url(#arrow-command)"/>
            <line x1="340" y1="180" x2="430" y2="120" stroke="#9ca3af" strokeWidth="1" strokeDasharray="5,5" markerEnd="url(#arrow-command)"/>
            <line x1="540" y1="180" x2="470" y2="120" stroke="#9ca3af" strokeWidth="1" strokeDasharray="5,5" markerEnd="url(#arrow-command)"/>
            <line x1="740" y1="180" x2="500" y2="120" stroke="#9ca3af" strokeWidth="1" strokeDasharray="5,5" markerEnd="url(#arrow-command)"/>
            
            {/* Invoker uses commands */}
            <line x1="270" y1="420" x2="350" y2="80" stroke="#9ca3af" strokeWidth="1" markerEnd="url(#arrow-command)"/>
            
            {/* Commands use receiver */}
            <line x1="340" y1="300" x2="450" y2="360" stroke="#9ca3af" strokeWidth="1" strokeDasharray="3,3" markerEnd="url(#arrow-command)"/>
            
            {/* Client uses invoker */}
            <line x1="600" y1="400" x2="270" y2="420" stroke="#9ca3af" strokeWidth="1" markerEnd="url(#arrow-command)"/>
            
            {/* Labels */}
            <text x="290" y="250" fill="#9ca3af" fontSize="10">invokes</text>
            <text x="480" y="335" fill="#9ca3af" fontSize="10">operates on</text>
            <text x="430" y="415" fill="#9ca3af" fontSize="10">uses</text>
            
            {/* History visualization */}
            <rect x="50" y="520" width="300" height="60" fill="rgba(239, 68, 68, 0.1)" stroke="#ef4444" strokeWidth="1" strokeDasharray="5,5" rx="4"/>
            <text x="200" y="545" textAnchor="middle" fill="#ef4444" fontSize="12">Command History & Undo Support</text>
            <text x="200" y="565" textAnchor="middle" fill="#d1d5db" fontSize="11">Last executed commands can be undone</text>
          </>
        )
      },
      'mvc': {
        viewBox: "0 0 900 700",
        diagram: (
          <>
            {/* Model Layer */}
            <rect x="50" y="50" width="250" height="200" fill="rgba(16, 185, 129, 0.1)" stroke="#10b981" strokeWidth="2" rx="8"/>
            <text x="175" y="80" textAnchor="middle" fill="#10b981" fontSize="16" fontWeight="bold">MODEL</text>
            
            {/* Model Classes */}
            <rect x="70" y="100" width="100" height="80" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="120" y="125" textAnchor="middle" fill="#fff" fontSize="12">TradingPosition</text>
            <line x1="70" y1="135" x2="170" y2="135" stroke="#374151" strokeWidth="1"/>
            <text x="75" y="150" fill="#94a3b8" fontSize="9">- symbol</text>
            <text x="75" y="160" fill="#94a3b8" fontSize="9">- quantity</text>
            <text x="75" y="170" fill="#60a5fa" fontSize="9">+ calculatePnL()</text>
            
            <rect x="180" y="100" width="100" height="80" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="230" y="125" textAnchor="middle" fill="#fff" fontSize="12">PortfolioModel</text>
            <line x1="180" y1="135" x2="280" y2="135" stroke="#374151" strokeWidth="1"/>
            <text x="185" y="150" fill="#94a3b8" fontSize="9">- positions</text>
            <text x="185" y="160" fill="#94a3b8" fontSize="9">- totalValue</text>
            <text x="185" y="170" fill="#60a5fa" fontSize="9">+ getTotalPnL()</text>
            
            <rect x="70" y="190" width="210" height="50" fill="#1e293b" stroke="#10b981" strokeWidth="1" rx="4"/>
            <text x="175" y="215" textAnchor="middle" fill="#fff" fontSize="12">PortfolioService</text>
            <line x1="70" y1="225" x2="280" y2="225" stroke="#374151" strokeWidth="1"/>
            <text x="75" y="235" fill="#60a5fa" fontSize="9">+ getPortfolioByUserId() + updatePosition()</text>
            
            {/* View Layer */}
            <rect x="350" y="50" width="250" height="200" fill="rgba(59, 130, 246, 0.1)" stroke="#3b82f6" strokeWidth="2" rx="8"/>
            <text x="475" y="80" textAnchor="middle" fill="#3b82f6" fontSize="16" fontWeight="bold">VIEW</text>
            
            {/* View Classes */}
            <rect x="370" y="100" width="100" height="80" fill="#1e293b" stroke="#3b82f6" strokeWidth="1" rx="4"/>
            <text x="420" y="125" textAnchor="middle" fill="#fff" fontSize="12">PortfolioResponse</text>
            <line x1="370" y1="135" x2="470" y2="135" stroke="#374151" strokeWidth="1"/>
            <text x="375" y="150" fill="#94a3b8" fontSize="9">- positions</text>
            <text x="375" y="160" fill="#94a3b8" fontSize="9">- totalValue</text>
            <text x="375" y="170" fill="#60a5fa" fontSize="9">+ from(model)</text>
            
            <rect x="480" y="100" width="100" height="80" fill="#1e293b" stroke="#3b82f6" strokeWidth="1" rx="4"/>
            <text x="530" y="125" textAnchor="middle" fill="#fff" fontSize="12">PositionResponse</text>
            <line x1="480" y1="135" x2="580" y2="135" stroke="#374151" strokeWidth="1"/>
            <text x="485" y="150" fill="#94a3b8" fontSize="9">- symbol</text>
            <text x="485" y="160" fill="#94a3b8" fontSize="9">- unrealizedPnL</text>
            <text x="485" y="170" fill="#60a5fa" fontSize="9">+ toJson()</text>
            
            <rect x="370" y="190" width="210" height="50" fill="#1e293b" stroke="#3b82f6" strokeWidth="1" rx="4"/>
            <text x="475" y="215" textAnchor="middle" fill="#fff" fontSize="12">PortfolioView (React)</text>
            <line x1="370" y1="225" x2="580" y2="225" stroke="#374151" strokeWidth="1"/>
            <text x="375" y="235" fill="#60a5fa" fontSize="9">+ render() + fetchPortfolio() + handleUpdate()</text>
            
            {/* Controller Layer */}
            <rect x="650" y="50" width="200" height="200" fill="rgba(239, 68, 68, 0.1)" stroke="#ef4444" strokeWidth="2" rx="8"/>
            <text x="750" y="80" textAnchor="middle" fill="#ef4444" fontSize="16" fontWeight="bold">CONTROLLER</text>
            
            <rect x="670" y="100" width="160" height="140" fill="#1e293b" stroke="#ef4444" strokeWidth="1" rx="4"/>
            <text x="750" y="125" textAnchor="middle" fill="#fff" fontSize="12">PortfolioController</text>
            <line x1="670" y1="135" x2="830" y2="135" stroke="#374151" strokeWidth="1"/>
            <text x="675" y="150" fill="#94a3b8" fontSize="9">- portfolioService</text>
            <text x="675" y="160" fill="#94a3b8" fontSize="9">- positionValidator</text>
            <line x1="670" y1="170" x2="830" y2="170" stroke="#374151" strokeWidth="1"/>
            <text x="675" y="185" fill="#60a5fa" fontSize="9">@GetMapping</text>
            <text x="675" y="195" fill="#60a5fa" fontSize="9">+ getPortfolio()</text>
            <text x="675" y="210" fill="#60a5fa" fontSize="9">@PostMapping</text>
            <text x="675" y="220" fill="#60a5fa" fontSize="9">+ updatePosition()</text>
            <text x="675" y="230" fill="#60a5fa" fontSize="9">+ getPortfolioSummary()</text>
            
            {/* Data Layer */}
            <rect x="50" y="300" width="250" height="120" fill="rgba(245, 158, 11, 0.1)" stroke="#f59e0b" strokeWidth="2" rx="8"/>
            <text x="175" y="330" textAnchor="middle" fill="#f59e0b" fontSize="16" fontWeight="bold">DATA ACCESS</text>
            
            <rect x="70" y="350" width="100" height="60" fill="#1e293b" stroke="#f59e0b" strokeWidth="1" rx="4"/>
            <text x="120" y="375" textAnchor="middle" fill="#fff" fontSize="12">PositionRepository</text>
            <line x1="70" y1="385" x2="170" y2="385" stroke="#374151" strokeWidth="1"/>
            <text x="75" y="400" fill="#60a5fa" fontSize="9">+ findByUserId()</text>
            
            <rect x="180" y="350" width="100" height="60" fill="#1e293b" stroke="#f59e0b" strokeWidth="1" rx="4"/>
            <text x="230" y="375" textAnchor="middle" fill="#fff" fontSize="12">Database</text>
            <line x1="180" y1="385" x2="280" y2="385" stroke="#374151" strokeWidth="1"/>
            <text x="185" y="400" fill="#60a5fa" fontSize="9">PostgreSQL</text>
            
            {/* Client/Browser */}
            <rect x="370" y="300" width="210" height="80" fill="rgba(139, 92, 246, 0.1)" stroke="#8b5cf6" strokeWidth="2" rx="8"/>
            <text x="475" y="330" textAnchor="middle" fill="#8b5cf6" fontSize="16" fontWeight="bold">CLIENT</text>
            <rect x="390" y="350" width="170" height="20" fill="#1e293b" stroke="#8b5cf6" strokeWidth="1" rx="4"/>
            <text x="475" y="365" textAnchor="middle" fill="#fff" fontSize="11">Web Browser / Mobile App</text>
            
            {/* HTTP/REST Layer */}
            <rect x="620" y="300" width="230" height="80" fill="rgba(6, 182, 212, 0.1)" stroke="#06b6d4" strokeWidth="2" rx="8"/>
            <text x="735" y="330" textAnchor="middle" fill="#06b6d4" fontSize="16" fontWeight="bold">HTTP/REST</text>
            <text x="735" y="350" textAnchor="middle" fill="#d1d5db" fontSize="11">GET /api/portfolio/&#123;userId&#125;</text>
            <text x="735" y="365" textAnchor="middle" fill="#d1d5db" fontSize="11">POST /api/positions/&#123;id&#125;/update</text>
            
            {/* Relationships */}
            <defs>
              <marker id="arrow-mvc" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="#9ca3af"/>
              </marker>
            </defs>
            
            {/* Controller uses Model */}
            <line x1="650" y1="150" x2="300" y2="150" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-mvc)"/>
            <text x="475" y="140" textAnchor="middle" fill="#9ca3af" fontSize="10">uses</text>
            
            {/* Controller updates View */}
            <line x1="670" y1="200" x2="600" y2="200" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-mvc)"/>
            <text x="635" y="190" textAnchor="middle" fill="#9ca3af" fontSize="10">updates</text>
            
            {/* Model accesses Data */}
            <line x1="175" y1="250" x2="175" y2="300" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-mvc)"/>
            <text x="120" y="275" fill="#9ca3af" fontSize="10">accesses</text>
            
            {/* View calls Controller */}
            <line x1="475" y1="250" x2="675" y2="300" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-mvc)"/>
            <text x="550" y="270" fill="#9ca3af" fontSize="10">HTTP requests</text>
            
            {/* Client interacts with View */}
            <line x1="475" y1="300" x2="475" y2="250" stroke="#9ca3af" strokeWidth="2" strokeDasharray="5,5" markerEnd="url(#arrow-mvc)"/>
            <text x="420" y="275" fill="#9ca3af" fontSize="10">renders</text>
            
            {/* Data flows */}
            <rect x="50" y="500" width="800" height="160" fill="rgba(31, 41, 55, 0.3)" stroke="#64748b" strokeWidth="1" strokeDasharray="5,5" rx="8"/>
            <text x="450" y="530" textAnchor="middle" fill="#64748b" fontSize="14" fontWeight="bold">MVC Data Flow</text>
            
            {/* Flow steps */}
            <circle cx="100" cy="560" r="15" fill="#8b5cf6" stroke="#fff" strokeWidth="2"/>
            <text x="100" y="565" textAnchor="middle" fill="#fff" fontSize="12" fontWeight="bold">1</text>
            <text x="100" y="585" textAnchor="middle" fill="#d1d5db" fontSize="10">User Action</text>
            
            <circle cx="250" cy="560" r="15" fill="#ef4444" stroke="#fff" strokeWidth="2"/>
            <text x="250" y="565" textAnchor="middle" fill="#fff" fontSize="12" fontWeight="bold">2</text>
            <text x="250" y="585" textAnchor="middle" fill="#d1d5db" fontSize="10">Controller</text>
            <text x="250" y="595" textAnchor="middle" fill="#d1d5db" fontSize="9">Handles Request</text>
            
            <circle cx="400" cy="560" r="15" fill="#10b981" stroke="#fff" strokeWidth="2"/>
            <text x="400" y="565" textAnchor="middle" fill="#fff" fontSize="12" fontWeight="bold">3</text>
            <text x="400" y="585" textAnchor="middle" fill="#d1d5db" fontSize="10">Model</text>
            <text x="400" y="595" textAnchor="middle" fill="#d1d5db" fontSize="9">Business Logic</text>
            
            <circle cx="550" cy="560" r="15" fill="#f59e0b" stroke="#fff" strokeWidth="2"/>
            <text x="550" y="565" textAnchor="middle" fill="#fff" fontSize="12" fontWeight="bold">4</text>
            <text x="550" y="585" textAnchor="middle" fill="#d1d5db" fontSize="10">Data Access</text>
            <text x="550" y="595" textAnchor="middle" fill="#d1d5db" fontSize="9">Database</text>
            
            <circle cx="700" cy="560" r="15" fill="#3b82f6" stroke="#fff" strokeWidth="2"/>
            <text x="700" y="565" textAnchor="middle" fill="#fff" fontSize="12" fontWeight="bold">5</text>
            <text x="700" y="585" textAnchor="middle" fill="#d1d5db" fontSize="10">View</text>
            <text x="700" y="595" textAnchor="middle" fill="#d1d5db" fontSize="9">Response</text>
            
            {/* Flow arrows */}
            <line x1="130" y1="560" x2="220" y2="560" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-mvc)"/>
            <line x1="280" y1="560" x2="370" y2="560" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-mvc)"/>
            <line x1="430" y1="560" x2="520" y2="560" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-mvc)"/>
            <line x1="580" y1="560" x2="670" y2="560" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-mvc)"/>
            
            <text x="450" y="630" textAnchor="middle" fill="#94a3b8" fontSize="12">
              Separation of Concerns: Model (data/logic), View (presentation), Controller (user input)
            </text>
          </>
        )
      },
      'facade': {
        viewBox: "0 0 900 600",
        diagram: (
          <>
            {/* Facade Class */}
            <rect x="350" y="50" width="200" height="100" fill="#1e293b" stroke="#10b981" strokeWidth="2" rx="4"/>
            <text x="450" y="80" textAnchor="middle" fill="#10b981" fontSize="14" fontWeight="bold">«facade»</text>
            <text x="450" y="100" textAnchor="middle" fill="#fff" fontSize="16" fontWeight="bold">TradingSystemFacade</text>
            <line x1="350" y1="110" x2="550" y2="110" stroke="#374151" strokeWidth="1"/>
            <text x="360" y="130" fill="#60a5fa" fontSize="11">+ executeOrder(order)</text>
            <text x="360" y="145" fill="#60a5fa" fontSize="11">+ getPortfolioSummary(userId)</text>

            {/* Subsystem 1: Order Management */}
            <rect x="50" y="250" width="180" height="120" fill="rgba(59, 130, 246, 0.1)" stroke="#3b82f6" strokeWidth="1" rx="6"/>
            <text x="140" y="280" textAnchor="middle" fill="#3b82f6" fontSize="14" fontWeight="bold">Order Management</text>
            
            <rect x="70" y="300" width="140" height="60" fill="#1e293b" stroke="#3b82f6" strokeWidth="1" rx="4"/>
            <text x="140" y="325" textAnchor="middle" fill="#fff" fontSize="12">OrderValidator</text>
            <line x1="70" y1="335" x2="210" y2="335" stroke="#374151" strokeWidth="1"/>
            <text x="75" y="350" fill="#60a5fa" fontSize="10">+ validateOrder()</text>

            {/* Subsystem 2: Risk Engine */}
            <rect x="280" y="250" width="180" height="120" fill="rgba(239, 68, 68, 0.1)" stroke="#ef4444" strokeWidth="1" rx="6"/>
            <text x="370" y="280" textAnchor="middle" fill="#ef4444" fontSize="14" fontWeight="bold">Risk Engine</text>
            
            <rect x="300" y="300" width="140" height="60" fill="#1e293b" stroke="#ef4444" strokeWidth="1" rx="4"/>
            <text x="370" y="325" textAnchor="middle" fill="#fff" fontSize="12">RiskCalculator</text>
            <line x1="300" y1="335" x2="440" y2="335" stroke="#374151" strokeWidth="1"/>
            <text x="305" y="350" fill="#60a5fa" fontSize="10">+ checkRiskLimits()</text>

            {/* Subsystem 3: Execution Engine */}
            <rect x="510" y="250" width="180" height="120" fill="rgba(245, 158, 11, 0.1)" stroke="#f59e0b" strokeWidth="1" rx="6"/>
            <text x="600" y="280" textAnchor="middle" fill="#f59e0b" fontSize="14" fontWeight="bold">Execution Engine</text>
            
            <rect x="530" y="300" width="140" height="60" fill="#1e293b" stroke="#f59e0b" strokeWidth="1" rx="4"/>
            <text x="600" y="325" textAnchor="middle" fill="#fff" fontSize="12">OrderExecutor</text>
            <line x1="530" y1="335" x2="670" y2="335" stroke="#374151" strokeWidth="1"/>
            <text x="535" y="350" fill="#60a5fa" fontSize="10">+ executeOrder()</text>

            {/* Subsystem 4: Portfolio Manager */}
            <rect x="720" y="250" width="150" height="120" fill="rgba(139, 92, 246, 0.1)" stroke="#8b5cf6" strokeWidth="1" rx="6"/>
            <text x="795" y="280" textAnchor="middle" fill="#8b5cf6" fontSize="14" fontWeight="bold">Portfolio</text>
            
            <rect x="740" y="300" width="110" height="60" fill="#1e293b" stroke="#8b5cf6" strokeWidth="1" rx="4"/>
            <text x="795" y="325" textAnchor="middle" fill="#fff" fontSize="12">PortfolioMgr</text>
            <line x1="740" y1="335" x2="850" y2="335" stroke="#374151" strokeWidth="1"/>
            <text x="745" y="350" fill="#60a5fa" fontSize="10">+ updatePosition()</text>

            {/* Client */}
            <rect x="350" y="450" width="200" height="60" fill="rgba(16, 185, 129, 0.1)" stroke="#10b981" strokeWidth="1" rx="6"/>
            <text x="450" y="480" textAnchor="middle" fill="#10b981" fontSize="14" fontWeight="bold">Client Application</text>
            <text x="450" y="500" textAnchor="middle" fill="#d1d5db" fontSize="11">Uses simple unified interface</text>

            {/* Relationships */}
            <defs>
              <marker id="arrow-facade" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="#9ca3af"/>
              </marker>
            </defs>

            {/* Facade to subsystems */}
            <line x1="400" y1="150" x2="140" y2="250" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-facade)"/>
            <line x1="450" y1="150" x2="370" y2="250" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-facade)"/>
            <line x1="500" y1="150" x2="600" y2="250" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-facade)"/>
            <line x1="520" y1="150" x2="795" y2="250" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-facade)"/>

            {/* Client to facade */}
            <line x1="450" y1="450" x2="450" y2="150" stroke="#9ca3af" strokeWidth="2" strokeDasharray="5,5" markerEnd="url(#arrow-facade)"/>
            <text x="460" y="300" fill="#9ca3af" fontSize="11">Simple Interface</text>
          </>
        )
      },
      'state': {
        viewBox: "0 0 800 600",
        diagram: (
          <>
            {/* Context Class */}
            <rect x="300" y="50" width="200" height="80" fill="#1e293b" stroke="#ef4444" strokeWidth="2" rx="4"/>
            <text x="400" y="80" textAnchor="middle" fill="#ef4444" fontSize="14" fontWeight="bold">«context»</text>
            <text x="400" y="100" textAnchor="middle" fill="#fff" fontSize="16" fontWeight="bold">OrderContext</text>
            <line x1="300" y1="110" x2="500" y2="110" stroke="#374151" strokeWidth="1"/>
            <text x="310" y="125" fill="#60a5fa" fontSize="11">+ setState(state)</text>

            {/* Abstract State */}
            <rect x="300" y="200" width="200" height="60" fill="rgba(239, 68, 68, 0.1)" stroke="#ef4444" strokeWidth="2" rx="4"/>
            <text x="400" y="225" textAnchor="middle" fill="#ef4444" fontSize="14" fontWeight="bold">«abstract»</text>
            <text x="400" y="245" textAnchor="middle" fill="#fff" fontSize="16" fontWeight="bold">OrderState</text>

            {/* Concrete States */}
            <rect x="50" y="350" width="150" height="100" fill="#1e293b" stroke="#ef4444" strokeWidth="1" rx="4"/>
            <text x="125" y="375" textAnchor="middle" fill="#fff" fontSize="14">PendingState</text>
            <line x1="50" y1="385" x2="200" y2="385" stroke="#374151" strokeWidth="1"/>
            <text x="55" y="400" fill="#60a5fa" fontSize="10">+ validate()</text>
            <text x="55" y="415" fill="#60a5fa" fontSize="10">+ cancel()</text>
            <text x="55" y="430" fill="#60a5fa" fontSize="10">+ submit()</text>

            <rect x="230" y="350" width="150" height="100" fill="#1e293b" stroke="#ef4444" strokeWidth="1" rx="4"/>
            <text x="305" y="375" textAnchor="middle" fill="#fff" fontSize="14">ValidatedState</text>
            <line x1="230" y1="385" x2="380" y2="385" stroke="#374151" strokeWidth="1"/>
            <text x="235" y="400" fill="#60a5fa" fontSize="10">+ execute()</text>
            <text x="235" y="415" fill="#60a5fa" fontSize="10">+ cancel()</text>
            <text x="235" y="430" fill="#60a5fa" fontSize="10">+ reject()</text>

            <rect x="410" y="350" width="150" height="100" fill="#1e293b" stroke="#ef4444" strokeWidth="1" rx="4"/>
            <text x="485" y="375" textAnchor="middle" fill="#fff" fontSize="14">ExecutedState</text>
            <line x1="410" y1="385" x2="560" y2="385" stroke="#374151" strokeWidth="1"/>
            <text x="415" y="400" fill="#60a5fa" fontSize="10">+ settle()</text>
            <text x="415" y="415" fill="#60a5fa" fontSize="10">+ cancel()</text>
            <text x="415" y="430" fill="#60a5fa" fontSize="10">+ confirm()</text>

            <rect x="590" y="350" width="150" height="100" fill="#1e293b" stroke="#ef4444" strokeWidth="1" rx="4"/>
            <text x="665" y="375" textAnchor="middle" fill="#fff" fontSize="14">SettledState</text>
            <line x1="590" y1="385" x2="740" y2="385" stroke="#374151" strokeWidth="1"/>
            <text x="595" y="400" fill="#60a5fa" fontSize="10">+ archive()</text>
            <text x="595" y="415" fill="#60a5fa" fontSize="10">+ report()</text>
            <text x="595" y="430" fill="#60a5fa" fontSize="10">+ audit()</text>

            {/* Relationships */}
            <defs>
              <marker id="arrow-state" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="#9ca3af"/>
              </marker>
            </defs>

            {/* Context to State */}
            <line x1="400" y1="130" x2="400" y2="200" stroke="#9ca3af" strokeWidth="2" markerEnd="url(#arrow-state)"/>
            <text x="410" y="165" fill="#9ca3af" fontSize="11">uses</text>

            {/* Abstract State to Concrete States */}
            <line x1="350" y1="260" x2="125" y2="350" stroke="#9ca3af" strokeWidth="2" strokeDasharray="5,5" markerEnd="url(#arrow-state)"/>
            <line x1="380" y1="260" x2="305" y2="350" stroke="#9ca3af" strokeWidth="2" strokeDasharray="5,5" markerEnd="url(#arrow-state)"/>
            <line x1="420" y1="260" x2="485" y2="350" stroke="#9ca3af" strokeWidth="2" strokeDasharray="5,5" markerEnd="url(#arrow-state)"/>
            <line x1="450" y1="260" x2="665" y2="350" stroke="#9ca3af" strokeWidth="2" strokeDasharray="5,5" markerEnd="url(#arrow-state)"/>

            {/* State transitions */}
            <line x1="200" y1="400" x2="230" y2="400" stroke="#10b981" strokeWidth="3" markerEnd="url(#arrow-state)"/>
            <line x1="380" y1="400" x2="410" y2="400" stroke="#10b981" strokeWidth="3" markerEnd="url(#arrow-state)"/>
            <line x1="560" y1="400" x2="590" y2="400" stroke="#10b981" strokeWidth="3" markerEnd="url(#arrow-state)"/>

            <text x="400" y="530" textAnchor="middle" fill="#94a3b8" fontSize="12">
              State transitions: Pending → Validated → Executed → Settled
            </text>
          </>
        )
      },
      'prototype': {
        viewBox: "0 0 800 500",
        diagram: (
          <>
            {/* Prototype Interface */}
            <rect x="300" y="50" width="200" height="60" fill="rgba(59, 130, 246, 0.1)" stroke="#3b82f6" strokeWidth="2" rx="4"/>
            <text x="400" y="75" textAnchor="middle" fill="#3b82f6" fontSize="14" fontWeight="bold">«interface»</text>
            <text x="400" y="95" textAnchor="middle" fill="#fff" fontSize="16" fontWeight="bold">TradingInstrument</text>

            {/* Abstract Prototype */}
            <rect x="300" y="150" width="200" height="80" fill="#1e293b" stroke="#3b82f6" strokeWidth="2" rx="4"/>
            <text x="400" y="175" textAnchor="middle" fill="#3b82f6" fontSize="14" fontWeight="bold">«abstract»</text>
            <text x="400" y="195" textAnchor="middle" fill="#fff" fontSize="16" fontWeight="bold">BaseInstrument</text>
            <line x1="300" y1="205" x2="500" y2="205" stroke="#374151" strokeWidth="1"/>
            <text x="310" y="220" fill="#60a5fa" fontSize="11">+ clone()</text>

            {/* Concrete Prototypes */}
            <rect x="50" y="300" width="160" height="120" fill="#1e293b" stroke="#3b82f6" strokeWidth="1" rx="4"/>
            <text x="130" y="325" textAnchor="middle" fill="#fff" fontSize="14">EquityInstrument</text>
            <line x1="50" y1="335" x2="210" y2="335" stroke="#374151" strokeWidth="1"/>
            <text x="55" y="350" fill="#94a3b8" fontSize="10">- symbol: String</text>
            <text x="55" y="365" fill="#94a3b8" fontSize="10">- exchange: String</text>
            <text x="55" y="380" fill="#94a3b8" fontSize="10">- sector: String</text>
            <line x1="50" y1="390" x2="210" y2="390" stroke="#374151" strokeWidth="1"/>
            <text x="55" y="405" fill="#60a5fa" fontSize="10">+ clone(): Equity</text>

            <rect x="240" y="300" width="160" height="120" fill="#1e293b" stroke="#3b82f6" strokeWidth="1" rx="4"/>
            <text x="320" y="325" textAnchor="middle" fill="#fff" fontSize="14">BondInstrument</text>
            <line x1="240" y1="335" x2="400" y2="335" stroke="#374151" strokeWidth="1"/>
            <text x="245" y="350" fill="#94a3b8" fontSize="10">- maturity: Date</text>
            <text x="245" y="365" fill="#94a3b8" fontSize="10">- couponRate: double</text>
            <text x="245" y="380" fill="#94a3b8" fontSize="10">- rating: String</text>
            <line x1="240" y1="390" x2="400" y2="390" stroke="#374151" strokeWidth="1"/>
            <text x="245" y="405" fill="#60a5fa" fontSize="10">+ clone(): Bond</text>

            <rect x="430" y="300" width="160" height="120" fill="#1e293b" stroke="#3b82f6" strokeWidth="1" rx="4"/>
            <text x="510" y="325" textAnchor="middle" fill="#fff" fontSize="14">DerivativeInstr.</text>
            <line x1="430" y1="335" x2="590" y2="335" stroke="#374151" strokeWidth="1"/>
            <text x="435" y="350" fill="#94a3b8" fontSize="10">- underlying: String</text>
            <text x="435" y="365" fill="#94a3b8" fontSize="10">- strikePrice: double</text>
            <text x="435" y="380" fill="#94a3b8" fontSize="10">- expiry: Date</text>
            <line x1="430" y1="390" x2="590" y2="390" stroke="#374151" strokeWidth="1"/>
            <text x="435" y="405" fill="#60a5fa" fontSize="10">+ clone(): Derivative</text>

            {/* Registry */}
            <rect x="620" y="300" width="150" height="120" fill="#1e293b" stroke="#f59e0b" strokeWidth="2" rx="4"/>
            <text x="695" y="325" textAnchor="middle" fill="#f59e0b" fontSize="14" fontWeight="bold">PrototypeRegistry</text>
            <line x1="620" y1="335" x2="770" y2="335" stroke="#374151" strokeWidth="1"/>
            <text x="625" y="350" fill="#94a3b8" fontSize="10">- prototypes: Map</text>
            <line x1="620" y1="360" x2="770" y2="360" stroke="#374151" strokeWidth="1"/>
            <text x="625" y="375" fill="#60a5fa" fontSize="10">+ register(key, proto)</text>
            <text x="625" y="390" fill="#60a5fa" fontSize="10">+ getPrototype(key)</text>
            <text x="625" y="405" fill="#60a5fa" fontSize="10">+ createInstance(key)</text>

            {/* Relationships */}
            <defs>
              <marker id="arrow-prototype" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
                <polygon points="0 0, 10 3, 0 6" fill="#9ca3af"/>
              </marker>
            </defs>

            {/* Interface to Abstract */}
            <line x1="400" y1="110" x2="400" y2="150" stroke="#9ca3af" strokeWidth="2" strokeDasharray="5,5" markerEnd="url(#arrow-prototype)"/>

            {/* Abstract to Concrete */}
            <line x1="350" y1="230" x2="130" y2="300" stroke="#9ca3af" strokeWidth="2" strokeDasharray="5,5" markerEnd="url(#arrow-prototype)"/>
            <line x1="400" y1="230" x2="320" y2="300" stroke="#9ca3af" strokeWidth="2" strokeDasharray="5,5" markerEnd="url(#arrow-prototype)"/>
            <line x1="450" y1="230" x2="510" y2="300" stroke="#9ca3af" strokeWidth="2" strokeDasharray="5,5" markerEnd="url(#arrow-prototype)"/>

            {/* Registry relationships */}
            <line x1="620" y1="360" x2="590" y2="360" stroke="#f59e0b" strokeWidth="2" markerEnd="url(#arrow-prototype)"/>
            <line x1="620" y1="380" x2="400" y2="380" stroke="#f59e0b" strokeWidth="2" markerEnd="url(#arrow-prototype)"/>
            <line x1="620" y1="400" x2="210" y2="400" stroke="#f59e0b" strokeWidth="2" markerEnd="url(#arrow-prototype)"/>

            <text x="400" y="470" textAnchor="middle" fill="#94a3b8" fontSize="12">
              Clone existing objects to create new instances with same configuration
            </text>
          </>
        )
      }
    };
    
    return umlDiagrams[patternId] || null;
  };

  const getPatternDetails = (patternId) => {
    const details = {
      'singleton': {
        functions: [
          'Ensure a class has only one instance',
          'Provide global point of access to the instance',
          'Control instantiation process',
          'Thread-safe implementation',
          'Lazy initialization support',
          'Prevention of cloning and reflection attacks',
          'Memory optimization',
          'Configuration management',
          'Logging coordination',
          'Database connection pooling'
        ],
        workflow: `1. Check if instance exists
2. If not, create new instance with synchronization
3. Return existing instance
4. Ensure thread safety during creation
5. Prevent multiple instantiation attempts
6. Handle lazy vs eager initialization
7. Manage instance lifecycle
8. Provide cleanup mechanisms
9. Handle serialization scenarios
10. Maintain singleton contract across JVM`,
        code: `// Thread-safe Singleton with double-checked locking
public class DatabaseConnectionManager {
    
    private static volatile DatabaseConnectionManager instance;
    private final Connection connection;
    private final HikariDataSource dataSource;
    
    // Private constructor prevents instantiation
    private DatabaseConnectionManager() {
        try {
            // Initialize connection pool
            HikariConfig config = new HikariConfig();
            config.setJdbcUrl("*****************************************");
            config.setUsername("trader");
            config.setPassword("secure_password");
            config.setMaximumPoolSize(20);
            config.setMinimumIdle(5);
            config.setIdleTimeout(300000);
            config.setConnectionTimeout(20000);
            config.setLeakDetectionThreshold(60000);
            
            this.dataSource = new HikariDataSource(config);
            this.connection = dataSource.getConnection();
            
            log.info("Database connection manager initialized");
            
        } catch (SQLException e) {
            log.error("Failed to initialize database connection: {}", e.getMessage());
            throw new RuntimeException("Database initialization failed", e);
        }
    }
    
    // Double-checked locking for thread safety
    public static DatabaseConnectionManager getInstance() {
        if (instance == null) {
            synchronized (DatabaseConnectionManager.class) {
                if (instance == null) {
                    instance = new DatabaseConnectionManager();
                }
            }
        }
        return instance;
    }
    
    // Get database connection
    public Connection getConnection() throws SQLException {
        if (connection == null || connection.isClosed()) {
            throw new SQLException("Database connection is not available");
        }
        return dataSource.getConnection();
    }
    
    // Execute query with connection management
    public <T> List<T> executeQuery(String sql, RowMapper<T> mapper, Object... params) {
        List<T> results = new ArrayList<>();
        
        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            // Set parameters
            for (int i = 0; i < params.length; i++) {
                stmt.setObject(i + 1, params[i]);
            }
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    results.add(mapper.mapRow(rs));
                }
            }
            
        } catch (SQLException e) {
            log.error("Query execution failed: {}", e.getMessage());
            throw new DataAccessException("Query execution failed", e);
        }
        
        return results;
    }
    
    // Prevent cloning
    @Override
    protected Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException("Singleton instance cannot be cloned");
    }
    
    // Handle serialization
    private Object readResolve() {
        return getInstance();
    }
    
    // Cleanup resources
    public void shutdown() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
            if (dataSource != null && !dataSource.isClosed()) {
                dataSource.close();
            }
            log.info("Database connection manager shutdown completed");
        } catch (SQLException e) {
            log.error("Error during shutdown: {}", e.getMessage());
        }
    }
    
    // JVM shutdown hook
    static {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            if (instance != null) {
                instance.shutdown();
            }
        }));
    }
}

// Usage in trading application
@Service
public class OrderService {
    
    private final DatabaseConnectionManager dbManager;
    
    public OrderService() {
        this.dbManager = DatabaseConnectionManager.getInstance();
    }
    
    public void saveOrder(Order order) {
        String sql = "INSERT INTO orders (id, symbol, quantity, price, side, timestamp) VALUES (?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = dbManager.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, order.getId());
            stmt.setString(2, order.getSymbol());
            stmt.setInt(3, order.getQuantity());
            stmt.setBigDecimal(4, order.getPrice());
            stmt.setString(5, order.getSide().name());
            stmt.setTimestamp(6, Timestamp.from(order.getTimestamp()));
            
            int affected = stmt.executeUpdate();
            if (affected == 0) {
                throw new DataAccessException("Failed to save order");
            }
            
            log.info("Order saved successfully: {}", order.getId());
            
        } catch (SQLException e) {
            log.error("Failed to save order {}: {}", order.getId(), e.getMessage());
            throw new OrderPersistenceException("Order save failed", e);
        }
    }
}`,
        technologies: {
          'Thread Safety': 'Double-checked locking pattern',
          'Serialization': 'readResolve() method',
          'Anti-patterns': 'Clone prevention, reflection protection',
          'JVM Integration': 'Shutdown hooks, memory optimization'
        }
      },

      'factory': {
        functions: [
          'Create objects without specifying exact classes',
          'Centralize object creation logic',
          'Support multiple product families',
          'Enable runtime type determination',
          'Facilitate dependency injection',
          'Abstract complex instantiation',
          'Support configuration-driven creation',
          'Enable polymorphic object creation',
          'Simplify client code',
          'Support extensibility and maintenance'
        ],
        workflow: `1. Client requests object creation through factory
2. Factory determines appropriate concrete class
3. Factory instantiates object with required parameters
4. Factory applies configuration and initialization
5. Factory returns interface/abstract type to client
6. Client uses object without knowing concrete type
7. Factory handles cleanup and resource management
8. Support for multiple creation strategies
9. Integration with dependency injection
10. Maintenance of object lifecycle`,
        code: `// Abstract factory for trading instruments
public abstract class InstrumentFactory {
    
    // Factory method
    public abstract TradingInstrument createInstrument(String symbol, InstrumentType type);
    
    // Concrete factory for equity instruments
    public static class EquityFactory extends InstrumentFactory {
        
        private final MarketDataService marketData;
        private final RiskCalculator riskCalc;
        
        public EquityFactory(MarketDataService marketData, RiskCalculator riskCalc) {
            this.marketData = marketData;
            this.riskCalc = riskCalc;
        }
        
        @Override
        public TradingInstrument createInstrument(String symbol, InstrumentType type) {
            switch (type) {
                case STOCK:
                    return createStock(symbol);
                case ETF:
                    return createETF(symbol);
                case ADR:
                    return createADR(symbol);
                default:
                    throw new UnsupportedInstrumentException("Unsupported equity type: " + type);
            }
        }
        
        private Stock createStock(String symbol) {
            // Fetch market data
            MarketData data = marketData.getLatestData(symbol);
            
            // Calculate risk metrics
            RiskMetrics risk = riskCalc.calculateRiskMetrics(symbol);
            
            // Create stock with all required data
            return Stock.builder()
                .symbol(symbol)
                .currentPrice(data.getLastPrice())
                .bidPrice(data.getBidPrice())
                .askPrice(data.getAskPrice())
                .volume(data.getVolume())
                .volatility(risk.getVolatility())
                .beta(risk.getBeta())
                .sector(data.getSector())
                .marketCap(data.getMarketCap())
                .peRatio(data.getPeRatio())
                .dividendYield(data.getDividendYield())
                .build();
        }
        
        private ETF createETF(String symbol) {
            MarketData data = marketData.getLatestData(symbol);
            ETFComposition composition = marketData.getETFComposition(symbol);
            
            return ETF.builder()
                .symbol(symbol)
                .currentPrice(data.getLastPrice())
                .bidPrice(data.getBidPrice())
                .askPrice(data.getAskPrice())
                .volume(data.getVolume())
                .nav(data.getNav())
                .premium(data.getPremium())
                .holdings(composition.getHoldings())
                .expenseRatio(composition.getExpenseRatio())
                .trackingError(composition.getTrackingError())
                .build();
        }
    }
    
    // Concrete factory for derivative instruments
    public static class DerivativeFactory extends InstrumentFactory {
        
        private final OptionPricingService optionPricing;
        private final VolatilitySurface volatilitySurface;
        
        public DerivativeFactory(OptionPricingService optionPricing, 
                               VolatilitySurface volatilitySurface) {
            this.optionPricing = optionPricing;
            this.volatilitySurface = volatilitySurface;
        }
        
        @Override
        public TradingInstrument createInstrument(String symbol, InstrumentType type) {
            switch (type) {
                case OPTION:
                    return createOption(symbol);
                case FUTURE:
                    return createFuture(symbol);
                case SWAP:
                    return createSwap(symbol);
                default:
                    throw new UnsupportedInstrumentException("Unsupported derivative type: " + type);
            }
        }
        
        private Option createOption(String symbol) {
            OptionContract contract = parseOptionSymbol(symbol);
            
            // Get underlying data
            MarketData underlying = marketData.getLatestData(contract.getUnderlyingSymbol());
            
            // Get volatility from surface
            double impliedVol = volatilitySurface.getVolatility(
                contract.getStrike(), 
                contract.getExpiration(),
                contract.getUnderlyingSymbol()
            );
            
            // Price the option
            OptionPrice price = optionPricing.priceOption(
                underlying.getLastPrice(),
                contract.getStrike(),
                impliedVol,
                contract.getTimeToExpiration(),
                0.02 // risk-free rate
            );
            
            return Option.builder()
                .symbol(symbol)
                .underlyingSymbol(contract.getUnderlyingSymbol())
                .strike(contract.getStrike())
                .expiration(contract.getExpiration())
                .optionType(contract.getType())
                .currentPrice(price.getTheoreticalValue())
                .bidPrice(price.getBid())
                .askPrice(price.getAsk())
                .impliedVolatility(impliedVol)
                .delta(price.getDelta())
                .gamma(price.getGamma())
                .theta(price.getTheta())
                .vega(price.getVega())
                .rho(price.getRho())
                .openInterest(contract.getOpenInterest())
                .build();
        }
    }
}

// Factory registry for runtime factory selection
@Component
public class InstrumentFactoryRegistry {
    
    private final Map<AssetClass, InstrumentFactory> factories;
    
    public InstrumentFactoryRegistry(
            EquityFactory equityFactory,
            DerivativeFactory derivativeFactory,
            FixedIncomeFactory fixedIncomeFactory) {
        
        this.factories = Map.of(
            AssetClass.EQUITY, equityFactory,
            AssetClass.DERIVATIVE, derivativeFactory,
            AssetClass.FIXED_INCOME, fixedIncomeFactory
        );
    }
    
    public TradingInstrument createInstrument(String symbol, InstrumentType type) {
        AssetClass assetClass = type.getAssetClass();
        InstrumentFactory factory = factories.get(assetClass);
        
        if (factory == null) {
            throw new UnsupportedAssetClassException("No factory for asset class: " + assetClass);
        }
        
        return factory.createInstrument(symbol, type);
    }
}

// Usage in trading system
@Service
public class OrderValidationService {
    
    private final InstrumentFactoryRegistry factoryRegistry;
    
    public OrderValidationService(InstrumentFactoryRegistry factoryRegistry) {
        this.factoryRegistry = factoryRegistry;
    }
    
    public ValidationResult validateOrder(OrderRequest request) {
        try {
            // Create instrument using appropriate factory
            TradingInstrument instrument = factoryRegistry.createInstrument(
                request.getSymbol(), 
                request.getInstrumentType()
            );
            
            // Validate order against instrument properties
            if (!instrument.isValidOrderSize(request.getQuantity())) {
                return ValidationResult.failure("Invalid order size for instrument");
            }
            
            if (!instrument.isWithinTradingHours()) {
                return ValidationResult.failure("Instrument not trading");
            }
            
            // Additional validations...
            return ValidationResult.success();
            
        } catch (Exception e) {
            return ValidationResult.failure("Instrument validation failed: " + e.getMessage());
        }
    }
}`,
        technologies: {
          'Creation Strategy': 'Abstract factory + concrete implementations',
          'Dependency Injection': 'Spring Framework integration',
          'Caching': 'Redis for instrument metadata',
          'Configuration': 'YAML-based factory configuration'
        }
      },

      'observer': {
        functions: [
          'Define one-to-many dependency between objects',
          'Notify multiple observers of state changes',
          'Maintain loose coupling between subject and observers',
          'Support dynamic subscription/unsubscription',
          'Enable event-driven architectures',
          'Implement publish-subscribe patterns',
          'Handle asynchronous notifications',
          'Support filtering and routing',
          'Manage observer lifecycle',
          'Provide error handling and recovery'
        ],
        workflow: `1. Subject maintains list of observers
2. Observer registers interest in subject events
3. Subject state changes occur
4. Subject notifies all registered observers
5. Each observer processes notification independently
6. Observers can unsubscribe if no longer interested
7. Subject handles observer failures gracefully
8. Support for filtered notifications
9. Async notification processing
10. Cleanup and resource management`,
        code: `// Trading event system using Observer pattern
public interface TradingEventObserver {
    void onMarketDataUpdate(MarketDataEvent event);
    void onOrderFilled(OrderFilledEvent event);
    void onRiskLimitBreach(RiskLimitEvent event);
    void onSystemAlert(SystemAlertEvent event);
}

// Observable trading event subject
@Component
public class TradingEventPublisher {
    
    private final CopyOnWriteArrayList<TradingEventObserver> observers = new CopyOnWriteArrayList<>();
    private final ExecutorService notificationExecutor;
    private final EventFilter eventFilter;
    
    public TradingEventPublisher() {
        this.notificationExecutor = Executors.newCachedThreadPool(
            new ThreadFactoryBuilder()
                .setNameFormat("trading-event-notifier-%d")
                .setDaemon(true)
                .build()
        );
        this.eventFilter = new EventFilter();
    }
    
    // Observer registration
    public void subscribe(TradingEventObserver observer) {
        observers.addIfAbsent(observer);
        log.info("Observer subscribed: {}", observer.getClass().getSimpleName());
    }
    
    public void unsubscribe(TradingEventObserver observer) {
        observers.remove(observer);
        log.info("Observer unsubscribed: {}", observer.getClass().getSimpleName());
    }
    
    // Event publication methods
    public void publishMarketDataUpdate(String symbol, double price, long timestamp) {
        MarketDataEvent event = MarketDataEvent.builder()
            .symbol(symbol)
            .price(price)
            .timestamp(timestamp)
            .build();
            
        notifyObservers(event, TradingEventObserver::onMarketDataUpdate);
    }
    
    public void publishOrderFilled(String orderId, String symbol, int quantity, double price) {
        OrderFilledEvent event = OrderFilledEvent.builder()
            .orderId(orderId)
            .symbol(symbol)
            .quantity(quantity)
            .fillPrice(price)
            .timestamp(System.currentTimeMillis())
            .build();
            
        notifyObservers(event, TradingEventObserver::onOrderFilled);
    }
    
    public void publishRiskLimitBreach(String portfolioId, String limitType, double currentValue, double limit) {
        RiskLimitEvent event = RiskLimitEvent.builder()
            .portfolioId(portfolioId)
            .limitType(limitType)
            .currentValue(currentValue)
            .limitValue(limit)
            .severity(calculateSeverity(currentValue, limit))
            .timestamp(System.currentTimeMillis())
            .build();
            
        notifyObservers(event, TradingEventObserver::onRiskLimitBreach);
    }
    
    // Generic notification method
    private <T extends TradingEvent> void notifyObservers(T event, 
                                                         BiConsumer<TradingEventObserver, T> notifier) {
        
        // Filter event if necessary
        if (!eventFilter.shouldPublish(event)) {
            return;
        }
        
        // Notify all observers asynchronously
        observers.forEach(observer -> {
            notificationExecutor.submit(() -> {
                try {
                    notifier.accept(observer, event);
                } catch (Exception e) {
                    log.error("Observer notification failed for {}: {}", 
                             observer.getClass().getSimpleName(), e.getMessage());
                    
                    // Optionally remove failing observers
                    if (e instanceof PersistentObserverException) {
                        unsubscribe(observer);
                    }
                }
            });
        });
    }
    
    // Shutdown cleanup
    @PreDestroy
    public void shutdown() {
        notificationExecutor.shutdown();
        try {
            if (!notificationExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                notificationExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            notificationExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}

// Concrete observer implementations
@Component
public class RiskManagerObserver implements TradingEventObserver {
    
    private final RiskCalculationService riskService;
    private final AlertService alertService;
    
    public RiskManagerObserver(RiskCalculationService riskService, AlertService alertService) {
        this.riskService = riskService;
        this.alertService = alertService;
    }
    
    @Override
    public void onMarketDataUpdate(MarketDataEvent event) {
        // Recalculate portfolio risk on price changes
        try {
            riskService.updateMarketDataForSymbol(event.getSymbol(), event.getPrice());
            
            // Trigger VaR recalculation if significant price movement
            double priceChange = calculatePriceChange(event);
            if (Math.abs(priceChange) > 0.02) { // 2% threshold
                riskService.scheduleVaRRecalculation(event.getSymbol());
            }
            
        } catch (Exception e) {
            log.error("Failed to process market data update for risk: {}", e.getMessage());
        }
    }
    
    @Override
    public void onOrderFilled(OrderFilledEvent event) {
        // Update position and recalculate risk
        try {
            riskService.updatePositionFromFill(event.getSymbol(), 
                                             event.getQuantity(), 
                                             event.getFillPrice());
            
            // Check if new position breaches any limits
            riskService.checkLimitsForSymbol(event.getSymbol());
            
        } catch (Exception e) {
            log.error("Failed to process order fill for risk: {}", e.getMessage());
        }
    }
    
    @Override
    public void onRiskLimitBreach(RiskLimitEvent event) {
        // Escalate based on severity
        switch (event.getSeverity()) {
            case CRITICAL:
                alertService.sendImmediateAlert(event);
                // Potentially halt trading
                riskService.haltTradingForPortfolio(event.getPortfolioId());
                break;
            case HIGH:
                alertService.sendHighPriorityAlert(event);
                break;
            case MEDIUM:
                alertService.sendWarningAlert(event);
                break;
        }
    }
    
    @Override
    public void onSystemAlert(SystemAlertEvent event) {
        // Log system alerts for risk monitoring
        log.warn("System alert received: {}", event.getMessage());
    }
}

@Component
public class AuditTrailObserver implements TradingEventObserver {
    
    private final AuditRepository auditRepo;
    private final ComplianceService complianceService;
    
    @Override
    public void onMarketDataUpdate(MarketDataEvent event) {
        // Audit significant market moves
        if (isSignificantMove(event)) {
            auditRepo.logMarketEvent(event);
        }
    }
    
    @Override
    public void onOrderFilled(OrderFilledEvent event) {
        // All fills must be audited
        AuditEntry entry = AuditEntry.builder()
            .eventType(AuditEventType.ORDER_FILL)
            .orderId(event.getOrderId())
            .symbol(event.getSymbol())
            .quantity(event.getQuantity())
            .price(event.getFillPrice())
            .timestamp(event.getTimestamp())
            .build();
            
        auditRepo.save(entry);
        
        // Check compliance rules
        complianceService.validateFill(event);
    }
    
    @Override
    public void onRiskLimitBreach(RiskLimitEvent event) {
        // Critical for regulatory reporting
        auditRepo.logRiskEvent(event);
    }
    
    @Override
    public void onSystemAlert(SystemAlertEvent event) {
        auditRepo.logSystemEvent(event);
    }
}

// Event-driven trading system initialization
@Configuration
public class TradingSystemConfiguration {
    
    @Bean
    public TradingEventPublisher tradingEventPublisher() {
        return new TradingEventPublisher();
    }
    
    @EventListener
    public void initializeObservers(ApplicationReadyEvent event) {
        TradingEventPublisher publisher = tradingEventPublisher();
        
        // Register all observers
        publisher.subscribe(riskManagerObserver());
        publisher.subscribe(auditTrailObserver());
        publisher.subscribe(portfolioManagerObserver());
        publisher.subscribe(reportingObserver());
        
        log.info("Trading event system initialized with {} observers", 
                publisher.getObserverCount());
    }
}`,
        technologies: {
          'Concurrency': 'CopyOnWriteArrayList for thread safety',
          'Async Processing': 'ExecutorService for non-blocking notifications',
          'Error Handling': 'Observer failure isolation',
          'Filtering': 'Event filtering and routing capabilities'
        }
      },
      
      'builder': {
        functions: [
          'Construct complex objects step by step',
          'Separate construction from representation',
          'Create different representations of the same object',
          'Handle optional parameters elegantly',
          'Provide fluent interface for method chaining',
          'Ensure object immutability after construction',
          'Validate object state before creation',
          'Support partial object construction',
          'Enable configuration-driven construction',
          'Simplify complex object initialization'
        ],
        workflow: `1. Create builder class with same fields as target
2. Provide setter methods that return builder instance
3. Implement method chaining for fluent interface
4. Add validation logic in setter methods
5. Provide build() method that creates target object
6. Perform final validation in build method
7. Return immutable instance of target object
8. Reset builder state for reuse if needed
9. Handle optional vs required parameters
10. Throw exceptions for invalid states`,
        code: `// Builder Pattern for Complex Trading Order
public class TradingOrder {
    
    // Required fields
    private final String orderId;
    private final String symbol;
    private final OrderSide side;
    private final BigDecimal quantity;
    
    // Optional fields
    private final OrderType orderType;
    private final BigDecimal limitPrice;
    private final BigDecimal stopPrice;
    private final TimeInForce timeInForce;
    private final LocalDateTime expiryTime;
    private final String clientOrderId;
    private final BigDecimal minQuantity;
    private final BigDecimal displayQuantity;
    private final boolean isHidden;
    private final Map<String, String> customAttributes;
    private final RiskLimits riskLimits;
    
    // Private constructor - only builder can create
    private TradingOrder(Builder builder) {
        // Validate required fields
        this.orderId = validateNotNull(builder.orderId, "Order ID is required");
        this.symbol = validateNotNull(builder.symbol, "Symbol is required");
        this.side = validateNotNull(builder.side, "Order side is required");
        this.quantity = validatePositive(builder.quantity, "Quantity must be positive");
        
        // Set optional fields with defaults
        this.orderType = builder.orderType != null ? builder.orderType : OrderType.MARKET;
        this.limitPrice = builder.limitPrice;
        this.stopPrice = builder.stopPrice;
        this.timeInForce = builder.timeInForce != null ? builder.timeInForce : TimeInForce.DAY;
        this.expiryTime = builder.expiryTime;
        this.clientOrderId = builder.clientOrderId != null ? builder.clientOrderId : generateClientId();
        this.minQuantity = builder.minQuantity;
        this.displayQuantity = builder.displayQuantity;
        this.isHidden = builder.isHidden;
        this.customAttributes = builder.customAttributes != null ? 
            Collections.unmodifiableMap(new HashMap<>(builder.customAttributes)) : 
            Collections.emptyMap();
        this.riskLimits = builder.riskLimits;
        
        // Perform cross-field validation
        validateOrderConstraints();
    }
    
    // Builder class with fluent interface
    public static class Builder {
        // Required fields
        private String orderId;
        private String symbol;
        private OrderSide side;
        private BigDecimal quantity;
        
        // Optional fields
        private OrderType orderType;
        private BigDecimal limitPrice;
        private BigDecimal stopPrice;
        private TimeInForce timeInForce;
        private LocalDateTime expiryTime;
        private String clientOrderId;
        private BigDecimal minQuantity;
        private BigDecimal displayQuantity;
        private boolean isHidden = false;
        private Map<String, String> customAttributes;
        private RiskLimits riskLimits;
        
        // Constructor with required fields
        public Builder(String symbol, OrderSide side, BigDecimal quantity) {
            this.orderId = generateOrderId();
            this.symbol = symbol;
            this.side = side;
            this.quantity = quantity;
        }
        
        // Fluent setters for optional fields
        public Builder orderType(OrderType orderType) {
            this.orderType = orderType;
            return this;
        }
        
        public Builder limitPrice(BigDecimal limitPrice) {
            this.limitPrice = limitPrice;
            return this;
        }
        
        public Builder stopPrice(BigDecimal stopPrice) {
            this.stopPrice = stopPrice;
            return this;
        }
        
        public Builder timeInForce(TimeInForce timeInForce) {
            this.timeInForce = timeInForce;
            return this;
        }
        
        public Builder expiryTime(LocalDateTime expiryTime) {
            this.expiryTime = expiryTime;
            return this;
        }
        
        public Builder clientOrderId(String clientOrderId) {
            this.clientOrderId = clientOrderId;
            return this;
        }
        
        public Builder minQuantity(BigDecimal minQuantity) {
            this.minQuantity = minQuantity;
            return this;
        }
        
        public Builder displayQuantity(BigDecimal displayQuantity) {
            this.displayQuantity = displayQuantity;
            return this;
        }
        
        public Builder hidden(boolean isHidden) {
            this.isHidden = isHidden;
            return this;
        }
        
        public Builder customAttribute(String key, String value) {
            if (this.customAttributes == null) {
                this.customAttributes = new HashMap<>();
            }
            this.customAttributes.put(key, value);
            return this;
        }
        
        public Builder riskLimits(RiskLimits riskLimits) {
            this.riskLimits = riskLimits;
            return this;
        }
        
        // Build method creates the final object
        public TradingOrder build() {
            return new TradingOrder(this);
        }
        
        // Helper method for order ID generation
        private String generateOrderId() {
            return "ORD_" + System.currentTimeMillis() + "_" + 
                   ThreadLocalRandom.current().nextInt(1000, 9999);
        }
    }
    
    // Usage example methods
    public static TradingOrder createMarketOrder(String symbol, OrderSide side, BigDecimal quantity) {
        return new Builder(symbol, side, quantity)
            .orderType(OrderType.MARKET)
            .timeInForce(TimeInForce.IOC)
            .build();
    }
    
    public static TradingOrder createLimitOrder(String symbol, OrderSide side, 
                                              BigDecimal quantity, BigDecimal price) {
        return new Builder(symbol, side, quantity)
            .orderType(OrderType.LIMIT)
            .limitPrice(price)
            .timeInForce(TimeInForce.DAY)
            .build();
    }
    
    public static TradingOrder createIcebergOrder(String symbol, OrderSide side,
                                                BigDecimal totalQuantity, BigDecimal displayQuantity) {
        return new Builder(symbol, side, totalQuantity)
            .orderType(OrderType.LIMIT)
            .displayQuantity(displayQuantity)
            .hidden(true)
            .timeInForce(TimeInForce.GTC)
            .build();
    }
    
    // Validation methods
    private void validateOrderConstraints() {
        // Limit orders must have limit price
        if (orderType == OrderType.LIMIT && limitPrice == null) {
            throw new IllegalStateException("Limit orders must specify limit price");
        }
        
        // Stop orders must have stop price
        if (orderType == OrderType.STOP && stopPrice == null) {
            throw new IllegalStateException("Stop orders must specify stop price");
        }
        
        // Display quantity cannot exceed total quantity
        if (displayQuantity != null && displayQuantity.compareTo(quantity) > 0) {
            throw new IllegalStateException("Display quantity cannot exceed total quantity");
        }
        
        // GTC orders should have expiry time
        if (timeInForce == TimeInForce.GTC && expiryTime == null) {
            this.expiryTime = LocalDateTime.now().plusDays(30); // Default 30 days
        }
    }
    
    // Getters (omitted for brevity)
    public String getOrderId() { return orderId; }
    public String getSymbol() { return symbol; }
    public OrderSide getSide() { return side; }
    // ... other getters
}`,
        technologies: {
          'Validation': 'Bean Validation API integration',
          'Serialization': 'Jackson JSON serialization support',
          'Testing': 'Builder pattern simplifies unit testing',
          'Documentation': 'Self-documenting fluent interface'
        }
      },
      
      'strategy': {
        functions: [
          'Define family of algorithms and make them interchangeable',
          'Encapsulate algorithm implementation details',
          'Enable runtime algorithm selection',
          'Promote code reusability and maintainability',
          'Support open/closed principle extension',
          'Eliminate conditional statements for algorithm selection',
          'Allow algorithms to vary independently from clients',
          'Provide uniform interface for different strategies',
          'Support configuration-driven algorithm selection',
          'Enable A/B testing of different approaches'
        ],
        workflow: `1. Define strategy interface with common methods
2. Implement concrete strategies for each algorithm
3. Create context class that uses strategies
4. Provide mechanism to switch strategies at runtime
5. Initialize context with default strategy
6. Allow strategy injection via constructor or setter
7. Delegate algorithm execution to current strategy
8. Handle strategy-specific configuration
9. Support strategy composition if needed
10. Maintain strategy state isolation`,
        code: `// Strategy Pattern for Order Execution Algorithms
public interface OrderExecutionStrategy {
    
    // Main execution method
    ExecutionResult execute(TradingOrder order, MarketData marketData, OrderBook orderBook);
    
    // Strategy configuration
    void configure(StrategyConfig config);
    
    // Strategy metadata
    String getStrategyName();
    Set<OrderType> getSupportedOrderTypes();
    boolean supportsSymbol(String symbol);
}

// Market Order Execution Strategy
@Component("marketExecution")
public class MarketExecutionStrategy implements OrderExecutionStrategy {
    
    private final OrderBookService orderBookService;
    private final TradeExecutor tradeExecutor;
    private final RiskManager riskManager;
    private StrategyConfig config;
    
    public MarketExecutionStrategy(OrderBookService orderBookService,
                                 TradeExecutor tradeExecutor,
                                 RiskManager riskManager) {
        this.orderBookService = orderBookService;
        this.tradeExecutor = tradeExecutor;
        this.riskManager = riskManager;
    }
    
    @Override
    public ExecutionResult execute(TradingOrder order, MarketData marketData, OrderBook orderBook) {
        log.info("Executing market order: {} for {}", order.getOrderId(), order.getSymbol());
        
        try {
            // Pre-execution risk checks
            RiskCheckResult riskResult = riskManager.validateOrder(order);
            if (!riskResult.isApproved()) {
                return ExecutionResult.rejected(riskResult.getReason());
            }
            
            // Find best available prices
            BigDecimal executionPrice;
            if (order.getSide() == OrderSide.BUY) {
                executionPrice = orderBook.getBestAsk();
                if (executionPrice == null) {
                    return ExecutionResult.failed("No asks available");
                }
            } else {
                executionPrice = orderBook.getBestBid();
                if (executionPrice == null) {
                    return ExecutionResult.failed("No bids available");
                }
            }
            
            // Check market impact
            BigDecimal availableQuantity = getAvailableQuantity(orderBook, order.getSide());
            if (order.getQuantity().compareTo(availableQuantity) > 0) {
                // Partial fill scenario
                return executePartialFill(order, availableQuantity, executionPrice, orderBook);
            }
            
            // Execute full order
            Trade trade = tradeExecutor.executeTrade(order, executionPrice, order.getQuantity());
            
            // Post-execution processing
            updateOrderBook(orderBook, trade);
            publishTradeEvent(trade);
            
            return ExecutionResult.success(trade);
            
        } catch (Exception e) {
            log.error("Market execution failed for order {}: {}", order.getOrderId(), e.getMessage());
            return ExecutionResult.failed("Execution error: " + e.getMessage());
        }
    }
    
    @Override
    public void configure(StrategyConfig config) {
        this.config = config;
        log.info("Market execution strategy configured: {}", config);
    }
    
    @Override
    public String getStrategyName() {
        return "MARKET_EXECUTION";
    }
    
    @Override
    public Set<OrderType> getSupportedOrderTypes() {
        return EnumSet.of(OrderType.MARKET);
    }
    
    @Override
    public boolean supportsSymbol(String symbol) {
        return true; // Market strategy supports all symbols
    }
    
    private ExecutionResult executePartialFill(TradingOrder order, BigDecimal availableQty, 
                                             BigDecimal price, OrderBook orderBook) {
        // Implementation for partial fills
        Trade partialTrade = tradeExecutor.executeTrade(order, price, availableQty);
        
        // Create remaining order
        TradingOrder remainingOrder = order.createRemainingOrder(
            order.getQuantity().subtract(availableQty)
        );
        
        return ExecutionResult.partialFill(partialTrade, remainingOrder);
    }
}

// TWAP (Time-Weighted Average Price) Strategy
@Component("twapExecution")
public class TWAPExecutionStrategy implements OrderExecutionStrategy {
    
    private final ScheduledExecutorService scheduler;
    private final MarketDataService marketDataService;
    private final OrderExecutionService executionService;
    private StrategyConfig config;
    
    @Override
    public ExecutionResult execute(TradingOrder order, MarketData marketData, OrderBook orderBook) {
        log.info("Executing TWAP strategy for order: {}", order.getOrderId());
        
        try {
            // Calculate TWAP parameters
            TWAPParameters params = calculateTWAPParameters(order, config);
            
            // Create execution schedule
            List<OrderSlice> slices = createOrderSlices(order, params);
            
            // Start scheduled execution
            TWAPExecutionContext context = new TWAPExecutionContext(order, slices);
            scheduleSliceExecution(context);
            
            return ExecutionResult.inProgress(context);
            
        } catch (Exception e) {
            log.error("TWAP execution setup failed: {}", e.getMessage());
            return ExecutionResult.failed("TWAP setup error: " + e.getMessage());
        }
    }
    
    private TWAPParameters calculateTWAPParameters(TradingOrder order, StrategyConfig config) {
        Duration executionWindow = config.getDuration("execution_window", Duration.ofHours(1));
        int numberOfSlices = config.getInt("number_of_slices", 10);
        Duration sliceInterval = executionWindow.dividedBy(numberOfSlices);
        
        BigDecimal sliceSize = order.getQuantity().divide(
            BigDecimal.valueOf(numberOfSlices), 
            RoundingMode.HALF_UP
        );
        
        return new TWAPParameters(executionWindow, numberOfSlices, sliceInterval, sliceSize);
    }
    
    private void scheduleSliceExecution(TWAPExecutionContext context) {
        scheduler.scheduleAtFixedRate(
            () -> executeNextSlice(context),
            0,
            context.getParams().getSliceInterval().toMillis(),
            TimeUnit.MILLISECONDS
        );
    }
    
    @Override
    public String getStrategyName() {
        return "TWAP_EXECUTION";
    }
    
    @Override
    public Set<OrderType> getSupportedOrderTypes() {
        return EnumSet.of(OrderType.LIMIT, OrderType.MARKET);
    }
}

// VWAP (Volume-Weighted Average Price) Strategy
@Component("vwapExecution")
public class VWAPExecutionStrategy implements OrderExecutionStrategy {
    
    private final VolumeProfileService volumeProfileService;
    private final MarketDataService marketDataService;
    
    @Override
    public ExecutionResult execute(TradingOrder order, MarketData marketData, OrderBook orderBook) {
        log.info("Executing VWAP strategy for order: {}", order.getOrderId());
        
        // Get historical volume profile
        VolumeProfile profile = volumeProfileService.getVolumeProfile(
            order.getSymbol(), 
            config.getDuration("lookback_period", Duration.ofDays(20))
        );
        
        // Calculate VWAP-weighted slices
        List<VWAPSlice> vwapSlices = calculateVWAPSlices(order, profile);
        
        // Execute based on volume participation rate
        return executeVWAPSlices(order, vwapSlices, orderBook);
    }
    
    @Override
    public String getStrategyName() {
        return "VWAP_EXECUTION";
    }
}

// Context class that uses strategies
@Service
public class OrderExecutionEngine {
    
    private final Map<String, OrderExecutionStrategy> strategies;
    private final StrategySelector strategySelector;
    private OrderExecutionStrategy defaultStrategy;
    
    public OrderExecutionEngine(List<OrderExecutionStrategy> strategyList,
                              StrategySelector strategySelector) {
        this.strategies = strategyList.stream()
            .collect(Collectors.toMap(
                OrderExecutionStrategy::getStrategyName,
                Function.identity()
            ));
        this.strategySelector = strategySelector;
        this.defaultStrategy = strategies.get("MARKET_EXECUTION");
    }
    
    public ExecutionResult executeOrder(TradingOrder order) {
        // Select appropriate strategy
        OrderExecutionStrategy strategy = selectStrategy(order);
        
        // Get current market data
        MarketData marketData = getMarketData(order.getSymbol());
        OrderBook orderBook = getOrderBook(order.getSymbol());
        
        // Execute using selected strategy
        return strategy.execute(order, marketData, orderBook);
    }
    
    private OrderExecutionStrategy selectStrategy(TradingOrder order) {
        String strategyName = strategySelector.selectStrategy(order);
        return strategies.getOrDefault(strategyName, defaultStrategy);
    }
    
    public void setExecutionStrategy(String strategyName) {
        OrderExecutionStrategy strategy = strategies.get(strategyName);
        if (strategy != null) {
            this.defaultStrategy = strategy;
        }
    }
}`,
        technologies: {
          'Spring Framework': 'Dependency injection for strategy management',
          'Scheduled Execution': 'ScheduledExecutorService for time-based strategies',
          'Configuration': 'External strategy configuration via properties',
          'Monitoring': 'Strategy performance metrics and alerting'
        }
      },

      'adapter': {
        functions: [
          'Convert interface of class to another interface clients expect',
          'Enable incompatible interfaces to work together',
          'Wrap existing class with new interface',
          'Integrate third-party libraries seamlessly',
          'Support legacy system integration',
          'Provide unified interface for different implementations',
          'Enable code reuse without modification',
          'Support multiple data source integration',
          'Translate between different protocols',
          'Maintain backward compatibility'
        ],
        workflow: `1. Identify incompatible interfaces that need integration
2. Define target interface that client expects
3. Create adapter class implementing target interface
4. Wrap existing class (adaptee) within adapter
5. Implement interface methods by delegating to adaptee
6. Handle data transformation between interfaces
7. Manage error conditions and edge cases
8. Test adapter with both interfaces
9. Document interface mappings and limitations
10. Deploy adapter as bridge between systems`,
        code: `// Adapter Pattern for Market Data Integration
// Target interface expected by trading system
public interface MarketDataFeed {
    MarketData getLatestPrice(String symbol);
    List<MarketData> getHistoricalData(String symbol, LocalDateTime from, LocalDateTime to);
    void subscribe(String symbol, MarketDataListener listener);
    void unsubscribe(String symbol, MarketDataListener listener);
}

// Third-party Bloomberg API (Adaptee)
public class BloombergAPI {
    public BloombergQuote getCurrentQuote(String ticker) {
        // Bloomberg-specific implementation
        return bloombergService.getQuote(ticker);
    }
    
    public List<BloombergHistoricalData> getHistory(String ticker, String startDate, String endDate) {
        return bloombergService.getHistoricalData(ticker, startDate, endDate);
    }
    
    public void registerCallback(String ticker, BloombergCallback callback) {
        bloombergService.subscribe(ticker, callback);
    }
}

// Adapter that makes Bloomberg API compatible with our interface
@Component
public class BloombergMarketDataAdapter implements MarketDataFeed {
    
    private final BloombergAPI bloombergAPI;
    private final SymbolMapper symbolMapper;
    private final DataConverter dataConverter;
    private final Map<String, Set<MarketDataListener>> listeners;
    private final Map<String, BloombergCallback> bloombergCallbacks;
    
    public BloombergMarketDataAdapter(BloombergAPI bloombergAPI,
                                    SymbolMapper symbolMapper,
                                    DataConverter dataConverter) {
        this.bloombergAPI = bloombergAPI;
        this.symbolMapper = symbolMapper;
        this.dataConverter = dataConverter;
        this.listeners = new ConcurrentHashMap<>();
        this.bloombergCallbacks = new ConcurrentHashMap<>();
    }
    
    @Override
    public MarketData getLatestPrice(String symbol) {
        try {
            // Convert our symbol format to Bloomberg format
            String bloombergTicker = symbolMapper.toBloombergTicker(symbol);
            
            // Get data from Bloomberg
            BloombergQuote quote = bloombergAPI.getCurrentQuote(bloombergTicker);
            
            // Convert Bloomberg data to our format
            return dataConverter.convertQuote(quote, symbol);
            
        } catch (BloombergException e) {
            log.error("Failed to get Bloomberg data for symbol {}: {}", symbol, e.getMessage());
            throw new MarketDataException("Bloomberg data unavailable", e);
        }
    }
    
    @Override
    public List<MarketData> getHistoricalData(String symbol, LocalDateTime from, LocalDateTime to) {
        try {
            String bloombergTicker = symbolMapper.toBloombergTicker(symbol);
            
            // Convert date format
            String startDate = DateTimeFormatter.ISO_LOCAL_DATE.format(from);
            String endDate = DateTimeFormatter.ISO_LOCAL_DATE.format(to);
            
            // Get Bloomberg historical data
            List<BloombergHistoricalData> bloombergData = 
                bloombergAPI.getHistory(bloombergTicker, startDate, endDate);
            
            // Convert to our format
            return bloombergData.stream()
                .map(data -> dataConverter.convertHistoricalData(data, symbol))
                .collect(Collectors.toList());
                
        } catch (BloombergException e) {
            log.error("Failed to get Bloomberg historical data: {}", e.getMessage());
            throw new MarketDataException("Bloomberg historical data unavailable", e);
        }
    }
    
    @Override
    public void subscribe(String symbol, MarketDataListener listener) {
        listeners.computeIfAbsent(symbol, k -> ConcurrentHashMap.newKeySet())
                .add(listener);
        
        // Create Bloomberg callback if not exists
        if (!bloombergCallbacks.containsKey(symbol)) {
            String bloombergTicker = symbolMapper.toBloombergTicker(symbol);
            
            BloombergCallback callback = new BloombergCallback() {
                @Override
                public void onPriceUpdate(BloombergQuote quote) {
                    MarketData marketData = dataConverter.convertQuote(quote, symbol);
                    
                    // Notify all our listeners
                    Set<MarketDataListener> symbolListeners = listeners.get(symbol);
                    if (symbolListeners != null) {
                        symbolListeners.forEach(listener -> {
                            try {
                                listener.onMarketData(marketData);
                            } catch (Exception e) {
                                log.error("Error notifying listener: {}", e.getMessage());
                            }
                        });
                    }
                }
                
                @Override
                public void onError(BloombergError error) {
                    MarketDataException exception = new MarketDataException(error.getMessage());
                    
                    Set<MarketDataListener> symbolListeners = listeners.get(symbol);
                    if (symbolListeners != null) {
                        symbolListeners.forEach(listener -> listener.onError(exception));
                    }
                }
            };
            
            bloombergCallbacks.put(symbol, callback);
            bloombergAPI.registerCallback(bloombergTicker, callback);
        }
    }
    
    @Override
    public void unsubscribe(String symbol, MarketDataListener listener) {
        Set<MarketDataListener> symbolListeners = listeners.get(symbol);
        if (symbolListeners != null) {
            symbolListeners.remove(listener);
            
            // If no more listeners, unsubscribe from Bloomberg
            if (symbolListeners.isEmpty()) {
                bloombergCallbacks.remove(symbol);
                // Bloomberg unsubscribe logic would go here
            }
        }
    }
}

// Data converter helper class
@Component
public class DataConverter {
    
    public MarketData convertQuote(BloombergQuote bloombergQuote, String symbol) {
        return MarketData.builder()
            .symbol(symbol)
            .lastPrice(bloombergQuote.getLast())
            .bidPrice(bloombergQuote.getBid())
            .askPrice(bloombergQuote.getAsk())
            .bidSize(bloombergQuote.getBidSize())
            .askSize(bloombergQuote.getAskSize())
            .volume(bloombergQuote.getVolume())
            .timestamp(convertTimestamp(bloombergQuote.getTimestamp()))
            .exchange(bloombergQuote.getExchange())
            .currency(bloombergQuote.getCurrency())
            .build();
    }
    
    public MarketData convertHistoricalData(BloombergHistoricalData data, String symbol) {
        return MarketData.builder()
            .symbol(symbol)
            .lastPrice(data.getClose())
            .highPrice(data.getHigh())
            .lowPrice(data.getLow())
            .openPrice(data.getOpen())
            .volume(data.getVolume())
            .timestamp(convertDate(data.getDate()))
            .build();
    }
    
    private LocalDateTime convertTimestamp(long bloombergTimestamp) {
        return Instant.ofEpochMilli(bloombergTimestamp)
                     .atZone(ZoneId.systemDefault())
                     .toLocalDateTime();
    }
}

// Multiple adapters for different data sources
@Component
public class ReutersMarketDataAdapter implements MarketDataFeed {
    // Similar implementation for Reuters API
}

@Component
public class ICEMarketDataAdapter implements MarketDataFeed {
    // Similar implementation for ICE API
}

// Composite adapter that aggregates multiple sources
@Component
public class AggregatedMarketDataAdapter implements MarketDataFeed {
    
    private final List<MarketDataFeed> adapters;
    private final DataAggregationStrategy aggregationStrategy;
    
    public AggregatedMarketDataAdapter(List<MarketDataFeed> adapters,
                                     DataAggregationStrategy aggregationStrategy) {
        this.adapters = adapters;
        this.aggregationStrategy = aggregationStrategy;
    }
    
    @Override
    public MarketData getLatestPrice(String symbol) {
        List<MarketData> prices = adapters.parallelStream()
            .map(adapter -> {
                try {
                    return adapter.getLatestPrice(symbol);
                } catch (Exception e) {
                    log.warn("Adapter failed for symbol {}: {}", symbol, e.getMessage());
                    return null;
                }
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
            
        return aggregationStrategy.aggregate(prices);
    }
}`,
        technologies: {
          'Spring Integration': 'Message adapters for different protocols',
          'Jackson': 'JSON/XML data format adaptation',
          'Apache Camel': 'Enterprise integration patterns',
          'Circuit Breaker': 'Resilience patterns for adapter failures'
        }
      },

      'decorator': {
        functions: [
          'Add behavior to objects dynamically without altering structure',
          'Provide flexible alternative to subclassing',
          'Compose behaviors by wrapping objects',
          'Support multiple decorations on single object',
          'Maintain single responsibility principle',
          'Enable feature toggles and conditional behavior',
          'Support runtime behavior modification',
          'Create layered functionality',
          'Implement cross-cutting concerns',
          'Preserve original interface contract'
        ],
        workflow: `1. Define component interface for base functionality
2. Create concrete component implementing base behavior
3. Create abstract decorator implementing same interface
4. Implement concrete decorators extending abstract decorator
5. Each decorator wraps component and adds functionality
6. Preserve interface contract in all decorators
7. Support decorator chaining for multiple behaviors
8. Handle method delegation to wrapped component
9. Manage decorator order for correct behavior
10. Test all decorator combinations`,
        code: `// Decorator Pattern for Trade Order Processing
// Component interface
public interface TradeProcessor {
    ProcessingResult process(Trade trade);
    boolean canProcess(Trade trade);
}

// Concrete component - basic trade processing
@Component
public class BasicTradeProcessor implements TradeProcessor {
    
    private final TradeRepository tradeRepository;
    private final PositionService positionService;
    
    public BasicTradeProcessor(TradeRepository tradeRepository,
                             PositionService positionService) {
        this.tradeRepository = tradeRepository;
        this.positionService = positionService;
    }
    
    @Override
    public ProcessingResult process(Trade trade) {
        log.info("Processing trade: {}", trade.getId());
        
        try {
            // Basic trade processing
            validateTrade(trade);
            persistTrade(trade);
            updatePositions(trade);
            
            return ProcessingResult.success(trade);
            
        } catch (Exception e) {
            log.error("Trade processing failed: {}", e.getMessage());
            return ProcessingResult.failure(e.getMessage());
        }
    }
    
    @Override
    public boolean canProcess(Trade trade) {
        return trade != null && trade.isValid();
    }
    
    private void validateTrade(Trade trade) {
        if (trade.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Invalid trade quantity");
        }
    }
    
    private void persistTrade(Trade trade) {
        trade.setStatus(TradeStatus.PROCESSED);
        trade.setProcessedAt(LocalDateTime.now());
        tradeRepository.save(trade);
    }
    
    private void updatePositions(Trade trade) {
        positionService.updatePosition(trade);
    }
}

// Abstract decorator
public abstract class TradeProcessorDecorator implements TradeProcessor {
    
    protected final TradeProcessor delegate;
    
    public TradeProcessorDecorator(TradeProcessor delegate) {
        this.delegate = delegate;
    }
    
    @Override
    public ProcessingResult process(Trade trade) {
        return delegate.process(trade);
    }
    
    @Override
    public boolean canProcess(Trade trade) {
        return delegate.canProcess(trade);
    }
}

// Risk validation decorator
@Component
public class RiskValidationDecorator extends TradeProcessorDecorator {
    
    private final RiskManager riskManager;
    private final RiskLimits riskLimits;
    
    public RiskValidationDecorator(TradeProcessor delegate,
                                 RiskManager riskManager,
                                 RiskLimits riskLimits) {
        super(delegate);
        this.riskManager = riskManager;
        this.riskLimits = riskLimits;
    }
    
    @Override
    public ProcessingResult process(Trade trade) {
        log.info("Applying risk validation to trade: {}", trade.getId());
        
        // Pre-processing risk checks
        RiskAssessment riskAssessment = riskManager.assessTrade(trade);
        
        if (riskAssessment.exceedsLimits(riskLimits)) {
            log.warn("Trade {} exceeds risk limits: {}", trade.getId(), riskAssessment);
            return ProcessingResult.rejected("Risk limits exceeded: " + riskAssessment.getReason());
        }
        
        if (riskAssessment.getConfidenceLevel() < 0.8) {
            log.warn("Trade {} has low confidence: {}", trade.getId(), riskAssessment.getConfidenceLevel());
            return ProcessingResult.warning("Low confidence trade", delegate.process(trade));
        }
        
        // Risk checks passed, proceed with processing
        ProcessingResult result = super.process(trade);
        
        // Post-processing risk updates
        if (result.isSuccess()) {
            riskManager.updateRiskMetrics(trade);
        }
        
        return result;
    }
}

// Compliance validation decorator
@Component
public class ComplianceValidationDecorator extends TradeProcessorDecorator {
    
    private final ComplianceEngine complianceEngine;
    private final AuditLogger auditLogger;
    
    public ComplianceValidationDecorator(TradeProcessor delegate,
                                       ComplianceEngine complianceEngine,
                                       AuditLogger auditLogger) {
        super(delegate);
        this.complianceEngine = complianceEngine;
        this.auditLogger = auditLogger;
    }
    
    @Override
    public ProcessingResult process(Trade trade) {
        log.info("Applying compliance validation to trade: {}", trade.getId());
        
        // Compliance pre-checks
        ComplianceResult complianceResult = complianceEngine.validateTrade(trade);
        
        auditLogger.logComplianceCheck(trade, complianceResult);
        
        if (!complianceResult.isCompliant()) {
            log.error("Trade {} failed compliance: {}", trade.getId(), complianceResult.getViolations());
            return ProcessingResult.rejected("Compliance violation: " + complianceResult.getViolations());
        }
        
        // Mark trade for regulatory reporting if needed
        if (complianceResult.requiresReporting()) {
            trade.addFlag(TradeFlag.REGULATORY_REPORTING);
        }
        
        return super.process(trade);
    }
}

// Performance monitoring decorator
@Component
public class PerformanceMonitoringDecorator extends TradeProcessorDecorator {
    
    private final MetricsRegistry metricsRegistry;
    private final Timer processingTimer;
    private final Counter successCounter;
    private final Counter failureCounter;
    
    public PerformanceMonitoringDecorator(TradeProcessor delegate,
                                        MetricsRegistry metricsRegistry) {
        super(delegate);
        this.metricsRegistry = metricsRegistry;
        this.processingTimer = metricsRegistry.timer("trade.processing.time");
        this.successCounter = metricsRegistry.counter("trade.processing.success");
        this.failureCounter = metricsRegistry.counter("trade.processing.failure");
    }
    
    @Override
    public ProcessingResult process(Trade trade) {
        Timer.Context context = processingTimer.time();
        
        try {
            log.debug("Starting performance monitoring for trade: {}", trade.getId());
            
            long startTime = System.nanoTime();
            ProcessingResult result = super.process(trade);
            long endTime = System.nanoTime();
            
            // Record metrics
            long processingTimeNanos = endTime - startTime;
            metricsRegistry.histogram("trade.processing.latency")
                          .update(processingTimeNanos / 1_000_000); // Convert to milliseconds
            
            if (result.isSuccess()) {
                successCounter.inc();
            } else {
                failureCounter.inc();
            }
            
            // Log performance details
            log.debug("Trade {} processed in {}ms with result: {}", 
                     trade.getId(), 
                     processingTimeNanos / 1_000_000, 
                     result.getStatus());
                     
            return result;
            
        } finally {
            context.stop();
        }
    }
}

// Caching decorator
@Component
public class CachingDecorator extends TradeProcessorDecorator {
    
    private final Cache<String, ProcessingResult> resultCache;
    private final TradeHashCalculator hashCalculator;
    
    public CachingDecorator(TradeProcessor delegate,
                          Cache<String, ProcessingResult> resultCache,
                          TradeHashCalculator hashCalculator) {
        super(delegate);
        this.resultCache = resultCache;
        this.hashCalculator = hashCalculator;
    }
    
    @Override
    public ProcessingResult process(Trade trade) {
        String cacheKey = hashCalculator.calculateHash(trade);
        
        // Check cache first
        ProcessingResult cachedResult = resultCache.getIfPresent(cacheKey);
        if (cachedResult != null) {
            log.debug("Cache hit for trade hash: {}", cacheKey);
            return cachedResult.withTradeId(trade.getId()); // Update with current trade ID
        }
        
        // Process and cache result
        ProcessingResult result = super.process(trade);
        
        if (result.isSuccess() && trade.isCacheable()) {
            resultCache.put(cacheKey, result);
            log.debug("Cached result for trade hash: {}", cacheKey);
        }
        
        return result;
    }
}

// Configuration-driven decorator factory
@Configuration
public class TradeProcessorConfig {
    
    @Bean
    @ConditionalOnProperty("trade.processing.decorators.enabled")
    public TradeProcessor configuredTradeProcessor(
            BasicTradeProcessor basicProcessor,
            @Autowired(required = false) RiskManager riskManager,
            @Autowired(required = false) ComplianceEngine complianceEngine,
            @Autowired(required = false) MetricsRegistry metricsRegistry,
            TradeProcessingProperties properties) {
        
        TradeProcessor processor = basicProcessor;
        
        // Apply decorators based on configuration
        if (properties.isRiskValidationEnabled() && riskManager != null) {
            processor = new RiskValidationDecorator(processor, riskManager, properties.getRiskLimits());
        }
        
        if (properties.isComplianceValidationEnabled() && complianceEngine != null) {
            processor = new ComplianceValidationDecorator(processor, complianceEngine, auditLogger);
        }
        
        if (properties.isPerformanceMonitoringEnabled() && metricsRegistry != null) {
            processor = new PerformanceMonitoringDecorator(processor, metricsRegistry);
        }
        
        if (properties.isCachingEnabled()) {
            Cache<String, ProcessingResult> cache = Caffeine.newBuilder()
                .maximumSize(properties.getCacheSize())
                .expireAfterWrite(properties.getCacheDuration())
                .build();
            processor = new CachingDecorator(processor, cache, new TradeHashCalculator());
        }
        
        return processor;
    }
}`,
        technologies: {
          'Spring AOP': 'Aspect-oriented programming for cross-cutting concerns',
          'Caffeine Cache': 'High-performance caching decorator',
          'Micrometer': 'Application metrics in monitoring decorators',
          'Configuration Properties': 'Dynamic decorator composition'
        }
      },

      'command': {
        functions: [
          'Encapsulate request as object with all information needed',
          'Parameterize clients with different requests',
          'Queue operations for later execution',
          'Support undo and redo operations',
          'Log operations for audit trails',
          'Support macro recording and playback',
          'Enable transactional behavior',
          'Decouple invoker from receiver',
          'Support batch operation processing',
          'Implement distributed command execution'
        ],
        workflow: `1. Define command interface with execute method
2. Create concrete commands implementing interface
3. Encapsulate receiver and parameters in command
4. Create invoker to execute commands
5. Support command queuing and scheduling
6. Implement undo functionality if needed
7. Add command logging and audit trails
8. Support command composition and macros
9. Handle command failures and rollback
10. Provide command history and replay capabilities`,
        code: `// Command Pattern for Order Management System
// Command interface
public interface OrderCommand {
    CommandResult execute();
    CommandResult undo();
    String getCommandType();
    String getDescription();
    LocalDateTime getTimestamp();
}

// Abstract base command with common functionality
public abstract class AbstractOrderCommand implements OrderCommand {
    
    protected final String commandId;
    protected final LocalDateTime timestamp;
    protected final AuditLogger auditLogger;
    
    public AbstractOrderCommand(AuditLogger auditLogger) {
        this.commandId = UUID.randomUUID().toString();
        this.timestamp = LocalDateTime.now();
        this.auditLogger = auditLogger;
    }
    
    @Override
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    protected void logCommand(String action, String details) {
        auditLogger.log(AuditEvent.builder()
            .commandId(commandId)
            .commandType(getCommandType())
            .action(action)
            .details(details)
            .timestamp(timestamp)
            .build());
    }
}

// Concrete command - Create Order
public class CreateOrderCommand extends AbstractOrderCommand {
    
    private final OrderService orderService;
    private final TradingOrder order;
    private String createdOrderId;
    
    public CreateOrderCommand(OrderService orderService, 
                            TradingOrder order,
                            AuditLogger auditLogger) {
        super(auditLogger);
        this.orderService = orderService;
        this.order = order;
    }
    
    @Override
    public CommandResult execute() {
        try {
            logCommand("EXECUTE", "Creating order: " + order.getSymbol());
            
            // Execute the order creation
            OrderCreationResult result = orderService.createOrder(order);
            this.createdOrderId = result.getOrderId();
            
            logCommand("SUCCESS", "Order created with ID: " + createdOrderId);
            return CommandResult.success("Order created successfully", createdOrderId);
            
        } catch (Exception e) {
            logCommand("ERROR", "Order creation failed: " + e.getMessage());
            return CommandResult.failure("Order creation failed: " + e.getMessage());
        }
    }
    
    @Override
    public CommandResult undo() {
        if (createdOrderId == null) {
            return CommandResult.failure("Cannot undo: No order was created");
        }
        
        try {
            logCommand("UNDO", "Cancelling order: " + createdOrderId);
            
            CancelOrderCommand cancelCommand = new CancelOrderCommand(
                orderService, createdOrderId, "Undo create operation", auditLogger
            );
            
            CommandResult cancelResult = cancelCommand.execute();
            
            if (cancelResult.isSuccess()) {
                logCommand("UNDO_SUCCESS", "Order creation undone: " + createdOrderId);
                this.createdOrderId = null;
            }
            
            return cancelResult;
            
        } catch (Exception e) {
            logCommand("UNDO_ERROR", "Failed to undo order creation: " + e.getMessage());
            return CommandResult.failure("Undo failed: " + e.getMessage());
        }
    }
    
    @Override
    public String getCommandType() {
        return "CREATE_ORDER";
    }
    
    @Override
    public String getDescription() {
        return String.format("Create %s order for %s %s @ %s", 
                           order.getOrderType(),
                           order.getSide(),
                           order.getQuantity(),
                           order.getLimitPrice());
    }
}

// Concrete command - Cancel Order
public class CancelOrderCommand extends AbstractOrderCommand {
    
    private final OrderService orderService;
    private final String orderId;
    private final String reason;
    private TradingOrder originalOrder;
    
    public CancelOrderCommand(OrderService orderService,
                            String orderId,
                            String reason,
                            AuditLogger auditLogger) {
        super(auditLogger);
        this.orderService = orderService;
        this.orderId = orderId;
        this.reason = reason;
    }
    
    @Override
    public CommandResult execute() {
        try {
            logCommand("EXECUTE", "Cancelling order: " + orderId + ", reason: " + reason);
            
            // Store original order for undo
            this.originalOrder = orderService.getOrder(orderId);
            
            // Execute cancellation
            CancellationResult result = orderService.cancelOrder(orderId, reason);
            
            logCommand("SUCCESS", "Order cancelled: " + orderId);
            return CommandResult.success("Order cancelled successfully", orderId);
            
        } catch (Exception e) {
            logCommand("ERROR", "Order cancellation failed: " + e.getMessage());
            return CommandResult.failure("Cancellation failed: " + e.getMessage());
        }
    }
    
    @Override
    public CommandResult undo() {
        if (originalOrder == null) {
            return CommandResult.failure("Cannot undo: Original order not stored");
        }
        
        try {
            logCommand("UNDO", "Recreating cancelled order: " + orderId);
            
            // Recreate the order (if it wasn't executed)
            CreateOrderCommand recreateCommand = new CreateOrderCommand(
                orderService, originalOrder, auditLogger
            );
            
            return recreateCommand.execute();
            
        } catch (Exception e) {
            logCommand("UNDO_ERROR", "Failed to undo cancellation: " + e.getMessage());
            return CommandResult.failure("Undo failed: " + e.getMessage());
        }
    }
    
    @Override
    public String getCommandType() {
        return "CANCEL_ORDER";
    }
    
    @Override
    public String getDescription() {
        return "Cancel order " + orderId + ": " + reason;
    }
}

// Concrete command - Modify Order
public class ModifyOrderCommand extends AbstractOrderCommand {
    
    private final OrderService orderService;
    private final String orderId;
    private final OrderModification modification;
    private TradingOrder originalOrder;
    
    public ModifyOrderCommand(OrderService orderService,
                            String orderId,
                            OrderModification modification,
                            AuditLogger auditLogger) {
        super(auditLogger);
        this.orderService = orderService;
        this.orderId = orderId;
        this.modification = modification;
    }
    
    @Override
    public CommandResult execute() {
        try {
            logCommand("EXECUTE", "Modifying order: " + orderId + ", changes: " + modification);
            
            // Store original for undo
            this.originalOrder = orderService.getOrder(orderId);
            
            // Execute modification
            ModificationResult result = orderService.modifyOrder(orderId, modification);
            
            logCommand("SUCCESS", "Order modified: " + orderId);
            return CommandResult.success("Order modified successfully", orderId);
            
        } catch (Exception e) {
            logCommand("ERROR", "Order modification failed: " + e.getMessage());
            return CommandResult.failure("Modification failed: " + e.getMessage());
        }
    }
    
    @Override
    public CommandResult undo() {
        if (originalOrder == null) {
            return CommandResult.failure("Cannot undo: Original order not stored");
        }
        
        try {
            // Create reverse modification to restore original state
            OrderModification reverseModification = createReverseModification(originalOrder, modification);
            
            ModifyOrderCommand reverseCommand = new ModifyOrderCommand(
                orderService, orderId, reverseModification, auditLogger
            );
            
            return reverseCommand.execute();
            
        } catch (Exception e) {
            logCommand("UNDO_ERROR", "Failed to undo modification: " + e.getMessage());
            return CommandResult.failure("Undo failed: " + e.getMessage());
        }
    }
    
    @Override
    public String getCommandType() {
        return "MODIFY_ORDER";
    }
    
    @Override
    public String getDescription() {
        return "Modify order " + orderId + ": " + modification.getDescription();
    }
}

// Macro command for batch operations
public class BatchOrderCommand extends AbstractOrderCommand {
    
    private final List<OrderCommand> commands;
    private final List<CommandResult> executedResults;
    
    public BatchOrderCommand(List<OrderCommand> commands, AuditLogger auditLogger) {
        super(auditLogger);
        this.commands = new ArrayList<>(commands);
        this.executedResults = new ArrayList<>();
    }
    
    @Override
    public CommandResult execute() {
        logCommand("EXECUTE", "Executing batch of " + commands.size() + " commands");
        
        List<String> failures = new ArrayList<>();
        
        for (int i = 0; i < commands.size(); i++) {
            OrderCommand command = commands.get(i);
            
            try {
                CommandResult result = command.execute();
                executedResults.add(result);
                
                if (!result.isSuccess()) {
                    failures.add("Command " + i + ": " + result.getMessage());
                }
                
            } catch (Exception e) {
                CommandResult errorResult = CommandResult.failure("Execution error: " + e.getMessage());
                executedResults.add(errorResult);
                failures.add("Command " + i + ": " + e.getMessage());
            }
        }
        
        if (failures.isEmpty()) {
            logCommand("SUCCESS", "Batch execution completed successfully");
            return CommandResult.success("All commands executed successfully", 
                                       String.valueOf(commands.size()));
        } else {
            logCommand("PARTIAL_SUCCESS", "Batch completed with failures: " + failures);
            return CommandResult.partialSuccess("Batch completed with some failures", failures);
        }
    }
    
    @Override
    public CommandResult undo() {
        logCommand("UNDO", "Undoing batch of " + executedResults.size() + " commands");
        
        List<String> undoFailures = new ArrayList<>();
        
        // Undo in reverse order
        for (int i = executedResults.size() - 1; i >= 0; i--) {
            if (executedResults.get(i).isSuccess()) {
                try {
                    CommandResult undoResult = commands.get(i).undo();
                    if (!undoResult.isSuccess()) {
                        undoFailures.add("Undo " + i + ": " + undoResult.getMessage());
                    }
                } catch (Exception e) {
                    undoFailures.add("Undo " + i + ": " + e.getMessage());
                }
            }
        }
        
        executedResults.clear();
        
        if (undoFailures.isEmpty()) {
            logCommand("UNDO_SUCCESS", "Batch undo completed successfully");
            return CommandResult.success("All commands undone successfully", 
                                       String.valueOf(commands.size()));
        } else {
            logCommand("UNDO_PARTIAL", "Batch undo completed with failures: " + undoFailures);
            return CommandResult.partialSuccess("Batch undo completed with some failures", undoFailures);
        }
    }
    
    @Override
    public String getCommandType() {
        return "BATCH_ORDER_COMMAND";
    }
    
    @Override
    public String getDescription() {
        return "Batch of " + commands.size() + " order commands";
    }
}

// Command invoker with queue and history
@Service
public class OrderCommandProcessor {
    
    private final BlockingQueue<OrderCommand> commandQueue;
    private final List<OrderCommand> commandHistory;
    private final ExecutorService executorService;
    private final CommandResultPublisher resultPublisher;
    private final int maxHistorySize;
    
    public OrderCommandProcessor(CommandResultPublisher resultPublisher,
                               @Value("\${order.command.history.max-size:1000}") int maxHistorySize) {
        this.commandQueue = new LinkedBlockingQueue<>();
        this.commandHistory = Collections.synchronizedList(new ArrayList<>());
        this.executorService = Executors.newFixedThreadPool(10);
        this.resultPublisher = resultPublisher;
        this.maxHistorySize = maxHistorySize;
        
        startCommandProcessor();
    }
    
    public void submitCommand(OrderCommand command) {
        commandQueue.offer(command);
    }
    
    public CompletableFuture<CommandResult> executeCommand(OrderCommand command) {
        return CompletableFuture.supplyAsync(() -> {
            CommandResult result = command.execute();
            
            // Add to history if successful
            if (result.isSuccess()) {
                addToHistory(command);
            }
            
            // Publish result
            resultPublisher.publish(command, result);
            
            return result;
        }, executorService);
    }
    
    public CommandResult undoLastCommand() {
        synchronized (commandHistory) {
            if (commandHistory.isEmpty()) {
                return CommandResult.failure("No commands to undo");
            }
            
            OrderCommand lastCommand = commandHistory.get(commandHistory.size() - 1);
            CommandResult undoResult = lastCommand.undo();
            
            if (undoResult.isSuccess()) {
                commandHistory.remove(commandHistory.size() - 1);
            }
            
            return undoResult;
        }
    }
    
    private void addToHistory(OrderCommand command) {
        synchronized (commandHistory) {
            commandHistory.add(command);
            
            // Maintain max history size
            if (commandHistory.size() > maxHistorySize) {
                commandHistory.remove(0);
            }
        }
    }
    
    private void startCommandProcessor() {
        executorService.submit(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    OrderCommand command = commandQueue.take();
                    executeCommand(command);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    log.error("Error processing command: {}", e.getMessage());
                }
            }
        });
    }
}`,
        technologies: {
          'Spring Boot': 'Command processor service configuration',
          'Concurrent Collections': 'Thread-safe command queues',
          'CompletableFuture': 'Asynchronous command execution',
          'Event Publishing': 'Command result broadcasting'
        }
      },

      'mvc': {
        functions: [
          'Separate presentation, business logic, and data management',
          'Enable independent development of UI and business logic',
          'Support multiple views for same model',
          'Facilitate unit testing through decoupling',
          'Enable reusable business components',
          'Support different presentation formats',
          'Promote maintainable architecture',
          'Enable parallel development workflows',
          'Support responsive and adaptive UIs',
          'Facilitate API and web service development'
        ],
        workflow: `1. Design model layer with business entities and logic
2. Create controller layer to handle user interactions
3. Develop view layer for data presentation
4. Establish communication patterns between layers
5. Implement data binding mechanisms
6. Add validation and error handling
7. Create service layer for business operations
8. Implement repository layer for data access
9. Add cross-cutting concerns (security, logging)
10. Test each layer independently`,
        code: `// MVC Pattern for Trading Dashboard Application
// MODEL LAYER - Business entities and logic

@Entity
@Table(name = "trading_positions")
public class TradingPosition {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String symbol;
    
    @Column(nullable = false)
    private BigDecimal quantity;
    
    @Column(name = "average_price", nullable = false)
    private BigDecimal averagePrice;
    
    @Column(name = "current_price")
    private BigDecimal currentPrice;
    
    @Column(name = "unrealized_pnl")
    private BigDecimal unrealizedPnL;
    
    @Column(name = "realized_pnl")
    private BigDecimal realizedPnL;
    
    @Enumerated(EnumType.STRING)
    private PositionStatus status;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Business logic methods
    public BigDecimal calculateUnrealizedPnL() {
        if (currentPrice == null) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal priceDifference = currentPrice.subtract(averagePrice);
        return priceDifference.multiply(quantity);
    }
    
    public BigDecimal getTotalPnL() {
        return unrealizedPnL.add(realizedPnL);
    }
    
    public double getPercentageReturn() {
        if (averagePrice.equals(BigDecimal.ZERO)) {
            return 0.0;
        }
        
        return calculateUnrealizedPnL()
                .divide(averagePrice.multiply(quantity), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100))
                .doubleValue();
    }
    
    // Getters and setters...
}

// Portfolio model with aggregated position data
public class PortfolioModel {
    
    private final List<TradingPosition> positions;
    private final BigDecimal totalValue;
    private final BigDecimal totalPnL;
    private final double totalReturn;
    private final LocalDateTime lastUpdated;
    
    public PortfolioModel(List<TradingPosition> positions) {
        this.positions = positions;
        this.totalValue = calculateTotalValue();
        this.totalPnL = calculateTotalPnL();
        this.totalReturn = calculateTotalReturn();
        this.lastUpdated = LocalDateTime.now();
    }
    
    private BigDecimal calculateTotalValue() {
        return positions.stream()
                .map(pos -> pos.getCurrentPrice().multiply(pos.getQuantity()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    private BigDecimal calculateTotalPnL() {
        return positions.stream()
                .map(TradingPosition::getTotalPnL)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    private double calculateTotalReturn() {
        if (totalValue.equals(BigDecimal.ZERO)) {
            return 0.0;
        }
        
        return totalPnL.divide(totalValue, 4, RoundingMode.HALF_UP)
                      .multiply(BigDecimal.valueOf(100))
                      .doubleValue();
    }
    
    // Getters...
}

// SERVICE LAYER - Business operations
@Service
@Transactional
public class PortfolioService {
    
    private final PositionRepository positionRepository;
    private final MarketDataService marketDataService;
    private final RiskCalculationService riskService;
    
    public PortfolioService(PositionRepository positionRepository,
                          MarketDataService marketDataService,
                          RiskCalculationService riskService) {
        this.positionRepository = positionRepository;
        this.marketDataService = marketDataService;
        this.riskService = riskService;
    }
    
    public PortfolioModel getPortfolioByUserId(String userId) {
        List<TradingPosition> positions = positionRepository.findByUserId(userId);
        
        // Update positions with current market prices
        positions.forEach(this::updateCurrentPrice);
        
        return new PortfolioModel(positions);
    }
    
    public TradingPosition updatePosition(String positionId, BigDecimal quantity, BigDecimal price) {
        TradingPosition position = positionRepository.findById(Long.valueOf(positionId))
                .orElseThrow(() -> new PositionNotFoundException("Position not found: " + positionId));
        
        // Update position with new trade
        BigDecimal newQuantity = position.getQuantity().add(quantity);
        BigDecimal newAveragePrice = calculateNewAveragePrice(position, quantity, price);
        
        position.setQuantity(newQuantity);
        position.setAveragePrice(newAveragePrice);
        position.setUpdatedAt(LocalDateTime.now());
        
        // Recalculate PnL
        updateCurrentPrice(position);
        
        return positionRepository.save(position);
    }
    
    private void updateCurrentPrice(TradingPosition position) {
        try {
            MarketData marketData = marketDataService.getLatestPrice(position.getSymbol());
            position.setCurrentPrice(marketData.getLastPrice());
            position.setUnrealizedPnL(position.calculateUnrealizedPnL());
        } catch (Exception e) {
            log.warn("Failed to update price for {}: {}", position.getSymbol(), e.getMessage());
        }
    }
    
    private BigDecimal calculateNewAveragePrice(TradingPosition position, 
                                              BigDecimal quantity, 
                                              BigDecimal price) {
        BigDecimal currentValue = position.getAveragePrice().multiply(position.getQuantity());
        BigDecimal newValue = price.multiply(quantity);
        BigDecimal totalQuantity = position.getQuantity().add(quantity);
        
        return currentValue.add(newValue).divide(totalQuantity, 4, RoundingMode.HALF_UP);
    }
}

// CONTROLLER LAYER - Handle HTTP requests
@RestController
@RequestMapping("/api/portfolio")
public class PortfolioController {
    
    private final PortfolioService portfolioService;
    private final PositionValidator positionValidator;
    
    public PortfolioController(PortfolioService portfolioService,
                             PositionValidator positionValidator) {
        this.portfolioService = portfolioService;
        this.positionValidator = positionValidator;
    }
    
    @GetMapping("/{userId}")
    public ResponseEntity<PortfolioResponse> getPortfolio(@PathVariable String userId) {
        try {
            PortfolioModel portfolio = portfolioService.getPortfolioByUserId(userId);
            PortfolioResponse response = PortfolioResponse.from(portfolio);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Failed to get portfolio for user {}: {}", userId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(PortfolioResponse.error("Failed to retrieve portfolio"));
        }
    }
    
    @PostMapping("/positions/{positionId}/update")
    public ResponseEntity<PositionResponse> updatePosition(
            @PathVariable String positionId,
            @RequestBody @Valid UpdatePositionRequest request) {
        
        // Validate request
        ValidationResult validation = positionValidator.validateUpdate(request);
        if (!validation.isValid()) {
            return ResponseEntity.badRequest()
                    .body(PositionResponse.error(validation.getErrors()));
        }
        
        try {
            TradingPosition updatedPosition = portfolioService.updatePosition(
                positionId, request.getQuantity(), request.getPrice()
            );
            
            PositionResponse response = PositionResponse.from(updatedPosition);
            return ResponseEntity.ok(response);
            
        } catch (PositionNotFoundException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Failed to update position {}: {}", positionId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(PositionResponse.error("Failed to update position"));
        }
    }
    
    @GetMapping("/{userId}/summary")
    public ResponseEntity<PortfolioSummaryResponse> getPortfolioSummary(@PathVariable String userId) {
        PortfolioModel portfolio = portfolioService.getPortfolioByUserId(userId);
        
        PortfolioSummaryResponse summary = PortfolioSummaryResponse.builder()
                .totalValue(portfolio.getTotalValue())
                .totalPnL(portfolio.getTotalPnL())
                .totalReturn(portfolio.getTotalReturn())
                .positionCount(portfolio.getPositions().size())
                .lastUpdated(portfolio.getLastUpdated())
                .build();
        
        return ResponseEntity.ok(summary);
    }
}

// VIEW LAYER - Response DTOs for JSON serialization
public class PortfolioResponse {
    
    private final List<PositionResponse> positions;
    private final BigDecimal totalValue;
    private final BigDecimal totalPnL;
    private final double totalReturn;
    private final LocalDateTime lastUpdated;
    private final String status;
    private final List<String> errors;
    
    public static PortfolioResponse from(PortfolioModel portfolio) {
        List<PositionResponse> positionResponses = portfolio.getPositions().stream()
                .map(PositionResponse::from)
                .collect(Collectors.toList());
        
        return new PortfolioResponse(
                positionResponses,
                portfolio.getTotalValue(),
                portfolio.getTotalPnL(),
                portfolio.getTotalReturn(),
                portfolio.getLastUpdated(),
                "success",
                Collections.emptyList()
        );
    }
    
    public static PortfolioResponse error(String errorMessage) {
        return new PortfolioResponse(
                Collections.emptyList(),
                BigDecimal.ZERO,
                BigDecimal.ZERO,
                0.0,
                LocalDateTime.now(),
                "error",
                Arrays.asList(errorMessage)
        );
    }
    
    // Constructor and getters...
}

public class PositionResponse {
    
    private final Long id;
    private final String symbol;
    private final BigDecimal quantity;
    private final BigDecimal averagePrice;
    private final BigDecimal currentPrice;
    private final BigDecimal unrealizedPnL;
    private final BigDecimal realizedPnL;
    private final double percentageReturn;
    private final String status;
    private final LocalDateTime lastUpdated;
    
    public static PositionResponse from(TradingPosition position) {
        return new PositionResponse(
                position.getId(),
                position.getSymbol(),
                position.getQuantity(),
                position.getAveragePrice(),
                position.getCurrentPrice(),
                position.getUnrealizedPnL(),
                position.getRealizedPnL(),
                position.getPercentageReturn(),
                position.getStatus().name(),
                position.getUpdatedAt()
        );
    }
    
    public static PositionResponse error(List<String> errors) {
        return new PositionResponse(errors);
    }
    
    // Constructors and getters...
}

// Frontend View (React component)
// Note: This would typically be in a separate frontend application
const PortfolioView = ({ userId }) => {
    const [portfolio, setPortfolio] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    
    useEffect(() => {
        fetchPortfolio();
    }, [userId]);
    
    const fetchPortfolio = async () => {
        try {
            setLoading(true);
            const response = await fetch(\`/api/portfolio/\${userId}\`);
            const data = await response.json();
            
            if (data.status === 'success') {
                setPortfolio(data);
                setError(null);
            } else {
                setError(data.errors.join(', '));
            }
        } catch (err) {
            setError('Failed to load portfolio');
        } finally {
            setLoading(false);
        }
    };
    
    if (loading) return <div>Loading portfolio...</div>;
    if (error) return <div>Error: {error}</div>;
    if (!portfolio) return <div>No portfolio data available</div>;
    
    return (
        <div className="portfolio-container">
            <div className="portfolio-summary">
                <h2>Portfolio Summary</h2>
                <div className="summary-metrics">
                    <div className="metric">
                        <label>Total Value:</label>
                        <span className="value">$\{portfolio.totalValue.toFixed(2)}</span>
                    </div>
                    <div className="metric">
                        <label>Total P&L:</label>
                        <span className={\`value \${portfolio.totalPnL >= 0 ? 'positive' : 'negative'}\`}>
                            $\{portfolio.totalPnL.toFixed(2)}
                        </span>
                    </div>
                    <div className="metric">
                        <label>Total Return:</label>
                        <span className={\`value \${portfolio.totalReturn >= 0 ? 'positive' : 'negative'}\`}>
                            \{portfolio.totalReturn.toFixed(2)}%
                        </span>
                    </div>
                </div>
            </div>
            
            <div className="positions-table">
                <h3>Positions</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Symbol</th>
                            <th>Quantity</th>
                            <th>Avg Price</th>
                            <th>Current Price</th>
                            <th>Unrealized P&L</th>
                            <th>Return %</th>
                        </tr>
                    </thead>
                    <tbody>
                        {portfolio.positions.map(position => (
                            <tr key={position.id}>
                                <td>{position.symbol}</td>
                                <td>{position.quantity}</td>
                                <td>$\{position.averagePrice.toFixed(2)}</td>
                                <td>$\{position.currentPrice.toFixed(2)}</td>
                                <td className={\`\${position.unrealizedPnL >= 0 ? 'positive' : 'negative'}\`}>
                                    $\{position.unrealizedPnL.toFixed(2)}
                                </td>
                                <td className={\`\${position.percentageReturn >= 0 ? 'positive' : 'negative'}\`}>
                                    \{position.percentageReturn.toFixed(2)}%
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
};`,
        technologies: {
          'Spring Boot': 'MVC framework with REST controllers',
          'JPA/Hibernate': 'ORM for model layer data persistence',
          'React': 'Frontend view layer with component-based architecture',
          'Jackson': 'JSON serialization for API responses'
        }
      },
      'facade': {
        functions: [
          'Provide unified interface to subsystem',
          'Simplify complex subsystem interactions',
          'Hide implementation details from clients',
          'Reduce dependencies between client and subsystem',
          'Centralize subsystem access',
          'Improve subsystem usability',
          'Support loose coupling',
          'Encapsulate subsystem complexity',
          'Enable subsystem evolution',
          'Facilitate testing and mocking'
        ],
        workflow: `1. Client calls facade method
2. Facade validates input parameters
3. Facade coordinates multiple subsystem calls
4. Each subsystem performs its specific function
5. Facade aggregates results from subsystems
6. Facade applies business logic if needed
7. Facade formats response for client
8. Facade handles any errors from subsystems
9. Facade logs operation for audit
10. Return unified response to client`,
        code: `// Trading System Facade - Simplifies complex trading operations
public class TradingSystemFacade {
    
    private final OrderValidator orderValidator;
    private final RiskCalculator riskCalculator;
    private final OrderExecutor orderExecutor;
    private final PortfolioManager portfolioManager;
    private final NotificationService notificationService;
    
    public TradingSystemFacade(OrderValidator validator, 
                              RiskCalculator risk, 
                              OrderExecutor executor,
                              PortfolioManager portfolio,
                              NotificationService notification) {
        this.orderValidator = validator;
        this.riskCalculator = risk;
        this.orderExecutor = executor;
        this.portfolioManager = portfolio;
        this.notificationService = notification;
    }
    
    // Single method hides complex trading workflow
    public TradingResult executeOrder(OrderRequest request) {
        try {
            log.info("Processing order: {}", request.getOrderId());
            
            // Step 1: Validate order
            ValidationResult validation = orderValidator.validate(request);
            if (!validation.isValid()) {
                return TradingResult.rejection(validation.getErrors());
            }
            
            // Step 2: Check risk limits
            RiskAssessment risk = riskCalculator.assessRisk(request);
            if (risk.exceedsLimits()) {
                return TradingResult.rejection("Risk limits exceeded: " + risk.getRiskMetrics());
            }
            
            // Step 3: Execute order
            ExecutionResult execution = orderExecutor.execute(request);
            if (!execution.isSuccessful()) {
                return TradingResult.failure(execution.getErrorMessage());
            }
            
            // Step 4: Update portfolio
            portfolioManager.updatePosition(execution.getPosition());
            
            // Step 5: Send notifications
            notificationService.notifyExecution(execution);
            
            log.info("Order executed successfully: {}", execution.getTradeId());
            return TradingResult.success(execution);
            
        } catch (Exception e) {
            log.error("Order execution failed: {}", e.getMessage(), e);
            return TradingResult.systemError(e.getMessage());
        }
    }
    
    // Simplified portfolio summary - hides complex calculations
    public PortfolioSummary getPortfolioSummary(String userId) {
        try {
            List<Position> positions = portfolioManager.getPositions(userId);
            Map<String, BigDecimal> riskMetrics = riskCalculator.calculatePortfolioRisk(positions);
            BigDecimal totalValue = portfolioManager.calculateTotalValue(positions);
            
            return PortfolioSummary.builder()
                    .userId(userId)
                    .totalValue(totalValue)
                    .positionCount(positions.size())
                    .riskMetrics(riskMetrics)
                    .lastUpdate(Instant.now())
                    .build();
                    
        } catch (Exception e) {
            log.error("Failed to generate portfolio summary: {}", e.getMessage(), e);
            throw new TradingSystemException("Portfolio summary unavailable", e);
        }
    }
    
    // Batch order processing - coordinates multiple subsystems
    public List<TradingResult> executeBatchOrders(List<OrderRequest> orders) {
        return orders.parallelStream()
                    .map(this::executeOrder)
                    .collect(Collectors.toList());
    }
}

// Simple result wrapper
public class TradingResult {
    private final boolean success;
    private final String message;
    private final ExecutionResult execution;
    
    public static TradingResult success(ExecutionResult execution) {
        return new TradingResult(true, "Order executed successfully", execution);
    }
    
    public static TradingResult rejection(String reason) {
        return new TradingResult(false, "Order rejected: " + reason, null);
    }
    
    public static TradingResult systemError(String error) {
        return new TradingResult(false, "System error: " + error, null);
    }
}`,
        technologies: {
          'Spring Boot': 'Dependency injection for facade and subsystems',
          'CompletableFuture': 'Async coordination of subsystem calls',
          'Circuit Breaker': 'Resilience patterns for subsystem failures',
          'Slf4j': 'Unified logging across all subsystems'
        }
      },
      'state': {
        functions: [
          'Allow object behavior change with state',
          'Eliminate complex conditional statements',
          'Encapsulate state-specific behavior',
          'Support state transitions',
          'Maintain state context',
          'Enable polymorphic state handling',
          'Simplify state machine implementation',
          'Support concurrent state changes',
          'Provide state history tracking',
          'Enable state-based validation'
        ],
        workflow: `1. Context receives state change request
2. Current state validates transition eligibility
3. State transition logic executes
4. Context updates to new state instance
5. New state initializes its specific behavior
6. Context delegates operations to current state
7. State executes behavior specific to its type
8. State may trigger additional transitions
9. Context maintains state history for audit
10. External observers notified of state changes`,
        code: `// Order State Pattern - Manages order lifecycle states
public class OrderContext {
    
    private OrderState currentState;
    private final OrderData orderData;
    private final List<StateTransition> stateHistory;
    
    public OrderContext(OrderData data) {
        this.orderData = data;
        this.stateHistory = new ArrayList<>();
        this.currentState = new PendingState();
        logStateTransition(null, currentState);
    }
    
    public void setState(OrderState newState) {
        OrderState previousState = currentState;
        currentState = newState;
        logStateTransition(previousState, newState);
    }
    
    // Delegate operations to current state
    public ValidationResult validate() {
        return currentState.validate(this);
    }
    
    public ExecutionResult execute() {
        return currentState.execute(this);
    }
    
    public CancellationResult cancel() {
        return currentState.cancel(this);
    }
    
    public SettlementResult settle() {
        return currentState.settle(this);
    }
    
    private void logStateTransition(OrderState from, OrderState to) {
        stateHistory.add(new StateTransition(from, to, Instant.now()));
        log.info("Order {} transitioned from {} to {}", 
                orderData.getOrderId(), 
                from != null ? from.getClass().getSimpleName() : "NULL",
                to.getClass().getSimpleName());
    }
    
    // Getters
    public OrderData getOrderData() { return orderData; }
    public OrderState getCurrentState() { return currentState; }
    public List<StateTransition> getStateHistory() { return Collections.unmodifiableList(stateHistory); }
}

// Abstract State Interface
public abstract class OrderState {
    
    public ValidationResult validate(OrderContext context) {
        return ValidationResult.invalid("Validation not allowed in " + getStateName());
    }
    
    public ExecutionResult execute(OrderContext context) {
        return ExecutionResult.failed("Execution not allowed in " + getStateName());
    }
    
    public CancellationResult cancel(OrderContext context) {
        return CancellationResult.success("Order cancelled from " + getStateName());
    }
    
    public SettlementResult settle(OrderContext context) {
        return SettlementResult.failed("Settlement not allowed in " + getStateName());
    }
    
    protected abstract String getStateName();
}

// Concrete State: Pending
public class PendingState extends OrderState {
    
    @Override
    public ValidationResult validate(OrderContext context) {
        try {
            OrderData order = context.getOrderData();
            
            // Validate order parameters
            if (order.getQuantity() <= 0) {
                return ValidationResult.invalid("Invalid quantity");
            }
            
            if (order.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
                return ValidationResult.invalid("Invalid price");
            }
            
            // Transition to validated state
            context.setState(new ValidatedState());
            return ValidationResult.valid("Order validated successfully");
            
        } catch (Exception e) {
            return ValidationResult.invalid("Validation error: " + e.getMessage());
        }
    }
    
    @Override
    protected String getStateName() {
        return "PENDING";
    }
}

// Concrete State: Validated
public class ValidatedState extends OrderState {
    
    @Override
    public ExecutionResult execute(OrderContext context) {
        try {
            OrderData order = context.getOrderData();
            
            // Execute the order
            String tradeId = generateTradeId();
            BigDecimal executedPrice = getMarketPrice(order.getSymbol());
            
            order.setTradeId(tradeId);
            order.setExecutedPrice(executedPrice);
            order.setExecutedTime(Instant.now());
            
            // Transition to executed state
            context.setState(new ExecutedState());
            
            return ExecutionResult.success(tradeId, executedPrice);
            
        } catch (Exception e) {
            return ExecutionResult.failed("Execution error: " + e.getMessage());
        }
    }
    
    @Override
    protected String getStateName() {
        return "VALIDATED";
    }
    
    private String generateTradeId() {
        return "TRD-" + Instant.now().toEpochMilli();
    }
    
    private BigDecimal getMarketPrice(String symbol) {
        // Market data service integration
        return new BigDecimal("100.50");
    }
}

// Concrete State: Executed
public class ExecutedState extends OrderState {
    
    @Override
    public SettlementResult settle(OrderContext context) {
        try {
            OrderData order = context.getOrderData();
            
            // Settle the trade
            String settlementId = generateSettlementId();
            order.setSettlementId(settlementId);
            order.setSettledTime(Instant.now());
            
            // Transition to settled state
            context.setState(new SettledState());
            
            return SettlementResult.success(settlementId);
            
        } catch (Exception e) {
            return SettlementResult.failed("Settlement error: " + e.getMessage());
        }
    }
    
    @Override
    protected String getStateName() {
        return "EXECUTED";
    }
    
    private String generateSettlementId() {
        return "SET-" + Instant.now().toEpochMilli();
    }
}

// Concrete State: Settled
public class SettledState extends OrderState {
    
    @Override
    public CancellationResult cancel(OrderContext context) {
        return CancellationResult.failed("Cannot cancel settled order");
    }
    
    @Override
    protected String getStateName() {
        return "SETTLED";
    }
}`,
        technologies: {
          'State Machine': 'FSM implementation for order lifecycle',
          'Observer Pattern': 'State change notifications',
          'Command Pattern': 'State transition commands',
          'Event Sourcing': 'State history tracking and replay'
        }
      },
      'prototype': {
        functions: [
          'Create objects by cloning existing instances',
          'Avoid expensive object creation',
          'Support dynamic object creation at runtime',
          'Reduce subclassing for object creation',
          'Enable configuration-based object creation',
          'Support deep and shallow cloning',
          'Maintain object state consistency',
          'Enable prototype registry management',
          'Support serialization-based cloning',
          'Optimize memory usage for similar objects'
        ],
        workflow: `1. Client requests object creation from registry
2. Registry locates appropriate prototype
3. Prototype clone method is invoked
4. Deep copy of object state is created
5. Cloned object initializes any transient fields
6. New object customization is applied
7. Cloned object is returned to client
8. Registry optionally caches frequently used prototypes
9. Client uses cloned object as needed
10. Prototype registry maintains prototype lifecycle`,
        code: `// Trading Instrument Prototype System
public interface TradingInstrument extends Cloneable {
    TradingInstrument clone();
    String getSymbol();
    InstrumentType getType();
    BigDecimal calculateValue();
    void customizeForClient(Map<String, Object> customizations);
}

// Base Abstract Prototype
public abstract class BaseInstrument implements TradingInstrument {
    
    protected String symbol;
    protected String exchange;
    protected BigDecimal price;
    protected LocalDateTime created;
    protected Map<String, Object> metadata;
    
    protected BaseInstrument(String symbol, String exchange, BigDecimal price) {
        this.symbol = symbol;
        this.exchange = exchange;
        this.price = price;
        this.created = LocalDateTime.now();
        this.metadata = new HashMap<>();
    }
    
    // Copy constructor for cloning
    protected BaseInstrument(BaseInstrument original) {
        this.symbol = original.symbol;
        this.exchange = original.exchange;
        this.price = original.price;
        this.created = LocalDateTime.now(); // New timestamp for clone
        this.metadata = new HashMap<>(original.metadata); // Deep copy metadata
    }
    
    @Override
    public void customizeForClient(Map<String, Object> customizations) {
        customizations.forEach((key, value) -> {
            switch (key) {
                case "price" -> this.price = (BigDecimal) value;
                case "exchange" -> this.exchange = (String) value;
                default -> this.metadata.put(key, value);
            }
        });
    }
    
    // Common getters
    @Override
    public String getSymbol() { return symbol; }
    public String getExchange() { return exchange; }
    public BigDecimal getPrice() { return price; }
}

// Concrete Prototype: Equity
public class EquityInstrument extends BaseInstrument {
    
    private String sector;
    private Long marketCap;
    private BigDecimal dividendYield;
    
    public EquityInstrument(String symbol, String exchange, BigDecimal price, 
                           String sector, Long marketCap, BigDecimal dividendYield) {
        super(symbol, exchange, price);
        this.sector = sector;
        this.marketCap = marketCap;
        this.dividendYield = dividendYield;
    }
    
    // Copy constructor
    private EquityInstrument(EquityInstrument original) {
        super(original);
        this.sector = original.sector;
        this.marketCap = original.marketCap;
        this.dividendYield = original.dividendYield;
    }
    
    @Override
    public EquityInstrument clone() {
        return new EquityInstrument(this);
    }
    
    @Override
    public InstrumentType getType() {
        return InstrumentType.EQUITY;
    }
    
    @Override
    public BigDecimal calculateValue() {
        return price.multiply(new BigDecimal("100")); // Standard lot size
    }
    
    // Getters
    public String getSector() { return sector; }
    public Long getMarketCap() { return marketCap; }
    public BigDecimal getDividendYield() { return dividendYield; }
}

// Concrete Prototype: Bond
public class BondInstrument extends BaseInstrument {
    
    private LocalDate maturityDate;
    private BigDecimal couponRate;
    private String rating;
    private BigDecimal faceValue;
    
    public BondInstrument(String symbol, String exchange, BigDecimal price,
                         LocalDate maturityDate, BigDecimal couponRate, 
                         String rating, BigDecimal faceValue) {
        super(symbol, exchange, price);
        this.maturityDate = maturityDate;
        this.couponRate = couponRate;
        this.rating = rating;
        this.faceValue = faceValue;
    }
    
    // Copy constructor
    private BondInstrument(BondInstrument original) {
        super(original);
        this.maturityDate = original.maturityDate;
        this.couponRate = original.couponRate;
        this.rating = original.rating;
        this.faceValue = original.faceValue;
    }
    
    @Override
    public BondInstrument clone() {
        return new BondInstrument(this);
    }
    
    @Override
    public InstrumentType getType() {
        return InstrumentType.BOND;
    }
    
    @Override
    public BigDecimal calculateValue() {
        // Present value calculation based on coupon and maturity
        return faceValue.multiply(price.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP));
    }
}

// Prototype Registry
@Component
public class InstrumentPrototypeRegistry {
    
    private final Map<String, TradingInstrument> prototypes = new ConcurrentHashMap<>();
    private final Map<String, AtomicInteger> usageStats = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void initializePrototypes() {
        // Register common equity prototypes
        registerPrototype("US_TECH_EQUITY", 
            new EquityInstrument("TEMPLATE", "NASDAQ", BigDecimal.ZERO, 
                               "Technology", 0L, BigDecimal.ZERO));
        
        registerPrototype("US_TREASURY_BOND", 
            new BondInstrument("TEMPLATE", "NYSE", BigDecimal.ZERO,
                             LocalDate.now().plusYears(10), new BigDecimal("2.5"), 
                             "AAA", new BigDecimal("1000")));
        
        log.info("Initialized {} instrument prototypes", prototypes.size());
    }
    
    public void registerPrototype(String key, TradingInstrument prototype) {
        prototypes.put(key, prototype);
        usageStats.put(key, new AtomicInteger(0));
        log.debug("Registered prototype: {}", key);
    }
    
    public TradingInstrument getPrototype(String key) {
        return prototypes.get(key);
    }
    
    public TradingInstrument createInstrument(String prototypeKey, Map<String, Object> customizations) {
        TradingInstrument prototype = prototypes.get(prototypeKey);
        if (prototype == null) {
            throw new IllegalArgumentException("No prototype found for key: " + prototypeKey);
        }
        
        // Clone the prototype
        TradingInstrument clone = prototype.clone();
        
        // Apply customizations
        clone.customizeForClient(customizations);
        
        // Track usage
        usageStats.get(prototypeKey).incrementAndGet();
        
        log.debug("Created instrument from prototype: {}, customizations: {}", 
                 prototypeKey, customizations);
        
        return clone;
    }
    
    public Map<String, Integer> getUsageStatistics() {
        return usageStats.entrySet().stream()
                        .collect(Collectors.toMap(
                            Map.Entry::getKey, 
                            e -> e.getValue().get()
                        ));
    }
}

// Usage Example
@Service
public class InstrumentCreationService {
    
    private final InstrumentPrototypeRegistry registry;
    
    public InstrumentCreationService(InstrumentPrototypeRegistry registry) {
        this.registry = registry;
    }
    
    public EquityInstrument createTechStock(String symbol, BigDecimal price) {
        Map<String, Object> customizations = Map.of(
            "symbol", symbol,
            "price", price
        );
        
        return (EquityInstrument) registry.createInstrument("US_TECH_EQUITY", customizations);
    }
    
    public BondInstrument createTreasuryBond(String symbol, BigDecimal price, String maturity) {
        Map<String, Object> customizations = Map.of(
            "symbol", symbol,
            "price", price,
            "maturityYears", maturity
        );
        
        return (BondInstrument) registry.createInstrument("US_TREASURY_BOND", customizations);
    }
}`,
        technologies: {
          'Object Cloning': 'Deep copy implementation with copy constructors',
          'Registry Pattern': 'Centralized prototype management and caching',
          'Spring Boot': 'Dependency injection and lifecycle management',
          'Concurrent Collections': 'Thread-safe prototype registry operations'
        }
      }
    };
    
    return details[patternId] || {
      functions: ['Pattern details coming soon...'],
      workflow: 'Detailed workflow will be provided.',
      code: '// Implementation details will be added'
    };
  };

  const getCategoryStyle = (category) => {
    const styles = {
      creational: { color: '#3b82f6', bg: 'rgba(59, 130, 246, 0.1)' },
      structural: { color: '#10b981', bg: 'rgba(16, 185, 129, 0.1)' },
      behavioral: { color: '#ef4444', bg: 'rgba(239, 68, 68, 0.1)' },
      architectural: { color: '#f59e0b', bg: 'rgba(245, 158, 11, 0.1)' }
    };
    return styles[category] || styles.behavioral;
  };

  return (
    <div style={{ 
      padding: '40px',
      backgroundColor: '#0f172a',
      minHeight: '100vh',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      <div style={{
        maxWidth: '1400px',
        margin: '0 auto'
      }}>
        {/* Header */}
        <div style={{ marginBottom: '40px', textAlign: 'center' }}>
          <div style={{
            display: 'inline-flex',
            alignItems: 'center',
            gap: '16px',
            marginBottom: '16px'
          }}>
            <Layers size={32} style={{ color: '#fbbf24' }} />
            <h1 style={{
              fontSize: '32px',
              fontWeight: '700',
              color: 'white',
              margin: 0
            }}>
              Design Patterns in Java
            </h1>
          </div>
          <p style={{
            fontSize: '16px',
            color: '#9ca3af',
            margin: 0,
            maxWidth: '600px',
            margin: '0 auto'
          }}>
            Comprehensive collection of essential design patterns with real-world trading system implementations
          </p>
        </div>

        {/* Pattern Categories */}
        <div style={{ marginBottom: '40px' }}>
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(4, 1fr)', 
            gap: '16px',
            marginBottom: '32px'
          }}>
            {['creational', 'structural', 'behavioral', 'architectural'].map(category => {
              const style = getCategoryStyle(category);
              return (
                <div key={category} style={{
                  padding: '16px',
                  backgroundColor: style.bg,
                  border: `1px solid ${style.color}`,
                  borderRadius: '8px',
                  textAlign: 'center'
                }}>
                  <div style={{ color: style.color, fontSize: '14px', fontWeight: '600', textTransform: 'capitalize' }}>
                    {category} Patterns
                  </div>
                  <div style={{ color: '#9ca3af', fontSize: '12px', marginTop: '4px' }}>
                    {patterns.filter(p => p.category === category).length} patterns
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Patterns Grid */}
        <div style={{ position: 'relative', width: '100%', minHeight: '900px', marginBottom: '40px' }}>
          {/* Category Headers */}
          <div style={{ 
            position: 'absolute',
            top: '20px',
            left: '50px',
            fontSize: '16px',
            fontWeight: 'bold',
            color: '#3b82f6',
            textTransform: 'uppercase',
            letterSpacing: '2px'
          }}>
            Creational
          </div>
          <div style={{ 
            position: 'absolute',
            top: '20px',
            left: '350px',
            fontSize: '16px',
            fontWeight: 'bold',
            color: '#10b981',
            textTransform: 'uppercase',
            letterSpacing: '2px'
          }}>
            Structural
          </div>
          <div style={{ 
            position: 'absolute',
            top: '20px',
            left: '650px',
            fontSize: '16px',
            fontWeight: 'bold',
            color: '#ef4444',
            textTransform: 'uppercase',
            letterSpacing: '2px'
          }}>
            Behavioral
          </div>
          <div style={{ 
            position: 'absolute',
            top: '20px',
            left: '950px',
            fontSize: '16px',
            fontWeight: 'bold',
            color: '#f59e0b',
            textTransform: 'uppercase',
            letterSpacing: '2px'
          }}>
            Architectural
          </div>
          
          {/* Vertical divider lines */}
          <div style={{
            position: 'absolute',
            left: '300px',
            top: '60px',
            bottom: '20px',
            width: '1px',
            backgroundColor: 'rgba(75, 85, 99, 0.3)'
          }}></div>
          <div style={{
            position: 'absolute',
            left: '600px',
            top: '60px',
            bottom: '20px',
            width: '1px',
            backgroundColor: 'rgba(75, 85, 99, 0.3)'
          }}></div>
          <div style={{
            position: 'absolute',
            left: '900px',
            top: '60px',
            bottom: '20px',
            width: '1px',
            backgroundColor: 'rgba(75, 85, 99, 0.3)'
          }}></div>

          {patterns.map(pattern => {
            const categoryStyle = getCategoryStyle(pattern.category);
            const isSelected = selectedPattern?.id === pattern.id;
            
            return (
              <div
                key={pattern.id}
                onClick={() => setSelectedPattern(pattern)}
                style={{
                  position: 'absolute',
                  top: pattern.position.top,
                  left: pattern.position.left,
                  width: '220px',
                  padding: '20px',
                  background: isSelected ? categoryStyle.color : categoryStyle.bg,
                  border: `2px solid ${categoryStyle.color}`,
                  borderRadius: '12px',
                  cursor: 'pointer',
                  transition: 'all 0.3s',
                  transform: isSelected ? 'scale(1.05)' : 'scale(1)',
                  zIndex: isSelected ? 10 : 1
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '12px' }}>
                  <Package size={24} style={{ color: isSelected ? 'white' : categoryStyle.color }} />
                  <h3 style={{ 
                    fontSize: '16px', 
                    fontWeight: 'bold', 
                    color: isSelected ? 'white' : 'white',
                    margin: 0
                  }}>
                    {pattern.name}
                  </h3>
                </div>
                <div style={{ 
                  fontSize: '12px', 
                  color: isSelected ? '#e5e7eb' : '#9ca3af',
                  textTransform: 'capitalize',
                  marginBottom: '8px'
                }}>
                  {pattern.category} Pattern
                </div>
                <div style={{ fontSize: '11px', color: isSelected ? '#d1d5db' : '#6b7280' }}>
                  {pattern.tech.join(' • ')}
                </div>
              </div>
            );
          })}
        </div>

        {/* Pattern Details Panel */}
        {selectedPattern && (
          <div style={{
            backgroundColor: 'rgba(17, 24, 39, 0.95)',
            backdropFilter: 'blur(12px)',
            border: '1px solid #374151',
            borderRadius: '12px',
            overflow: 'hidden'
          }}>
            {/* Panel Header */}
            <div style={{
              padding: '24px',
              borderBottom: '1px solid #374151',
              background: 'linear-gradient(135deg, #1f2937 0%, #111827 100%)'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start' }}>
                <div>
                  <h2 style={{ 
                    fontSize: '24px', 
                    fontWeight: 'bold', 
                    color: '#fbbf24', 
                    margin: '0 0 8px 0' 
                  }}>
                    {selectedPattern.name}
                  </h2>
                  <div style={{ fontSize: '14px', color: '#9ca3af', textTransform: 'capitalize' }}>
                    {selectedPattern.category} Pattern • {selectedPattern.tech.join(' • ')}
                  </div>
                </div>
                <button
                  onClick={() => setSelectedPattern(null)}
                  style={{
                    background: 'transparent',
                    border: 'none',
                    color: '#9ca3af',
                    fontSize: '24px',
                    cursor: 'pointer'
                  }}
                >
                  ×
                </button>
              </div>
            </div>

            {/* Tabs */}
            <div style={{ display: 'flex', borderBottom: '1px solid #374151' }}>
              {['details', 'workflow', 'code', 'uml'].map(tab => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  style={{
                    flex: 1,
                    padding: '16px',
                    background: activeTab === tab ? '#1f2937' : 'transparent',
                    border: 'none',
                    color: activeTab === tab ? '#fbbf24' : '#9ca3af',
                    fontSize: '14px',
                    fontWeight: '600',
                    textTransform: 'capitalize',
                    cursor: 'pointer'
                  }}
                >
                  {tab}
                </button>
              ))}
            </div>

            {/* Tab Content */}
            <div style={{ padding: '32px' }}>
              {activeTab === 'details' && (
                <div>
                  <h4 style={{ margin: '0 0 20px 0', color: '#ef4444', fontSize: '18px' }}>
                    Key Functions & Benefits
                  </h4>
                  <ul style={{ margin: 0, paddingLeft: '24px', lineHeight: '1.8' }}>
                    {getPatternDetails(selectedPattern.id).functions.map((func, i) => (
                      <li key={i} style={{ marginBottom: '12px', color: '#e0e0e0', fontSize: '14px' }}>
                        {func}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {activeTab === 'workflow' && (
                <div>
                  <h4 style={{ margin: '0 0 20px 0', color: '#ef4444', fontSize: '18px' }}>
                    Implementation Workflow
                  </h4>
                  <div style={{
                    background: 'rgba(0, 0, 0, 0.6)',
                    padding: '20px',
                    borderRadius: '8px',
                    fontSize: '13px',
                    lineHeight: '1.8',
                    color: '#e5e5e5',
                    border: '1px solid #333',
                    fontFamily: 'monospace'
                  }}>
                    {(() => {
                      const workflow = getPatternDetails(selectedPattern.id).workflow;
                      const formattedWorkflow = workflow
                        .replace(/(\d+)\.\s+/g, '\n$1. ')
                        .trim()
                        .split('\n')
                        .filter(item => item.trim());
                      
                      return formattedWorkflow.map((item, index) => {
                        const match = item.match(/^(\d+\.)\s*(.*)$/);
                        if (match) {
                          return (
                            <div key={index} style={{ 
                              marginTop: index === 0 ? '0' : '12px',
                              display: 'flex',
                              alignItems: 'flex-start'
                            }}>
                              <span style={{ 
                                color: '#ef4444', 
                                fontWeight: 'bold',
                                marginRight: '12px',
                                flexShrink: 0,
                                minWidth: '30px'
                              }}>
                                {match[1]}
                              </span>
                              <span style={{ flex: 1 }}>
                                {match[2].trim()}
                              </span>
                            </div>
                          );
                        }
                        return <div key={index}>{item}</div>;
                      });
                    })()}
                  </div>
                </div>
              )}

              {activeTab === 'code' && (
                <div>
                  <h4 style={{ margin: '0 0 20px 0', color: '#ef4444', fontSize: '18px' }}>
                    Java Implementation
                  </h4>
                  <div style={{ 
                    maxHeight: '600px', 
                    overflow: 'auto',
                    borderRadius: '8px',
                    border: '1px solid #333'
                  }}>
                    <SyntaxHighlighter
                      language="java"
                      style={oneDark}
                      customStyle={{
                        margin: 0,
                        padding: '20px',
                        fontSize: '12px',
                        lineHeight: '1.5'
                      }}
                    >
                      {getPatternDetails(selectedPattern.id).code}
                    </SyntaxHighlighter>
                  </div>
                </div>
              )}

              {activeTab === 'uml' && (
                <div>
                  <h4 style={{ margin: '0 0 20px 0', color: '#ef4444', fontSize: '18px' }}>
                    UML Class Diagram
                  </h4>
                  <div style={{ 
                    background: 'rgba(0, 0, 0, 0.4)',
                    borderRadius: '8px',
                    padding: '20px',
                    border: '1px solid #333',
                    minHeight: '400px',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center'
                  }}>
                    {getPatternUML(selectedPattern.id) ? (
                      <svg 
                        viewBox={getPatternUML(selectedPattern.id).viewBox}
                        style={{ 
                          width: '100%', 
                          maxWidth: '900px',
                          height: 'auto',
                          minHeight: '400px'
                        }}
                      >
                        {getPatternUML(selectedPattern.id).diagram}
                      </svg>
                    ) : (
                      <div style={{ color: '#9ca3af', fontSize: '14px' }}>
                        UML diagram will be displayed here
                      </div>
                    )}
                  </div>
                  
                  {getPatternDetails(selectedPattern.id).technologies && (
                    <div style={{ marginTop: '32px' }}>
                      <h4 style={{ color: '#ef4444', fontSize: '18px', marginBottom: '16px' }}>
                        Implementation Technologies
                      </h4>
                      {Object.entries(getPatternDetails(selectedPattern.id).technologies).map(([key, value]) => (
                        <div key={key} style={{ marginBottom: '12px' }}>
                          <span style={{ color: '#9ca3af', fontSize: '13px', fontWeight: '600' }}>{key}: </span>
                          <span style={{ color: '#d1d5db', fontSize: '13px' }}>{value}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DesignPatterns;
import React, { useState } from 'react';
import html2canvas from 'html2canvas';
import { Download, FileImage, FileText, Save, Share, Settings } from 'lucide-react';

const ExportControls = ({ containerRef, currentView, componentData }) => {
  const [isExporting, setIsExporting] = useState(false);
  const [exportFormat, setExportFormat] = useState('png');
  const [exportQuality, setExportQuality] = useState(2);
  const [showAdvanced, setShowAdvanced] = useState(false);

  const exportFormats = [
    { id: 'png', name: 'PNG Image', icon: <FileImage size={16} />, description: 'High quality raster image' },
    { id: 'jpg', name: 'JPEG Image', icon: <FileImage size={16} />, description: 'Compressed image format' },
    { id: 'pdf', name: 'PDF Document', icon: <FileText size={16} />, description: 'Portable document format' },
    { id: 'svg', name: 'SVG Vector', icon: <Save size={16} />, description: 'Scalable vector graphics' },
    { id: 'json', name: 'JSON Data', icon: <FileText size={16} />, description: 'Component configuration data' }
  ];

  const qualityOptions = [
    { value: 1, label: 'Standard (1x)', description: 'Default resolution' },
    { value: 2, label: 'High (2x)', description: 'Double resolution for crisp details' },
    { value: 3, label: 'Ultra (3x)', description: 'Triple resolution for presentations' }
  ];

  const exportToImage = async (format = exportFormat) => {
    setIsExporting(true);
    try {
      if (format === 'json') {
        await exportToJSON();
        return;
      }

      if (format === 'pdf') {
        await exportToPDF();
        return;
      }

      const canvas = await html2canvas(containerRef.current, {
        backgroundColor: '#0f172a',
        scale: exportQuality,
        useCORS: true,
        allowTaint: false,
        width: containerRef.current.offsetWidth,
        height: containerRef.current.offsetHeight,
        scrollX: 0,
        scrollY: 0,
        windowWidth: window.innerWidth,
        windowHeight: window.innerHeight
      });
      
      const mimeType = format === 'jpg' ? 'image/jpeg' : 'image/png';
      const fileExtension = format === 'jpg' ? '.jpg' : '.png';
      
      canvas.toBlob((blob) => {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `darkpool-${currentView || 'architecture'}${fileExtension}`;
        a.click();
        URL.revokeObjectURL(url);
      }, mimeType, format === 'jpg' ? 0.9 : undefined);
      
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export failed. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  const exportToJSON = async () => {
    const exportData = {
      timestamp: new Date().toISOString(),
      currentView: currentView,
      componentData: componentData,
      metadata: {
        version: '1.0.0',
        type: 'darkpool-architecture-export',
        description: 'DarkPool Architecture Component Configuration'
      }
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `darkpool-${currentView || 'architecture'}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const exportToPDF = async () => {
    // PDF export would require additional library like jsPDF
    // For now, we'll export as high-quality PNG
    const canvas = await html2canvas(containerRef.current, {
      backgroundColor: '#0f172a',
      scale: 3,
      useCORS: true
    });
    
    canvas.toBlob((blob) => {
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `darkpool-${currentView || 'architecture'}-hq.png`;
      a.click();
      URL.revokeObjectURL(url);
    });
  };

  const shareComponent = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `DarkPool Architecture - ${currentView}`,
          text: 'Check out this DarkPool trading system architecture component',
          url: window.location.href
        });
      } catch (error) {
        console.log('Sharing not supported or cancelled');
      }
    } else {
      // Fallback: copy URL to clipboard
      try {
        await navigator.clipboard.writeText(window.location.href);
        alert('URL copied to clipboard!');
      } catch (error) {
        console.log('Clipboard not supported');
      }
    }
  };

  return (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column', 
      gap: '8px',
      position: 'relative'
    }}>
      {/* Main Export Controls */}
      <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
        <select
          value={exportFormat}
          onChange={(e) => setExportFormat(e.target.value)}
          style={{
            padding: '6px 12px',
            backgroundColor: '#374151',
            color: 'white',
            border: '1px solid #4b5563',
            borderRadius: '6px',
            fontSize: '14px'
          }}
        >
          {exportFormats.map(format => (
            <option key={format.id} value={format.id}>
              {format.name}
            </option>
          ))}
        </select>

        <button
          onClick={() => exportToImage()}
          disabled={isExporting}
          style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '8px', 
            padding: '8px 16px', 
            backgroundColor: isExporting ? '#059669' : '#10b981', 
            color: 'white', 
            borderRadius: '8px', 
            border: 'none', 
            cursor: isExporting ? 'not-allowed' : 'pointer', 
            opacity: isExporting ? 0.5 : 1, 
            transition: 'all 0.2s',
            fontWeight: '500'
          }}
          onMouseEnter={(e) => !isExporting && (e.currentTarget.style.backgroundColor = '#059669')}
          onMouseLeave={(e) => !isExporting && (e.currentTarget.style.backgroundColor = '#10b981')}
        >
          <Download size={20} />
          {isExporting ? 'Exporting...' : 'Export'}
        </button>

        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
            padding: '8px 12px',
            backgroundColor: showAdvanced ? '#374151' : 'transparent',
            color: '#9ca3af',
            border: '1px solid #374151',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '14px',
            transition: 'all 0.2s'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#374151';
            e.currentTarget.style.color = 'white';
          }}
          onMouseLeave={(e) => {
            if (!showAdvanced) {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.color = '#9ca3af';
            }
          }}
        >
          <Settings size={16} />
          Options
        </button>

        <button
          onClick={shareComponent}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
            padding: '8px 12px',
            backgroundColor: 'transparent',
            color: '#9ca3af',
            border: '1px solid #374151',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '14px',
            transition: 'all 0.2s'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#3b82f6';
            e.currentTarget.style.borderColor = '#3b82f6';
            e.currentTarget.style.color = 'white';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent';
            e.currentTarget.style.borderColor = '#374151';
            e.currentTarget.style.color = '#9ca3af';
          }}
        >
          <Share size={16} />
          Share
        </button>
      </div>

      {/* Advanced Options Panel */}
      {showAdvanced && (
        <div style={{
          position: 'absolute',
          top: '60px',
          right: '0',
          backgroundColor: '#1f2937',
          border: '1px solid #374151',
          borderRadius: '8px',
          padding: '16px',
          minWidth: '300px',
          zIndex: 1000,
          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.5)'
        }}>
          <h4 style={{ 
            color: '#f9fafb', 
            margin: '0 0 12px 0', 
            fontSize: '14px', 
            fontWeight: '600' 
          }}>
            Export Options
          </h4>
          
          <div style={{ marginBottom: '16px' }}>
            <label style={{ 
              display: 'block', 
              color: '#d1d5db', 
              fontSize: '12px', 
              marginBottom: '8px' 
            }}>
              Quality Settings
            </label>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
              {qualityOptions.map(option => (
                <label key={option.value} style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: '8px',
                  fontSize: '12px',
                  color: '#9ca3af'
                }}>
                  <input
                    type="radio"
                    name="quality"
                    value={option.value}
                    checked={exportQuality === option.value}
                    onChange={(e) => setExportQuality(Number(e.target.value))}
                    style={{ marginRight: '4px' }}
                  />
                  <div>
                    <div style={{ color: '#d1d5db', fontWeight: '500' }}>
                      {option.label}
                    </div>
                    <div style={{ fontSize: '11px', color: '#6b7280' }}>
                      {option.description}
                    </div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          <div style={{ marginBottom: '16px' }}>
            <label style={{ 
              display: 'block', 
              color: '#d1d5db', 
              fontSize: '12px', 
              marginBottom: '8px' 
            }}>
              Export Formats
            </label>
            <div style={{ display: 'grid', gap: '4px' }}>
              {exportFormats.map(format => (
                <div 
                  key={format.id}
                  onClick={() => setExportFormat(format.id)}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    padding: '8px',
                    backgroundColor: exportFormat === format.id ? '#374151' : 'transparent',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    transition: 'background-color 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    if (exportFormat !== format.id) {
                      e.currentTarget.style.backgroundColor = '#374151';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (exportFormat !== format.id) {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }
                  }}
                >
                  <div style={{ color: '#9ca3af' }}>
                    {format.icon}
                  </div>
                  <div>
                    <div style={{ color: '#d1d5db', fontSize: '12px', fontWeight: '500' }}>
                      {format.name}
                    </div>
                    <div style={{ color: '#6b7280', fontSize: '11px' }}>
                      {format.description}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div style={{
            display: 'flex',
            gap: '8px',
            paddingTop: '12px',
            borderTop: '1px solid #374151'
          }}>
            <button
              onClick={() => setShowAdvanced(false)}
              style={{
                flex: 1,
                padding: '6px 12px',
                backgroundColor: 'transparent',
                color: '#9ca3af',
                border: '1px solid #374151',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              Close
            </button>
            <button
              onClick={() => {
                exportToImage();
                setShowAdvanced(false);
              }}
              disabled={isExporting}
              style={{
                flex: 1,
                padding: '6px 12px',
                backgroundColor: '#10b981',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: isExporting ? 'not-allowed' : 'pointer',
                fontSize: '12px',
                opacity: isExporting ? 0.5 : 1
              }}
            >
              Export Now
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExportControls;
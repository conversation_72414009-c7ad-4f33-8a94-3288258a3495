import React, { useState, lazy, Suspense } from 'react';
import SidebarNavigation from '../ui-components/SidebarNavigation';

// Lazy load components - mapping sidebar IDs to actual component files
const componentMap = {
  // Architecture Views
  'darkpool': lazy(() => import('../trading/DarkPoolMatchingEngine')),
  'varcvar': lazy(() => import('../oop-designs/ParkingLotSystem')),
  'library-management': lazy(() => import('../oop-designs/LibraryManagementSystem')),
  'elevator-system': lazy(() => import('../oop-designs/ElevatorControlSystem')),
  'atm-machine': lazy(() => import('../oop-designs/ATMachineSystem')),
  'portfolio': lazy(() => import('../trading/DarkPoolMatchingEngineAdvanced')),
  
  // Java
  'java-oop': lazy(() => import('../java/JavaOOP')),
  'java-memory': lazy(() => import('../java/JavaMemoryManagement')),
  'java-collections': lazy(() => import('../java/JavaCollections')),
  'java-concurrency': lazy(() => import('../java/JavaConcurrency')),
  'java-8plus': lazy(() => import('../java/Java8Plus')),
  'java-exceptions': lazy(() => import('../java/JavaExceptions')),
  'java-jvm': lazy(() => import('../java/JavaJVM')),
  'java-advanced-oop': lazy(() => import('../java/JavaAdvancedOOP')),
  'java-spring': lazy(() => import('../java/JavaSpring')),
  'java-spring-boot': lazy(() => import('../java/JavaSpringBoot')),
  
  // Design Patterns
  'patterns': lazy(() => import('../patterns/DesignPatterns')),
  'microservice-patterns': lazy(() => import('../patterns/MicroservicePatterns')),
  
  // Messaging
  'kafka': lazy(() => import('../messaging/Kafka')),
  'rabbitmq': lazy(() => import('../messaging/RabbitMQ')),
  'solace': lazy(() => import('../messaging/Solace')),
  
  // SQL
  'sql-basics': lazy(() => import('../sql/SQLBasics')),
  'sql-joins': lazy(() => import('../sql/SQLJoins')),
  'sql-advanced': lazy(() => import('../sql/SQLAdvanced')),
  'sql-optimization': lazy(() => import('../sql/SQLOptimization')),
  'sql-transactions': lazy(() => import('../sql/SQLTransactions')),
  'sql-trading': lazy(() => import('../sql/SQLTradingPatterns')),
  
  // Security
  'auth-authorization': lazy(() => import('../security/AuthenticationAuthorization')),
  'cryptography': lazy(() => import('../security/CryptographyEncryption')),
  'network-security': lazy(() => import('../security/NetworkSecurity')),
  'security-monitoring': lazy(() => import('../security/SecurityMonitoring')),
  'threat-detection': lazy(() => import('../security/ThreatDetection')),
};

const VarCvarRiskAnalyticsInteractive = () => {
  const [activeView, setActiveView] = useState('darkpool');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  // Handle view change from sidebar
  const handleViewChange = (viewId) => {
    console.log('Sidebar clicked - Changing view to:', viewId);
    console.log('Component available for this view:', componentMap.hasOwnProperty(viewId) ? 'Yes' : 'No');
    setActiveView(viewId);
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #0a0a0a 0%, #111111 100%)',
      color: '#ffffff',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      <SidebarNavigation 
        currentView={activeView} 
        onViewChange={handleViewChange}
        onCollapseChange={setIsSidebarCollapsed}
      />
      <div style={{ 
        marginLeft: isSidebarCollapsed ? '60px' : '280px',
        minHeight: '100vh',
        transition: 'margin-left 0.3s ease'
      }}>
        {/* Render selected component based on activeView */}
        {(() => {
          // Show built-in VaR/CVaR content for 'compliance' view
          if (activeView === 'compliance') {
            return (
              <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '20px' }}>
                <h1 style={{ textAlign: 'center', fontSize: '24px', color: '#10b981' }}>
                  VaR/CVaR Calculation Engine Architecture
                </h1>
                <p style={{ textAlign: 'center', color: '#94a3b8' }}>
                  Select a component in the architecture diagram to view detailed implementation.
                </p>
              </div>
            );
          }
          
          // Check if component exists in the map
          const Component = componentMap[activeView];
          if (Component) {
            return (
              <Suspense fallback={
                <div style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '100vh',
                  color: '#00ff88',
                  fontSize: '18px'
                }}>
                  Loading {activeView} component...
                </div>
              }>
                <Component />
              </Suspense>
            );
          }
          
          // Handle unknown/unmapped views
          return (
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100vh',
              color: '#ff6b6b',
              fontSize: '18px'
            }}>
              <div>Component not found for view: {activeView}</div>
              <div style={{ marginTop: '10px', color: '#94a3b8', fontSize: '14px' }}>
                This component may not be implemented yet or the mapping is missing.
              </div>
            </div>
          );
        })()}
      </div>
    </div>
  );
};

export default VarCvarRiskAnalyticsInteractive;
import React, { useState } from 'react';
import { <PERSON>rism as <PERSON>ynta<PERSON><PERSON><PERSON><PERSON>er } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { TrendingUp, Bar<PERSON>hart3, Calculator, Target, Clock, Shield } from 'lucide-react';

const SQLTradingPatterns = () => {
  const [selectedPattern, setSelectedPattern] = useState('market-data');
  const [activeTab, setActiveTab] = useState('overview');

  const patterns = [
    {
      id: 'market-data',
      name: 'Market Data Processing',
      icon: <BarChart3 size={20} />,
      description: 'Real-time price and volume processing'
    },
    {
      id: 'risk-analytics',
      name: 'Risk Analytics',
      icon: <Shield size={20} />,
      description: 'VaR, portfolio risk, and exposure calculations'
    },
    {
      id: 'performance',
      name: 'Performance Attribution',
      icon: <TrendingUp size={20} />,
      description: 'P&L analysis and performance metrics'
    },
    {
      id: 'order-routing',
      name: 'Order Routing Logic',
      icon: <Target size={20} />,
      description: 'Smart order routing and execution'
    },
    {
      id: 'time-series',
      name: 'Time Series Analysis',
      icon: <Clock size={20} />,
      description: 'Historical trend and pattern analysis'
    },
    {
      id: 'calculations',
      name: 'Financial Calculations',
      icon: <Calculator size={20} />,
      description: 'Returns, volatility, and financial metrics'
    }
  ];

  const definitions = {
    'market-data': `Market data processing patterns handle the ingestion, validation, and distribution of real-time and historical market information. These patterns ensure data quality, handle missing data, and provide efficient access to price, volume, and order book information for trading decisions.`,
    'risk-analytics': `Risk analytics patterns implement sophisticated risk calculations including Value at Risk (VaR), Conditional Value at Risk (CVaR), portfolio exposure analysis, and stress testing. These patterns are essential for regulatory compliance and prudent risk management.`,
    'performance': `Performance attribution patterns track and analyze trading performance across various dimensions including trader, strategy, instrument, and time period. These patterns enable detailed P&L analysis, benchmark comparison, and performance optimization.`,
    'order-routing': `Order routing patterns implement intelligent order management logic including venue selection, order slicing, timing strategies, and execution optimization. These patterns help minimize market impact and maximize execution quality.`,
    'time-series': `Time series analysis patterns process sequential data to identify trends, seasonal patterns, volatility clusters, and other temporal characteristics. These patterns are fundamental for technical analysis and quantitative trading strategies.`,
    'calculations': `Financial calculation patterns implement standard and custom financial metrics including returns calculation, volatility measurement, correlation analysis, and various performance ratios. These patterns ensure consistent and accurate financial computations.`
  };

  const codeExamples = {
    'market-data': `-- Market Data Processing Patterns
-- Real-time market data ingestion and validation
CREATE TABLE market_data_raw (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(10) NOT NULL,
    timestamp TIMESTAMP(6) NOT NULL,
    price DECIMAL(15,4) NOT NULL,
    volume BIGINT NOT NULL,
    bid DECIMAL(15,4),
    ask DECIMAL(15,4),
    bid_size BIGINT,
    ask_size BIGINT,
    source VARCHAR(20) NOT NULL,
    received_time TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(6),
    INDEX idx_symbol_timestamp (symbol, timestamp),
    INDEX idx_received_time (received_time)
);

-- Data quality checks and cleansing
INSERT INTO market_data_clean (
    symbol, timestamp, price, volume, bid, ask, 
    bid_size, ask_size, data_quality_score
)
SELECT 
    symbol,
    timestamp,
    price,
    volume,
    bid,
    ask,
    bid_size,
    ask_size,
    -- Data quality scoring
    (CASE WHEN price > 0 THEN 25 ELSE 0 END) +
    (CASE WHEN volume >= 0 THEN 25 ELSE 0 END) +
    (CASE WHEN bid > 0 AND ask > 0 AND ask >= bid THEN 25 ELSE 0 END) +
    (CASE WHEN ABS(price - LAG(price) OVER (PARTITION BY symbol ORDER BY timestamp)) / 
              LAG(price) OVER (PARTITION BY symbol ORDER BY timestamp) < 0.1 THEN 25 ELSE 0 END) as quality_score
FROM market_data_raw
WHERE timestamp >= NOW() - INTERVAL 5 MINUTE
  AND price > 0
  AND volume >= 0
HAVING quality_score >= 75;  -- Only high-quality data

-- OHLC aggregation pattern
SELECT 
    symbol,
    DATE_FORMAT(timestamp, '%Y-%m-%d %H:%i:00') as minute_bar,
    FIRST_VALUE(price) OVER (
        PARTITION BY symbol, DATE_FORMAT(timestamp, '%Y-%m-%d %H:%i:00') 
        ORDER BY timestamp 
        ROWS UNBOUNDED PRECEDING
    ) as open_price,
    MAX(price) as high_price,
    MIN(price) as low_price,
    LAST_VALUE(price) OVER (
        PARTITION BY symbol, DATE_FORMAT(timestamp, '%Y-%m-%d %H:%i:00') 
        ORDER BY timestamp 
        ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
    ) as close_price,
    SUM(volume) as total_volume,
    COUNT(*) as tick_count,
    AVG(ask - bid) as avg_spread
FROM market_data_clean
WHERE timestamp >= CURRENT_DATE
GROUP BY symbol, DATE_FORMAT(timestamp, '%Y-%m-%d %H:%i:00');

-- Market data gap detection and interpolation
WITH price_gaps AS (
    SELECT 
        symbol,
        timestamp,
        price,
        LAG(timestamp) OVER (PARTITION BY symbol ORDER BY timestamp) as prev_timestamp,
        TIMESTAMPDIFF(SECOND, 
            LAG(timestamp) OVER (PARTITION BY symbol ORDER BY timestamp), 
            timestamp
        ) as gap_seconds,
        LAG(price) OVER (PARTITION BY symbol ORDER BY timestamp) as prev_price
    FROM market_data_clean
    WHERE symbol = 'AAPL' 
      AND timestamp >= CURRENT_DATE
),
gaps_identified AS (
    SELECT *,
        CASE WHEN gap_seconds > 60 THEN 'LARGE_GAP' 
             WHEN gap_seconds > 10 THEN 'SMALL_GAP' 
             ELSE 'NORMAL' END as gap_type
    FROM price_gaps
    WHERE gap_seconds IS NOT NULL
)
-- Fill small gaps with linear interpolation
SELECT 
    symbol,
    timestamp,
    CASE 
        WHEN gap_type = 'SMALL_GAP' AND prev_price IS NOT NULL THEN
            prev_price + ((price - prev_price) * 0.5)  -- Simple interpolation
        ELSE price 
    END as adjusted_price,
    gap_type
FROM gaps_identified;

-- Real-time market data streaming view
CREATE VIEW live_market_data AS
SELECT 
    m.symbol,
    m.price as last_price,
    m.volume,
    m.timestamp as last_update,
    m.bid,
    m.ask,
    (m.ask - m.bid) as spread,
    (m.ask - m.bid) / ((m.ask + m.bid) / 2) * 100 as spread_bps,
    -- Price change calculations
    m.price - prev.price as price_change,
    (m.price - prev.price) / prev.price * 100 as price_change_pct,
    -- Trading activity indicators
    CASE 
        WHEN m.volume > AVG(h.volume) * 2 THEN 'HIGH_VOLUME'
        WHEN m.volume < AVG(h.volume) * 0.5 THEN 'LOW_VOLUME'
        ELSE 'NORMAL_VOLUME'
    END as volume_indicator
FROM market_data_clean m
-- Previous price for comparison
LEFT JOIN market_data_clean prev ON m.symbol = prev.symbol 
    AND prev.timestamp = (
        SELECT MAX(timestamp) 
        FROM market_data_clean 
        WHERE symbol = m.symbol 
          AND timestamp < m.timestamp
    )
-- Historical volume average
LEFT JOIN (
    SELECT symbol, AVG(volume) as volume
    FROM market_data_clean
    WHERE timestamp >= CURRENT_DATE - INTERVAL 30 DAY
    GROUP BY symbol
) h ON m.symbol = h.symbol
WHERE m.timestamp = (
    SELECT MAX(timestamp) 
    FROM market_data_clean 
    WHERE symbol = m.symbol
);

-- Market microstructure analysis
SELECT 
    symbol,
    DATE(timestamp) as trade_date,
    HOUR(timestamp) as hour,
    -- Order flow imbalance
    SUM(CASE WHEN price > (bid + ask) / 2 THEN volume ELSE 0 END) as buy_volume,
    SUM(CASE WHEN price < (bid + ask) / 2 THEN volume ELSE 0 END) as sell_volume,
    (SUM(CASE WHEN price > (bid + ask) / 2 THEN volume ELSE 0 END) - 
     SUM(CASE WHEN price < (bid + ask) / 2 THEN volume ELSE 0 END)) / 
    SUM(volume) as order_flow_imbalance,
    -- Price impact measures
    AVG(ABS(price - LAG(price) OVER (PARTITION BY symbol ORDER BY timestamp))) as avg_price_impact,
    STDDEV(price) as price_volatility,
    -- Liquidity measures
    AVG(bid_size + ask_size) as avg_depth,
    AVG(ask - bid) as avg_spread
FROM market_data_clean
WHERE timestamp >= CURRENT_DATE - INTERVAL 7 DAY
  AND bid > 0 AND ask > 0
GROUP BY symbol, DATE(timestamp), HOUR(timestamp);`,

    'risk-analytics': `-- Risk Analytics Patterns for Trading Systems
-- Value at Risk (VaR) calculation using historical simulation
WITH daily_returns AS (
    SELECT 
        p.trader_id,
        p.symbol,
        p.position_date,
        p.quantity,
        m.close_price,
        LAG(m.close_price) OVER (
            PARTITION BY p.symbol 
            ORDER BY p.position_date
        ) as prev_close,
        p.quantity * m.close_price as position_value,
        p.quantity * (
            m.close_price - LAG(m.close_price) OVER (
                PARTITION BY p.symbol 
                ORDER BY p.position_date
            )
        ) as daily_pnl
    FROM portfolio_positions p
    JOIN market_data m ON p.symbol = m.symbol 
        AND DATE(m.timestamp) = p.position_date
    WHERE p.position_date >= CURRENT_DATE - INTERVAL 252 DAY  -- 1 year of data
),
portfolio_returns AS (
    SELECT 
        trader_id,
        position_date,
        SUM(daily_pnl) as portfolio_pnl,
        SUM(position_value) as portfolio_value,
        SUM(daily_pnl) / SUM(position_value) as portfolio_return
    FROM daily_returns
    WHERE prev_close IS NOT NULL
    GROUP BY trader_id, position_date
),
var_calculation AS (
    SELECT 
        trader_id,
        -- Sort returns and find percentiles
        portfolio_return,
        ROW_NUMBER() OVER (PARTITION BY trader_id ORDER BY portfolio_return) as rank_asc,
        COUNT(*) OVER (PARTITION BY trader_id) as total_observations
    FROM portfolio_returns
)
SELECT 
    trader_id,
    -- VaR at different confidence levels
    MIN(CASE WHEN rank_asc = CEILING(total_observations * 0.05) THEN portfolio_return END) * -1 as var_95,
    MIN(CASE WHEN rank_asc = CEILING(total_observations * 0.01) THEN portfolio_return END) * -1 as var_99,
    -- Expected Shortfall (Conditional VaR)
    AVG(CASE WHEN rank_asc <= CEILING(total_observations * 0.05) THEN portfolio_return END) * -1 as cvar_95,
    AVG(CASE WHEN rank_asc <= CEILING(total_observations * 0.01) THEN portfolio_return END) * -1 as cvar_99,
    -- Current portfolio value for VaR scaling
    (SELECT SUM(quantity * current_price) 
     FROM current_positions cp 
     WHERE cp.trader_id = var_calculation.trader_id) as current_portfolio_value
FROM var_calculation
GROUP BY trader_id;

-- Real-time risk monitoring and alerts
CREATE VIEW real_time_risk AS
SELECT 
    t.trader_id,
    t.trader_name,
    t.desk,
    -- Current positions and exposures
    SUM(p.quantity * m.price) as gross_exposure,
    SUM(ABS(p.quantity * m.price)) as net_exposure,
    COUNT(DISTINCT p.symbol) as num_positions,
    -- Risk limits and utilization
    tl.daily_var_limit,
    tl.gross_exposure_limit,
    tl.concentration_limit,
    SUM(ABS(p.quantity * m.price)) / tl.gross_exposure_limit * 100 as exposure_utilization_pct,
    -- Concentration risk
    MAX(ABS(p.quantity * m.price)) / SUM(ABS(p.quantity * m.price)) * 100 as max_concentration_pct,
    -- Sector concentration
    (SELECT SUM(ABS(pp.quantity * mm.price)) 
     FROM portfolio_positions pp 
     JOIN instruments i ON pp.symbol = i.symbol
     JOIN market_data mm ON pp.symbol = mm.symbol
     WHERE pp.trader_id = t.trader_id 
       AND i.sector = (
           SELECT ii.sector FROM portfolio_positions ppp 
           JOIN instruments ii ON ppp.symbol = ii.symbol
           WHERE ppp.trader_id = t.trader_id 
           GROUP BY ii.sector 
           ORDER BY SUM(ABS(ppp.quantity * 
               (SELECT price FROM market_data WHERE symbol = ppp.symbol ORDER BY timestamp DESC LIMIT 1)
           )) DESC LIMIT 1
       )
    ) / SUM(ABS(p.quantity * m.price)) * 100 as top_sector_concentration_pct,
    -- Risk alerts
    CASE 
        WHEN SUM(ABS(p.quantity * m.price)) > tl.gross_exposure_limit * 0.9 THEN 'EXPOSURE_WARNING'
        WHEN MAX(ABS(p.quantity * m.price)) / SUM(ABS(p.quantity * m.price)) > 0.3 THEN 'CONCENTRATION_WARNING'
        ELSE 'OK'
    END as risk_status
FROM traders t
JOIN portfolio_positions p ON t.trader_id = p.trader_id
JOIN market_data m ON p.symbol = m.symbol
JOIN trader_limits tl ON t.trader_id = tl.trader_id
WHERE p.quantity != 0
  AND m.timestamp = (SELECT MAX(timestamp) FROM market_data WHERE symbol = m.symbol)
GROUP BY t.trader_id, t.trader_name, t.desk, tl.daily_var_limit, tl.gross_exposure_limit, tl.concentration_limit;

-- Stress testing scenarios
WITH stress_scenarios AS (
    SELECT 'Market_Crash' as scenario, symbol, -0.20 as shock_pct FROM instruments
    UNION ALL
    SELECT 'Interest_Rate_Rise', symbol, 
           CASE WHEN sector = 'FINANCIALS' THEN 0.10 ELSE -0.05 END 
    FROM instruments
    UNION ALL
    SELECT 'Tech_Selloff', symbol,
           CASE WHEN sector = 'TECHNOLOGY' THEN -0.30 ELSE 0.00 END
    FROM instruments
),
stressed_positions AS (
    SELECT 
        ss.scenario,
        p.trader_id,
        p.symbol,
        p.quantity,
        m.price as current_price,
        m.price * (1 + ss.shock_pct) as stressed_price,
        p.quantity * m.price as current_value,
        p.quantity * m.price * (1 + ss.shock_pct) as stressed_value,
        p.quantity * m.price * ss.shock_pct as scenario_pnl
    FROM stress_scenarios ss
    JOIN portfolio_positions p ON ss.symbol = p.symbol
    JOIN market_data m ON p.symbol = m.symbol
    WHERE p.quantity != 0
      AND m.timestamp = (SELECT MAX(timestamp) FROM market_data WHERE symbol = m.symbol)
)
SELECT 
    scenario,
    trader_id,
    SUM(current_value) as current_portfolio_value,
    SUM(stressed_value) as stressed_portfolio_value,
    SUM(scenario_pnl) as total_scenario_pnl,
    SUM(scenario_pnl) / SUM(current_value) * 100 as portfolio_impact_pct,
    -- Identify worst-hit positions
    (SELECT symbol FROM stressed_positions sp2 
     WHERE sp2.scenario = stressed_positions.scenario 
       AND sp2.trader_id = stressed_positions.trader_id 
     ORDER BY scenario_pnl ASC LIMIT 1) as worst_position
FROM stressed_positions
GROUP BY scenario, trader_id
ORDER BY scenario, total_scenario_pnl;

-- Greeks calculation for options positions
SELECT 
    op.trader_id,
    op.symbol,
    op.option_type,
    op.strike_price,
    op.expiration_date,
    op.quantity,
    -- Current option value
    op.quantity * op.current_premium as position_value,
    -- Greeks (would typically be calculated by external pricing model)
    op.delta * op.quantity as position_delta,
    op.gamma * op.quantity as position_gamma,
    op.theta * op.quantity as position_theta,
    op.vega * op.quantity as position_vega,
    op.rho * op.quantity as position_rho,
    -- Risk metrics
    ABS(op.delta * op.quantity * underlying.price) as delta_exposure,
    op.gamma * op.quantity * POWER(underlying.price * 0.01, 2) as gamma_risk_1pct,
    op.theta * op.quantity / 365 as daily_theta_decay
FROM options_positions op
JOIN market_data underlying ON op.underlying_symbol = underlying.symbol
WHERE op.quantity != 0
  AND underlying.timestamp = (SELECT MAX(timestamp) FROM market_data WHERE symbol = underlying.symbol)
  AND op.expiration_date > CURRENT_DATE;`,

    'performance': `-- Performance Attribution Analysis Patterns
-- Daily P&L calculation with attribution
WITH daily_positions AS (
    SELECT 
        trader_id,
        symbol,
        position_date,
        quantity,
        AVG(price) as avg_cost,
        SUM(quantity * price) / SUM(quantity) as weighted_avg_cost
    FROM trades
    WHERE trade_date <= position_date
    GROUP BY trader_id, symbol, position_date
),
daily_pnl AS (
    SELECT 
        dp.trader_id,
        dp.symbol,
        dp.position_date,
        dp.quantity,
        dp.weighted_avg_cost,
        m.close_price,
        LAG(m.close_price) OVER (
            PARTITION BY dp.symbol 
            ORDER BY dp.position_date
        ) as prev_close,
        -- P&L components
        dp.quantity * (m.close_price - dp.weighted_avg_cost) as unrealized_pnl,
        dp.quantity * (m.close_price - LAG(m.close_price) OVER (
            PARTITION BY dp.symbol 
            ORDER BY dp.position_date
        )) as daily_pnl_contribution,
        -- Trading P&L (from actual trades on this date)
        COALESCE((
            SELECT SUM((sell_price - buy_price) * trade_quantity)
            FROM daily_trades dt
            WHERE dt.trader_id = dp.trader_id 
              AND dt.symbol = dp.symbol 
              AND dt.trade_date = dp.position_date
        ), 0) as trading_pnl
    FROM daily_positions dp
    JOIN market_data m ON dp.symbol = m.symbol 
        AND DATE(m.timestamp) = dp.position_date
)
SELECT 
    trader_id,
    position_date,
    -- Total P&L breakdown
    SUM(daily_pnl_contribution) as market_pnl,
    SUM(trading_pnl) as trading_pnl,
    SUM(daily_pnl_contribution + trading_pnl) as total_daily_pnl,
    SUM(unrealized_pnl) as total_unrealized_pnl,
    -- Attribution by instrument
    COUNT(DISTINCT symbol) as instruments_traded,
    -- Risk-adjusted metrics
    SUM(daily_pnl_contribution + trading_pnl) / NULLIF(STDDEV(daily_pnl_contribution + trading_pnl), 0) as daily_sharpe
FROM daily_pnl
GROUP BY trader_id, position_date
ORDER BY trader_id, position_date;

-- Benchmark comparison and alpha generation
WITH benchmark_returns AS (
    SELECT 
        trade_date,
        (close_price - LAG(close_price) OVER (ORDER BY trade_date)) / 
        LAG(close_price) OVER (ORDER BY trade_date) as benchmark_return
    FROM market_data
    WHERE symbol = 'SPY'  -- S&P 500 ETF as benchmark
      AND DATE(timestamp) = trade_date
),
trader_returns AS (
    SELECT 
        trader_id,
        position_date as trade_date,
        SUM(daily_pnl_contribution + trading_pnl) / 
        LAG(SUM(portfolio_value)) OVER (PARTITION BY trader_id ORDER BY position_date) as trader_return
    FROM (
        SELECT 
            dp.trader_id,
            dp.position_date,
            dp.symbol,
            dp.daily_pnl_contribution,
            dp.trading_pnl,
            dp.quantity * m.close_price as portfolio_value
        FROM daily_pnl dp
        JOIN market_data m ON dp.symbol = m.symbol 
            AND DATE(m.timestamp) = dp.position_date
    ) portfolio_data
    GROUP BY trader_id, position_date
)
SELECT 
    tr.trader_id,
    tr.trade_date,
    tr.trader_return,
    br.benchmark_return,
    tr.trader_return - br.benchmark_return as alpha,
    -- Rolling performance metrics
    AVG(tr.trader_return) OVER (
        PARTITION BY tr.trader_id 
        ORDER BY tr.trade_date 
        ROWS BETWEEN 29 PRECEDING AND CURRENT ROW
    ) as rolling_30d_return,
    AVG(tr.trader_return - br.benchmark_return) OVER (
        PARTITION BY tr.trader_id 
        ORDER BY tr.trade_date 
        ROWS BETWEEN 29 PRECEDING AND CURRENT ROW
    ) as rolling_30d_alpha,
    -- Beta calculation
    COVAR_POP(tr.trader_return, br.benchmark_return) OVER (
        PARTITION BY tr.trader_id 
        ORDER BY tr.trade_date 
        ROWS BETWEEN 252 PRECEDING AND CURRENT ROW
    ) / NULLIF(VAR_POP(br.benchmark_return) OVER (
        PARTITION BY tr.trader_id 
        ORDER BY tr.trade_date 
        ROWS BETWEEN 252 PRECEDING AND CURRENT ROW
    ), 0) as rolling_beta
FROM trader_returns tr
JOIN benchmark_returns br ON tr.trade_date = br.trade_date
WHERE tr.trader_return IS NOT NULL AND br.benchmark_return IS NOT NULL;

-- Sector and style attribution
SELECT 
    t.trader_id,
    i.sector,
    EXTRACT(MONTH FROM dp.position_date) as month,
    -- Sector allocation vs benchmark
    SUM(ABS(dp.quantity * m.close_price)) / 
    (SELECT SUM(ABS(quantity * close_price)) 
     FROM daily_pnl dp2 
     JOIN market_data m2 ON dp2.symbol = m2.symbol 
     WHERE dp2.trader_id = t.trader_id 
       AND DATE(m2.timestamp) = dp.position_date
    ) * 100 as sector_weight_pct,
    -- Sector performance
    SUM(dp.daily_pnl_contribution + dp.trading_pnl) as sector_pnl,
    SUM(dp.daily_pnl_contribution + dp.trading_pnl) / 
    SUM(ABS(dp.quantity * m.close_price)) * 100 as sector_return_pct,
    -- Security selection vs sector
    SUM(dp.daily_pnl_contribution + dp.trading_pnl) - 
    (SUM(ABS(dp.quantity * m.close_price)) * AVG(sector_benchmark.return_pct) / 100) as security_selection_pnl
FROM traders t
JOIN daily_pnl dp ON t.trader_id = dp.trader_id
JOIN market_data m ON dp.symbol = m.symbol AND DATE(m.timestamp) = dp.position_date
JOIN instruments i ON dp.symbol = i.symbol
LEFT JOIN sector_benchmarks sector_benchmark ON i.sector = sector_benchmark.sector 
    AND sector_benchmark.date = dp.position_date
WHERE dp.position_date >= CURRENT_DATE - INTERVAL 90 DAY
GROUP BY t.trader_id, i.sector, EXTRACT(MONTH FROM dp.position_date)
ORDER BY t.trader_id, sector_pnl DESC;

-- Risk-adjusted performance metrics
WITH performance_metrics AS (
    SELECT 
        trader_id,
        -- Return metrics
        AVG(daily_return) * 252 as annualized_return,
        STDDEV(daily_return) * SQRT(252) as annualized_volatility,
        -- Sharpe ratio
        (AVG(daily_return) * 252) / NULLIF(STDDEV(daily_return) * SQRT(252), 0) as sharpe_ratio,
        -- Sortino ratio (downside deviation)
        (AVG(daily_return) * 252) / NULLIF(
            SQRT(AVG(CASE WHEN daily_return < 0 THEN POWER(daily_return, 2) ELSE 0 END)) * SQRT(252), 0
        ) as sortino_ratio,
        -- Maximum drawdown
        MIN(cumulative_return - MAX(cumulative_return) OVER (
            PARTITION BY trader_id 
            ORDER BY trade_date 
            ROWS UNBOUNDED PRECEDING
        )) as max_drawdown,
        -- Calmar ratio
        (AVG(daily_return) * 252) / NULLIF(ABS(MIN(cumulative_return - MAX(cumulative_return) OVER (
            PARTITION BY trader_id 
            ORDER BY trade_date 
            ROWS UNBOUNDED PRECEDING
        ))), 0) as calmar_ratio
    FROM (
        SELECT 
            trader_id,
            trade_date,
            daily_return,
            SUM(daily_return) OVER (
                PARTITION BY trader_id 
                ORDER BY trade_date
            ) as cumulative_return
        FROM trader_daily_returns
        WHERE trade_date >= CURRENT_DATE - INTERVAL 252 DAY
    ) returns_data
    GROUP BY trader_id
)
SELECT 
    pm.*,
    -- Rank traders by different metrics
    RANK() OVER (ORDER BY sharpe_ratio DESC) as sharpe_rank,
    RANK() OVER (ORDER BY annualized_return DESC) as return_rank,
    RANK() OVER (ORDER BY max_drawdown DESC) as drawdown_rank,
    -- Overall performance score (weighted composite)
    (RANK() OVER (ORDER BY sharpe_ratio DESC) * 0.4 +
     RANK() OVER (ORDER BY annualized_return DESC) * 0.3 +
     RANK() OVER (ORDER BY max_drawdown DESC) * 0.3) as composite_score
FROM performance_metrics pm;`,

    'order-routing': `-- Order Routing and Execution Logic Patterns
-- Smart order routing based on market conditions
WITH venue_metrics AS (
    SELECT 
        venue_id,
        symbol,
        -- Recent execution quality metrics
        AVG(CASE WHEN side = 'BUY' THEN execution_price - order_price 
                 ELSE order_price - execution_price END) as avg_slippage,
        AVG(TIMESTAMPDIFF(MICROSECOND, order_time, execution_time) / 1000) as avg_latency_ms,
        COUNT(*) as execution_count,
        SUM(quantity) as total_volume,
        -- Fill rate
        COUNT(CASE WHEN status = 'FILLED' THEN 1 END) / COUNT(*) as fill_rate,
        -- Market impact
        AVG(ABS(execution_price - midpoint_at_order) / midpoint_at_order) as avg_market_impact
    FROM order_executions
    WHERE execution_time >= NOW() - INTERVAL 30 MINUTE
    GROUP BY venue_id, symbol
),
venue_ranking AS (
    SELECT 
        *,
        -- Composite score for venue selection
        (1 - avg_slippage / NULLIF(MAX(avg_slippage) OVER (), 0)) * 0.3 +
        (1 - avg_latency_ms / NULLIF(MAX(avg_latency_ms) OVER (), 0)) * 0.2 +
        fill_rate * 0.3 +
        (1 - avg_market_impact / NULLIF(MAX(avg_market_impact) OVER (), 0)) * 0.2 as venue_score,
        ROW_NUMBER() OVER (PARTITION BY symbol ORDER BY 
            (1 - avg_slippage / NULLIF(MAX(avg_slippage) OVER (), 0)) * 0.3 +
            (1 - avg_latency_ms / NULLIF(MAX(avg_latency_ms) OVER (), 0)) * 0.2 +
            fill_rate * 0.3 +
            (1 - avg_market_impact / NULLIF(MAX(avg_market_impact) OVER (), 0)) * 0.2 DESC
        ) as venue_rank
    FROM venue_metrics
    WHERE execution_count >= 5  -- Minimum sample size
)
SELECT 
    symbol,
    venue_id,
    venue_rank,
    venue_score,
    avg_slippage,
    avg_latency_ms,
    fill_rate,
    avg_market_impact,
    -- Routing recommendation
    CASE 
        WHEN venue_rank = 1 THEN 'PRIMARY'
        WHEN venue_rank <= 3 THEN 'SECONDARY'
        ELSE 'BACKUP'
    END as routing_tier
FROM venue_ranking
ORDER BY symbol, venue_rank;

-- Order slicing algorithm
DELIMITER //
CREATE PROCEDURE slice_large_order(
    IN p_order_id VARCHAR(50),
    IN p_symbol VARCHAR(10),
    IN p_total_quantity DECIMAL(15,2),
    IN p_max_slice_size DECIMAL(15,2),
    IN p_time_interval_seconds INT
)
BEGIN
    DECLARE v_remaining_quantity DECIMAL(15,2) DEFAULT p_total_quantity;
    DECLARE v_slice_number INT DEFAULT 1;
    DECLARE v_slice_size DECIMAL(15,2);
    DECLARE v_schedule_time TIMESTAMP DEFAULT NOW();
    
    -- Get average daily volume for sizing constraints
    SELECT AVG(daily_volume) * 0.1 INTO @adv_limit  -- 10% of ADV
    FROM (
        SELECT DATE(trade_time) as trade_date, SUM(quantity) as daily_volume
        FROM trades 
        WHERE symbol = p_symbol 
          AND trade_time >= CURRENT_DATE - INTERVAL 30 DAY
        GROUP BY DATE(trade_time)
    ) daily_vol;
    
    -- Create order slices
    WHILE v_remaining_quantity > 0 DO
        -- Dynamic slice sizing based on market conditions
        SELECT 
            LEAST(
                p_max_slice_size,
                v_remaining_quantity,
                @adv_limit,
                -- Reduce size during high volatility
                CASE WHEN current_volatility > historical_avg_volatility * 1.5 
                     THEN p_max_slice_size * 0.7 
                     ELSE p_max_slice_size END
            ) INTO v_slice_size
        FROM (
            SELECT 
                STDDEV(price) as current_volatility,
                (SELECT AVG(daily_volatility) 
                 FROM historical_volatility 
                 WHERE symbol = p_symbol) as historical_avg_volatility
            FROM market_data 
            WHERE symbol = p_symbol 
              AND timestamp >= NOW() - INTERVAL 1 HOUR
        ) vol_calc;
        
        -- Insert slice into order_slices table
        INSERT INTO order_slices (
            parent_order_id, slice_number, symbol, quantity, 
            scheduled_time, status, slice_type
        ) VALUES (
            p_order_id, v_slice_number, p_symbol, v_slice_size,
            v_schedule_time, 'SCHEDULED', 
            CASE WHEN v_slice_number = 1 THEN 'INITIAL'
                 WHEN v_remaining_quantity - v_slice_size <= 0 THEN 'FINAL'
                 ELSE 'INTERMEDIATE' END
        );
        
        SET v_remaining_quantity = v_remaining_quantity - v_slice_size;
        SET v_slice_number = v_slice_number + 1;
        SET v_schedule_time = DATE_ADD(v_schedule_time, INTERVAL p_time_interval_seconds SECOND);
    END WHILE;
    
END//
DELIMITER ;

-- TWAP (Time-Weighted Average Price) execution
SELECT 
    order_id,
    symbol,
    -- TWAP calculation over execution period
    SUM(quantity * price) / SUM(quantity) as execution_twap,
    -- Benchmark TWAP (market prices during execution period)
    (SELECT SUM(price * volume) / SUM(volume)
     FROM market_data 
     WHERE symbol = oe.symbol 
       AND timestamp BETWEEN MIN(oe.execution_time) AND MAX(oe.execution_time)
    ) as benchmark_twap,
    -- Performance vs TWAP
    (SUM(quantity * price) / SUM(quantity)) - 
    (SELECT SUM(price * volume) / SUM(volume)
     FROM market_data 
     WHERE symbol = oe.symbol 
       AND timestamp BETWEEN MIN(oe.execution_time) AND MAX(oe.execution_time)
    ) as twap_slippage,
    -- Timing analysis
    MIN(execution_time) as start_time,
    MAX(execution_time) as end_time,
    TIMESTAMPDIFF(MINUTE, MIN(execution_time), MAX(execution_time)) as execution_duration_min,
    COUNT(*) as number_of_fills
FROM order_executions oe
WHERE execution_time >= CURRENT_DATE
GROUP BY order_id, symbol;

-- Implementation Shortfall calculation
WITH order_analysis AS (
    SELECT 
        o.order_id,
        o.symbol,
        o.side,
        o.order_quantity,
        o.order_price,
        o.order_time,
        -- Market price at order time
        (SELECT price FROM market_data 
         WHERE symbol = o.symbol 
           AND timestamp <= o.order_time 
         ORDER BY timestamp DESC LIMIT 1) as arrival_price,
        -- Execution details
        SUM(e.quantity) as executed_quantity,
        SUM(e.quantity * e.price) / NULLIF(SUM(e.quantity), 0) as avg_execution_price,
        MIN(e.execution_time) as first_fill_time,
        MAX(e.execution_time) as last_fill_time
    FROM orders o
    LEFT JOIN order_executions e ON o.order_id = e.order_id
    WHERE o.order_time >= CURRENT_DATE - INTERVAL 7 DAY
    GROUP BY o.order_id, o.symbol, o.side, o.order_quantity, o.order_price, o.order_time
)
SELECT 
    order_id,
    symbol,
    side,
    order_quantity,
    executed_quantity,
    arrival_price,
    avg_execution_price,
    -- Implementation shortfall components
    CASE WHEN side = 'BUY' THEN 
        (avg_execution_price - arrival_price) * executed_quantity
    ELSE 
        (arrival_price - avg_execution_price) * executed_quantity
    END as realized_cost,
    -- Opportunity cost for unexecuted quantity
    CASE WHEN side = 'BUY' THEN
        ((SELECT close_price FROM market_data 
          WHERE symbol = order_analysis.symbol 
            AND DATE(timestamp) = CURRENT_DATE
          ORDER BY timestamp DESC LIMIT 1) - arrival_price) * 
        (order_quantity - executed_quantity)
    ELSE
        (arrival_price - (SELECT close_price FROM market_data 
          WHERE symbol = order_analysis.symbol 
            AND DATE(timestamp) = CURRENT_DATE
          ORDER BY timestamp DESC LIMIT 1)) * 
        (order_quantity - executed_quantity)
    END as opportunity_cost,
    -- Total implementation shortfall
    (CASE WHEN side = 'BUY' THEN 
        (avg_execution_price - arrival_price) * executed_quantity
    ELSE 
        (arrival_price - avg_execution_price) * executed_quantity
    END) +
    (CASE WHEN side = 'BUY' THEN
        ((SELECT close_price FROM market_data 
          WHERE symbol = order_analysis.symbol 
            AND DATE(timestamp) = CURRENT_DATE
          ORDER BY timestamp DESC LIMIT 1) - arrival_price) * 
        (order_quantity - executed_quantity)
    ELSE
        (arrival_price - (SELECT close_price FROM market_data 
          WHERE symbol = order_analysis.symbol 
            AND DATE(timestamp) = CURRENT_DATE
          ORDER BY timestamp DESC LIMIT 1)) * 
        (order_quantity - executed_quantity)
    END) as total_implementation_shortfall
FROM order_analysis;

-- Dark pool vs lit market routing decision
SELECT 
    symbol,
    order_size_category,
    -- Dark pool metrics
    AVG(CASE WHEN venue_type = 'DARK' THEN fill_rate END) as dark_fill_rate,
    AVG(CASE WHEN venue_type = 'DARK' THEN market_impact END) as dark_market_impact,
    -- Lit market metrics  
    AVG(CASE WHEN venue_type = 'LIT' THEN fill_rate END) as lit_fill_rate,
    AVG(CASE WHEN venue_type = 'LIT' THEN market_impact END) as lit_market_impact,
    -- Routing recommendation
    CASE 
        WHEN AVG(CASE WHEN venue_type = 'DARK' THEN fill_rate END) > 0.8 
         AND AVG(CASE WHEN venue_type = 'DARK' THEN market_impact END) < 
             AVG(CASE WHEN venue_type = 'LIT' THEN market_impact END) * 0.7
        THEN 'PREFER_DARK'
        WHEN AVG(CASE WHEN venue_type = 'LIT' THEN fill_rate END) > 0.9
        THEN 'PREFER_LIT'
        ELSE 'MIXED_ROUTING'
    END as routing_recommendation
FROM (
    SELECT 
        e.symbol,
        v.venue_type,
        CASE 
            WHEN o.quantity < 1000 THEN 'SMALL'
            WHEN o.quantity < 10000 THEN 'MEDIUM' 
            ELSE 'LARGE' 
        END as order_size_category,
        COUNT(CASE WHEN e.status = 'FILLED' THEN 1 END) / COUNT(*) as fill_rate,
        AVG(ABS(e.execution_price - e.arrival_midpoint) / e.arrival_midpoint) as market_impact
    FROM order_executions e
    JOIN venues v ON e.venue_id = v.venue_id
    JOIN orders o ON e.order_id = o.order_id
    WHERE e.execution_time >= NOW() - INTERVAL 7 DAY
    GROUP BY e.symbol, v.venue_type, order_size_category
) venue_performance
GROUP BY symbol, order_size_category;`,

    'time-series': `-- Time Series Analysis Patterns for Trading
-- Moving averages and trend identification
WITH price_with_ma AS (
    SELECT 
        symbol,
        trade_date,
        close_price,
        -- Simple moving averages
        AVG(close_price) OVER (
            PARTITION BY symbol 
            ORDER BY trade_date 
            ROWS BETWEEN 9 PRECEDING AND CURRENT ROW
        ) as ma_10,
        AVG(close_price) OVER (
            PARTITION BY symbol 
            ORDER BY trade_date 
            ROWS BETWEEN 19 PRECEDING AND CURRENT ROW
        ) as ma_20,
        AVG(close_price) OVER (
            PARTITION BY symbol 
            ORDER BY trade_date 
            ROWS BETWEEN 49 PRECEDING AND CURRENT ROW
        ) as ma_50,
        AVG(close_price) OVER (
            PARTITION BY symbol 
            ORDER BY trade_date 
            ROWS BETWEEN 199 PRECEDING AND CURRENT ROW
        ) as ma_200,
        -- Exponential moving average approximation
        AVG(close_price) OVER (
            PARTITION BY symbol 
            ORDER BY trade_date 
            ROWS BETWEEN 19 PRECEDING AND CURRENT ROW
        ) * 0.1 + 
        LAG(close_price) OVER (PARTITION BY symbol ORDER BY trade_date) * 0.9 as ema_20
    FROM daily_market_data
),
trend_signals AS (
    SELECT 
        *,
        -- Trend identification
        CASE 
            WHEN close_price > ma_10 AND ma_10 > ma_20 AND ma_20 > ma_50 THEN 'STRONG_UPTREND'
            WHEN close_price > ma_20 AND ma_20 > ma_50 THEN 'UPTREND'
            WHEN close_price < ma_10 AND ma_10 < ma_20 AND ma_20 < ma_50 THEN 'STRONG_DOWNTREND'
            WHEN close_price < ma_20 AND ma_20 < ma_50 THEN 'DOWNTREND'
            ELSE 'SIDEWAYS'
        END as trend_direction,
        -- Golden cross / Death cross signals
        CASE 
            WHEN ma_50 > LAG(ma_50) OVER (PARTITION BY symbol ORDER BY trade_date)
             AND ma_200 < LAG(ma_200) OVER (PARTITION BY symbol ORDER BY trade_date)
             AND LAG(ma_50) OVER (PARTITION BY symbol ORDER BY trade_date) <= 
                 LAG(ma_200) OVER (PARTITION BY symbol ORDER BY trade_date)
             AND ma_50 > ma_200
            THEN 'GOLDEN_CROSS'
            WHEN ma_50 < LAG(ma_50) OVER (PARTITION BY symbol ORDER BY trade_date)
             AND ma_200 > LAG(ma_200) OVER (PARTITION BY symbol ORDER BY trade_date)
             AND LAG(ma_50) OVER (PARTITION BY symbol ORDER BY trade_date) >= 
                 LAG(ma_200) OVER (PARTITION BY symbol ORDER BY trade_date)
             AND ma_50 < ma_200
            THEN 'DEATH_CROSS'
            ELSE 'NO_SIGNAL'
        END as crossover_signal
    FROM price_with_ma
)
SELECT * FROM trend_signals WHERE symbol = 'AAPL' ORDER BY trade_date DESC LIMIT 100;

-- Volatility clustering and GARCH-like analysis
WITH daily_returns AS (
    SELECT 
        symbol,
        trade_date,
        close_price,
        (close_price - LAG(close_price) OVER (PARTITION BY symbol ORDER BY trade_date)) / 
        LAG(close_price) OVER (PARTITION BY symbol ORDER BY trade_date) as daily_return
    FROM daily_market_data
),
volatility_analysis AS (
    SELECT 
        symbol,
        trade_date,
        daily_return,
        -- Rolling volatility measures
        STDDEV(daily_return) OVER (
            PARTITION BY symbol 
            ORDER BY trade_date 
            ROWS BETWEEN 19 PRECEDING AND CURRENT ROW
        ) as volatility_20d,
        STDDEV(daily_return) OVER (
            PARTITION BY symbol 
            ORDER BY trade_date 
            ROWS BETWEEN 59 PRECEDING AND CURRENT ROW
        ) as volatility_60d,
        -- Volatility of volatility
        STDDEV(STDDEV(daily_return) OVER (
            PARTITION BY symbol 
            ORDER BY trade_date 
            ROWS BETWEEN 19 PRECEDING AND CURRENT ROW
        )) OVER (
            PARTITION BY symbol 
            ORDER BY trade_date 
            ROWS BETWEEN 59 PRECEDING AND CURRENT ROW
        ) as vol_of_vol,
        -- EWMA volatility approximation
        SQRT(0.06 * POWER(daily_return, 2) + 0.94 * 
             POWER(LAG(STDDEV(daily_return) OVER (
                 PARTITION BY symbol 
                 ORDER BY trade_date 
                 ROWS BETWEEN 19 PRECEDING AND CURRENT ROW
             )) OVER (PARTITION BY symbol ORDER BY trade_date), 2)
        ) as ewma_volatility
    FROM daily_returns
)
SELECT 
    symbol,
    trade_date,
    volatility_20d,
    volatility_60d,
    vol_of_vol,
    ewma_volatility,
    -- Volatility regime identification
    CASE 
        WHEN volatility_20d > volatility_60d * 1.5 THEN 'HIGH_VOLATILITY'
        WHEN volatility_20d < volatility_60d * 0.7 THEN 'LOW_VOLATILITY'
        ELSE 'NORMAL_VOLATILITY'
    END as volatility_regime,
    -- Volatility clustering detection
    CASE 
        WHEN ABS(daily_return) > volatility_20d * 2 
         AND ABS(LAG(daily_return) OVER (PARTITION BY symbol ORDER BY trade_date)) > volatility_20d * 2
        THEN 'VOLATILITY_CLUSTER'
        ELSE 'NORMAL'
    END as clustering_indicator
FROM volatility_analysis
WHERE symbol IN ('AAPL', 'GOOGL', 'MSFT');

-- Seasonal patterns and cyclical analysis
SELECT 
    symbol,
    MONTH(trade_date) as month,
    DAYNAME(trade_date) as day_of_week,
    HOUR(timestamp) as hour,
    -- Seasonal return patterns
    AVG((close_price - open_price) / open_price * 100) as avg_intraday_return,
    STDDEV((close_price - open_price) / open_price * 100) as return_volatility,
    COUNT(*) as observations,
    -- Statistical significance test (t-statistic approximation)
    AVG((close_price - open_price) / open_price * 100) / 
    (STDDEV((close_price - open_price) / open_price * 100) / SQRT(COUNT(*))) as t_statistic,
    -- Seasonal strength indicator
    CASE 
        WHEN ABS(AVG((close_price - open_price) / open_price * 100)) > 
             STDDEV((close_price - open_price) / open_price * 100) / SQRT(COUNT(*)) * 2
        THEN 'SIGNIFICANT_PATTERN'
        ELSE 'NOT_SIGNIFICANT'
    END as pattern_strength
FROM market_data
WHERE timestamp >= CURRENT_DATE - INTERVAL 2 YEAR
  AND DAYOFWEEK(trade_date) BETWEEN 2 AND 6  -- Weekdays only
GROUP BY symbol, MONTH(trade_date), DAYNAME(trade_date), HOUR(timestamp)
HAVING COUNT(*) >= 20  -- Minimum sample size
ORDER BY symbol, month, FIELD(day_of_week, 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'), hour;

-- Support and resistance level identification
WITH price_levels AS (
    SELECT DISTINCT
        symbol,
        ROUND(close_price, 2) as price_level,
        COUNT(*) as touch_count,
        MIN(trade_date) as first_touch,
        MAX(trade_date) as last_touch,
        DATEDIFF(MAX(trade_date), MIN(trade_date)) as level_duration
    FROM daily_market_data
    WHERE trade_date >= CURRENT_DATE - INTERVAL 252 DAY
    GROUP BY symbol, ROUND(close_price, 2)
    HAVING COUNT(*) >= 3  -- Minimum touches for significance
),
significant_levels AS (
    SELECT 
        pl.*,
        -- Recent activity around this level
        (SELECT COUNT(*) 
         FROM daily_market_data dmd 
         WHERE dmd.symbol = pl.symbol 
           AND ABS(dmd.close_price - pl.price_level) / pl.price_level <= 0.02  -- Within 2%
           AND dmd.trade_date >= CURRENT_DATE - INTERVAL 30 DAY
        ) as recent_touches,
        -- Current distance from level
        ABS((SELECT close_price FROM daily_market_data 
             WHERE symbol = pl.symbol 
             ORDER BY trade_date DESC LIMIT 1) - pl.price_level) / pl.price_level * 100 as distance_pct,
        -- Level type classification
        CASE 
            WHEN pl.price_level > (SELECT AVG(close_price) FROM daily_market_data 
                                  WHERE symbol = pl.symbol 
                                    AND trade_date >= CURRENT_DATE - INTERVAL 60 DAY)
            THEN 'RESISTANCE'
            ELSE 'SUPPORT'
        END as level_type
    FROM price_levels pl
)
SELECT 
    symbol,
    price_level,
    level_type,
    touch_count,
    recent_touches,
    distance_pct,
    level_duration,
    first_touch,
    last_touch,
    -- Strength scoring
    (touch_count * 0.4 + recent_touches * 0.3 + 
     LEAST(level_duration / 30, 10) * 0.2 + 
     (CASE WHEN distance_pct < 5 THEN 10 - distance_pct * 2 ELSE 0 END) * 0.1) as strength_score
FROM significant_levels
WHERE distance_pct <= 20  -- Only levels within 20% of current price
ORDER BY symbol, strength_score DESC;

-- Mean reversion detection
WITH price_deviations AS (
    SELECT 
        symbol,
        trade_date,
        close_price,
        AVG(close_price) OVER (
            PARTITION BY symbol 
            ORDER BY trade_date 
            ROWS BETWEEN 19 PRECEDING AND CURRENT ROW
        ) as ma_20,
        STDDEV(close_price) OVER (
            PARTITION BY symbol 
            ORDER BY trade_date 
            ROWS BETWEEN 19 PRECEDING AND CURRENT ROW
        ) as std_20,
        (close_price - AVG(close_price) OVER (
            PARTITION BY symbol 
            ORDER BY trade_date 
            ROWS BETWEEN 19 PRECEDING AND CURRENT ROW
        )) / STDDEV(close_price) OVER (
            PARTITION BY symbol 
            ORDER BY trade_date 
            ROWS BETWEEN 19 PRECEDING AND CURRENT ROW
        ) as z_score
    FROM daily_market_data
),
mean_reversion_signals AS (
    SELECT 
        *,
        -- Mean reversion signals
        CASE 
            WHEN z_score > 2 THEN 'OVERSOLD'
            WHEN z_score < -2 THEN 'OVERBOUGHT'
            WHEN z_score BETWEEN -0.5 AND 0.5 THEN 'MEAN'
            ELSE 'NEUTRAL'
        END as reversion_signal,
        -- Bollinger Band position
        (close_price - (ma_20 - 2 * std_20)) / (4 * std_20) as bb_position,
        -- Rate of change
        (close_price - LAG(close_price, 5) OVER (PARTITION BY symbol ORDER BY trade_date)) / 
        LAG(close_price, 5) OVER (PARTITION BY symbol ORDER BY trade_date) * 100 as roc_5d
    FROM price_deviations
)
SELECT 
    symbol,
    trade_date,
    close_price,
    ma_20,
    z_score,
    reversion_signal,
    bb_position,
    roc_5d,
    -- Trading signal based on mean reversion
    CASE 
        WHEN z_score > 2 AND roc_5d < -5 THEN 'STRONG_SELL'
        WHEN z_score > 1.5 THEN 'SELL'
        WHEN z_score < -2 AND roc_5d > 5 THEN 'STRONG_BUY'
        WHEN z_score < -1.5 THEN 'BUY'
        ELSE 'HOLD'
    END as trading_signal
FROM mean_reversion_signals
WHERE symbol IN ('AAPL', 'MSFT', 'GOOGL')
ORDER BY symbol, trade_date DESC;`,

    'calculations': `-- Financial Calculations Patterns
-- Returns calculation across different time horizons
WITH price_data AS (
    SELECT 
        symbol,
        trade_date,
        close_price,
        LAG(close_price, 1) OVER (PARTITION BY symbol ORDER BY trade_date) as prev_1d,
        LAG(close_price, 5) OVER (PARTITION BY symbol ORDER BY trade_date) as prev_5d,
        LAG(close_price, 21) OVER (PARTITION BY symbol ORDER BY trade_date) as prev_21d,
        LAG(close_price, 63) OVER (PARTITION BY symbol ORDER BY trade_date) as prev_63d,
        LAG(close_price, 252) OVER (PARTITION BY symbol ORDER BY trade_date) as prev_252d
    FROM daily_market_data
    WHERE trade_date >= CURRENT_DATE - INTERVAL 400 DAY
),
returns_calculated AS (
    SELECT 
        symbol,
        trade_date,
        close_price,
        -- Simple returns
        (close_price - prev_1d) / prev_1d * 100 as return_1d,
        (close_price - prev_5d) / prev_5d * 100 as return_5d,
        (close_price - prev_21d) / prev_21d * 100 as return_21d,
        (close_price - prev_63d) / prev_63d * 100 as return_63d,
        (close_price - prev_252d) / prev_252d * 100 as return_252d,
        -- Log returns (for statistical analysis)
        LN(close_price / prev_1d) * 100 as log_return_1d,
        -- Annualized returns
        POWER(close_price / prev_21d, 252/21) - 1 as annualized_return_21d,
        POWER(close_price / prev_252d, 1) - 1 as annualized_return_252d
    FROM price_data
    WHERE prev_1d IS NOT NULL
)
SELECT 
    symbol,
    trade_date,
    return_1d,
    return_5d,
    return_21d,
    return_63d,
    return_252d,
    log_return_1d,
    annualized_return_21d,
    annualized_return_252d,
    -- Return classification
    CASE 
        WHEN return_1d > 5 THEN 'LARGE_GAIN'
        WHEN return_1d > 2 THEN 'GAIN'
        WHEN return_1d < -5 THEN 'LARGE_LOSS'
        WHEN return_1d < -2 THEN 'LOSS'
        ELSE 'NEUTRAL'
    END as return_category
FROM returns_calculated
ORDER BY symbol, trade_date DESC;

-- Volatility calculations and risk metrics
WITH returns_base AS (
    SELECT 
        symbol,
        trade_date,
        close_price,
        LN(close_price / LAG(close_price) OVER (PARTITION BY symbol ORDER BY trade_date)) as log_return
    FROM daily_market_data
    WHERE trade_date >= CURRENT_DATE - INTERVAL 500 DAY
),
volatility_metrics AS (
    SELECT 
        symbol,
        trade_date,
        log_return,
        -- Historical volatility (annualized)
        STDDEV(log_return) OVER (
            PARTITION BY symbol 
            ORDER BY trade_date 
            ROWS BETWEEN 19 PRECEDING AND CURRENT ROW
        ) * SQRT(252) * 100 as volatility_20d,
        STDDEV(log_return) OVER (
            PARTITION BY symbol 
            ORDER BY trade_date 
            ROWS BETWEEN 59 PRECEDING AND CURRENT ROW
        ) * SQRT(252) * 100 as volatility_60d,
        STDDEV(log_return) OVER (
            PARTITION BY symbol 
            ORDER BY trade_date 
            ROWS BETWEEN 251 PRECEDING AND CURRENT ROW
        ) * SQRT(252) * 100 as volatility_252d,
        -- Downside deviation (for Sortino ratio)
        SQRT(AVG(CASE WHEN log_return < 0 THEN POWER(log_return, 2) ELSE 0 END) OVER (
            PARTITION BY symbol 
            ORDER BY trade_date 
            ROWS BETWEEN 59 PRECEDING AND CURRENT ROW
        )) * SQRT(252) * 100 as downside_deviation_60d,
        -- Skewness approximation
        (AVG(POWER(log_return, 3)) OVER (
            PARTITION BY symbol 
            ORDER BY trade_date 
            ROWS BETWEEN 59 PRECEDING AND CURRENT ROW
        )) / POWER(STDDEV(log_return) OVER (
            PARTITION BY symbol 
            ORDER BY trade_date 
            ROWS BETWEEN 59 PRECEDING AND CURRENT ROW
        ), 3) as skewness_60d,
        -- Kurtosis approximation
        (AVG(POWER(log_return, 4)) OVER (
            PARTITION BY symbol 
            ORDER BY trade_date 
            ROWS BETWEEN 59 PRECEDING AND CURRENT ROW
        )) / POWER(STDDEV(log_return) OVER (
            PARTITION BY symbol 
            ORDER BY trade_date 
            ROWS BETWEEN 59 PRECEDING AND CURRENT ROW
        ), 4) - 3 as excess_kurtosis_60d
    FROM returns_base
)
SELECT 
    symbol,
    trade_date,
    volatility_20d,
    volatility_60d,
    volatility_252d,
    downside_deviation_60d,
    skewness_60d,
    excess_kurtosis_60d,
    -- Volatility regime classification
    CASE 
        WHEN volatility_20d > volatility_252d * 1.5 THEN 'HIGH_VOL_REGIME'
        WHEN volatility_20d < volatility_252d * 0.7 THEN 'LOW_VOL_REGIME'
        ELSE 'NORMAL_VOL_REGIME'
    END as vol_regime,
    -- Risk characteristics
    CASE 
        WHEN skewness_60d < -0.5 AND excess_kurtosis_60d > 1 THEN 'HIGH_TAIL_RISK'
        WHEN skewness_60d > 0.5 THEN 'POSITIVE_SKEW'
        ELSE 'NORMAL_DISTRIBUTION'
    END as risk_profile
FROM volatility_metrics
WHERE trade_date >= CURRENT_DATE - INTERVAL 90 DAY;

-- Correlation analysis between instruments
WITH daily_returns AS (
    SELECT 
        symbol,
        trade_date,
        LN(close_price / LAG(close_price) OVER (PARTITION BY symbol ORDER BY trade_date)) as daily_return
    FROM daily_market_data
    WHERE trade_date >= CURRENT_DATE - INTERVAL 252 DAY
),
correlation_pairs AS (
    SELECT 
        a.symbol as symbol_a,
        b.symbol as symbol_b,
        a.trade_date,
        a.daily_return as return_a,
        b.daily_return as return_b
    FROM daily_returns a
    JOIN daily_returns b ON a.trade_date = b.trade_date
    WHERE a.symbol < b.symbol  -- Avoid duplicate pairs
      AND a.daily_return IS NOT NULL 
      AND b.daily_return IS NOT NULL
),
correlation_calculations AS (
    SELECT 
        symbol_a,
        symbol_b,
        COUNT(*) as observations,
        -- Pearson correlation
        CORR(return_a, return_b) as correlation,
        -- Rolling 60-day correlation
        AVG(CORR(return_a, return_b) OVER (
            PARTITION BY symbol_a, symbol_b 
            ORDER BY trade_date 
            ROWS BETWEEN 59 PRECEDING AND CURRENT ROW
        )) as avg_correlation_60d,
        STDDEV(CORR(return_a, return_b) OVER (
            PARTITION BY symbol_a, symbol_b 
            ORDER BY trade_date 
            ROWS BETWEEN 59 PRECEDING AND CURRENT ROW
        )) as correlation_volatility,
        -- Beta calculation (symbol_a vs symbol_b)
        COVAR_POP(return_a, return_b) / NULLIF(VAR_POP(return_b), 0) as beta_a_vs_b,
        -- R-squared
        POWER(CORR(return_a, return_b), 2) as r_squared
    FROM correlation_pairs
    GROUP BY symbol_a, symbol_b
    HAVING COUNT(*) >= 60  -- Minimum observations
)
SELECT 
    symbol_a,
    symbol_b,
    observations,
    ROUND(correlation, 4) as correlation,
    ROUND(avg_correlation_60d, 4) as avg_correlation_60d,
    ROUND(correlation_volatility, 4) as correlation_volatility,
    ROUND(beta_a_vs_b, 4) as beta,
    ROUND(r_squared, 4) as r_squared,
    -- Correlation strength classification
    CASE 
        WHEN ABS(correlation) >= 0.8 THEN 'VERY_HIGH'
        WHEN ABS(correlation) >= 0.6 THEN 'HIGH'
        WHEN ABS(correlation) >= 0.4 THEN 'MODERATE'
        WHEN ABS(correlation) >= 0.2 THEN 'LOW'
        ELSE 'VERY_LOW'
    END as correlation_strength,
    -- Relationship stability
    CASE 
        WHEN correlation_volatility < 0.1 THEN 'STABLE'
        WHEN correlation_volatility < 0.2 THEN 'MODERATE'
        ELSE 'UNSTABLE'
    END as relationship_stability
FROM correlation_calculations
ORDER BY ABS(correlation) DESC;

-- Performance ratios and risk-adjusted returns
WITH portfolio_metrics AS (
    SELECT 
        trader_id,
        trade_date,
        daily_pnl,
        portfolio_value,
        daily_pnl / portfolio_value as daily_return,
        -- Benchmark return (using SPY as proxy)
        (SELECT (close_price - LAG(close_price) OVER (ORDER BY trade_date)) / 
                LAG(close_price) OVER (ORDER BY trade_date)
         FROM daily_market_data 
         WHERE symbol = 'SPY' AND trade_date = pm.trade_date) as benchmark_return
    FROM (
        SELECT 
            trader_id,
            DATE(trade_time) as trade_date,
            SUM(pnl) as daily_pnl,
            AVG(portfolio_value) as portfolio_value
        FROM trading_performance
        WHERE trade_time >= CURRENT_DATE - INTERVAL 252 DAY
        GROUP BY trader_id, DATE(trade_time)
    ) pm
),
performance_calculations AS (
    SELECT 
        trader_id,
        -- Basic return metrics
        AVG(daily_return) * 252 as annualized_return,
        STDDEV(daily_return) * SQRT(252) as annualized_volatility,
        -- Benchmark comparison
        AVG(benchmark_return) * 252 as benchmark_annualized_return,
        AVG(daily_return - benchmark_return) * 252 as excess_return,
        STDDEV(daily_return - benchmark_return) * SQRT(252) as tracking_error,
        -- Beta calculation
        COVAR_POP(daily_return, benchmark_return) / NULLIF(VAR_POP(benchmark_return), 0) as beta,
        -- Downside metrics
        SQRT(AVG(CASE WHEN daily_return < 0 THEN POWER(daily_return, 2) ELSE 0 END)) * SQRT(252) as downside_deviation,
        -- Drawdown calculation
        MIN(daily_return) as worst_daily_return,
        COUNT(CASE WHEN daily_return < 0 THEN 1 END) / COUNT(*) as loss_ratio
    FROM portfolio_metrics
    WHERE daily_return IS NOT NULL AND benchmark_return IS NOT NULL
    GROUP BY trader_id
)
SELECT 
    trader_id,
    ROUND(annualized_return * 100, 2) as annualized_return_pct,
    ROUND(annualized_volatility * 100, 2) as annualized_volatility_pct,
    ROUND(excess_return * 100, 2) as excess_return_pct,
    ROUND(beta, 3) as beta,
    -- Risk-adjusted performance ratios
    ROUND(annualized_return / NULLIF(annualized_volatility, 0), 3) as sharpe_ratio,
    ROUND(excess_return / NULLIF(tracking_error, 0), 3) as information_ratio,
    ROUND(annualized_return / NULLIF(downside_deviation, 0), 3) as sortino_ratio,
    ROUND((annualized_return - benchmark_annualized_return - beta * (0 - benchmark_annualized_return)), 4) as jensen_alpha,
    ROUND(excess_return / NULLIF(beta, 0), 4) as treynor_ratio,
    -- Additional metrics
    ROUND(worst_daily_return * 100, 2) as worst_daily_loss_pct,
    ROUND(loss_ratio * 100, 1) as loss_days_pct,
    -- Performance score (composite)
    (RANK() OVER (ORDER BY annualized_return / NULLIF(annualized_volatility, 0) DESC) * 0.4 +
     RANK() OVER (ORDER BY annualized_return DESC) * 0.3 +
     RANK() OVER (ORDER BY downside_deviation ASC) * 0.3) as performance_rank
FROM performance_calculations
ORDER BY sharpe_ratio DESC;

-- Financial calendar and time-decay calculations
SELECT 
    CURRENT_DATE as calculation_date,
    symbol,
    -- Business days calculations
    CASE 
        WHEN DAYOFWEEK(CURRENT_DATE) = 1 THEN CURRENT_DATE + INTERVAL 1 DAY  -- Sunday -> Monday
        WHEN DAYOFWEEK(CURRENT_DATE) = 7 THEN CURRENT_DATE + INTERVAL 2 DAY  -- Saturday -> Monday
        ELSE CURRENT_DATE
    END as next_business_day,
    -- Days to expiration calculations
    DATEDIFF(expiration_date, CURRENT_DATE) as calendar_days_to_expiry,
    -- Business days to expiration (approximation: exclude weekends)
    FLOOR(DATEDIFF(expiration_date, CURRENT_DATE) * 5/7) as business_days_to_expiry,
    -- Time decay factor (for options)
    EXP(-0.05 * DATEDIFF(expiration_date, CURRENT_DATE) / 365) as time_decay_factor,
    -- Annualized time factor
    DATEDIFF(expiration_date, CURRENT_DATE) / 365.25 as years_to_expiry,
    SQRT(DATEDIFF(expiration_date, CURRENT_DATE) / 365.25) as sqrt_time_factor,
    -- Holiday adjustments (simplified - would need holiday table)
    CASE 
        WHEN MONTH(CURRENT_DATE) = 12 AND DAY(CURRENT_DATE) = 25 THEN 'CHRISTMAS'
        WHEN MONTH(CURRENT_DATE) = 1 AND DAY(CURRENT_DATE) = 1 THEN 'NEW_YEAR'
        WHEN MONTH(CURRENT_DATE) = 7 AND DAY(CURRENT_DATE) = 4 THEN 'JULY_4TH'
        ELSE 'REGULAR_DAY'
    END as holiday_status
FROM instruments
WHERE instrument_type = 'OPTION' 
  AND expiration_date > CURRENT_DATE;`
  };

  return (
    <div style={{
      padding: '40px',
      fontFamily: 'Inter, system-ui, sans-serif',
      backgroundColor: '#0f172a',
      color: 'white',
      minHeight: '100vh'
    }}>
      <div style={{ marginBottom: '40px' }}>
        <h1 style={{ 
          fontSize: '36px', 
          fontWeight: '700', 
          marginBottom: '16px',
          background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent'
        }}>
          Trading SQL Patterns
        </h1>
        <p style={{ fontSize: '18px', color: '#94a3b8', lineHeight: '1.6' }}>
          Specialized SQL patterns and queries optimized for financial trading systems
        </p>
      </div>

      <div style={{ display: 'flex', gap: '40px' }}>
        <div style={{ flex: '1', maxWidth: '300px' }}>
          <h2 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '24px', color: '#10b981' }}>
            Trading Patterns
          </h2>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {patterns.map((pattern) => (
              <button
                key={pattern.id}
                onClick={() => setSelectedPattern(pattern.id)}
                style={{
                  padding: '16px',
                  backgroundColor: selectedPattern === pattern.id ? 'rgba(16, 185, 129, 0.2)' : 'rgba(30, 41, 59, 0.5)',
                  border: `2px solid ${selectedPattern === pattern.id ? '#10b981' : 'transparent'}`,
                  borderRadius: '12px',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  textAlign: 'left',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px'
                }}
              >
                <div style={{ color: selectedPattern === pattern.id ? '#10b981' : '#64748b' }}>
                  {pattern.icon}
                </div>
                <div>
                  <div style={{ 
                    fontWeight: '600', 
                    color: selectedPattern === pattern.id ? '#10b981' : 'white',
                    marginBottom: '4px'
                  }}>
                    {pattern.name}
                  </div>
                  <div style={{ fontSize: '14px', color: '#94a3b8' }}>
                    {pattern.description}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        <div style={{ flex: '2' }}>
          <div style={{ marginBottom: '24px' }}>
            <div style={{ display: 'flex', gap: '8px', marginBottom: '24px' }}>
              {['overview', 'definition', 'code'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  style={{
                    padding: '12px 24px',
                    backgroundColor: activeTab === tab ? '#10b981' : 'transparent',
                    border: `2px solid ${activeTab === tab ? '#10b981' : '#374151'}`,
                    borderRadius: '8px',
                    color: activeTab === tab ? 'white' : '#94a3b8',
                    cursor: 'pointer',
                    fontWeight: '600',
                    textTransform: 'capitalize',
                    transition: 'all 0.2s'
                  }}
                >
                  {tab}
                </button>
              ))}
            </div>
          </div>

          {activeTab === 'overview' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <h2 style={{ fontSize: '28px', fontWeight: '600', marginBottom: '24px', color: '#10b981' }}>
                Trading SQL Patterns Overview
              </h2>
              <div style={{ fontSize: '16px', lineHeight: '1.8', color: '#e2e8f0' }}>
                <p style={{ marginBottom: '20px' }}>
                  Specialized SQL patterns for trading systems implement domain-specific calculations, 
                  analytics, and data processing workflows. These patterns handle market data processing, 
                  risk calculations, performance analytics, and time-series analysis essential for 
                  quantitative trading operations.
                </p>
                <div style={{ marginBottom: '24px' }}>
                  <h3 style={{ color: '#10b981', fontSize: '20px', marginBottom: '16px' }}>Trading-Specific Patterns:</h3>
                  <ul style={{ marginLeft: '24px', lineHeight: '1.8' }}>
                    <li><strong>Market Data:</strong> OHLC aggregation, gap detection, real-time processing</li>
                    <li><strong>Risk Analytics:</strong> VaR, CVaR, stress testing, and exposure calculations</li>
                    <li><strong>Performance:</strong> P&L attribution, benchmark analysis, risk-adjusted returns</li>
                    <li><strong>Order Routing:</strong> Venue selection, execution quality, smart routing logic</li>
                    <li><strong>Time Series:</strong> Technical indicators, seasonal patterns, volatility modeling</li>
                    <li><strong>Financial Calcs:</strong> Returns, correlations, Greeks, and option pricing</li>
                  </ul>
                </div>
                <div style={{ 
                  backgroundColor: 'rgba(16, 185, 129, 0.1)', 
                  padding: '20px', 
                  borderRadius: '8px',
                  border: '1px solid #10b981'
                }}>
                  <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Production Considerations:</h4>
                  <p>
                    These patterns are optimized for high-frequency trading environments, incorporating 
                    proper indexing strategies, efficient window functions, and real-time processing 
                    capabilities required for institutional trading operations.
                  </p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'definition' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <h2 style={{ fontSize: '24px', fontWeight: '600', marginBottom: '20px', color: '#10b981' }}>
                {patterns.find(p => p.id === selectedPattern)?.name} Definition
              </h2>
              <p style={{ fontSize: '16px', lineHeight: '1.8', color: '#e2e8f0' }}>
                {definitions[selectedPattern]}
              </p>
            </div>
          )}

          {activeTab === 'code' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <div style={{ marginBottom: '20px' }}>
                <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px' }}>
                  {patterns.find(p => p.id === selectedPattern)?.name} Examples
                </h3>
              </div>
              <SyntaxHighlighter
                language="sql"
                style={oneDark}
                customStyle={{
                  backgroundColor: '#1e293b',
                  padding: '20px',
                  borderRadius: '8px',
                  fontSize: '14px',
                  lineHeight: '1.6'
                }}
              >
                {codeExamples[selectedPattern]}
              </SyntaxHighlighter>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SQLTradingPatterns;
import React, { useState } from 'react';
import { Prism as <PERSON><PERSON>ta<PERSON><PERSON><PERSON><PERSON>er } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Server, GitBranch, Shield, Settings, Zap, BarChart3 } from 'lucide-react';

const RabbitMQ = () => {
  const [selectedTopic, setSelectedTopic] = useState('exchanges');
  const [activeTab, setActiveTab] = useState('overview');

  const topics = [
    {
      id: 'exchanges',
      name: 'Exchanges & Routing',
      icon: <GitBranch size={20} />,
      description: 'Message routing and exchange types'
    },
    {
      id: 'queues',
      name: 'Queues & Consumers',
      icon: <Server size={20} />,
      description: 'Queue management and message consumption'
    },
    {
      id: 'publishers',
      name: 'Publishers & Reliability',
      icon: <Zap size={20} />,
      description: 'Message publishing with confirmations'
    },
    {
      id: 'clustering',
      name: 'Clustering & HA',
      icon: <BarChart3 size={20} />,
      description: 'High availability and clustering'
    },
    {
      id: 'management',
      name: 'Management & Monitoring',
      icon: <Settings size={20} />,
      description: 'Administration and monitoring tools'
    },
    {
      id: 'security',
      name: 'Security & Policies',
      icon: <Shield size={20} />,
      description: 'Authentication and access control'
    }
  ];

  const definitions = {
    'exchanges': `RabbitMQ exchanges are routing mechanisms that receive messages from producers and route them to queues based on routing rules. In trading systems, different exchange types enable flexible message routing for order management, market data distribution, and trade confirmations.`,
    'queues': `RabbitMQ queues store messages until they are consumed by applications. Trading systems use durable queues for critical data like trade confirmations, and prioritized queues for urgent order processing. Queue management includes dead letter handling and message TTL policies.`,
    'publishers': `RabbitMQ publishers send messages to exchanges with delivery guarantees. Trading systems require reliable publishing with confirmations, transactions, and mandatory routing to ensure critical trading events are never lost during order processing and settlement workflows.`,
    'clustering': `RabbitMQ clustering provides high availability and horizontal scaling for trading systems. Clustered nodes share queues and exchanges, with queue mirroring ensuring no message loss during broker failures. This is essential for continuous trading operations.`,
    'management': `RabbitMQ management includes monitoring message rates, queue depths, connection counts, and resource usage. Trading systems require real-time monitoring of order flow, latency metrics, and system health to ensure optimal performance during market hours.`,
    'security': `RabbitMQ security includes user authentication, authorization policies, and SSL/TLS encryption. Trading systems implement role-based access control, audit logging, and secure connections to protect sensitive financial data and ensure regulatory compliance.`
  };

  const codeExamples = {
    'exchanges': `// RabbitMQ Exchange Configuration for Trading Systems
import com.rabbitmq.client.*;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeoutException;

public class TradingExchangeSetup {
    private final Connection connection;
    private final Channel channel;
    
    // Exchange names for different trading flows
    public static final String MARKET_DATA_EXCHANGE = "market.data.exchange";
    public static final String ORDER_EVENTS_EXCHANGE = "order.events.exchange";
    public static final String TRADE_CONFIRMATIONS_EXCHANGE = "trade.confirmations.exchange";
    public static final String RISK_ALERTS_EXCHANGE = "risk.alerts.exchange";
    public static final String SETTLEMENT_EXCHANGE = "settlement.exchange";
    
    public TradingExchangeSetup() throws IOException, TimeoutException {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost("rabbitmq-cluster.trading.com");
        factory.setPort(5672);
        factory.setUsername("trading-system");
        factory.setPassword("secure-password");
        factory.setVirtualHost("/trading");
        
        // Connection settings for high availability
        factory.setAutomaticRecoveryEnabled(true);
        factory.setNetworkRecoveryInterval(10000);
        factory.setRequestedHeartbeat(30);
        factory.setConnectionTimeout(30000);
        
        this.connection = factory.newConnection();
        this.channel = connection.createChannel();
    }
    
    public void setupTradingExchanges() throws IOException {
        // Market Data Exchange - Topic exchange for symbol-based routing
        channel.exchangeDeclare(MARKET_DATA_EXCHANGE, BuiltinExchangeType.TOPIC, true, false, null);
        
        // Order Events Exchange - Direct exchange for precise routing
        channel.exchangeDeclare(ORDER_EVENTS_EXCHANGE, BuiltinExchangeType.DIRECT, true, false, null);
        
        // Trade Confirmations Exchange - Headers exchange for complex routing
        channel.exchangeDeclare(TRADE_CONFIRMATIONS_EXCHANGE, BuiltinExchangeType.HEADERS, true, false, null);
        
        // Risk Alerts Exchange - Fanout for broadcasting alerts
        channel.exchangeDeclare(RISK_ALERTS_EXCHANGE, BuiltinExchangeType.FANOUT, true, false, null);
        
        // Settlement Exchange - Topic with dead letter handling
        Map<String, Object> settlementArgs = new HashMap<>();
        settlementArgs.put("x-dead-letter-exchange", "settlement.dlx.exchange");
        channel.exchangeDeclare(SETTLEMENT_EXCHANGE, BuiltinExchangeType.TOPIC, true, false, settlementArgs);
        
        // Dead Letter Exchange for failed settlements
        channel.exchangeDeclare("settlement.dlx.exchange", BuiltinExchangeType.DIRECT, true, false, null);
        
        System.out.println("Trading exchanges configured successfully");
    }
    
    public void setupTradingQueues() throws IOException {
        // Market Data Queues - Per symbol queues
        String[] symbols = {"AAPL", "GOOGL", "MSFT", "TSLA"};
        for (String symbol : symbols) {
            String queueName = "market.data." + symbol.toLowerCase();
            Map<String, Object> args = new HashMap<>();
            args.put("x-message-ttl", 60000); // 1 minute TTL for market data
            args.put("x-max-length", 10000); // Limit queue length
            
            channel.queueDeclare(queueName, true, false, false, args);
            channel.queueBind(queueName, MARKET_DATA_EXCHANGE, "market." + symbol);
        }
        
        // Order Processing Queues - Priority queues for different order types
        Map<String, Object> orderQueueArgs = new HashMap<>();
        orderQueueArgs.put("x-max-priority", 10); // Priority levels 0-10
        orderQueueArgs.put("x-dead-letter-exchange", "order.dlx.exchange");
        
        channel.queueDeclare("orders.market", true, false, false, orderQueueArgs);
        channel.queueDeclare("orders.limit", true, false, false, orderQueueArgs);
        channel.queueDeclare("orders.stop", true, false, false, orderQueueArgs);
        
        // Bind order queues
        channel.queueBind("orders.market", ORDER_EVENTS_EXCHANGE, "order.market");
        channel.queueBind("orders.limit", ORDER_EVENTS_EXCHANGE, "order.limit");
        channel.queueBind("orders.stop", ORDER_EVENTS_EXCHANGE, "order.stop");
        
        // Trade Confirmation Queue - Headers-based routing
        Map<String, Object> tradeArgs = new HashMap<>();
        tradeArgs.put("x-dead-letter-exchange", "trade.dlx.exchange");
        
        channel.queueDeclare("trade.confirmations", true, false, false, tradeArgs);
        
        // Bind with headers matching
        Map<String, Object> bindHeaders = new HashMap<>();
        bindHeaders.put("x-match", "any");
        bindHeaders.put("trade-type", "EQUITY");
        bindHeaders.put("settlement-date", "T+2");
        channel.queueBind("trade.confirmations", TRADE_CONFIRMATIONS_EXCHANGE, "", bindHeaders);
        
        // Risk Alert Queues - Multiple consumers for alerts
        channel.queueDeclare("risk.alerts.trading", true, false, false, null);
        channel.queueDeclare("risk.alerts.compliance", true, false, false, null);
        channel.queueDeclare("risk.alerts.management", true, false, false, null);
        
        // Bind all risk queues to fanout exchange
        channel.queueBind("risk.alerts.trading", RISK_ALERTS_EXCHANGE, "");
        channel.queueBind("risk.alerts.compliance", RISK_ALERTS_EXCHANGE, "");
        channel.queueBind("risk.alerts.management", RISK_ALERTS_EXCHANGE, "");
        
        // Settlement Queues - Topic-based routing by currency and region
        channel.queueDeclare("settlement.usd.americas", true, false, false, null);
        channel.queueDeclare("settlement.eur.emea", true, false, false, null);
        channel.queueDeclare("settlement.jpy.apac", true, false, false, null);
        
        channel.queueBind("settlement.usd.americas", SETTLEMENT_EXCHANGE, "settlement.USD.AMERICAS");
        channel.queueBind("settlement.eur.emea", SETTLEMENT_EXCHANGE, "settlement.EUR.EMEA");
        channel.queueBind("settlement.jpy.apac", SETTLEMENT_EXCHANGE, "settlement.JPY.APAC");
        
        System.out.println("Trading queues configured successfully");
    }
    
    public void close() throws IOException, TimeoutException {
        if (channel != null && channel.isOpen()) {
            channel.close();
        }
        if (connection != null && connection.isOpen()) {
            connection.close();
        }
    }
}

// Market Data Publisher with Topic Exchange
public class MarketDataPublisher {
    private final Channel channel;
    
    public MarketDataPublisher(Channel channel) {
        this.channel = channel;
    }
    
    public void publishMarketTick(String symbol, double price, long volume, double bid, double ask) {
        try {
            String routingKey = "market." + symbol;
            
            // Create message with headers
            AMQP.BasicProperties properties = new AMQP.BasicProperties.Builder()
                .contentType("application/json")
                .deliveryMode(1) // Non-persistent for real-time data
                .timestamp(new java.util.Date())
                .messageId(java.util.UUID.randomUUID().toString())
                .headers(Map.of(
                    "symbol", symbol,
                    "data-type", "TICK",
                    "exchange", "NYSE"
                ))
                .build();
            
            String message = String.format(
                "{\\"symbol\\": \\"%s\\", \\"price\\": %f, \\"volume\\": %d, " +
                "\\"bid\\": %f, \\"ask\\": %f, \\"timestamp\\": \\"%s\\"}",
                symbol, price, volume, bid, ask, java.time.Instant.now()
            );
            
            channel.basicPublish(
                TradingExchangeSetup.MARKET_DATA_EXCHANGE,
                routingKey,
                properties,
                message.getBytes("UTF-8")
            );
            
        } catch (Exception e) {
            System.err.println("Failed to publish market data: " + e.getMessage());
        }
    }
}

// Order Event Publisher with Priority
public class OrderEventPublisher {
    private final Channel channel;
    
    public OrderEventPublisher(Channel channel) {
        this.channel = channel;
    }
    
    public void publishOrderEvent(String orderId, String orderType, String symbol, 
                                 String side, double quantity, double price, int priority) {
        try {
            String routingKey = "order." + orderType.toLowerCase();
            
            AMQP.BasicProperties properties = new AMQP.BasicProperties.Builder()
                .contentType("application/json")
                .deliveryMode(2) // Persistent for order events
                .priority(priority) // Message priority
                .timestamp(new java.util.Date())
                .messageId(orderId)
                .correlationId(orderId)
                .headers(Map.of(
                    "order-type", orderType,
                    "symbol", symbol,
                    "side", side,
                    "trader-id", "TRADER_123"
                ))
                .build();
            
            String message = String.format(
                "{\\"orderId\\": \\"%s\\", \\"type\\": \\"%s\\", \\"symbol\\": \\"%s\\", " +
                "\\"side\\": \\"%s\\", \\"quantity\\": %f, \\"price\\": %f, \\"timestamp\\": \\"%s\\"}",
                orderId, orderType, symbol, side, quantity, price, java.time.Instant.now()
            );
            
            channel.basicPublish(
                TradingExchangeSetup.ORDER_EVENTS_EXCHANGE,
                routingKey,
                properties,
                message.getBytes("UTF-8")
            );
            
        } catch (Exception e) {
            System.err.println("Failed to publish order event: " + e.getMessage());
        }
    }
}

// Trade Confirmation Publisher with Headers Exchange
public class TradeConfirmationPublisher {
    private final Channel channel;
    
    public TradeConfirmationPublisher(Channel channel) {
        this.channel = channel;
    }
    
    public void publishTradeConfirmation(String tradeId, String symbol, String tradeType,
                                       String currency, String region, double amount) {
        try {
            Map<String, Object> headers = new HashMap<>();
            headers.put("trade-type", tradeType);
            headers.put("currency", currency);
            headers.put("region", region);
            headers.put("amount", amount);
            headers.put("settlement-date", "T+2");
            headers.put("requires-clearing", true);
            
            AMQP.BasicProperties properties = new AMQP.BasicProperties.Builder()
                .contentType("application/json")
                .deliveryMode(2) // Persistent for trade confirmations
                .timestamp(new java.util.Date())
                .messageId(tradeId)
                .headers(headers)
                .build();
            
            String message = String.format(
                "{\\"tradeId\\": \\"%s\\", \\"symbol\\": \\"%s\\", \\"type\\": \\"%s\\", " +
                "\\"currency\\": \\"%s\\", \\"region\\": \\"%s\\", \\"amount\\": %f, " +
                "\\"timestamp\\": \\"%s\\", \\"status\\": \\"CONFIRMED\\"}",
                tradeId, symbol, tradeType, currency, region, amount, java.time.Instant.now()
            );
            
            channel.basicPublish(
                TradingExchangeSetup.TRADE_CONFIRMATIONS_EXCHANGE,
                "", // Routing key not used with headers exchange
                properties,
                message.getBytes("UTF-8")
            );
            
        } catch (Exception e) {
            System.err.println("Failed to publish trade confirmation: " + e.getMessage());
        }
    }
}`,

    'queues': `// RabbitMQ Queue Management and Consumers for Trading Systems
import com.rabbitmq.client.*;
import java.io.IOException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeoutException;

public class TradingQueueManager {
    private final Connection connection;
    private final ExecutorService executorService;
    
    public TradingQueueManager() throws IOException, TimeoutException {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost("rabbitmq-cluster.trading.com");
        factory.setPort(5672);
        factory.setUsername("trading-consumer");
        factory.setPassword("consumer-password");
        factory.setVirtualHost("/trading");
        
        // Consumer-optimized settings
        factory.setAutomaticRecoveryEnabled(true);
        factory.setNetworkRecoveryInterval(5000);
        factory.setRequestedHeartbeat(30);
        factory.setRequestedChannelMax(50);
        
        this.connection = factory.newConnection();
        this.executorService = Executors.newFixedThreadPool(20);
    }
    
    // High-performance market data consumer
    public void startMarketDataConsumer(String symbol) throws IOException {
        Channel channel = connection.createChannel();
        String queueName = "market.data." + symbol.toLowerCase();
        
        // Configure channel for high throughput
        channel.basicQos(1000); // Prefetch up to 1000 messages
        
        DeliverCallback deliverCallback = (consumerTag, delivery) -> {
            try {
                long startTime = System.nanoTime();
                
                String message = new String(delivery.getBody(), "UTF-8");
                String routingKey = delivery.getEnvelope().getRoutingKey();
                
                // Fast processing for market data
                processMarketData(symbol, message, delivery.getProperties());
                
                long processingTime = System.nanoTime() - startTime;
                
                // Log if processing is slow (> 1ms)
                if (processingTime > 1_000_000) {
                    System.err.printf("Slow market data processing: %s - %.2fms%n", 
                                    symbol, processingTime / 1_000_000.0);
                }
                
                // Manual acknowledgment for reliability
                channel.basicAck(delivery.getEnvelope().getDeliveryTag(), false);
                
            } catch (Exception e) {
                System.err.printf("Market data processing error for %s: %s%n", symbol, e.getMessage());
                // Negative acknowledgment - requeue message
                channel.basicNack(delivery.getEnvelope().getDeliveryTag(), false, true);
            }
        };
        
        CancelCallback cancelCallback = consumerTag -> {
            System.out.printf("Market data consumer cancelled: %s%n", consumerTag);
        };
        
        // Start consuming with manual acknowledgment
        channel.basicConsume(queueName, false, deliverCallback, cancelCallback);
        
        System.out.printf("Started market data consumer for %s%n", symbol);
    }
    
    // Priority-aware order processing consumer
    public void startOrderProcessingConsumer(String orderType) throws IOException {
        Channel channel = connection.createChannel();
        String queueName = "orders." + orderType.toLowerCase();
        
        // Configure for order processing
        channel.basicQos(10); // Process 10 orders at a time
        
        DeliverCallback deliverCallback = (consumerTag, delivery) -> {
            executorService.submit(() -> {
                try {
                    String message = new String(delivery.getBody(), "UTF-8");
                    AMQP.BasicProperties props = delivery.getProperties();
                    
                    // Extract priority and order details
                    int priority = props.getPriority() != null ? props.getPriority() : 0;
                    String orderId = props.getMessageId();
                    String correlationId = props.getCorrelationId();
                    
                    System.out.printf("Processing %s order %s (priority: %d)%n", 
                                    orderType, orderId, priority);
                    
                    // Process order based on type and priority
                    OrderProcessingResult result = processOrder(orderType, message, props);
                    
                    if (result.isSuccess()) {
                        // Acknowledge successful processing
                        channel.basicAck(delivery.getEnvelope().getDeliveryTag(), false);
                        
                        // Publish order update if needed
                        if (result.requiresUpdate()) {
                            publishOrderUpdate(orderId, result.getStatus(), result.getMessage());
                        }
                        
                    } else {
                        // Handle processing failure
                        handleOrderProcessingFailure(channel, delivery, result);
                    }
                    
                } catch (Exception e) {
                    try {
                        System.err.printf("Order processing error: %s%n", e.getMessage());
                        channel.basicNack(delivery.getEnvelope().getDeliveryTag(), false, true);
                    } catch (IOException ioException) {
                        System.err.printf("Failed to nack message: %s%n", ioException.getMessage());
                    }
                }
            });
        };
        
        channel.basicConsume(queueName, false, deliverCallback, consumerTag -> {
            System.out.printf("Order processing consumer cancelled: %s%n", consumerTag);
        });
        
        System.out.printf("Started order processing consumer for %s orders%n", orderType);
    }
    
    // Reliable trade confirmation consumer with dead letter handling
    public void startTradeConfirmationConsumer() throws IOException {
        Channel channel = connection.createChannel();
        String queueName = "trade.confirmations";
        
        // Lower prefetch for critical trade confirmations
        channel.basicQos(5);
        
        DeliverCallback deliverCallback = (consumerTag, delivery) -> {
            try {
                String message = new String(delivery.getBody(), "UTF-8");
                AMQP.BasicProperties props = delivery.getProperties();
                String tradeId = props.getMessageId();
                
                System.out.printf("Processing trade confirmation: %s%n", tradeId);
                
                // Critical trade confirmation processing
                boolean success = processTradeConfirmation(message, props);
                
                if (success) {
                    channel.basicAck(delivery.getEnvelope().getDeliveryTag(), false);
                    
                    // Log successful trade confirmation
                    System.out.printf("Trade confirmation processed successfully: %s%n", tradeId);
                    
                    // Trigger settlement workflow
                    triggerSettlementWorkflow(tradeId, message);
                    
                } else {
                    // Check retry count
                    Map<String, Object> headers = props.getHeaders();
                    Integer retryCount = headers != null ? (Integer) headers.get("x-retry-count") : 0;
                    
                    if (retryCount == null) retryCount = 0;
                    
                    if (retryCount < 3) {
                        // Retry with exponential backoff
                        retryTradeConfirmation(channel, delivery, retryCount + 1);
                    } else {
                        // Send to dead letter queue after max retries
                        System.err.printf("Trade confirmation failed after %d retries: %s%n", retryCount, tradeId);
                        channel.basicNack(delivery.getEnvelope().getDeliveryTag(), false, false);
                        
                        // Alert operations team
                        alertOperationsTeam("TRADE_CONFIRMATION_FAILED", tradeId, message);
                    }
                }
                
            } catch (Exception e) {
                System.err.printf("Trade confirmation processing error: %s%n", e.getMessage());
                try {
                    channel.basicNack(delivery.getEnvelope().getDeliveryTag(), false, true);
                } catch (IOException ioException) {
                    System.err.printf("Failed to nack trade confirmation: %s%n", ioException.getMessage());
                }
            }
        };
        
        channel.basicConsume(queueName, false, deliverCallback, consumerTag -> {
            System.out.printf("Trade confirmation consumer cancelled: %s%n", consumerTag);
        });
        
        System.out.println("Started trade confirmation consumer");
    }
    
    // Risk alert consumer with fanout pattern
    public void startRiskAlertConsumer(String department) throws IOException {
        Channel channel = connection.createChannel();
        String queueName = "risk.alerts." + department.toLowerCase();
        
        DeliverCallback deliverCallback = (consumerTag, delivery) -> {
            try {
                String message = new String(delivery.getBody(), "UTF-8");
                AMQP.BasicProperties props = delivery.getProperties();
                
                System.out.printf("Risk alert received by %s: %s%n", department, message);
                
                // Process risk alert based on department
                processRiskAlert(department, message, props);
                
                // Always acknowledge risk alerts (they're broadcasted)
                channel.basicAck(delivery.getEnvelope().getDeliveryTag(), false);
                
            } catch (Exception e) {
                System.err.printf("Risk alert processing error (%s): %s%n", department, e.getMessage());
                try {
                    channel.basicAck(delivery.getEnvelope().getDeliveryTag(), false);
                } catch (IOException ioException) {
                    System.err.printf("Failed to ack risk alert: %s%n", ioException.getMessage());
                }
            }
        };
        
        channel.basicConsume(queueName, false, deliverCallback, consumerTag -> {
            System.out.printf("Risk alert consumer cancelled (%s): %s%n", department, consumerTag);
        });
        
        System.out.printf("Started risk alert consumer for %s%n", department);
    }
    
    // Queue monitoring and management
    public void monitorQueueHealth() {
        executorService.submit(() -> {
            try {
                while (true) {
                    Channel channel = connection.createChannel();
                    
                    // Monitor critical queues
                    String[] criticalQueues = {
                        "orders.market", "orders.limit", "orders.stop",
                        "trade.confirmations", "settlement.usd.americas"
                    };
                    
                    for (String queueName : criticalQueues) {
                        try {
                            AMQP.Queue.DeclareOk response = channel.queueDeclarePassive(queueName);
                            int messageCount = response.getMessageCount();
                            int consumerCount = response.getConsumerCount();
                            
                            System.out.printf("Queue %s: %d messages, %d consumers%n", 
                                            queueName, messageCount, consumerCount);
                            
                            // Alert on high message count
                            if (messageCount > 1000) {
                                System.err.printf("HIGH QUEUE DEPTH ALERT: %s has %d messages%n", 
                                                queueName, messageCount);
                            }
                            
                            // Alert on no consumers
                            if (consumerCount == 0) {
                                System.err.printf("NO CONSUMER ALERT: %s has no active consumers%n", queueName);
                            }
                            
                        } catch (IOException e) {
                            System.err.printf("Failed to check queue %s: %s%n", queueName, e.getMessage());
                        }
                    }
                    
                    channel.close();
                    Thread.sleep(30000); // Check every 30 seconds
                }
                
            } catch (Exception e) {
                System.err.println("Queue monitoring error: " + e.getMessage());
            }
        });
    }
    
    // Helper methods
    private void processMarketData(String symbol, String message, AMQP.BasicProperties props) {
        // Process market data (update cache, trigger algorithms, etc.)
    }
    
    private OrderProcessingResult processOrder(String orderType, String message, AMQP.BasicProperties props) {
        // Process order based on type
        return new OrderProcessingResult(true, "PROCESSED", "Order processed successfully", false);
    }
    
    private void handleOrderProcessingFailure(Channel channel, Delivery delivery, OrderProcessingResult result) 
            throws IOException {
        // Handle order processing failure
        channel.basicNack(delivery.getEnvelope().getDeliveryTag(), false, result.shouldRequeue());
    }
    
    private boolean processTradeConfirmation(String message, AMQP.BasicProperties props) {
        // Process trade confirmation
        return true; // Placeholder
    }
    
    private void triggerSettlementWorkflow(String tradeId, String message) {
        // Trigger settlement workflow
    }
    
    private void retryTradeConfirmation(Channel channel, Delivery delivery, int retryCount) throws IOException {
        // Implement retry logic with exponential backoff
        // For now, just requeue
        channel.basicNack(delivery.getEnvelope().getDeliveryTag(), false, true);
    }
    
    private void alertOperationsTeam(String alertType, String tradeId, String message) {
        // Alert operations team
        System.err.printf("OPERATIONS ALERT: %s for trade %s%n", alertType, tradeId);
    }
    
    private void processRiskAlert(String department, String message, AMQP.BasicProperties props) {
        // Process risk alert based on department
    }
    
    private void publishOrderUpdate(String orderId, String status, String message) {
        // Publish order status update
    }
    
    public void close() throws IOException, TimeoutException {
        executorService.shutdown();
        if (connection != null && connection.isOpen()) {
            connection.close();
        }
    }
}

// Supporting classes
class OrderProcessingResult {
    private final boolean success;
    private final String status;
    private final String message;
    private final boolean requiresUpdate;
    
    public OrderProcessingResult(boolean success, String status, String message, boolean requiresUpdate) {
        this.success = success;
        this.status = status;
        this.message = message;
        this.requiresUpdate = requiresUpdate;
    }
    
    // Getters
    public boolean isSuccess() { return success; }
    public String getStatus() { return status; }
    public String getMessage() { return message; }
    public boolean requiresUpdate() { return requiresUpdate; }
    public boolean shouldRequeue() { return !success; }
}`,

    'publishers': `// RabbitMQ Publishers with Reliability for Trading Systems
import com.rabbitmq.client.*;
import java.io.IOException;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

public class ReliableTradingPublisher {
    private final Connection connection;
    private final Channel channel;
    private final AtomicLong sequenceNumber = new AtomicLong(0);
    private final ConcurrentHashMap<Long, PendingConfirmation> unconfirmedMessages = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
    
    public ReliableTradingPublisher() throws IOException, TimeoutException {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost("rabbitmq-cluster.trading.com");
        factory.setPort(5672);
        factory.setUsername("trading-publisher");
        factory.setPassword("publisher-password");
        factory.setVirtualHost("/trading");
        
        // Publisher-optimized settings
        factory.setAutomaticRecoveryEnabled(true);
        factory.setNetworkRecoveryInterval(5000);
        factory.setRequestedHeartbeat(30);
        factory.setChannelRpcTimeout(60000);
        
        this.connection = factory.newConnection();
        this.channel = connection.createChannel();
        
        // Enable publisher confirms for reliability
        channel.confirmSelect();
        setupConfirmListener();
        
        // Start timeout monitoring
        startTimeoutMonitoring();
    }
    
    private void setupConfirmListener() {
        channel.addConfirmListener(new ConfirmListener() {
            @Override
            public void handleAck(long deliveryTag, boolean multiple) throws IOException {
                if (multiple) {
                    // Handle multiple confirmations
                    unconfirmedMessages.entrySet().removeIf(entry -> entry.getKey() <= deliveryTag);
                } else {
                    // Handle single confirmation
                    PendingConfirmation confirmed = unconfirmedMessages.remove(deliveryTag);
                    if (confirmed != null) {
                        confirmed.getFuture().complete(true);
                        System.out.printf("Message confirmed: %s%n", confirmed.getMessageId());
                    }
                }
            }
            
            @Override
            public void handleNack(long deliveryTag, boolean multiple) throws IOException {
                if (multiple) {
                    // Handle multiple negative confirmations
                    unconfirmedMessages.entrySet().forEach(entry -> {
                        if (entry.getKey() <= deliveryTag) {
                            entry.getValue().getFuture().complete(false);
                            System.err.printf("Message nacked: %s%n", entry.getValue().getMessageId());
                        }
                    });
                    unconfirmedMessages.entrySet().removeIf(entry -> entry.getKey() <= deliveryTag);
                } else {
                    // Handle single negative confirmation
                    PendingConfirmation nacked = unconfirmedMessages.remove(deliveryTag);
                    if (nacked != null) {
                        nacked.getFuture().complete(false);
                        System.err.printf("Message nacked: %s%n", nacked.getMessageId());
                    }
                }
            }
        });
    }
    
    // Reliable order event publishing with confirmation
    public CompletableFuture<Boolean> publishOrderEventAsync(String orderId, String exchange, 
                                                           String routingKey, String message, 
                                                           int priority, boolean persistent) {
        try {
            long seqNum = channel.getNextPublishSeqNo();
            CompletableFuture<Boolean> future = new CompletableFuture<>();
            
            AMQP.BasicProperties properties = new AMQP.BasicProperties.Builder()
                .contentType("application/json")
                .deliveryMode(persistent ? 2 : 1)
                .priority(priority)
                .timestamp(new java.util.Date())
                .messageId(orderId)
                .correlationId(orderId)
                .expiration(String.valueOf(30000)) // 30 second expiration
                .headers(java.util.Map.of(
                    "published-by", "trading-system",
                    "publish-time", System.currentTimeMillis()
                ))
                .build();
            
            // Store pending confirmation
            unconfirmedMessages.put(seqNum, new PendingConfirmation(orderId, future, System.currentTimeMillis()));
            
            // Publish with mandatory flag for critical orders
            boolean mandatory = "orders.market".equals(routingKey) || priority >= 8;
            
            channel.basicPublish(exchange, routingKey, mandatory, properties, message.getBytes("UTF-8"));
            
            System.out.printf("Published order event %s (seq: %d, mandatory: %b)%n", orderId, seqNum, mandatory);
            
            return future;
            
        } catch (Exception e) {
            System.err.printf("Failed to publish order event %s: %s%n", orderId, e.getMessage());
            return CompletableFuture.completedFuture(false);
        }
    }
    
    // Synchronous publishing with retry for critical messages
    public boolean publishOrderEventSync(String orderId, String exchange, String routingKey, 
                                       String message, int priority, boolean persistent, 
                                       int maxRetries, long timeoutMs) {
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                CompletableFuture<Boolean> future = publishOrderEventAsync(orderId, exchange, routingKey, 
                                                                         message, priority, persistent);
                
                Boolean result = future.get(timeoutMs, TimeUnit.MILLISECONDS);
                
                if (result) {
                    return true;
                } else {
                    System.err.printf("Order event publish failed (attempt %d/%d): %s%n", 
                                    attempt, maxRetries, orderId);
                }
                
            } catch (TimeoutException e) {
                System.err.printf("Order event publish timeout (attempt %d/%d): %s%n", 
                                attempt, maxRetries, orderId);
            } catch (Exception e) {
                System.err.printf("Order event publish error (attempt %d/%d): %s - %s%n", 
                                attempt, maxRetries, orderId, e.getMessage());
            }
            
            if (attempt < maxRetries) {
                try {
                    Thread.sleep(1000 * attempt); // Exponential backoff
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        return false;
    }
    
    // Transaction-based publishing for atomic operations
    public boolean publishTradeSettlementTransaction(String tradeId, 
                                                   java.util.List<SettlementMessage> messages) {
        try {
            // Start transaction
            channel.txSelect();
            
            System.out.printf("Starting settlement transaction for trade %s%n", tradeId);
            
            for (SettlementMessage settlementMsg : messages) {
                AMQP.BasicProperties properties = new AMQP.BasicProperties.Builder()
                    .contentType("application/json")
                    .deliveryMode(2) // Persistent for settlement
                    .timestamp(new java.util.Date())
                    .messageId(settlementMsg.getMessageId())
                    .correlationId(tradeId)
                    .headers(java.util.Map.of(
                        "settlement-type", settlementMsg.getType(),
                        "currency", settlementMsg.getCurrency(),
                        "amount", settlementMsg.getAmount()
                    ))
                    .build();
                
                channel.basicPublish(
                    settlementMsg.getExchange(),
                    settlementMsg.getRoutingKey(),
                    true, // Mandatory
                    properties,
                    settlementMsg.getPayload().getBytes("UTF-8")
                );
            }
            
            // Commit transaction
            channel.txCommit();
            System.out.printf("Settlement transaction committed for trade %s%n", tradeId);
            return true;
            
        } catch (Exception e) {
            try {
                // Rollback transaction
                channel.txRollback();
                System.err.printf("Settlement transaction rolled back for trade %s: %s%n", tradeId, e.getMessage());
            } catch (IOException rollbackException) {
                System.err.printf("Failed to rollback settlement transaction: %s%n", rollbackException.getMessage());
            }
            return false;
        }
    }
    
    // Batch publishing for high throughput market data
    public void publishMarketDataBatch(java.util.List<MarketDataMessage> messages) {
        try {
            System.out.printf("Publishing market data batch: %d messages%n", messages.size());
            
            for (MarketDataMessage mdMsg : messages) {
                AMQP.BasicProperties properties = new AMQP.BasicProperties.Builder()
                    .contentType("application/json")
                    .deliveryMode(1) // Non-persistent for market data
                    .timestamp(new java.util.Date())
                    .messageId(mdMsg.getMessageId())
                    .headers(java.util.Map.of(
                        "symbol", mdMsg.getSymbol(),
                        "data-type", mdMsg.getDataType(),
                        "exchange", mdMsg.getExchange()
                    ))
                    .build();
                
                channel.basicPublish(
                    mdMsg.getExchange(),
                    mdMsg.getRoutingKey(),
                    false, // Not mandatory for market data
                    properties,
                    mdMsg.getPayload().getBytes("UTF-8")
                );
            }
            
            // Wait for all confirmations (with timeout)
            boolean allConfirmed = channel.waitForConfirms(5000);
            
            if (allConfirmed) {
                System.out.printf("Market data batch confirmed: %d messages%n", messages.size());
            } else {
                System.err.printf("Market data batch partial confirmation: %d messages%n", messages.size());
            }
            
        } catch (Exception e) {
            System.err.printf("Failed to publish market data batch: %s%n", e.getMessage());
        }
    }
    
    // Return callback for mandatory messages that couldn't be routed
    private void setupReturnListener() {
        channel.addReturnListener(returnMessage -> {
            String messageId = returnMessage.getProperties().getMessageId();
            System.err.printf("Message returned (could not route): %s - %s%n", 
                            messageId, returnMessage.getReplyText());
            
            // Handle returned message (maybe retry with different routing)
            handleReturnedMessage(returnMessage);
        });
    }
    
    private void handleReturnedMessage(Return returnMessage) {
        // Log and potentially re-route or alert
        String messageId = returnMessage.getProperties().getMessageId();
        System.err.printf("CRITICAL: Message %s could not be routed - %s%n", 
                        messageId, returnMessage.getReplyText());
        
        // Alert operations team for critical messages
        if (returnMessage.getRoutingKey().contains("trade.confirmations")) {
            alertOperationsTeam("MESSAGE_ROUTING_FAILED", messageId);
        }
    }
    
    private void startTimeoutMonitoring() {
        scheduler.scheduleAtFixedRate(() -> {
            long currentTime = System.currentTimeMillis();
            long timeoutMs = 30000; // 30 seconds
            
            unconfirmedMessages.entrySet().removeIf(entry -> {
                PendingConfirmation pending = entry.getValue();
                if (currentTime - pending.getTimestamp() > timeoutMs) {
                    pending.getFuture().complete(false);
                    System.err.printf("Message confirmation timeout: %s%n", pending.getMessageId());
                    return true;
                }
                return false;
            });
        }, 10, 10, TimeUnit.SECONDS);
    }
    
    private void alertOperationsTeam(String alertType, String messageId) {
        // Implementation would send alert to operations team
        System.err.printf("OPERATIONS ALERT: %s for message %s%n", alertType, messageId);
    }
    
    public void close() throws IOException, TimeoutException {
        scheduler.shutdown();
        if (channel != null && channel.isOpen()) {
            channel.close();
        }
        if (connection != null && connection.isOpen()) {
            connection.close();
        }
    }
}

// Supporting classes
class PendingConfirmation {
    private final String messageId;
    private final CompletableFuture<Boolean> future;
    private final long timestamp;
    
    public PendingConfirmation(String messageId, CompletableFuture<Boolean> future, long timestamp) {
        this.messageId = messageId;
        this.future = future;
        this.timestamp = timestamp;
    }
    
    public String getMessageId() { return messageId; }
    public CompletableFuture<Boolean> getFuture() { return future; }
    public long getTimestamp() { return timestamp; }
}

class SettlementMessage {
    private final String messageId;
    private final String type;
    private final String currency;
    private final double amount;
    private final String exchange;
    private final String routingKey;
    private final String payload;
    
    public SettlementMessage(String messageId, String type, String currency, double amount, 
                           String exchange, String routingKey, String payload) {
        this.messageId = messageId;
        this.type = type;
        this.currency = currency;
        this.amount = amount;
        this.exchange = exchange;
        this.routingKey = routingKey;
        this.payload = payload;
    }
    
    // Getters
    public String getMessageId() { return messageId; }
    public String getType() { return type; }
    public String getCurrency() { return currency; }
    public double getAmount() { return amount; }
    public String getExchange() { return exchange; }
    public String getRoutingKey() { return routingKey; }
    public String getPayload() { return payload; }
}

class MarketDataMessage {
    private final String messageId;
    private final String symbol;
    private final String dataType;
    private final String exchange;
    private final String routingKey;
    private final String payload;
    
    public MarketDataMessage(String messageId, String symbol, String dataType, String exchange, 
                           String routingKey, String payload) {
        this.messageId = messageId;
        this.symbol = symbol;
        this.dataType = dataType;
        this.exchange = exchange;
        this.routingKey = routingKey;
        this.payload = payload;
    }
    
    // Getters
    public String getMessageId() { return messageId; }
    public String getSymbol() { return symbol; }
    public String getDataType() { return dataType; }
    public String getExchange() { return exchange; }
    public String getRoutingKey() { return routingKey; }
    public String getPayload() { return payload; }
}`,

    'clustering': `# RabbitMQ Clustering and High Availability for Trading Systems

# Cluster Configuration (rabbitmq.conf)
cluster_name = trading-cluster
cluster_formation.peer_discovery_backend = dns
cluster_formation.dns.hostname = rabbitmq-cluster.trading.com

# Node names and networking
NODENAME=rabbit@rabbitmq1
RABBITMQ_USE_LONGNAME=true
ERL_EPMD_PORT=4369
RABBITMQ_NODE_PORT=5672
RABBITMQ_DIST_PORT=25672

# Memory and disk thresholds
vm_memory_high_watermark.relative = 0.6
disk_free_limit.absolute = 10GB

# Clustering settings
cluster_keepalive_interval = 10000
net_ticktime = 60

# High availability settings
ha-mode = all
ha-sync-mode = automatic
ha-promote-on-shutdown = when-synced
ha-promote-on-failure = when-synced

# Docker Compose for RabbitMQ Cluster
version: '3.8'
services:
  rabbitmq1:
    image: rabbitmq:3.12-management
    hostname: rabbitmq1
    environment:
      RABBITMQ_ERLANG_COOKIE: "trading-cluster-secret-cookie"
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: trading-admin-password
    volumes:
      - ./rabbitmq1:/var/lib/rabbitmq
      - ./config/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
      - ./config/definitions.json:/etc/rabbitmq/definitions.json
    ports:
      - "5672:5672"
      - "15672:15672"
    networks:
      - trading-network

  rabbitmq2:
    image: rabbitmq:3.12-management
    hostname: rabbitmq2
    environment:
      RABBITMQ_ERLANG_COOKIE: "trading-cluster-secret-cookie"
    volumes:
      - ./rabbitmq2:/var/lib/rabbitmq
      - ./config/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
    ports:
      - "5673:5672"
      - "15673:15672"
    networks:
      - trading-network
    depends_on:
      - rabbitmq1

  rabbitmq3:
    image: rabbitmq:3.12-management
    hostname: rabbitmq3
    environment:
      RABBITMQ_ERLANG_COOKIE: "trading-cluster-secret-cookie"
    volumes:
      - ./rabbitmq3:/var/lib/rabbitmq
      - ./config/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
    ports:
      - "5674:5672"
      - "15674:15672"
    networks:
      - trading-network
    depends_on:
      - rabbitmq1

  haproxy:
    image: haproxy:2.8
    volumes:
      - ./config/haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg
    ports:
      - "5675:5672"  # Load balanced AMQP
      - "15675:15672" # Load balanced Management UI
    networks:
      - trading-network
    depends_on:
      - rabbitmq1
      - rabbitmq2
      - rabbitmq3

networks:
  trading-network:
    driver: bridge

# HAProxy Configuration (haproxy.cfg)
global
    daemon
    log stdout local0 info
    maxconn 4096

defaults
    mode tcp
    option tcplog
    timeout connect 5s
    timeout client 30s
    timeout server 30s
    retries 3

# AMQP Load Balancer
listen rabbitmq_cluster
    bind *:5672
    mode tcp
    balance roundrobin
    option tcpka
    
    # Health checks
    option tcp-check
    tcp-check connect port 5672
    
    # RabbitMQ nodes
    server rabbitmq1 rabbitmq1:5672 check inter 5s rise 2 fall 3
    server rabbitmq2 rabbitmq2:5672 check inter 5s rise 2 fall 3
    server rabbitmq3 rabbitmq3:5672 check inter 5s rise 2 fall 3

# Management UI Load Balancer
listen rabbitmq_management
    bind *:15672
    mode http
    balance roundrobin
    option httpchk GET /api/aliveness-test/%2F
    
    # Management interfaces
    server rabbitmq1-mgmt rabbitmq1:15672 check inter 10s rise 2 fall 3
    server rabbitmq2-mgmt rabbitmq2:15672 check inter 10s rise 2 fall 3
    server rabbitmq3-mgmt rabbitmq3:15672 check inter 10s rise 2 fall 3

# Statistics
stats enable
stats uri /stats
stats refresh 30s

# Cluster Setup Script (setup-cluster.sh)
#!/bin/bash

echo "Setting up RabbitMQ Trading Cluster..."

# Start all nodes
docker-compose up -d

# Wait for nodes to start
sleep 30

# Join nodes to cluster
echo "Joining rabbitmq2 to cluster..."
docker exec rabbitmq2 rabbitmqctl stop_app
docker exec rabbitmq2 rabbitmqctl reset
docker exec rabbitmq2 rabbitmqctl join_cluster rabbit@rabbitmq1
docker exec rabbitmq2 rabbitmqctl start_app

echo "Joining rabbitmq3 to cluster..."
docker exec rabbitmq3 rabbitmqctl stop_app
docker exec rabbitmq3 rabbitmqctl reset
docker exec rabbitmq3 rabbitmqctl join_cluster rabbit@rabbitmq1
docker exec rabbitmq3 rabbitmqctl start_app

# Wait for cluster to form
sleep 10

# Verify cluster status
echo "Cluster status:"
docker exec rabbitmq1 rabbitmqctl cluster_status

# Set up high availability policies
echo "Setting up HA policies..."
docker exec rabbitmq1 rabbitmqctl set_policy trading-ha-all "^(market-data|order-events|trade-confirmations)" '{"ha-mode":"all","ha-sync-mode":"automatic"}'
docker exec rabbitmq1 rabbitmqctl set_policy settlement-ha-exactly "^settlement" '{"ha-mode":"exactly","ha-params":3,"ha-sync-mode":"automatic"}'
docker exec rabbitmq1 rabbitmqctl set_policy risk-alerts-ha-nodes "^risk-alerts" '{"ha-mode":"nodes","ha-params":["rabbit@rabbitmq1","rabbit@rabbitmq2"]}'

# Set up queue length limits
docker exec rabbitmq1 rabbitmqctl set_policy max-length-policy "^market-data" '{"max-length":100000,"overflow":"drop-head"}'

# Set up dead letter policies
docker exec rabbitmq1 rabbitmqctl set_policy dlx-policy "^(order|settlement)" '{"dead-letter-exchange":"dlx","message-ttl":300000}'

echo "Trading cluster setup complete!"

# Java Client with Cluster Support
import com.rabbitmq.client.*;
import java.io.IOException;
import java.net.URISyntaxException;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeoutException;

public class ClusterAwareTradingClient {
    private Connection connection;
    private final List<Address> clusterNodes;
    
    public ClusterAwareTradingClient() {
        // Define cluster nodes
        this.clusterNodes = Arrays.asList(
            new Address("rabbitmq1.trading.com", 5672),
            new Address("rabbitmq2.trading.com", 5672),
            new Address("rabbitmq3.trading.com", 5672)
        );
    }
    
    public void connect() throws IOException, TimeoutException {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setUsername("trading-system");
        factory.setPassword("trading-password");
        factory.setVirtualHost("/trading");
        
        // Connection recovery settings
        factory.setAutomaticRecoveryEnabled(true);
        factory.setNetworkRecoveryInterval(5000);
        factory.setRequestedHeartbeat(30);
        factory.setConnectionTimeout(10000);
        
        // Cluster connection with automatic failover
        this.connection = factory.newConnection(clusterNodes, "TradingSystemConnection");
        
        // Add connection recovery listener
        ((Recoverable) connection).addRecoveryListener(new RecoveryListener() {
            @Override
            public void handleRecovery(Recoverable recoverable) {
                System.out.println("Connection recovered successfully");
            }
            
            @Override
            public void handleRecoveryStarted(Recoverable recoverable) {
                System.out.println("Connection recovery started");
            }
        });
        
        System.out.println("Connected to RabbitMQ cluster");
    }
    
    public Channel createChannel() throws IOException {
        if (connection == null || !connection.isOpen()) {
            throw new IllegalStateException("Connection is not open");
        }
        
        Channel channel = connection.createChannel();
        
        // Add channel recovery listener
        ((Recoverable) channel).addRecoveryListener(new RecoveryListener() {
            @Override
            public void handleRecovery(Recoverable recoverable) {
                System.out.println("Channel recovered successfully");
                // Re-setup channel state if needed
                try {
                    setupChannelState(channel);
                } catch (IOException e) {
                    System.err.println("Failed to setup channel state after recovery: " + e.getMessage());
                }
            }
            
            @Override
            public void handleRecoveryStarted(Recoverable recoverable) {
                System.out.println("Channel recovery started");
            }
        });
        
        setupChannelState(channel);
        return channel;
    }
    
    private void setupChannelState(Channel channel) throws IOException {
        // Enable publisher confirms
        channel.confirmSelect();
        
        // Set up return listener for mandatory messages
        channel.addReturnListener(returned -> {
            System.err.printf("Message returned: %s%n", returned.getReplyText());
        });
    }
    
    // Monitor cluster health
    public void monitorClusterHealth() {
        new Thread(() -> {
            while (true) {
                try {
                    if (connection != null && connection.isOpen()) {
                        // Connection is healthy
                        Thread.sleep(30000); // Check every 30 seconds
                    } else {
                        System.err.println("Connection is not healthy, attempting reconnection...");
                        try {
                            connect();
                        } catch (Exception e) {
                            System.err.println("Reconnection failed: " + e.getMessage());
                        }
                        Thread.sleep(5000); // Retry in 5 seconds
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }).start();
    }
    
    public void close() throws IOException, TimeoutException {
        if (connection != null && connection.isOpen()) {
            connection.close();
        }
    }
}

# Kubernetes Deployment for RabbitMQ Cluster
apiVersion: v1
kind: ConfigMap
metadata:
  name: rabbitmq-config
  namespace: trading
data:
  enabled_plugins: |
    [rabbitmq_management,rabbitmq_peer_discovery_k8s].
  rabbitmq.conf: |
    cluster_formation.peer_discovery_backend = rabbit_peer_discovery_k8s
    cluster_formation.k8s.host = kubernetes.default.svc.cluster.local
    cluster_formation.node_cleanup.interval = 30
    cluster_formation.node_cleanup.only_log_warning = true
    cluster_keeper.sync_interval = 10000
    vm_memory_high_watermark.relative = 0.6
    disk_free_limit.absolute = 2GB

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: rabbitmq-cluster
  namespace: trading
spec:
  serviceName: rabbitmq-cluster
  replicas: 3
  selector:
    matchLabels:
      app: rabbitmq
  template:
    metadata:
      labels:
        app: rabbitmq
    spec:
      serviceAccountName: rabbitmq-cluster
      containers:
      - name: rabbitmq
        image: rabbitmq:3.12-management
        env:
        - name: RABBITMQ_DEFAULT_USER
          valueFrom:
            secretKeyRef:
              name: rabbitmq-secret
              key: username
        - name: RABBITMQ_DEFAULT_PASS
          valueFrom:
            secretKeyRef:
              name: rabbitmq-secret
              key: password
        - name: RABBITMQ_ERLANG_COOKIE
          valueFrom:
            secretKeyRef:
              name: rabbitmq-secret
              key: cookie
        - name: K8S_SERVICE_NAME
          value: rabbitmq-cluster
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: RABBITMQ_NODENAME
          value: rabbit@$(POD_NAME).rabbitmq-cluster.$(POD_NAMESPACE).svc.cluster.local
        ports:
        - containerPort: 5672
          name: amqp
        - containerPort: 15672
          name: management
        - containerPort: 25672
          name: clustering
        volumeMounts:
        - name: config
          mountPath: /etc/rabbitmq
        - name: data
          mountPath: /var/lib/rabbitmq
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
      volumes:
      - name: config
        configMap:
          name: rabbitmq-config
  volumeClaimTemplates:
  - metadata:
      name: data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 100Gi

---
apiVersion: v1
kind: Service
metadata:
  name: rabbitmq-cluster
  namespace: trading
spec:
  clusterIP: None
  ports:
  - port: 5672
    targetPort: 5672
    name: amqp
  - port: 15672
    targetPort: 15672
    name: management
  selector:
    app: rabbitmq`,

    'management': `// RabbitMQ Management and Monitoring for Trading Systems
import com.rabbitmq.http.client.Client;
import com.rabbitmq.http.client.domain.*;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class RabbitMQTradingMonitor {
    private final Client managementClient;
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(3);
    private final Map<String, QueueMetrics> previousMetrics = new HashMap<>();
    
    // Critical thresholds for trading systems
    private static final int MAX_QUEUE_DEPTH_CRITICAL = 10000;
    private static final int MAX_QUEUE_DEPTH_WARNING = 5000;
    private static final double MAX_MEMORY_USAGE = 0.8;
    private static final double MAX_DISK_USAGE = 0.9;
    private static final int MIN_CONSUMERS_CRITICAL = 1;
    
    public RabbitMQTradingMonitor(String managementUrl, String username, String password) 
            throws MalformedURLException, URISyntaxException {
        this.managementClient = new Client(managementUrl, username, password);
    }
    
    public void startMonitoring() {
        System.out.println("Starting RabbitMQ trading system monitoring...");
        
        // Monitor cluster health every 30 seconds
        scheduler.scheduleAtFixedRate(this::monitorClusterHealth, 0, 30, TimeUnit.SECONDS);
        
        // Monitor queue metrics every 10 seconds
        scheduler.scheduleAtFixedRate(this::monitorQueueMetrics, 0, 10, TimeUnit.SECONDS);
        
        // Monitor connection metrics every 60 seconds
        scheduler.scheduleAtFixedRate(this::monitorConnectionMetrics, 0, 60, TimeUnit.SECONDS);
        
        // Generate performance report every 5 minutes
        scheduler.scheduleAtFixedRate(this::generatePerformanceReport, 300, 300, TimeUnit.SECONDS);
    }
    
    private void monitorClusterHealth() {
        try {
            // Check node health
            List<NodeInfo> nodes = managementClient.getNodes();
            
            System.out.println("=== Cluster Health Check ===");
            for (NodeInfo node : nodes) {
                String nodeName = node.getName();
                boolean running = node.isRunning();
                long memUsed = node.getMemoryUsed();
                long memLimit = node.getMemoryLimit();
                long diskFree = node.getDiskFree();
                long diskFreeLimit = node.getDiskFreeLimit();
                
                double memUsage = (double) memUsed / memLimit;
                double diskUsage = 1.0 - ((double) diskFree / (diskFree + 10_000_000_000L)); // Approximate
                
                System.out.printf("Node: %s, Running: %b, Memory: %.1f%%, Disk Free: %s%n", 
                                nodeName, running, memUsage * 100, formatBytes(diskFree));
                
                // Alert conditions
                if (!running) {
                    sendAlert("NODE_DOWN", nodeName, "Node is not running");
                }
                if (memUsage > MAX_MEMORY_USAGE) {
                    sendAlert("HIGH_MEMORY", nodeName, 
                            String.format("Memory usage %.1f%% exceeds threshold", memUsage * 100));
                }
                if (diskFree < diskFreeLimit) {
                    sendAlert("LOW_DISK_SPACE", nodeName, 
                            String.format("Disk free %s below limit", formatBytes(diskFree)));
                }
            }
            
            // Check cluster partition status
            if (nodes.size() > 1) {
                checkClusterPartitions(nodes);
            }
            
        } catch (Exception e) {
            System.err.println("Cluster health monitoring failed: " + e.getMessage());
            sendAlert("MONITORING_ERROR", "cluster-health", e.getMessage());
        }
    }
    
    private void monitorQueueMetrics() {
        try {
            List<QueueInfo> queues = managementClient.getQueues();
            
            for (QueueInfo queue : queues) {
                String queueName = queue.getName();
                String vhost = queue.getVhost();
                
                // Skip system queues
                if (queueName.startsWith("amq.")) continue;
                
                int messageCount = queue.getMessageCount();
                int consumerCount = queue.getConsumerCount();
                double messageRate = queue.getMessageStats().getPublishRate();
                double consumeRate = queue.getMessageStats().getDeliverRate();
                
                // Calculate queue depth growth
                QueueMetrics current = new QueueMetrics(messageCount, consumerCount, messageRate, consumeRate);
                QueueMetrics previous = previousMetrics.get(queueName);
                
                if (previous != null) {
                    int depthChange = messageCount - previous.messageCount;
                    double rateChange = messageRate - previous.messageRate;
                    
                    // Alert conditions for trading queues
                    if (isCriticalTradingQueue(queueName)) {
                        checkCriticalQueueAlerts(queueName, messageCount, consumerCount, depthChange, rateChange);
                    }
                    
                    System.out.printf("Queue: %s, Messages: %d (%+d), Consumers: %d, " +
                                    "Pub Rate: %.1f/s, Consume Rate: %.1f/s%n",
                                    queueName, messageCount, depthChange, consumerCount, 
                                    messageRate, consumeRate);
                }
                
                previousMetrics.put(queueName, current);
            }
            
        } catch (Exception e) {
            System.err.println("Queue metrics monitoring failed: " + e.getMessage());
        }
    }
    
    private void checkCriticalQueueAlerts(String queueName, int messageCount, int consumerCount, 
                                        int depthChange, double rateChange) {
        // High queue depth alerts
        if (messageCount > MAX_QUEUE_DEPTH_CRITICAL) {
            sendAlert("CRITICAL_QUEUE_DEPTH", queueName, 
                    String.format("Queue depth %d exceeds critical threshold", messageCount));
        } else if (messageCount > MAX_QUEUE_DEPTH_WARNING) {
            sendAlert("WARNING_QUEUE_DEPTH", queueName, 
                    String.format("Queue depth %d exceeds warning threshold", messageCount));
        }
        
        // No consumer alerts
        if (consumerCount < MIN_CONSUMERS_CRITICAL) {
            sendAlert("NO_CONSUMERS", queueName, 
                    String.format("Critical queue has %d consumers", consumerCount));
        }
        
        // Rapid queue growth
        if (depthChange > 1000 && messageCount > MAX_QUEUE_DEPTH_WARNING) {
            sendAlert("RAPID_QUEUE_GROWTH", queueName, 
                    String.format("Queue grew by %d messages, now at %d", depthChange, messageCount));
        }
        
        // Message rate drop (potential processing issue)
        if (rateChange < -100 && messageCount > 1000) {
            sendAlert("MESSAGE_RATE_DROP", queueName, 
                    String.format("Message rate dropped by %.1f/s", Math.abs(rateChange)));
        }
    }
    
    private void monitorConnectionMetrics() {
        try {
            List<ConnectionInfo> connections = managementClient.getConnections();
            
            Map<String, Integer> connectionsByClient = new HashMap<>();
            Map<String, Integer> channelsByClient = new HashMap<>();
            int totalConnections = connections.size();
            int totalChannels = 0;
            
            for (ConnectionInfo conn : connections) {
                String clientName = conn.getClientProperties().get("connection_name");
                if (clientName == null) clientName = "unknown";
                
                int channels = conn.getChannelsCount();
                totalChannels += channels;
                
                connectionsByClient.merge(clientName, 1, Integer::sum);
                channelsByClient.merge(clientName, channels, Integer::sum);
                
                // Check for inactive connections
                long lastActivity = System.currentTimeMillis() - conn.getLastActivityTimestamp();
                if (lastActivity > 300000) { // 5 minutes
                    System.out.printf("WARNING: Inactive connection from %s (%.1f min)%n", 
                                    clientName, lastActivity / 60000.0);
                }
            }
            
            System.out.println("=== Connection Summary ===");
            System.out.printf("Total Connections: %d, Total Channels: %d%n", totalConnections, totalChannels);
            
            connectionsByClient.forEach((client, count) -> {
                int channels = channelsByClient.getOrDefault(client, 0);
                System.out.printf("  %s: %d connections, %d channels%n", client, count, channels);
            });
            
        } catch (Exception e) {
            System.err.println("Connection metrics monitoring failed: " + e.getMessage());
        }
    }
    
    private void generatePerformanceReport() {
        try {
            System.out.println("\\n=== Trading System Performance Report ===");
            
            // Get overview statistics
            OverviewDetails overview = managementClient.getOverview();
            
            System.out.printf("RabbitMQ Version: %s%n", overview.getRabbitMQVersion());
            System.out.printf("Cluster Name: %s%n", overview.getClusterName());
            System.out.printf("Total Queues: %d%n", overview.getQueueTotals().size());
            
            // Message rates
            MessageStats stats = overview.getMessageStats();
            if (stats != null) {
                System.out.printf("Message Rates - Publish: %.1f/s, Deliver: %.1f/s, Ack: %.1f/s%n",
                                stats.getPublishRate(), stats.getDeliverRate(), stats.getAckRate());
            }
            
            // Top queues by message count
            List<QueueInfo> queues = managementClient.getQueues();
            queues.sort((q1, q2) -> Integer.compare(q2.getMessageCount(), q1.getMessageCount()));
            
            System.out.println("\\nTop 10 Queues by Message Count:");
            queues.stream().limit(10).forEach(queue -> {
                System.out.printf("  %s: %d messages, %d consumers%n", 
                                queue.getName(), queue.getMessageCount(), queue.getConsumerCount());
            });
            
            // Trading-specific metrics
            generateTradingMetrics(queues);
            
        } catch (Exception e) {
            System.err.println("Performance report generation failed: " + e.getMessage());
        }
    }
    
    private void generateTradingMetrics(List<QueueInfo> queues) {
        System.out.println("\\n=== Trading System Specific Metrics ===");
        
        // Market data queues
        long marketDataMessages = queues.stream()
                .filter(q -> q.getName().startsWith("market.data"))
                .mapToInt(QueueInfo::getMessageCount)
                .sum();
        
        // Order queues
        long orderMessages = queues.stream()
                .filter(q -> q.getName().startsWith("orders."))
                .mapToInt(QueueInfo::getMessageCount)
                .sum();
        
        // Trade confirmation queues
        long tradeMessages = queues.stream()
                .filter(q -> q.getName().contains("trade.confirmation"))
                .mapToInt(QueueInfo::getMessageCount)
                .sum();
        
        // Risk alert queues
        long riskMessages = queues.stream()
                .filter(q -> q.getName().startsWith("risk.alerts"))
                .mapToInt(QueueInfo::getMessageCount)
                .sum();
        
        System.out.printf("Market Data Queue Depth: %d messages%n", marketDataMessages);
        System.out.printf("Order Queue Depth: %d messages%n", orderMessages);
        System.out.printf("Trade Confirmation Queue Depth: %d messages%n", tradeMessages);
        System.out.printf("Risk Alert Queue Depth: %d messages%n", riskMessages);
        
        // Calculate processing health score
        double healthScore = calculateTradingHealthScore(marketDataMessages, orderMessages, tradeMessages, riskMessages);
        System.out.printf("Trading System Health Score: %.1f/100%n", healthScore);
        
        if (healthScore < 70) {
            sendAlert("LOW_HEALTH_SCORE", "trading-system", 
                    String.format("Health score %.1f below acceptable threshold", healthScore));
        }
    }
    
    private double calculateTradingHealthScore(long marketData, long orders, long trades, long risks) {
        double score = 100.0;
        
        // Penalize high queue depths
        if (orders > 1000) score -= 20;
        if (trades > 500) score -= 30;
        if (risks > 100) score -= 25;
        if (marketData > 50000) score -= 10;
        
        return Math.max(0, score);
    }
    
    private void checkClusterPartitions(List<NodeInfo> nodes) {
        // Check for network partitions
        boolean partitionDetected = false;
        for (NodeInfo node : nodes) {
            // This would require access to cluster status details
            // Implementation depends on RabbitMQ management API version
        }
        
        if (partitionDetected) {
            sendAlert("CLUSTER_PARTITION", "cluster", "Network partition detected in cluster");
        }
    }
    
    private boolean isCriticalTradingQueue(String queueName) {
        String[] criticalQueues = {
            "orders.market", "orders.limit", "orders.stop",
            "trade.confirmations", "settlement", "risk.alerts"
        };
        
        return Arrays.stream(criticalQueues).anyMatch(queueName::startsWith);
    }
    
    private void sendAlert(String alertType, String component, String message) {
        // Implementation would send alerts to monitoring system, email, Slack, etc.
        System.err.printf("ALERT [%s] %s: %s%n", alertType, component, message);
        
        // Log to monitoring system
        logToMonitoringSystem(alertType, component, message);
    }
    
    private void logToMonitoringSystem(String alertType, String component, String message) {
        // Integration with monitoring systems like Prometheus, DataDog, etc.
        // For now, just structured logging
        String logEntry = String.format("{\\"timestamp\\": \\"%s\\", \\"type\\": \\"%s\\", " +
                                      "\\"component\\": \\"%s\\", \\"message\\": \\"%s\\"}",
                                      java.time.Instant.now(), alertType, component, message);
        System.out.println("MONITORING_LOG: " + logEntry);
    }
    
    private String formatBytes(long bytes) {
        if (bytes >= 1_000_000_000) {
            return String.format("%.1f GB", bytes / 1_000_000_000.0);
        } else if (bytes >= 1_000_000) {
            return String.format("%.1f MB", bytes / 1_000_000.0);
        } else {
            return String.format("%d bytes", bytes);
        }
    }
    
    public void shutdown() {
        System.out.println("Shutting down monitoring...");
        scheduler.shutdown();
    }
}

// Supporting classes
class QueueMetrics {
    final int messageCount;
    final int consumerCount;
    final double messageRate;
    final double consumeRate;
    
    QueueMetrics(int messageCount, int consumerCount, double messageRate, double consumeRate) {
        this.messageCount = messageCount;
        this.consumerCount = consumerCount;
        this.messageRate = messageRate;
        this.consumeRate = consumeRate;
    }
}

// Usage example with monitoring dashboard integration
public class TradingMonitoringDashboard {
    public static void main(String[] args) {
        try {
            RabbitMQTradingMonitor monitor = new RabbitMQTradingMonitor(
                "http://rabbitmq-cluster.trading.com:15672/api/",
                "monitoring-user",
                "monitoring-password"
            );
            
            monitor.startMonitoring();
            
            // Keep running
            Runtime.getRuntime().addShutdownHook(new Thread(monitor::shutdown));
            Thread.sleep(Long.MAX_VALUE);
            
        } catch (Exception e) {
            System.err.println("Monitoring startup failed: " + e.getMessage());
        }
    }
}`,

    'security': `// RabbitMQ Security and Authentication for Trading Systems
import com.rabbitmq.client.*;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeoutException;
import javax.net.ssl.SSLContext;

public class SecureTradingRabbitMQClient {
    private Connection connection;
    
    // SSL/TLS Configuration
    public void connectWithSSL() throws IOException, TimeoutException, 
                                      NoSuchAlgorithmException, KeyManagementException {
        ConnectionFactory factory = new ConnectionFactory();
        
        // Basic connection settings
        factory.setHost("secure-rabbitmq.trading.com");
        factory.setPort(5671); // SSL port
        factory.setVirtualHost("/trading");
        
        // SSL configuration
        factory.useSslProtocol();
        factory.useSslProtocol("TLSv1.2");
        
        // Client certificate authentication
        System.setProperty("javax.net.ssl.keyStore", "/etc/ssl/trading-client.jks");
        System.setProperty("javax.net.ssl.keyStorePassword", "client-keystore-password");
        System.setProperty("javax.net.ssl.trustStore", "/etc/ssl/trading-truststore.jks");
        System.setProperty("javax.net.ssl.trustStorePassword", "truststore-password");
        
        // SASL authentication
        factory.setSaslConfig(DefaultSaslConfig.EXTERNAL);
        
        // Connection properties for audit trail
        Map<String, Object> clientProperties = new HashMap<>();
        clientProperties.put("connection_name", "TradingSystem-" + System.getProperty("user.name"));
        clientProperties.put("application", "trading-platform");
        clientProperties.put("version", "1.0.0");
        clientProperties.put("environment", "production");
        clientProperties.put("client_id", java.util.UUID.randomUUID().toString());
        factory.setClientProperties(clientProperties);
        
        // Connection recovery
        factory.setAutomaticRecoveryEnabled(true);
        factory.setNetworkRecoveryInterval(10000);
        
        this.connection = factory.newConnection("SecureTradingConnection");
        
        System.out.println("Secure connection established to RabbitMQ cluster");
    }
    
    // Role-based channel creation
    public Channel createTradingChannel(String userRole) throws IOException {
        if (connection == null || !connection.isOpen()) {
            throw new IllegalStateException("No active connection");
        }
        
        Channel channel = connection.createChannel();
        
        // Configure channel based on user role
        configureChannelForRole(channel, userRole);
        
        return channel;
    }
    
    private void configureChannelForRole(Channel channel, String role) throws IOException {
        switch (role.toUpperCase()) {
            case "TRADER":
                setupTraderPermissions(channel);
                break;
            case "RISK_MANAGER":
                setupRiskManagerPermissions(channel);
                break;
            case "SETTLEMENT_OFFICER":
                setupSettlementPermissions(channel);
                break;
            case "COMPLIANCE_OFFICER":
                setupCompliancePermissions(channel);
                break;
            case "MARKET_DATA_PUBLISHER":
                setupMarketDataPublisherPermissions(channel);
                break;
            default:
                throw new IllegalArgumentException("Unknown role: " + role);
        }
    }
    
    private void setupTraderPermissions(Channel channel) throws IOException {
        // Traders can publish orders and receive confirmations
        channel.exchangeDeclarePassive("order.events.exchange");
        channel.exchangeDeclarePassive("trade.confirmations.exchange");
        
        // Create trader-specific queues with auto-delete
        Map<String, Object> queueArgs = new HashMap<>();
        queueArgs.put("x-message-ttl", 300000); // 5 minute TTL
        queueArgs.put("x-expires", 600000); // Queue expires in 10 minutes if unused
        
        String traderId = System.getProperty("trader.id", "default");
        String traderQueue = "trader." + traderId + ".notifications";
        
        channel.queueDeclare(traderQueue, false, true, true, queueArgs);
        channel.queueBind(traderQueue, "trade.confirmations.exchange", "trader." + traderId);
        
        System.out.println("Trader permissions configured for: " + traderId);
    }
    
    private void setupRiskManagerPermissions(Channel channel) throws IOException {
        // Risk managers can access all trading data and publish alerts
        channel.exchangeDeclarePassive("risk.alerts.exchange");
        channel.exchangeDeclarePassive("position.updates.exchange");
        
        // Risk manager gets durable queue for alerts
        Map<String, Object> queueArgs = new HashMap<>();
        queueArgs.put("x-max-priority", 10);
        
        channel.queueDeclare("risk.manager.alerts", true, false, false, queueArgs);
        channel.queueBind("risk.manager.alerts", "risk.alerts.exchange", "");
        
        System.out.println("Risk manager permissions configured");
    }
    
    private void setupSettlementPermissions(Channel channel) throws IOException {
        // Settlement officers process trade confirmations and generate instructions
        channel.exchangeDeclarePassive("settlement.exchange");
        channel.exchangeDeclarePassive("trade.confirmations.exchange");
        
        // Settlement processing queue
        Map<String, Object> queueArgs = new HashMap<>();
        queueArgs.put("x-dead-letter-exchange", "settlement.dlx.exchange");
        queueArgs.put("x-message-ttl", 86400000); // 24 hour TTL
        
        channel.queueDeclare("settlement.processing", true, false, false, queueArgs);
        channel.queueBind("settlement.processing", "trade.confirmations.exchange", "settlement.required");
        
        System.out.println("Settlement permissions configured");
    }
    
    private void setupCompliancePermissions(Channel channel) throws IOException {
        // Compliance officers get read-only access to all trading data
        channel.exchangeDeclarePassive("audit.trail.exchange");
        
        // Compliance audit queue
        Map<String, Object> queueArgs = new HashMap<>();
        queueArgs.put("x-max-length", 100000);
        queueArgs.put("x-overflow", "drop-head");
        
        channel.queueDeclare("compliance.audit", true, false, false, queueArgs);
        channel.queueBind("compliance.audit", "audit.trail.exchange", "#"); // All messages
        
        System.out.println("Compliance permissions configured");
    }
    
    private void setupMarketDataPublisherPermissions(Channel channel) throws IOException {
        // Market data publishers can only publish to market data exchange
        channel.exchangeDeclarePassive("market.data.exchange");
        
        System.out.println("Market data publisher permissions configured");
    }
}

// RabbitMQ Security Configuration (rabbitmq.conf)
/*
# SSL/TLS Configuration
ssl_options.cacertfile = /etc/ssl/ca-cert.pem
ssl_options.certfile = /etc/ssl/server-cert.pem
ssl_options.keyfile = /etc/ssl/server-key.pem
ssl_options.verify = verify_peer
ssl_options.fail_if_no_peer_cert = true
ssl_options.versions.1 = tlsv1.2
ssl_options.versions.2 = tlsv1.3
ssl_options.ciphers.1 = ECDHE-ECDSA-AES256-GCM-SHA384
ssl_options.ciphers.2 = ECDHE-RSA-AES256-GCM-SHA384
ssl_options.honor_cipher_order = true
ssl_options.honor_ecc_order = true

# Authentication and Authorization
auth_backends.1 = rabbit_auth_backend_ldap
auth_backends.2 = rabbit_auth_backend_internal

# LDAP Configuration for Active Directory Integration
auth_ldap.servers.1 = ldap.trading.com
auth_ldap.port = 636
auth_ldap.use_ssl = true
auth_ldap.user_dn_pattern = CN=\${username},OU=TradingUsers,DC=trading,DC=com
auth_ldap.dn_lookup_attribute = sAMAccountName
auth_ldap.dn_lookup_base = OU=TradingUsers,DC=trading,DC=com

# Group-based authorization
auth_ldap.group_dn = OU=TradingGroups,DC=trading,DC=com
auth_ldap.for_authorization = true

# Audit Logging
log.connection.level = info
log.channel.level = info
log.queue.level = info
log.exchange.level = info
log.binding.level = info

# Management UI Security
management.ssl.port = 15671
management.ssl.cacertfile = /etc/ssl/ca-cert.pem
management.ssl.certfile = /etc/ssl/server-cert.pem
management.ssl.keyfile = /etc/ssl/server-key.pem

# Rate Limiting
channel_max = 100
connection_max = 1000
heartbeat = 30
*/

// Security Policy Management
import com.rabbitmq.http.client.Client;
import com.rabbitmq.http.client.domain.UserInfo;
import com.rabbitmq.http.client.domain.UserPermissions;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TradingSecurityManager {
    private final Client managementClient;
    
    public TradingSecurityManager(String managementUrl, String adminUser, String adminPassword) 
            throws MalformedURLException, URISyntaxException {
        this.managementClient = new Client(managementUrl, adminUser, adminPassword);
    }
    
    public void setupTradingUserRoles() {
        try {
            // Create trading user roles with specific permissions
            createTradingUsers();
            configureTradingPermissions();
            setupSecurityPolicies();
            
            System.out.println("Trading security setup completed successfully");
            
        } catch (Exception e) {
            System.err.println("Security setup failed: " + e.getMessage());
        }
    }
    
    private void createTradingUsers() {
        Map<String, String[]> users = new HashMap<>();
        users.put("trader1", new String[]{"trading", "order-management"});
        users.put("riskmanager1", new String[]{"administrator", "risk-management"});
        users.put("settlement1", new String[]{"trading", "settlement"});
        users.put("compliance1", new String[]{"monitoring", "compliance"});
        users.put("marketdata1", new String[]{"management", "market-data"});
        
        for (Map.Entry<String, String[]> entry : users.entrySet()) {
            String username = entry.getKey();
            String[] tags = entry.getValue();
            
            try {
                // Create user with strong password policy
                String password = generateSecurePassword();
                managementClient.createUser(username, password.toCharArray(), Arrays.asList(tags));
                
                System.out.printf("Created user: %s with tags: %s%n", username, Arrays.toString(tags));
                
            } catch (Exception e) {
                System.err.printf("Failed to create user %s: %s%n", username, e.getMessage());
            }
        }
    }
    
    private void configureTradingPermissions() {
        // Trader permissions - can configure order queues, publish orders, receive confirmations
        setUserPermissions("trader1", "/trading", "^(order|trader1).*", "^(order|trade\\.confirmation).*", "^(order|trade\\.confirmation|market\\.data).*");
        
        // Risk manager permissions - full access to risk and position data
        setUserPermissions("riskmanager1", "/trading", "^(risk|position|order).*", "^(risk|position).*", ".*");
        
        // Settlement permissions - access to trade confirmations and settlement queues
        setUserPermissions("settlement1", "/trading", "^(settlement|trade\\.confirmation).*", "^(settlement|trade\\.confirmation).*", "^(settlement|trade\\.confirmation).*");
        
        // Compliance permissions - read-only access to all trading data
        setUserPermissions("compliance1", "/trading", "", "", ".*");
        
        // Market data publisher - can only publish market data
        setUserPermissions("marketdata1", "/trading", "", "^market\\.data.*", "^market\\.data.*");
    }
    
    private void setUserPermissions(String username, String vhost, String configure, String write, String read) {
        try {
            UserPermissions permissions = new UserPermissions();
            permissions.setVhost(vhost);
            permissions.setUser(username);
            permissions.setConfigure(configure);
            permissions.setWrite(write);
            permissions.setRead(read);
            
            managementClient.updatePermissions(vhost, username, permissions);
            
            System.out.printf("Set permissions for %s: configure=%s, write=%s, read=%s%n", 
                            username, configure, write, read);
            
        } catch (Exception e) {
            System.err.printf("Failed to set permissions for %s: %s%n", username, e.getMessage());
        }
    }
    
    private void setupSecurityPolicies() {
        try {
            // Policy for automatic queue expiration (prevent resource leaks)
            Map<String, Object> autoExpirePolicy = new HashMap<>();
            autoExpirePolicy.put("expires", 3600000); // 1 hour
            autoExpirePolicy.put("max-length", 100000);
            
            managementClient.declarePolicy("/trading", "auto-expire", "^temp\\\\.", 
                                        autoExpirePolicy, 0, "queues");
            
            // Policy for dead letter handling
            Map<String, Object> dlxPolicy = new HashMap<>();
            dlxPolicy.put("dead-letter-exchange", "dlx.exchange");
            dlxPolicy.put("message-ttl", 300000); // 5 minutes
            
            managementClient.declarePolicy("/trading", "dead-letter", "^(order|settlement)\\\\.", 
                                        dlxPolicy, 10, "queues");
            
            // Policy for high availability on critical queues
            Map<String, Object> haPolicy = new HashMap<>();
            haPolicy.put("ha-mode", "exactly");
            haPolicy.put("ha-params", 3);
            haPolicy.put("ha-sync-mode", "automatic");
            
            managementClient.declarePolicy("/trading", "ha-critical", 
                                        "^(trade\\\\.confirmation|settlement|risk\\\\.alert)\\\\.", 
                                        haPolicy, 20, "queues");
            
            System.out.println("Security policies configured successfully");
            
        } catch (Exception e) {
            System.err.println("Failed to setup security policies: " + e.getMessage());
        }
    }
    
    // Audit and compliance monitoring
    public void performSecurityAudit() {
        try {
            System.out.println("=== RabbitMQ Security Audit ===");
            
            // Audit user accounts
            List<UserInfo> users = managementClient.getUsers();
            System.out.println("\\nUser Accounts:");
            for (UserInfo user : users) {
                System.out.printf("  User: %s, Tags: %s%n", user.getName(), user.getTags());
                
                // Check for users without proper tags
                if (user.getTags().isEmpty()) {
                    System.err.printf("  WARNING: User %s has no role tags%n", user.getName());
                }
                
                // Check for admin users
                if (user.getTags().contains("administrator")) {
                    System.out.printf("  INFO: Admin user detected: %s%n", user.getName());
                }
            }
            
            // Audit permissions
            System.out.println("\\nPermission Audit:");
            for (UserInfo user : users) {
                auditUserPermissions(user.getName());
            }
            
            // Check for security policy compliance
            auditSecurityPolicies();
            
        } catch (Exception e) {
            System.err.println("Security audit failed: " + e.getMessage());
        }
    }
    
    private void auditUserPermissions(String username) {
        try {
            List<UserPermissions> permissions = managementClient.getPermissionsIn("/trading");
            
            for (UserPermissions perm : permissions) {
                if (perm.getUser().equals(username)) {
                    System.out.printf("  %s: configure='%s', write='%s', read='%s'%n",
                                    username, perm.getConfigure(), perm.getWrite(), perm.getRead());
                    
                    // Check for overly broad permissions
                    if (".*".equals(perm.getConfigure()) && !perm.getUser().contains("admin")) {
                        System.err.printf("  WARNING: User %s has broad configure permissions%n", username);
                    }
                }
            }
        } catch (Exception e) {
            System.err.printf("Failed to audit permissions for %s: %s%n", username, e.getMessage());
        }
    }
    
    private void auditSecurityPolicies() {
        // Implementation would check that required policies are in place
        System.out.println("\\nSecurity Policy Audit:");
        System.out.println("  - Dead letter policies: ✓");
        System.out.println("  - High availability policies: ✓");
        System.out.println("  - Auto-expiration policies: ✓");
        System.out.println("  - Resource limit policies: ✓");
    }
    
    private String generateSecurePassword() {
        // Generate cryptographically secure password
        // In production, integrate with password management system
        return "SecureTrading" + System.currentTimeMillis() + "!";
    }
}

// Usage example
public class TradingSecuritySetup {
    public static void main(String[] args) {
        try {
            TradingSecurityManager securityManager = new TradingSecurityManager(
                "https://rabbitmq-mgmt.trading.com:15671/api/",
                "admin",
                "secure-admin-password"
            );
            
            // Setup security
            securityManager.setupTradingUserRoles();
            
            // Perform audit
            securityManager.performSecurityAudit();
            
        } catch (Exception e) {
            System.err.println("Security setup failed: " + e.getMessage());
        }
    }
}`
  };

  return (
    <div style={{
      padding: '40px',
      fontFamily: 'Inter, system-ui, sans-serif',
      backgroundColor: '#0f172a',
      color: 'white',
      minHeight: '100vh'
    }}>
      <div style={{ marginBottom: '40px' }}>
        <h1 style={{ 
          fontSize: '36px', 
          fontWeight: '700', 
          marginBottom: '16px',
          background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent'
        }}>
          RabbitMQ
        </h1>
        <p style={{ fontSize: '18px', color: '#94a3b8', lineHeight: '1.6' }}>
          Advanced message broker with flexible routing for trading system messaging
        </p>
      </div>

      <div style={{ display: 'flex', gap: '40px' }}>
        <div style={{ flex: '1', maxWidth: '300px' }}>
          <h2 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '24px', color: '#10b981' }}>
            RabbitMQ Components
          </h2>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {topics.map((topic) => (
              <button
                key={topic.id}
                onClick={() => setSelectedTopic(topic.id)}
                style={{
                  padding: '16px',
                  backgroundColor: selectedTopic === topic.id ? 'rgba(16, 185, 129, 0.2)' : 'rgba(30, 41, 59, 0.5)',
                  border: `2px solid ${selectedTopic === topic.id ? '#10b981' : 'transparent'}`,
                  borderRadius: '12px',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  textAlign: 'left',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px'
                }}
              >
                <div style={{ color: selectedTopic === topic.id ? '#10b981' : '#64748b' }}>
                  {topic.icon}
                </div>
                <div>
                  <div style={{ 
                    fontWeight: '600', 
                    color: selectedTopic === topic.id ? '#10b981' : 'white',
                    marginBottom: '4px'
                  }}>
                    {topic.name}
                  </div>
                  <div style={{ fontSize: '14px', color: '#94a3b8' }}>
                    {topic.description}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        <div style={{ flex: '2' }}>
          <div style={{ marginBottom: '24px' }}>
            <div style={{ display: 'flex', gap: '8px', marginBottom: '24px' }}>
              {['overview', 'definition', 'code'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  style={{
                    padding: '12px 24px',
                    backgroundColor: activeTab === tab ? '#10b981' : 'transparent',
                    border: `2px solid ${activeTab === tab ? '#10b981' : '#374151'}`,
                    borderRadius: '8px',
                    color: activeTab === tab ? 'white' : '#94a3b8',
                    cursor: 'pointer',
                    fontWeight: '600',
                    textTransform: 'capitalize',
                    transition: 'all 0.2s'
                  }}
                >
                  {tab}
                </button>
              ))}
            </div>
          </div>

          {activeTab === 'overview' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <h2 style={{ fontSize: '28px', fontWeight: '600', marginBottom: '24px', color: '#10b981' }}>
                RabbitMQ Overview
              </h2>
              <div style={{ fontSize: '16px', lineHeight: '1.8', color: '#e2e8f0' }}>
                <p style={{ marginBottom: '20px' }}>
                  RabbitMQ is a feature-rich message broker that provides flexible routing, reliable delivery, 
                  and advanced messaging patterns. In trading systems, RabbitMQ excels at complex routing 
                  scenarios, priority queues, and guaranteed message delivery for critical financial operations.
                </p>
                <div style={{ marginBottom: '24px' }}>
                  <h3 style={{ color: '#10b981', fontSize: '20px', marginBottom: '16px' }}>Key Features:</h3>
                  <ul style={{ marginLeft: '24px', lineHeight: '1.8' }}>
                    <li><strong>Flexible Routing:</strong> Topic, direct, headers, and fanout exchanges</li>
                    <li><strong>Message Reliability:</strong> Publisher confirms, consumer acknowledgments, persistence</li>
                    <li><strong>Priority Queues:</strong> High-priority order processing and urgent alerts</li>
                    <li><strong>Clustering:</strong> High availability with queue mirroring and automatic failover</li>
                    <li><strong>Management:</strong> Web UI, REST API, and comprehensive monitoring</li>
                    <li><strong>Security:</strong> SSL/TLS, SASL, LDAP integration, and fine-grained permissions</li>
                  </ul>
                </div>
                <div style={{ 
                  backgroundColor: 'rgba(16, 185, 129, 0.1)', 
                  padding: '20px', 
                  borderRadius: '8px',
                  border: '1px solid #10b981'
                }}>
                  <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Trading System Advantages:</h4>
                  <p>
                    RabbitMQ's sophisticated routing capabilities enable complex trading workflows, 
                    while its reliability features ensure no financial transactions are lost. 
                    Advanced security and compliance features meet regulatory requirements.
                  </p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'definition' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <h2 style={{ fontSize: '24px', fontWeight: '600', marginBottom: '20px', color: '#10b981' }}>
                {topics.find(t => t.id === selectedTopic)?.name} Definition
              </h2>
              <p style={{ fontSize: '16px', lineHeight: '1.8', color: '#e2e8f0' }}>
                {definitions[selectedTopic]}
              </p>
            </div>
          )}

          {activeTab === 'code' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <div style={{ marginBottom: '20px' }}>
                <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px' }}>
                  {topics.find(t => t.id === selectedTopic)?.name} Examples
                </h3>
              </div>
              <SyntaxHighlighter
                language="java"
                style={oneDark}
                customStyle={{
                  backgroundColor: '#1e293b',
                  padding: '20px',
                  borderRadius: '8px',
                  fontSize: '14px',
                  lineHeight: '1.6'
                }}
              >
                {codeExamples[selectedTopic]}
              </SyntaxHighlighter>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RabbitMQ;
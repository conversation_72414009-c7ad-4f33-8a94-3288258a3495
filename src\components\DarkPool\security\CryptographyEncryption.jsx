import React, { useState } from 'react';
import { 
  Shield, Lock, Key, CheckCircle, AlertTriangle, 
  GitBranch, Code, X, Search, Play, Clock
} from 'lucide-react';

const CryptographyEncryption = () => {
  const [selectedQuestion, setSelectedQuestion] = useState(null);
  const [showAnswer, setShowAnswer] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [demoResult, setDemoResult] = useState('');

  const interviewQuestions = [
    {
      id: 1,
      category: 'Encryption Fundamentals',
      difficulty: 'Easy',
      question: 'What is the difference between symmetric and asymmetric encryption?',
      answer: {
        short: 'Symmetric uses same key for encrypt/decrypt (fast, shared secret). Asymmetric uses key pairs (slower, no shared secret needed).',
        detailed: `
**Symmetric Encryption**:
- Same key for encryption and decryption
- Fast performance, suitable for large data
- Key distribution problem (how to share key securely)
- Examples: AES, DES, 3DES, ChaCha20

**Asymmetric Encryption**:
- Public/private key pair
- Slower performance, suitable for small data
- Solves key distribution problem
- Examples: RSA, ECDSA, Elliptic Curve

**Use Cases**:
- Symmetric: Bulk data encryption, file encryption, database encryption
- Asymmetric: Key exchange, digital signatures, SSL/TLS handshake

**Hybrid Approach**:
Most systems use both: asymmetric to exchange symmetric keys, then symmetric for actual data encryption.
        `,
        codeExample: `// Symmetric Encryption (AES)
@Service
public class SymmetricEncryptionService {
    private final String ALGORITHM = "AES/GCM/NoPadding";
    private final int GCM_IV_LENGTH = 12;
    private final int GCM_TAG_LENGTH = 16;

    public String encrypt(String plainText, SecretKey key) throws Exception {
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        byte[] iv = new byte[GCM_IV_LENGTH];
        new SecureRandom().nextBytes(iv);
        GCMParameterSpec spec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
        
        cipher.init(Cipher.ENCRYPT_MODE, key, spec);
        byte[] encrypted = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
        
        // Prepend IV to encrypted data
        byte[] encryptedWithIv = new byte[GCM_IV_LENGTH + encrypted.length];
        System.arraycopy(iv, 0, encryptedWithIv, 0, GCM_IV_LENGTH);
        System.arraycopy(encrypted, 0, encryptedWithIv, GCM_IV_LENGTH, encrypted.length);
        
        return Base64.getEncoder().encodeToString(encryptedWithIv);
    }

    public String decrypt(String encryptedText, SecretKey key) throws Exception {
        byte[] encryptedWithIv = Base64.getDecoder().decode(encryptedText);
        
        // Extract IV
        byte[] iv = new byte[GCM_IV_LENGTH];
        System.arraycopy(encryptedWithIv, 0, iv, 0, GCM_IV_LENGTH);
        
        // Extract encrypted data
        byte[] encrypted = new byte[encryptedWithIv.length - GCM_IV_LENGTH];
        System.arraycopy(encryptedWithIv, GCM_IV_LENGTH, encrypted, 0, encrypted.length);
        
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        GCMParameterSpec spec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
        cipher.init(Cipher.DECRYPT_MODE, key, spec);
        
        byte[] decrypted = cipher.doFinal(encrypted);
        return new String(decrypted, StandardCharsets.UTF_8);
    }
}

// Asymmetric Encryption (RSA)
@Service
public class AsymmetricEncryptionService {
    private final String ALGORITHM = "RSA/OAEP/SHA-256";

    public KeyPair generateKeyPair() throws Exception {
        KeyPairGenerator keyGen = KeyPairGenerator.getInstance("RSA");
        keyGen.initialize(2048);
        return keyGen.generateKeyPair();
    }

    public String encrypt(String plainText, PublicKey publicKey) throws Exception {
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        byte[] encrypted = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encrypted);
    }

    public String decrypt(String encryptedText, PrivateKey privateKey) throws Exception {
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        byte[] decrypted = cipher.doFinal(Base64.getDecoder().decode(encryptedText));
        return new String(decrypted, StandardCharsets.UTF_8);
    }
}`
      }
    },
    {
      id: 2,
      category: 'Hashing & Digital Signatures',
      difficulty: 'Medium',
      question: 'Explain cryptographic hashing, digital signatures, and how to implement them securely.',
      answer: {
        short: 'Hash functions create fixed-size fingerprints (SHA-256). Digital signatures use private key to sign hash, public key to verify. Provides integrity and authenticity.',
        detailed: `
**Cryptographic Hashing**:
- Fixed output size regardless of input
- Deterministic (same input = same hash)
- Avalanche effect (small change = completely different hash)
- One-way function (computationally infeasible to reverse)
- Collision resistant

**Hash Function Applications**:
- Data integrity verification
- Password storage (with salt)
- Digital signatures
- Proof of work (blockchain)
- File deduplication

**Digital Signatures Process**:
1. Create hash of message
2. Encrypt hash with private key (signature)
3. Send message + signature
4. Receiver decrypts signature with public key
5. Compare with hash of received message

**Security Considerations**:
- Use SHA-256 or higher (SHA-1 is deprecated)
- Always use salt for password hashing
- Implement proper key management
- Use secure random number generators
        `,
        codeExample: `@Service
public class CryptographicHashService {
    
    // Secure password hashing with salt
    public String hashPassword(String password, byte[] salt) throws Exception {
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        md.update(salt);
        byte[] hashedPassword = md.digest(password.getBytes(StandardCharsets.UTF_8));
        
        // Combine salt + hash for storage
        byte[] saltAndHash = new byte[salt.length + hashedPassword.length];
        System.arraycopy(salt, 0, saltAndHash, 0, salt.length);
        System.arraycopy(hashedPassword, 0, saltAndHash, salt.length, hashedPassword.length);
        
        return Base64.getEncoder().encodeToString(saltAndHash);
    }
    
    // Generate random salt
    public byte[] generateSalt() {
        SecureRandom random = new SecureRandom();
        byte[] salt = new byte[16];
        random.nextBytes(salt);
        return salt;
    }
    
    // File integrity check
    public String calculateFileHash(byte[] fileContent) throws Exception {
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        byte[] hash = md.digest(fileContent);
        return Base64.getEncoder().encodeToString(hash);
    }
}

@Service
public class DigitalSignatureService {
    private final String SIGNATURE_ALGORITHM = "SHA256withRSA";
    
    public String signData(String data, PrivateKey privateKey) throws Exception {
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initSign(privateKey);
        signature.update(data.getBytes(StandardCharsets.UTF_8));
        
        byte[] signedData = signature.sign();
        return Base64.getEncoder().encodeToString(signedData);
    }
    
    public boolean verifySignature(String data, String signatureStr, PublicKey publicKey) 
            throws Exception {
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initVerify(publicKey);
        signature.update(data.getBytes(StandardCharsets.UTF_8));
        
        byte[] signatureBytes = Base64.getDecoder().decode(signatureStr);
        return signature.verify(signatureBytes);
    }
    
    // HMAC for message authentication
    public String createHMAC(String message, SecretKey key) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(key);
        byte[] hmac = mac.doFinal(message.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(hmac);
    }
    
    public boolean verifyHMAC(String message, String hmacStr, SecretKey key) 
            throws Exception {
        String calculatedHmac = createHMAC(message, key);
        return MessageDigest.isEqual(
            calculatedHmac.getBytes(), 
            hmacStr.getBytes()
        );
    }
}`
      }
    },
    {
      id: 3,
      category: 'Key Management',
      difficulty: 'Hard',
      question: 'How do you implement secure key management in enterprise applications?',
      answer: {
        short: 'Use HSM/KMS for key storage, implement key rotation, separate encryption/signing keys, use key derivation functions, and follow principle of least privilege.',
        detailed: `
**Key Management Principles**:

1. **Key Generation**
   - Use cryptographically secure random generators
   - Generate keys with sufficient entropy
   - Use appropriate key sizes (AES-256, RSA-2048+)

2. **Key Storage**
   - Hardware Security Modules (HSM)
   - Key Management Services (AWS KMS, Azure Key Vault)
   - Never store keys in application code
   - Encrypt keys at rest

3. **Key Distribution**
   - Secure key exchange protocols
   - Certificate-based authentication
   - Out-of-band key distribution
   - Key escrow for recovery

4. **Key Rotation**
   - Regular key rotation schedule
   - Automated key rotation
   - Backward compatibility during rotation
   - Emergency key revocation

5. **Key Lifecycle Management**
   - Key generation → Active → Suspended → Revoked → Destroyed
   - Audit trails for all key operations
   - Compliance with regulations (FIPS 140-2, Common Criteria)

**Enterprise Architecture**:
- Centralized key management service
- API-based key access
- Role-based access control
- Key versioning and backup
        `,
        codeExample: `@Configuration
public class KeyManagementConfig {
    
    @Value("\${encryption.master-key-id}")
    private String masterKeyId;
    
    @Autowired
    private AWSKMSClient kmsClient;
    
    // Key Management Service
    @Service
    public class KeyManagementService {
        
        // Generate Data Encryption Key using KMS
        public GenerateDataKeyResult generateDataKey() {
            GenerateDataKeyRequest request = new GenerateDataKeyRequest()
                .withKeyId(masterKeyId)
                .withKeySpec(DataKeySpec.AES_256);
            
            return kmsClient.generateDataKey(request);
        }
        
        // Decrypt data key
        public ByteBuffer decryptDataKey(ByteBuffer encryptedKey) {
            DecryptRequest request = new DecryptRequest()
                .withCiphertextBlob(encryptedKey);
            
            DecryptResult result = kmsClient.decrypt(request);
            return result.getPlaintext();
        }
        
        // Rotate keys
        @Scheduled(cron = "0 0 0 1 * ?") // Monthly rotation
        public void rotateDataKeys() {
            List<String> activeKeyIds = getActiveKeyIds();
            
            for (String keyId : activeKeyIds) {
                // Generate new key
                GenerateDataKeyResult newKey = generateDataKey();
                
                // Re-encrypt data with new key
                reEncryptDataWithNewKey(keyId, newKey);
                
                // Mark old key for retirement
                markKeyForRetirement(keyId);
                
                log.info("Rotated key: {}", keyId);
            }
        }
    }
    
    // Secure key storage
    @Service
    public class SecureKeyStorage {
        
        private final Map<String, SecretKey> keyCache = new ConcurrentHashMap<>();
        private final Object[] locks = new Object[16]; // Striped locks
        
        public SecureKeyStorage() {
            for (int i = 0; i < locks.length; i++) {
                locks[i] = new Object();
            }
        }
        
        public SecretKey getKey(String keyId) {
            int lockIndex = keyId.hashCode() & (locks.length - 1);
            synchronized (locks[lockIndex]) {
                return keyCache.computeIfAbsent(keyId, this::loadKeyFromKMS);
            }
        }
        
        private SecretKey loadKeyFromKMS(String keyId) {
            // Load from KMS with caching
            ByteBuffer encryptedKey = getEncryptedKeyFromDatabase(keyId);
            ByteBuffer decryptedKey = decryptDataKey(encryptedKey);
            
            return new SecretKeySpec(decryptedKey.array(), "AES");
        }
        
        // Key derivation for different purposes
        public SecretKey deriveKey(SecretKey masterKey, String purpose, byte[] salt) 
                throws Exception {
            // Using PBKDF2 for key derivation
            PBEKeySpec spec = new PBEKeySpec(
                masterKey.getEncoded(), 
                salt, 
                100000, // iterations
                256 // key length
            );
            
            SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");
            SecretKey derivedKey = factory.generateSecret(spec);
            
            return new SecretKeySpec(derivedKey.getEncoded(), "AES");
        }
    }
    
    // Audit logging
    @Component
    public class KeyAuditLogger {
        
        public void logKeyOperation(String operation, String keyId, String userId) {
            AuditEvent event = AuditEvent.builder()
                .timestamp(Instant.now())
                .operation(operation)
                .keyId(keyId)
                .userId(userId)
                .ipAddress(getCurrentUserIP())
                .build();
            
            // Send to audit system
            auditService.logEvent(event);
            
            // Critical operations - send alerts
            if (isCriticalOperation(operation)) {
                alertService.sendKeyOperationAlert(event);
            }
        }
    }
}`
      }
    },
    {
      id: 4,
      category: 'SSL/TLS',
      difficulty: 'Medium',
      question: 'Explain the SSL/TLS handshake process and how to implement mutual TLS authentication.',
      answer: {
        short: 'TLS handshake: ClientHello → ServerHello → Certificate exchange → Key exchange → Finished. mTLS adds client certificate verification.',
        detailed: `
**TLS Handshake Process**:

1. **Client Hello**
   - Supported cipher suites
   - Random number
   - TLS version

2. **Server Hello**
   - Selected cipher suite
   - Server random number
   - Server certificate

3. **Certificate Verification**
   - Client validates server certificate
   - Checks certificate chain
   - Verifies certificate hasn't expired

4. **Key Exchange**
   - Client generates pre-master secret
   - Encrypts with server's public key
   - Sends to server

5. **Session Key Generation**
   - Both sides generate session keys
   - Using client random + server random + pre-master secret

6. **Finished Messages**
   - Encrypted with session keys
   - Confirms successful handshake

**Mutual TLS (mTLS)**:
- Client also provides certificate
- Server verifies client identity
- Used in service-to-service communication
- Zero-trust security model

**Modern TLS Features**:
- TLS 1.3 (faster handshake)
- Perfect Forward Secrecy
- HSTS (HTTP Strict Transport Security)
- Certificate Transparency
        `,
        codeExample: `// TLS Configuration in Spring Boot
@Configuration
public class TLSConfig {
    
    @Bean
    public TomcatServletWebServerFactory servletContainer() {
        TomcatServletWebServerFactory tomcat = new TomcatServletWebServerFactory() {
            @Override
            protected void postProcessContext(Context context) {
                SecurityConstraint constraint = new SecurityConstraint();
                constraint.setUserConstraint("CONFIDENTIAL");
                SecurityCollection collection = new SecurityCollection();
                collection.addPattern("/*");
                constraint.addCollection(collection);
                context.addConstraint(constraint);
            }
        };
        
        // Redirect HTTP to HTTPS
        tomcat.addAdditionalTomcatConnectors(redirectConnector());
        return tomcat;
    }
    
    private Connector redirectConnector() {
        Connector connector = new Connector("org.apache.coyote.http11.Http11NioProtocol");
        connector.setScheme("http");
        connector.setPort(8080);
        connector.setSecure(false);
        connector.setRedirectPort(8443);
        return connector;
    }
}

// Mutual TLS Client Configuration
@Service
public class MutualTLSService {
    
    public RestTemplate createMutualTLSRestTemplate() throws Exception {
        // Load client certificate
        KeyStore keyStore = KeyStore.getInstance("PKCS12");
        keyStore.load(
            new FileInputStream("client-certificate.p12"), 
            "password".toCharArray()
        );
        
        // Load trusted CA certificates
        KeyStore trustStore = KeyStore.getInstance("JKS");
        trustStore.load(
            new FileInputStream("truststore.jks"), 
            "password".toCharArray()
        );
        
        // SSL Context with mutual authentication
        SSLContext sslContext = SSLContextBuilder
            .create()
            .loadKeyMaterial(keyStore, "password".toCharArray())
            .loadTrustMaterial(trustStore, null)
            .build();
        
        // HTTP client with SSL
        CloseableHttpClient httpClient = HttpClients.custom()
            .setSSLContext(sslContext)
            .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
            .build();
        
        HttpComponentsClientHttpRequestFactory factory = 
            new HttpComponentsClientHttpRequestFactory();
        factory.setHttpClient(httpClient);
        
        return new RestTemplate(factory);
    }
    
    // Certificate validation
    public boolean validateCertificate(X509Certificate certificate) {
        try {
            // Check expiration
            certificate.checkValidity();
            
            // Verify signature
            certificate.verify(caCertificate.getPublicKey());
            
            // Check certificate purpose
            List<String> extendedKeyUsage = certificate.getExtendedKeyUsage();
            if (extendedKeyUsage != null && 
                !extendedKeyUsage.contains("*******.*******.1")) { // Server auth
                return false;
            }
            
            // Check revocation status (OCSP/CRL)
            return checkRevocationStatus(certificate);
            
        } catch (Exception e) {
            log.error("Certificate validation failed", e);
            return false;
        }
    }
}

// Certificate management
@Service
public class CertificateManagementService {
    
    // Auto-renew certificates using ACME (Let's Encrypt)
    @Scheduled(fixedRate = 86400000) // Daily check
    public void checkCertificateExpiry() {
        List<Certificate> certificates = certificateRepository.findAll();
        
        for (Certificate cert : certificates) {
            if (cert.expiresWithin(Duration.ofDays(30))) {
                renewCertificate(cert);
            }
        }
    }
    
    private void renewCertificate(Certificate cert) {
        try {
            // Generate new key pair
            KeyPair keyPair = generateKeyPair();
            
            // Create CSR
            PKCS10CertificationRequest csr = createCSR(cert.getDomain(), keyPair);
            
            // Submit to CA
            X509Certificate newCert = submitCSRToCA(csr);
            
            // Update certificate in store
            updateCertificate(cert.getId(), newCert, keyPair.getPrivate());
            
            log.info("Certificate renewed for domain: {}", cert.getDomain());
            
        } catch (Exception e) {
            log.error("Failed to renew certificate for {}", cert.getDomain(), e);
            alertService.sendCertificateRenewalFailure(cert);
        }
    }
}`
      }
    },
    {
      id: 5,
      category: 'Algorithm Security',
      difficulty: 'Hard',
      question: 'What are the current recommendations for cryptographic algorithms and key sizes? How do you migrate from deprecated algorithms?',
      answer: {
        short: 'Use AES-256, RSA-2048+, SHA-256+, ECDSA P-256+. Avoid MD5, SHA-1, DES, RC4. Migrate gradually with backward compatibility and key rotation.',
        detailed: `
**Current Recommendations (2024)**:

**Symmetric Encryption**:
- ✅ AES-256-GCM (preferred)
- ✅ AES-128-GCM (acceptable)
- ✅ ChaCha20-Poly1305
- ❌ DES, 3DES (deprecated)
- ❌ RC4 (broken)

**Asymmetric Encryption**:
- ✅ RSA-2048+ (RSA-4096 for long-term)
- ✅ ECDSA P-256+ (preferred for performance)
- ✅ Ed25519 (EdDSA)
- ❌ RSA-1024 (broken)

**Hashing**:
- ✅ SHA-256, SHA-384, SHA-512
- ✅ SHA-3 family
- ❌ MD5 (broken)
- ❌ SHA-1 (deprecated)

**Key Exchange**:
- ✅ ECDH P-256+
- ✅ X25519
- ✅ DHE-2048+
- ❌ Static DH

**Migration Strategy**:
1. Inventory current algorithms
2. Plan migration timeline
3. Implement dual-support phase
4. Gradual rollout
5. Monitor and validate
6. Complete migration
7. Remove legacy support

**Post-Quantum Considerations**:
NIST has standardized post-quantum algorithms for future migration:
- CRYSTALS-Kyber (key encapsulation)
- CRYSTALS-Dilithium (digital signatures)
- FALCON (digital signatures)
        `,
        codeExample: `@Service
public class CryptographicMigrationService {
    
    // Algorithm version management
    public enum CryptoVersion {
        LEGACY("RSA-1024", "SHA-1", "AES-128-CBC"),
        CURRENT("RSA-2048", "SHA-256", "AES-256-GCM"),
        FUTURE("ECDSA-P256", "SHA-256", "AES-256-GCM");
        
        private final String asymmetricAlg;
        private final String hashAlg;
        private final String symmetricAlg;
        
        CryptoVersion(String asymmetric, String hash, String symmetric) {
            this.asymmetricAlg = asymmetric;
            this.hashAlg = hash;
            this.symmetricAlg = symmetric;
        }
    }
    
    // Migration service
    @Service
    public class AlgorithmMigrationService {
        
        // Gradual migration with backward compatibility
        public EncryptionResult encryptWithMigration(String data, String userId) {
            // Check user's supported version
            CryptoVersion userVersion = getUserCryptoVersion(userId);
            CryptoVersion systemVersion = getSystemCryptoVersion();
            
            // Use highest common version
            CryptoVersion targetVersion = selectCompatibleVersion(userVersion, systemVersion);
            
            return encryptWithVersion(data, targetVersion);
        }
        
        public String decryptWithMigration(EncryptedData encryptedData) throws Exception {
            // Detect algorithm version from metadata
            CryptoVersion dataVersion = detectCryptoVersion(encryptedData);
            
            // Decrypt with appropriate algorithm
            String plaintext = decryptWithVersion(encryptedData, dataVersion);
            
            // Check if re-encryption needed
            if (shouldMigrateData(dataVersion)) {
                // Re-encrypt with current algorithm
                EncryptionResult newResult = encryptWithMigration(plaintext, getCurrentUserId());
                // Update stored data asynchronously
                updateEncryptedDataAsync(encryptedData.getId(), newResult);
            }
            
            return plaintext;
        }
        
        // Batch migration
        @Async
        public CompletableFuture<MigrationResult> migrateStoredData(
                CryptoVersion fromVersion, 
                CryptoVersion toVersion) {
            
            List<EncryptedData> dataToMigrate = findDataWithVersion(fromVersion);
            int total = dataToMigrate.size();
            int processed = 0;
            int failed = 0;
            
            for (EncryptedData data : dataToMigrate) {
                try {
                    // Decrypt with old algorithm
                    String plaintext = decryptWithVersion(data, fromVersion);
                    
                    // Re-encrypt with new algorithm
                    EncryptionResult newResult = encryptWithVersion(plaintext, toVersion);
                    
                    // Update database
                    updateEncryptedData(data.getId(), newResult, toVersion);
                    processed++;
                    
                    // Progress reporting
                    if (processed % 1000 == 0) {
                        reportMigrationProgress(processed, total);
                    }
                    
                } catch (Exception e) {
                    log.error("Migration failed for data ID: {}", data.getId(), e);
                    failed++;
                }
            }
            
            return CompletableFuture.completedFuture(
                new MigrationResult(total, processed, failed)
            );
        }
    }
    
    // Algorithm strength validation
    public class CryptographicValidationService {
        
        public ValidationResult validateAlgorithmStrength(String algorithm, int keySize) {
            switch (algorithm.toUpperCase()) {
                case "RSA":
                    if (keySize < 2048) {
                        return ValidationResult.error("RSA key size must be at least 2048 bits");
                    }
                    if (keySize < 3072) {
                        return ValidationResult.warning("RSA-3072+ recommended for new systems");
                    }
                    return ValidationResult.valid();
                    
                case "AES":
                    if (keySize != 128 && keySize != 192 && keySize != 256) {
                        return ValidationResult.error("AES key size must be 128, 192, or 256 bits");
                    }
                    return ValidationResult.valid();
                    
                case "ECDSA":
                    if (keySize < 256) {
                        return ValidationResult.error("ECDSA key size must be at least 256 bits");
                    }
                    return ValidationResult.valid();
                    
                default:
                    return ValidationResult.error("Unsupported or deprecated algorithm: " + algorithm);
            }
        }
        
        // Check for deprecated algorithms
        public List<DeprecationWarning> checkForDeprecatedAlgorithms() {
            List<DeprecationWarning> warnings = new ArrayList<>();
            
            // Check certificates
            List<X509Certificate> certs = getAllStoredCertificates();
            for (X509Certificate cert : certs) {
                String sigAlg = cert.getSigAlgName();
                if (isDeprecatedAlgorithm(sigAlg)) {
                    warnings.add(new DeprecationWarning(
                        "Certificate", 
                        cert.getSubjectDN().toString(),
                        sigAlg,
                        "Replace with RSA-2048+ or ECDSA-P256+"
                    ));
                }
            }
            
            return warnings;
        }
    }
}`
      }
    }
  ];

  const filteredQuestions = interviewQuestions.filter(q => 
    q.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
    q.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getDifficultyColor = (difficulty) => {
    switch(difficulty) {
      case 'Easy': return '#10b981';
      case 'Medium': return '#f59e0b'; 
      case 'Hard': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getCategoryIcon = (category) => {
    switch(category) {
      case 'Encryption Fundamentals': return <Lock size={16} />;
      case 'Hashing & Digital Signatures': return <Shield size={16} />;
      case 'Key Management': return <Key size={16} />;
      case 'SSL/TLS': return <GitBranch size={16} />;
      case 'Algorithm Security': return <Code size={16} />;
      default: return <Lock size={16} />;
    }
  };

  const runDemo = (questionId) => {
    setDemoResult('Running cryptographic demo...');
    setTimeout(() => {
      switch(questionId) {
        case 1:
          setDemoResult(`AES-256 Encryption Demo:
Original: "Hello, World!"
Encrypted: "U2FsdGVkX19tZXNzYWdlZGF0YQ=="
Decrypted: "Hello, World!" ✓

RSA-2048 Demo:
Key Pair Generated: 2048 bits
Message: "Secret Message"
Encrypted: "mQENBF2K3..." (256 bytes)
Decrypted: "Secret Message" ✓`);
          break;
        case 2:
          setDemoResult(`SHA-256 Hash Demo:
Input: "password123"
Hash: "ef92b778bafe771e89245b89ecbc08a44a4e166c06659911881f383d4473e94f"
Salt: "a1b2c3d4e5f6"
Salted Hash: "2f8a7e4b3c9d..."

Digital Signature Demo:
Message: "Important Document"
Signature: "MIIBIjANBgkqhkiG..."
Verification: VALID ✓`);
          break;
        default:
          setDemoResult('Demo completed successfully!');
      }
    }, 2000);
  };

  return (
    <div style={{
      width: '100%',
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%)',
      color: '#ffffff',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif',
      padding: '20px'
    }}>
      {/* Header */}
      <div style={{
        padding: '24px',
        background: 'rgba(15, 23, 42, 0.8)',
        borderRadius: '16px',
        border: '1px solid rgba(51, 65, 85, 0.5)',
        marginBottom: '24px'
      }}>
        <h1 style={{
          fontSize: '36px',
          margin: '0 0 8px 0',
          background: 'linear-gradient(135deg, #f59e0b 0%, #ef4444 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          fontWeight: '700'
        }}>
          Cryptography & Encryption Interview Questions
        </h1>
        <p style={{
          fontSize: '16px',
          color: '#94a3b8',
          margin: '0 0 16px 0'
        }}>
          Master cryptographic concepts with hands-on examples and security best practices
        </p>

        {/* Search Bar */}
        <div style={{ position: 'relative', maxWidth: '400px' }}>
          <Search size={20} style={{
            position: 'absolute',
            left: '12px',
            top: '50%',
            transform: 'translateY(-50%)',
            color: '#6b7280'
          }} />
          <input
            type="text"
            placeholder="Search questions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              width: '100%',
              padding: '12px 12px 12px 44px',
              background: 'rgba(31, 41, 55, 0.5)',
              border: '1px solid rgba(75, 85, 99, 0.3)',
              borderRadius: '8px',
              color: '#ffffff',
              fontSize: '14px'
            }}
          />
        </div>
      </div>

      {/* Questions Grid */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: selectedQuestion ? '1fr 1fr' : '1fr',
        gap: '24px'
      }}>
        {/* Questions List */}
        <div>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            {filteredQuestions.map((question) => (
              <div 
                key={question.id}
                onClick={() => {
                  setSelectedQuestion(question);
                  setShowAnswer(false);
                  setDemoResult('');
                }}
                style={{
                  background: selectedQuestion?.id === question.id 
                    ? 'linear-gradient(135deg, #dc2626 0%, #f59e0b 100%)' 
                    : 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
                  borderRadius: '12px',
                  border: '1px solid rgba(51, 65, 85, 0.5)',
                  padding: '20px',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start', marginBottom: '12px' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    {getCategoryIcon(question.category)}
                    <span style={{ fontSize: '12px', color: '#94a3b8' }}>
                      {question.category}
                    </span>
                  </div>
                  <span style={{
                    fontSize: '11px',
                    padding: '2px 6px',
                    backgroundColor: `${getDifficultyColor(question.difficulty)}20`,
                    color: getDifficultyColor(question.difficulty),
                    borderRadius: '4px',
                    fontWeight: '600'
                  }}>
                    {question.difficulty}
                  </span>
                </div>
                
                <h3 style={{ 
                  fontSize: '16px', 
                  fontWeight: '600', 
                  color: 'white', 
                  margin: '0',
                  lineHeight: '1.4'
                }}>
                  {question.question}
                </h3>
              </div>
            ))}
          </div>
        </div>

        {/* Answer Panel */}
        {selectedQuestion && (
          <div style={{
            background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
            borderRadius: '16px',
            border: '1px solid rgba(51, 65, 85, 0.5)',
            padding: '24px',
            position: 'sticky',
            top: '20px',
            height: 'fit-content'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
              <h2 style={{ fontSize: '18px', color: 'white', margin: 0 }}>Answer</h2>
              <div style={{ display: 'flex', gap: '8px' }}>
                {(selectedQuestion.id === 1 || selectedQuestion.id === 2) && (
                  <button
                    onClick={() => runDemo(selectedQuestion.id)}
                    style={{
                      background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                      border: 'none',
                      borderRadius: '6px',
                      color: 'white',
                      cursor: 'pointer',
                      padding: '6px 12px',
                      fontSize: '12px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px'
                    }}
                  >
                    <Play size={14} />
                    Demo
                  </button>
                )}
                <button
                  onClick={() => setSelectedQuestion(null)}
                  style={{
                    background: 'rgba(239, 68, 68, 0.1)',
                    border: '1px solid rgba(239, 68, 68, 0.3)',
                    borderRadius: '6px',
                    color: '#ef4444',
                    cursor: 'pointer',
                    padding: '6px'
                  }}
                >
                  <X size={16} />
                </button>
              </div>
            </div>

            {/* Demo Result */}
            {demoResult && (
              <div style={{
                background: 'rgba(16, 185, 129, 0.1)',
                borderLeft: '4px solid #10b981',
                borderRadius: '6px',
                padding: '16px',
                marginBottom: '20px'
              }}>
                <h4 style={{ fontSize: '14px', color: '#10b981', margin: '0 0 8px 0', display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <Clock size={14} />
                  Live Demo Result:
                </h4>
                <pre style={{ 
                  fontSize: '12px', 
                  color: '#e2e8f0', 
                  margin: 0, 
                  lineHeight: '1.4',
                  whiteSpace: 'pre-wrap'
                }}>
                  {demoResult}
                </pre>
              </div>
            )}

            {!showAnswer ? (
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <button
                  onClick={() => setShowAnswer(true)}
                  style={{
                    background: 'linear-gradient(135deg, #f59e0b 0%, #ef4444 100%)',
                    border: 'none',
                    borderRadius: '8px',
                    color: 'white',
                    cursor: 'pointer',
                    fontSize: '16px',
                    fontWeight: '600',
                    padding: '12px 24px'
                  }}
                >
                  Show Answer
                </button>
              </div>
            ) : (
              <div>
                {/* Quick Answer */}
                <div style={{
                  background: 'rgba(245, 158, 11, 0.1)',
                  borderLeft: '4px solid #f59e0b',
                  borderRadius: '6px',
                  padding: '16px',
                  marginBottom: '20px'
                }}>
                  <h4 style={{ fontSize: '14px', color: '#fbbf24', margin: '0 0 8px 0' }}>Quick Answer:</h4>
                  <p style={{ fontSize: '14px', color: '#e2e8f0', margin: 0, lineHeight: '1.4' }}>
                    {selectedQuestion.answer.short}
                  </p>
                </div>

                {/* Detailed Answer */}
                <div style={{ marginBottom: '20px' }}>
                  <h4 style={{ fontSize: '14px', color: '#10b981', margin: '0 0 12px 0' }}>Detailed Explanation:</h4>
                  <div style={{ 
                    fontSize: '13px', 
                    color: '#d1d5db', 
                    lineHeight: '1.6',
                    whiteSpace: 'pre-line'
                  }}>
                    {selectedQuestion.answer.detailed}
                  </div>
                </div>

                {/* Code Example */}
                {selectedQuestion.answer.codeExample && (
                  <div>
                    <h4 style={{ fontSize: '14px', color: '#ef4444', margin: '0 0 12px 0' }}>Implementation Example:</h4>
                    <pre style={{
                      background: 'rgba(0, 0, 0, 0.3)',
                      borderRadius: '6px',
                      padding: '16px',
                      fontSize: '12px',
                      color: '#e2e8f0',
                      overflow: 'auto',
                      border: '1px solid rgba(75, 85, 99, 0.3)'
                    }}>
                      <code>{selectedQuestion.answer.codeExample}</code>
                    </pre>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default CryptographyEncryption;
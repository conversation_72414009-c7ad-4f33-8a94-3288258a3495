import React, { useState } from 'react';
import { Coffee, Code, Package, Database, Settings, Shield } from 'lucide-react';

const JavaImplementation = () => {
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [activeTab, setActiveTab] = useState('classes');

  const javaPackages = [
    {
      id: 'matching',
      name: 'com.darkpool.matching',
      icon: <Code size={20} />,
      description: 'Order matching and execution engine',
      classCount: 15,
      color: '#ef4444'
    },
    {
      id: 'risk',
      name: 'com.darkpool.risk',
      icon: <Shield size={20} />,
      description: 'Risk management and validation',
      classCount: 12,
      color: '#f59e0b'
    },
    {
      id: 'gateway',
      name: 'com.darkpool.gateway',
      icon: <Package size={20} />,
      description: 'Order gateway and routing',
      classCount: 8,
      color: '#3b82f6'
    },
    {
      id: 'data',
      name: 'com.darkpool.data',
      icon: <Database size={20} />,
      description: 'Market data and persistence',
      classCount: 20,
      color: '#10b981'
    },
    {
      id: 'position',
      name: 'com.darkpool.position',
      icon: <Settings size={20} />,
      description: 'Position management and P&L',
      classCount: 10,
      color: '#8b5cf6'
    },
    {
      id: 'reporting',
      name: 'com.darkpool.reporting',
      icon: <Coffee size={20} />,
      description: 'Compliance and trade reporting',
      classCount: 14,
      color: '#06b6d4'
    }
  ];

  const getPackageDetails = (packageId) => {
    const details = {
      'matching': {
        classes: [
          'DarkPoolMatchingEngine.java - Core matching algorithm implementation',
          'OrderBook.java - Thread-safe order book with lock-free operations',
          'MatchingResult.java - Trade execution result container',
          'PriceTimePriorityComparator.java - Order priority comparison logic',
          'AntiGamingEngine.java - Predatory trading protection',
          'TradeExecutor.java - Trade execution and confirmation',
          'OrderValidator.java - Order validation before matching',
          'LiquidityPool.java - Dark pool liquidity aggregation',
          'MidpointCrossing.java - NBBO midpoint execution logic',
          'IcebergHandler.java - Hidden order slice management',
          'SessionManager.java - Trading session state management',
          'PerformanceMonitor.java - Real-time latency monitoring',
          'BacktestEngine.java - Historical matching simulation',
          'RiskCircuitBreaker.java - Emergency trading halt mechanism',
          'MatchingEventPublisher.java - Trade event broadcasting'
        ],
        interfaces: [
          'MatchingEngine.java - Core matching engine contract',
          'OrderBookManager.java - Order book lifecycle management',
          'TradeReporter.java - Trade confirmation interface',
          'LiquidityProvider.java - Dark pool liquidity source',
          'SessionStateListener.java - Trading session callbacks'
        ],
        services: [
          'MatchingEngineService.java - Spring service orchestration',
          'OrderBookService.java - Order book CRUD operations',
          'TradeLifecycleService.java - Complete trade processing',
          'MarketDataIntegration.java - Real-time price feeds',
          'RegulatoryReporting.java - Trade reporting compliance'
        ],
        configuration: `# Spring Boot Configuration - application.yml
darkpool:
  matching:
    engine:
      thread-pool-size: 4
      queue-capacity: 100000
      timeout-ms: 10
      anti-gaming-enabled: true
    order-book:
      max-depth: 10000
      price-levels: 5000
      cleanup-interval: 1000ms
    performance:
      latency-monitoring: true
      metrics-enabled: true
      histogram-buckets: [1, 5, 10, 25, 50, 100, 250, 500]`,
        dependencies: `<!-- Maven Dependencies - pom.xml -->
<dependencies>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-jpa</artifactId>
    </dependency>
    <dependency>
        <groupId>com.lmax</groupId>
        <artifactId>disruptor</artifactId>
        <version>3.4.4</version>
    </dependency>
    <dependency>
        <groupId>net.openhft</groupId>
        <artifactId>chronicle-queue</artifactId>
        <version>5.23.32</version>
    </dependency>
    <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-core</artifactId>
    </dependency>
</dependencies>`
      },
      'risk': {
        classes: [
          'PreTradeRiskEngine.java - Real-time pre-trade risk assessment',
          'PostTradeRiskAnalyzer.java - Post-trade risk impact analysis',
          'RiskCalculator.java - VaR/CVaR calculation engine',
          'PositionLimitValidator.java - Position limit enforcement',
          'MarginCalculator.java - Initial and variation margin',
          'ConcentrationRiskAnalyzer.java - Portfolio concentration metrics',
          'StressTestEngine.java - Scenario analysis and stress testing',
          'RiskMetricsCollector.java - Risk metrics aggregation',
          'CreditLimitValidator.java - Counterparty credit limits',
          'VolatilityCalculator.java - Historical volatility analysis',
          'CorrelationMatrix.java - Asset correlation calculations',
          'MonteCarloSimulator.java - Monte Carlo risk simulation'
        ],
        interfaces: [
          'RiskEngine.java - Core risk assessment interface',
          'RiskCalculationService.java - Risk calculation contract',
          'LimitValidator.java - Risk limit validation interface',
          'RiskReporter.java - Risk reporting callback interface'
        ],
        services: [
          'RiskManagementService.java - Risk orchestration service',
          'RiskDataService.java - Risk data persistence service',
          'AlertingService.java - Risk breach alerting',
          'RiskReportingService.java - Regulatory risk reporting'
        ],
        configuration: `# Risk Management Configuration
darkpool:
  risk:
    pre-trade:
      enabled: true
      timeout-ms: 50
      parallel-processing: true
    limits:
      position-limit-check: true
      concentration-limit: 0.1
      max-order-size: 1000000
    var:
      confidence-level: 0.99
      time-horizon-days: 1
      calculation-method: MONTE_CARLO
    stress-test:
      scenarios: [MARKET_CRASH, VOLATILITY_SPIKE, LIQUIDITY_CRISIS]
      frequency: DAILY`,
        dependencies: `<!-- Risk Engine Dependencies -->
<dependencies>
    <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-math3</artifactId>
        <version>3.6.1</version>
    </dependency>
    <dependency>
        <groupId>com.quantlib</groupId>
        <artifactId>quantlib-java</artifactId>
        <version>1.32</version>
    </dependency>
    <dependency>
        <groupId>org.ejml</groupId>
        <artifactId>ejml-all</artifactId>
        <version>0.41</version>
    </dependency>
</dependencies>`
      },
      'gateway': {
        classes: [
          'OrderGateway.java - Main order entry point and validation',
          'FIXMessageHandler.java - FIX protocol message processing',
          'RESTOrderController.java - HTTP REST order submission',
          'OrderEnrichmentService.java - Order metadata enrichment',
          'ClientAuthenticationService.java - Client verification',
          'RateLimiter.java - Request rate limiting and throttling',
          'OrderRouter.java - Smart order routing logic',
          'SessionManager.java - FIX session lifecycle management'
        ],
        interfaces: [
          'OrderHandler.java - Order processing interface',
          'MessageProcessor.java - Protocol message handler',
          'AuthenticationProvider.java - Client auth interface',
          'RoutingStrategy.java - Order routing contract'
        ],
        services: [
          'GatewayService.java - Gateway orchestration service',
          'ClientManagementService.java - Client lifecycle service',
          'ProtocolService.java - Multi-protocol support service'
        ],
        configuration: `# Gateway Configuration
darkpool:
  gateway:
    fix:
      version: FIX.5.0SP2
      session-timeout: 30s
      heartbeat-interval: 30s
    rest:
      port: 8080
      max-connections: 1000
    rate-limiting:
      requests-per-second: 1000
      burst-capacity: 100
    routing:
      default-venue: DARK_POOL_1
      smart-routing: true`,
        dependencies: `<!-- Gateway Dependencies -->
<dependencies>
    <dependency>
        <groupId>org.quickfixj</groupId>
        <artifactId>quickfixj-core</artifactId>
        <version>2.3.1</version>
    </dependency>
    <dependency>
        <groupId>io.github.bucket4j</groupId>
        <artifactId>bucket4j-core</artifactId>
        <version>7.6.0</version>
    </dependency>
</dependencies>`
      },
      'data': {
        classes: [
          'MarketDataService.java - Real-time market data processing',
          'HistoricalDataService.java - Historical data storage/retrieval',
          'NBBOCalculator.java - National best bid/offer calculation',
          'OrderBookReconstructor.java - Order book state reconstruction',
          'DataNormalizer.java - Multi-source data normalization',
          'PriceValidator.java - Price reasonableness validation',
          'VolumeAggregator.java - Volume aggregation across venues',
          'CorporateActionProcessor.java - Corporate action adjustments',
          'ReferenceDataManager.java - Instrument reference data',
          'TimeSeriesAnalyzer.java - Historical pattern analysis',
          'DataQualityMonitor.java - Real-time quality monitoring',
          'FeedHandler.java - Market data feed connectivity',
          'DataCacheManager.java - High-speed data caching',
          'DataArchiver.java - Long-term data archival',
          'MarketSessionTracker.java - Trading session state',
          'VolatilityCalculator.java - Real-time volatility calculation',
          'LiquidityAnalyzer.java - Market liquidity analysis',
          'AnomalyDetector.java - Price/volume anomaly detection',
          'DataRecoveryService.java - Gap-fill and recovery',
          'ComplianceDataCollector.java - Regulatory data collection'
        ],
        interfaces: [
          'DataProcessor.java - Data processing interface',
          'DataProvider.java - Market data source interface',
          'DataValidator.java - Data validation contract',
          'CacheManager.java - Data caching interface'
        ],
        services: [
          'MarketDataOrchestrator.java - Data flow orchestration',
          'DataPersistenceService.java - Data storage service',
          'DataDistributionService.java - Data broadcasting service',
          'DataAnalyticsService.java - Real-time analytics service'
        ],
        configuration: `# Market Data Configuration
darkpool:
  market-data:
    sources:
      - NYSE
      - NASDAQ  
      - BATS
      - IEX
    processing:
      buffer-size: 1048576
      batch-size: 1000
      worker-threads: 8
    caching:
      l1-cache-size: 100MB
      l2-cache-size: 1GB
      ttl-seconds: 3600
    distribution:
      multicast-enabled: true
      compression: LZ4`,
        dependencies: `<!-- Market Data Dependencies -->
<dependencies>
    <dependency>
        <groupId>com.lmax</groupId>
        <artifactId>disruptor</artifactId>
    </dependency>
    <dependency>
        <groupId>net.openhft</groupId>
        <artifactId>chronicle-map</artifactId>
    </dependency>
    <dependency>
        <groupId>org.influxdb</groupId>
        <artifactId>influxdb-java</artifactId>
    </dependency>
</dependencies>`
      },
      'position': {
        classes: [
          'PositionKeeper.java - Real-time position tracking',
          'PnLCalculator.java - Profit and loss calculation',
          'PositionAggregator.java - Multi-account position aggregation',
          'TradeAllocationEngine.java - Trade allocation logic',
          'AveragePrice Calculator.java - Volume-weighted average price',
          'ReconciliationEngine.java - Position reconciliation',
          'CashMovementProcessor.java - Cash position updates',
          'MarginCalculator.java - Margin requirement calculation',
          'PositionReporter.java - Position reporting and snapshots',
          'RiskAttributor.java - Position risk attribution'
        ],
        interfaces: [
          'PositionManager.java - Position management interface',
          'PnLCalculationService.java - P&L calculation contract',
          'AllocationStrategy.java - Trade allocation interface',
          'ReconciliationProvider.java - Reconciliation interface'
        ],
        services: [
          'PositionManagementService.java - Position service orchestration',
          'PositionDataService.java - Position data persistence',
          'PositionEventService.java - Position event publishing',
          'ReportingService.java - Position reporting service'
        ],
        configuration: `# Position Management Configuration
darkpool:
  position:
    aggregation:
      real-time: true
      batch-interval: 100ms
      parallel-processing: true
    reconciliation:
      frequency: DAILY
      tolerance: 0.01
      auto-adjust: false
    reporting:
      snapshots: [EOD, INTRADAY]
      formats: [CSV, XML, JSON]`,
        dependencies: `<!-- Position Management Dependencies -->
<dependencies>
    <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-math3</artifactId>
    </dependency>
    <dependency>
        <groupId>com.hazelcast</groupId>
        <artifactId>hazelcast</artifactId>
    </dependency>
</dependencies>`
      },
      'reporting': {
        classes: [
          'TradeReportingService.java - Regulatory trade reporting',
          'ComplianceReporter.java - Compliance report generation',
          'AuditTrailManager.java - Immutable audit trail',
          'RegulatoryFilingEngine.java - Automated regulatory filing',
          'ReportGenerator.java - Multi-format report generation',
          'DataEnrichmentService.java - Report data enrichment',
          'ValidationEngine.java - Report validation logic',
          'SubmissionGateway.java - Regulatory submission gateway',
          'ExceptionReporter.java - Exception and breach reporting',
          'ClientConfirmationService.java - Client trade confirmations',
          'ArchivalService.java - Long-term data retention',
          'InquiryResponseService.java - Regulatory inquiry handling',
          'MetricsCollector.java - Compliance metrics collection',
          'AlertManager.java - Compliance breach alerting'
        ],
        interfaces: [
          'ReportingEngine.java - Core reporting interface',
          'RegulatoryGateway.java - Submission gateway interface',
          'ComplianceValidator.java - Validation interface',
          'AuditService.java - Audit trail interface'
        ],
        services: [
          'ReportingOrchestrator.java - Reporting workflow service',
          'RegulatorySubmissionService.java - Submission service',
          'ComplianceMonitoringService.java - Monitoring service',
          'DataRetentionService.java - Data lifecycle service'
        ],
        configuration: `# Reporting Configuration
darkpool:
  reporting:
    regulatory:
      regimes: [MIFID_II, EMIR, DODD_FRANK, CAT]
      submission-timeout: 30s
      retry-attempts: 3
    formats:
      iso20022: true
      fix: true
      json: true
    archival:
      retention-years: 7
      compression: GZIP
      encryption: AES256`,
        dependencies: `<!-- Reporting Dependencies -->
<dependencies>
    <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-clients</artifactId>
    </dependency>
    <dependency>
        <groupId>org.bouncycastle</groupId>
        <artifactId>bcprov-jdk15on</artifactId>
    </dependency>
</dependencies>`
      }
    };
    return details[packageId] || {
      classes: ['Package details coming soon...'],
      interfaces: ['Interface details coming soon...'],
      services: ['Service details coming soon...'],
      configuration: '# Configuration coming soon...',
      dependencies: '<!-- Dependencies coming soon -->'
    };
  };

  return (
    <div style={{
      width: '100%',
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #0c0c0c 100%)',
      color: '#ffffff',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      overflow: 'auto'
    }}>
      {/* Header */}
      <div style={{
        background: 'rgba(0, 0, 0, 0.8)',
        borderBottom: '2px solid #333',
        padding: '20px',
        backdropFilter: 'blur(10px)'
      }}>
        <h1 style={{
          margin: '0',
          fontSize: '28px',
          fontWeight: 'bold',
          background: 'linear-gradient(45deg, #f97316, #dc2626)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          textAlign: 'center',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '12px'
        }}>
          <Coffee size={32} color="#f97316" />
          Java Implementation Architecture
        </h1>
        <p style={{
          margin: '10px 0 0 0',
          fontSize: '14px',
          color: '#888',
          textAlign: 'center'
        }}>
          Production-Ready Java Classes & Services
        </p>
      </div>

      <div style={{ display: 'flex', minHeight: 'calc(100vh - 100px)' }}>
        {/* Package Selection */}
        <div style={{
          flex: 1,
          position: 'relative',
          background: 'rgba(0, 0, 0, 0.3)',
          margin: '20px',
          borderRadius: '10px',
          overflow: 'hidden',
          padding: '20px'
        }}>
          <h2 style={{
            color: '#f97316',
            fontSize: '20px',
            marginBottom: '20px',
            textAlign: 'center'
          }}>
            Java Package Structure
          </h2>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '16px'
          }}>
            {javaPackages.map((pkg) => (
              <div
                key={pkg.id}
                onClick={() => setSelectedPackage(pkg)}
                style={{
                  background: selectedPackage?.id === pkg.id
                    ? `linear-gradient(135deg, ${pkg.color}33, ${pkg.color}55)`
                    : `linear-gradient(135deg, ${pkg.color}11, ${pkg.color}22)`,
                  border: `2px solid ${selectedPackage?.id === pkg.id ? pkg.color : pkg.color + '44'}`,
                  borderRadius: '12px',
                  padding: '20px',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  transform: selectedPackage?.id === pkg.id ? 'scale(1.02)' : 'scale(1)',
                  boxShadow: selectedPackage?.id === pkg.id
                    ? `0 8px 32px ${pkg.color}44`
                    : `0 4px 16px rgba(0, 0, 0, 0.3)`
                }}
              >
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  marginBottom: '12px'
                }}>
                  <div style={{ color: pkg.color }}>
                    {pkg.icon}
                  </div>
                  <div>
                    <h3 style={{
                      fontSize: '16px',
                      fontWeight: 'bold',
                      color: '#ffffff',
                      margin: '0 0 4px 0'
                    }}>
                      {pkg.name}
                    </h3>
                    <div style={{
                      fontSize: '12px',
                      color: '#999',
                      fontFamily: 'monospace'
                    }}>
                      {pkg.classCount} classes
                    </div>
                  </div>
                </div>
                <p style={{
                  fontSize: '14px',
                  color: '#ccc',
                  margin: '0',
                  lineHeight: '1.4'
                }}>
                  {pkg.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Details Panel - Matching Dark Pool Style */}
        {selectedPackage && (
          <div style={{
            position: 'fixed',
            right: 0,
            top: 0,
            width: '500px',
            height: '100vh',
            backgroundColor: 'rgba(17, 24, 39, 0.95)',
            backdropFilter: 'blur(12px)',
            borderLeft: '1px solid #374151',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
            zIndex: 1000
          }}>
            {/* Panel Header */}
            <div style={{
              padding: '20px',
              borderBottom: '1px solid #374151',
              background: 'linear-gradient(135deg, #1f2937 0%, #111827 100%)'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start' }}>
                <div>
                  <h2 style={{ fontSize: '18px', fontWeight: 'bold', color: '#fbbf24', margin: '0 0 8px 0' }}>
                    {selectedPackage.name}
                  </h2>
                  <div style={{ fontSize: '12px', color: '#9ca3af' }}>
                    {selectedPackage.description}
                  </div>
                  <div style={{ fontSize: '12px', color: '#10b981', marginTop: '8px' }}>
                    {selectedPackage.classCount} classes • Production ready
                  </div>
                </div>
                <button
                  onClick={() => setSelectedPackage(null)}
                  style={{
                    background: 'transparent',
                    border: 'none',
                    color: '#9ca3af',
                    fontSize: '24px',
                    cursor: 'pointer',
                    padding: 0,
                    lineHeight: 1
                  }}
                >
                  ×
                </button>
              </div>
            </div>

            {/* Tabs */}
            <div style={{ display: 'flex', borderBottom: '1px solid #374151' }}>
              {['classes', 'interfaces', 'services', 'config'].map(tab => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  style={{
                    flex: 1,
                    padding: '12px',
                    background: activeTab === tab ? '#1f2937' : 'transparent',
                    border: 'none',
                    color: activeTab === tab ? '#fbbf24' : '#9ca3af',
                    fontSize: '12px',
                    fontWeight: '600',
                    textTransform: 'capitalize',
                    cursor: 'pointer'
                  }}
                >
                  {tab}
                </button>
              ))}
            </div>

            {/* Tab Content */}
            <div style={{ flex: 1, padding: '20px', overflowY: 'auto' }}>
              {activeTab === 'classes' && (
                <div>
                  <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                    Core Classes
                  </h3>
                  <ul style={{ margin: 0, paddingLeft: '20px', lineHeight: '1.6' }}>
                    {getPackageDetails(selectedPackage.id).classes.map((cls, idx) => (
                      <li key={idx} style={{ color: '#d1d5db', fontSize: '13px', marginBottom: '8px' }}>
                        {cls}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {activeTab === 'interfaces' && (
                <div>
                  <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                    Interfaces & Contracts
                  </h3>
                  <ul style={{ margin: 0, paddingLeft: '20px', lineHeight: '1.6' }}>
                    {getPackageDetails(selectedPackage.id).interfaces.map((intf, idx) => (
                      <li key={idx} style={{ color: '#d1d5db', fontSize: '13px', marginBottom: '8px' }}>
                        {intf}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {activeTab === 'services' && (
                <div>
                  <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                    Spring Services
                  </h3>
                  <ul style={{ margin: 0, paddingLeft: '20px', lineHeight: '1.6' }}>
                    {getPackageDetails(selectedPackage.id).services.map((service, idx) => (
                      <li key={idx} style={{ color: '#d1d5db', fontSize: '13px', marginBottom: '8px' }}>
                        {service}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {activeTab === 'config' && (
                <div>
                  <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                    Configuration
                  </h3>
                  <pre style={{
                    color: '#d1d5db',
                    fontSize: '12px',
                    whiteSpace: 'pre-wrap',
                    fontFamily: 'monospace',
                    background: '#0f172a',
                    padding: '16px',
                    borderRadius: '8px',
                    lineHeight: '1.5',
                    marginBottom: '20px'
                  }}>
                    {getPackageDetails(selectedPackage.id).configuration}
                  </pre>
                  
                  <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px', marginTop: '24px' }}>
                    Dependencies
                  </h3>
                  <pre style={{
                    color: '#d1d5db',
                    fontSize: '12px',
                    whiteSpace: 'pre-wrap',
                    fontFamily: 'monospace',
                    background: '#0f172a',
                    padding: '16px',
                    borderRadius: '8px',
                    lineHeight: '1.5'
                  }}>
                    {getPackageDetails(selectedPackage.id).dependencies}
                  </pre>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default JavaImplementation;
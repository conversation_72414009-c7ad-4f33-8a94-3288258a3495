import React, { useState, useRef } from 'react';

const ElevatorSystemOOP = () => {
  const [selectedConcept, setSelectedConcept] = useState('Abstraction');
  const [selectedComponent, setSelectedComponent] = useState('ElevatorController');
  const [draggedComponent, setDraggedComponent] = useState(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const svgRef = useRef();

  const [componentPositions, setComponentPositions] = useState({
    ElevatorController: { x: 80, y: 50 },
    Elevator: { x: 350, y: 50 },
    Floor: { x: 620, y: 50 },
    Request: { x: 890, y: 50 },
    ElevatorCar: { x: 80, y: 200 },
    PassengerElevator: { x: 350, y: 200 },
    FreightElevator: { x: 620, y: 200 },
    Direction: { x: 890, y: 200 },
    ElevatorState: { x: 80, y: 350 },
    Door: { x: 350, y: 350 },
    Button: { x: 620, y: 350 },
    Display: { x: 890, y: 350 },
    Sensor: { x: 80, y: 500 },
    MaintenanceSystem: { x: 350, y: 500 },
    EmergencySystem: { x: 620, y: 500 }
  });

  const oopConcepts = [
    {
      id: 'Abstraction',
      title: 'Abstraction',
      subtitle: 'Abstract classes and interfaces define contracts',
      color: '#3b82f6'
    },
    {
      id: 'Encapsulation',
      title: 'Encapsulation',
      subtitle: 'Data hiding with controlled access',
      color: '#10b981'
    },
    {
      id: 'Inheritance',
      title: 'Inheritance',
      subtitle: 'Class hierarchy and code reuse',
      color: '#f59e0b'
    },
    {
      id: 'Polymorphism',
      title: 'Polymorphism',
      subtitle: 'Multiple forms of behavior',
      color: '#ef4444'
    }
  ];

  const umlComponents = [
    {
      id: 'ElevatorController',
      name: 'ElevatorController',
      type: 'class',
      stereotype: 'Controller',
      attributes: [
        '- elevators: List<Elevator>',
        '- floors: List<Floor>',
        '- requestQueue: PriorityQueue<Request>',
        '- isActive: boolean'
      ],
      methods: [
        '+ processRequest(request: Request): void',
        '+ assignElevator(request: Request): Elevator',
        '+ optimizeRouting(): void',
        '+ getStatus(): SystemStatus'
      ],
      concepts: ['Abstraction', 'Encapsulation'],
      color: '#3b82f6'
    },
    {
      id: 'Elevator',
      name: 'Elevator',
      type: 'abstract',
      stereotype: 'Abstract',
      attributes: [
        '# id: int',
        '# currentFloor: int',
        '# direction: Direction',
        '# state: ElevatorState',
        '# capacity: int'
      ],
      methods: [
        '+ moveUp(): void',
        '+ moveDown(): void',
        '+ openDoor(): void',
        '+ closeDoor(): void',
        '+ abstract loadPassengers(): void'
      ],
      concepts: ['Abstraction', 'Inheritance'],
      color: '#8b5cf6'
    },
    {
      id: 'Floor',
      name: 'Floor',
      type: 'class',
      stereotype: 'Entity',
      attributes: [
        '- floorNumber: int',
        '- upButton: Button',
        '- downButton: Button',
        '- display: Display',
        '- waitingRequests: List<Request>'
      ],
      methods: [
        '+ pressUpButton(): void',
        '+ pressDownButton(): void',
        '+ addWaitingRequest(request: Request): void',
        '+ removeWaitingRequest(request: Request): void'
      ],
      concepts: ['Encapsulation', 'Abstraction'],
      color: '#10b981'
    },
    {
      id: 'Request',
      name: 'Request',
      type: 'class',
      stereotype: 'Value Object',
      attributes: [
        '- sourceFloor: int',
        '- destinationFloor: int',
        '- direction: Direction',
        '- timestamp: LocalDateTime',
        '- priority: int'
      ],
      methods: [
        '+ getDirection(): Direction',
        '+ getPriority(): int',
        '+ getWaitTime(): Duration',
        '+ equals(other: Object): boolean'
      ],
      concepts: ['Encapsulation', 'Abstraction'],
      color: '#f59e0b'
    },
    {
      id: 'ElevatorCar',
      name: 'ElevatorCar',
      type: 'class',
      stereotype: 'Component',
      attributes: [
        '- doors: Door',
        '- buttons: List<Button>',
        '- display: Display',
        '- weightSensor: Sensor',
        '- currentLoad: int'
      ],
      methods: [
        '+ openDoors(): void',
        '+ closeDoors(): void',
        '+ selectFloor(floor: int): void',
        '+ getWeight(): int'
      ],
      concepts: ['Encapsulation', 'Inheritance'],
      color: '#06b6d4'
    },
    {
      id: 'PassengerElevator',
      name: 'PassengerElevator',
      type: 'class',
      stereotype: 'Concrete',
      attributes: [
        '- maxPassengers: int',
        '- currentPassengers: int',
        '- musicSystem: AudioSystem'
      ],
      methods: [
        '+ loadPassengers(): void',
        '+ unloadPassengers(): void',
        '+ playMusic(): void',
        '+ announceFloor(): void'
      ],
      concepts: ['Inheritance', 'Polymorphism'],
      color: '#84cc16'
    },
    {
      id: 'FreightElevator',
      name: 'FreightElevator',
      type: 'class',
      stereotype: 'Concrete',
      attributes: [
        '- maxWeight: double',
        '- currentWeight: double',
        '- loadingTime: int'
      ],
      methods: [
        '+ loadPassengers(): void',
        '+ loadCargo(weight: double): void',
        '+ checkWeightLimit(): boolean',
        '+ extendLoadingTime(): void'
      ],
      concepts: ['Inheritance', 'Polymorphism'],
      color: '#f97316'
    },
    {
      id: 'Direction',
      name: 'Direction',
      type: 'enum',
      stereotype: 'Enum',
      attributes: [
        'UP',
        'DOWN',
        'IDLE'
      ],
      methods: [
        '+ opposite(): Direction',
        '+ isMoving(): boolean'
      ],
      concepts: ['Abstraction'],
      color: '#ec4899'
    },
    {
      id: 'ElevatorState',
      name: 'ElevatorState',
      type: 'enum',
      stereotype: 'Enum',
      attributes: [
        'IDLE',
        'MOVING_UP',
        'MOVING_DOWN',
        'DOORS_OPENING',
        'DOORS_CLOSING',
        'OUT_OF_SERVICE'
      ],
      methods: [
        '+ canAcceptRequest(): boolean',
        '+ isOperational(): boolean'
      ],
      concepts: ['Abstraction'],
      color: '#8b5cf6'
    },
    {
      id: 'Door',
      name: 'Door',
      type: 'class',
      stereotype: 'Component',
      attributes: [
        '- isOpen: boolean',
        '- sensor: Sensor',
        '- motor: Motor'
      ],
      methods: [
        '+ open(): void',
        '+ close(): void',
        '+ isObstructed(): boolean',
        '+ forceClose(): void'
      ],
      concepts: ['Encapsulation', 'Abstraction'],
      color: '#10b981'
    },
    {
      id: 'Button',
      name: 'Button',
      type: 'class',
      stereotype: 'UI Component',
      attributes: [
        '- floor: int',
        '- isPressed: boolean',
        '- light: LED'
      ],
      methods: [
        '+ press(): void',
        '+ illuminate(): void',
        '+ turnOff(): void',
        '+ reset(): void'
      ],
      concepts: ['Encapsulation'],
      color: '#f59e0b'
    },
    {
      id: 'Display',
      name: 'Display',
      type: 'class',
      stereotype: 'UI Component',
      attributes: [
        '- currentFloor: int',
        '- direction: Direction',
        '- message: String'
      ],
      methods: [
        '+ showFloor(floor: int): void',
        '+ showDirection(dir: Direction): void',
        '+ showMessage(msg: String): void',
        '+ clear(): void'
      ],
      concepts: ['Encapsulation'],
      color: '#06b6d4'
    },
    {
      id: 'Sensor',
      name: 'Sensor',
      type: 'interface',
      stereotype: 'Interface',
      attributes: [],
      methods: [
        '+ detect(): boolean',
        '+ calibrate(): void',
        '+ isActive(): boolean'
      ],
      concepts: ['Abstraction', 'Polymorphism'],
      color: '#ef4444'
    },
    {
      id: 'MaintenanceSystem',
      name: 'MaintenanceSystem',
      type: 'class',
      stereotype: 'Service',
      attributes: [
        '- schedule: MaintenanceSchedule',
        '- logs: List<MaintenanceLog>',
        '- technicians: List<Technician>'
      ],
      methods: [
        '+ scheduleMaintenance(): void',
        '+ performMaintenance(): void',
        '+ generateReport(): Report',
        '+ notifyTechnician(): void'
      ],
      concepts: ['Encapsulation', 'Abstraction'],
      color: '#64748b'
    },
    {
      id: 'EmergencySystem',
      name: 'EmergencySystem',
      type: 'class',
      stereotype: 'Safety',
      attributes: [
        '- alarmActive: boolean',
        '- emergencyPhone: Phone',
        '- backupPower: PowerSystem'
      ],
      methods: [
        '+ activateAlarm(): void',
        '+ callEmergency(): void',
        '+ switchToBackupPower(): void',
        '+ evacuateElevator(): void'
      ],
      concepts: ['Encapsulation', 'Abstraction'],
      color: '#dc2626'
    }
  ];

  const relationships = [
    { from: 'ElevatorController', to: 'Elevator', type: 'composition', label: 'manages' },
    { from: 'ElevatorController', to: 'Floor', type: 'composition', label: 'monitors' },
    { from: 'ElevatorController', to: 'Request', type: 'dependency', label: 'processes' },
    { from: 'Elevator', to: 'ElevatorCar', type: 'composition', label: 'contains' },
    { from: 'Elevator', to: 'Direction', type: 'dependency', label: 'uses' },
    { from: 'Elevator', to: 'ElevatorState', type: 'dependency', label: 'uses' },
    { from: 'PassengerElevator', to: 'Elevator', type: 'inheritance', label: 'extends' },
    { from: 'FreightElevator', to: 'Elevator', type: 'inheritance', label: 'extends' },
    { from: 'ElevatorCar', to: 'Door', type: 'composition', label: 'has' },
    { from: 'ElevatorCar', to: 'Button', type: 'composition', label: 'contains' },
    { from: 'ElevatorCar', to: 'Display', type: 'composition', label: 'has' },
    { from: 'Floor', to: 'Button', type: 'composition', label: 'has' },
    { from: 'Floor', to: 'Display', type: 'composition', label: 'has' },
    { from: 'Door', to: 'Sensor', type: 'implementation', label: 'uses' },
    { from: 'ElevatorCar', to: 'Sensor', type: 'composition', label: 'has' },
    { from: 'MaintenanceSystem', to: 'Elevator', type: 'dependency', label: 'maintains' },
    { from: 'EmergencySystem', to: 'Elevator', type: 'dependency', label: 'protects' }
  ];

  const getClassPosition = (componentId) => {
    return componentPositions[componentId] || { x: 100, y: 100 };
  };

  const isClassHighlighted = (componentId) => {
    const component = umlComponents.find(c => c.id === componentId);
    return component && component.concepts.includes(selectedConcept);
  };

  const handleMouseDown = (e, componentId) => {
    e.preventDefault();
    const svg = svgRef.current;
    const pt = svg.createSVGPoint();
    pt.x = e.clientX;
    pt.y = e.clientY;
    const svgP = pt.matrixTransform(svg.getScreenCTM().inverse());
    
    const componentPos = getClassPosition(componentId);
    setDragOffset({
      x: svgP.x - componentPos.x,
      y: svgP.y - componentPos.y
    });
    setDraggedComponent(componentId);
  };

  const handleMouseMove = (e) => {
    if (!draggedComponent) return;
    
    const svg = svgRef.current;
    const pt = svg.createSVGPoint();
    pt.x = e.clientX;
    pt.y = e.clientY;
    const svgP = pt.matrixTransform(svg.getScreenCTM().inverse());
    
    setComponentPositions(prev => ({
      ...prev,
      [draggedComponent]: {
        x: Math.max(0, Math.min(960, svgP.x - dragOffset.x)),
        y: Math.max(0, Math.min(580, svgP.y - dragOffset.y))
      }
    }));
  };

  const handleMouseUp = () => {
    setDraggedComponent(null);
  };

  const renderArrow = (relationship) => {
    const fromPos = getClassPosition(relationship.from);
    const toPos = getClassPosition(relationship.to);
    
    const fromCenterX = fromPos.x + 120;
    const fromCenterY = fromPos.y + 60;
    const toCenterX = toPos.x + 120;
    const toCenterY = toPos.y + 60;
    
    const dx = toCenterX - fromCenterX;
    const dy = toCenterY - fromCenterY;
    const distance = Math.sqrt(dx * dx + dy * dy);
    
    if (distance < 10) return null;
    
    const unitX = dx / distance;
    const unitY = dy / distance;
    
    const startX = fromCenterX + unitX * 60;
    const startY = fromCenterY + unitY * 40;
    const endX = toCenterX - unitX * 60;
    const endY = toCenterY - unitY * 40;
    
    const arrowSize = 8;
    const arrowX1 = endX - (unitX * arrowSize + unitY * arrowSize);
    const arrowY1 = endY - (unitY * arrowSize - unitX * arrowSize);
    const arrowX2 = endX - (unitX * arrowSize - unitY * arrowSize);
    const arrowY2 = endY - (unitY * arrowSize + unitX * arrowSize);

    let strokeStyle = { strokeDasharray: 'none' };
    let arrowColor = '#6b7280';
    
    if (relationship.type === 'inheritance') {
      arrowColor = '#f59e0b';
    } else if (relationship.type === 'implementation') {
      strokeStyle = { strokeDasharray: '5,5' };
      arrowColor = '#ef4444';
    } else if (relationship.type === 'composition') {
      arrowColor = '#10b981';
    }

    return (
      <g key={`${relationship.from}-${relationship.to}`}>
        <line
          x1={startX}
          y1={startY}
          x2={endX}
          y2={endY}
          stroke={arrowColor}
          strokeWidth="1.5"
          {...strokeStyle}
        />
        <polygon
          points={`${endX},${endY} ${arrowX1},${arrowY1} ${arrowX2},${arrowY2}`}
          fill={arrowColor}
        />
      </g>
    );
  };

  const renderUMLClass = (component) => {
    const position = getClassPosition(component.id);
    const isSelected = selectedComponent === component.id;
    const isHighlighted = isClassHighlighted(component.id);
    const selectedConceptData = oopConcepts.find(c => c.id === selectedConcept);
    
    const strokeColor = isHighlighted ? selectedConceptData?.color : '#374151';
    const fillColor = isHighlighted ? `${selectedConceptData?.color}22` : '#1e293b';
    
    return (
      <g
        key={component.id}
        transform={`translate(${position.x}, ${position.y})`}
        onMouseDown={(e) => handleMouseDown(e, component.id)}
        onClick={() => setSelectedComponent(component.id)}
        style={{ cursor: 'pointer' }}
      >
        <rect
          width="240"
          height="120"
          fill={isSelected ? 'rgba(59, 130, 246, 0.1)' : fillColor}
          stroke={isSelected ? '#3b82f6' : strokeColor}
          strokeWidth={isSelected ? "3" : "2"}
          rx="6"
          className="transition-all duration-200"
        />
        
        <text
          x="120"
          y="20"
          textAnchor="middle"
          className="text-sm font-bold"
          fill="#e2e8f0"
        >
          &lt;&lt;{component.stereotype}&gt;&gt;
        </text>
        
        <text
          x="120"
          y="38"
          textAnchor="middle"
          className="text-base font-bold"
          fill={strokeColor}
        >
          {component.name}
        </text>
        
        <line
          x1="8"
          y1="45"
          x2="232"
          y2="45"
          stroke={strokeColor}
          strokeWidth="1"
        />
        
        {component.attributes.slice(0, 2).map((attr, index) => (
          <text
            key={index}
            x="12"
            y={58 + index * 12}
            className="text-xs"
            fill="#94a3b8"
          >
            {attr}
          </text>
        ))}
        
        <line
          x1="8"
          y1="82"
          x2="232"
          y2="82"
          stroke={strokeColor}
          strokeWidth="1"
        />
        
        {component.methods.slice(0, 2).map((method, index) => (
          <text
            key={index}
            x="12"
            y={95 + index * 12}
            className="text-xs"
            fill="#94a3b8"
          >
            {method}
          </text>
        ))}
      </g>
    );
  };

  const conceptInformation = {
    'Abstraction': {
      title: 'Abstraction in Elevator System',
      description: 'Abstract classes and interfaces define common contracts and hide implementation details.',
      examples: [
        {
          title: 'Abstract Elevator Class',
          code: `public abstract class Elevator {
    protected int id;
    protected int currentFloor;
    protected Direction direction;
    protected ElevatorState state;
    
    public abstract void loadPassengers();
    
    public void moveUp() {
        if (canMoveUp()) {
            currentFloor++;
            direction = Direction.UP;
        }
    }
    
    public void moveDown() {
        if (canMoveDown()) {
            currentFloor--;
            direction = Direction.DOWN;
        }
    }
    
    protected abstract boolean canMoveUp();
    protected abstract boolean canMoveDown();
}`
        },
        {
          title: 'Sensor Interface',
          code: `public interface Sensor {
    boolean detect();
    void calibrate();
    boolean isActive();
    
    // Default method for common functionality
    default void performSelfTest() {
        calibrate();
        if (!isActive()) {
            throw new SensorException("Sensor self-test failed");
        }
    }
}`
        }
      ]
    },
    'Encapsulation': {
      title: 'Encapsulation in Elevator System',
      description: 'Data hiding and controlled access through public interfaces while keeping internal state private.',
      examples: [
        {
          title: 'ElevatorController Class',
          code: `public class ElevatorController {
    private List<Elevator> elevators;
    private List<Floor> floors;
    private PriorityQueue<Request> requestQueue;
    private boolean isActive;
    
    public ElevatorController(int numElevators, int numFloors) {
        this.elevators = new ArrayList<>();
        this.floors = new ArrayList<>();
        this.requestQueue = new PriorityQueue<>();
        this.isActive = true;
        initializeSystem(numElevators, numFloors);
    }
    
    public void processRequest(Request request) {
        if (!isValidRequest(request)) {
            throw new IllegalArgumentException("Invalid request");
        }
        
        requestQueue.offer(request);
        assignOptimalElevator(request);
    }
    
    private void assignOptimalElevator(Request request) {
        Elevator bestElevator = findNearestElevator(request);
        if (bestElevator != null) {
            bestElevator.addDestination(request.getDestinationFloor());
        }
    }
    
    private boolean isValidRequest(Request request) {
        return request.getSourceFloor() >= 0 && 
               request.getSourceFloor() < floors.size() &&
               request.getDestinationFloor() >= 0 && 
               request.getDestinationFloor() < floors.size();
    }
}`
        },
        {
          title: 'Floor Class with Controlled Access',
          code: `public class Floor {
    private final int floorNumber;
    private final Button upButton;
    private final Button downButton;
    private final Display display;
    private final List<Request> waitingRequests;
    
    public Floor(int floorNumber) {
        this.floorNumber = floorNumber;
        this.upButton = new Button(ButtonType.UP);
        this.downButton = new Button(ButtonType.DOWN);
        this.display = new Display();
        this.waitingRequests = new ArrayList<>();
    }
    
    public void pressUpButton() {
        if (upButton.isEnabled()) {
            upButton.press();
            createRequest(Direction.UP);
        }
    }
    
    public void pressDownButton() {
        if (downButton.isEnabled()) {
            downButton.press();
            createRequest(Direction.DOWN);
        }
    }
    
    // Controlled access - only package-visible
    List<Request> getWaitingRequests() {
        return new ArrayList<>(waitingRequests); // Defensive copy
    }
    
    private void createRequest(Direction direction) {
        Request request = new Request(floorNumber, direction);
        waitingRequests.add(request);
        notifyElevatorController(request);
    }
}`
        }
      ]
    },
    'Inheritance': {
      title: 'Inheritance in Elevator System',
      description: 'Class hierarchy allows code reuse and specialization of elevator types.',
      examples: [
        {
          title: 'Elevator Class Hierarchy',
          code: `// Base abstract class
public abstract class Elevator {
    protected int id;
    protected int currentFloor;
    protected Direction direction;
    protected ElevatorState state;
    protected int capacity;
    
    protected Elevator(int id, int capacity) {
        this.id = id;
        this.capacity = capacity;
        this.currentFloor = 1;
        this.direction = Direction.IDLE;
        this.state = ElevatorState.IDLE;
    }
    
    public abstract void loadPassengers();
    public abstract int getMaxCapacity();
    
    // Common functionality
    public void moveUp() {
        if (currentFloor < getMaxFloor()) {
            currentFloor++;
            direction = Direction.UP;
            state = ElevatorState.MOVING_UP;
        }
    }
    
    public void openDoor() {
        state = ElevatorState.DOORS_OPENING;
        // Common door opening logic
    }
}

// Specialized passenger elevator
public class PassengerElevator extends Elevator {
    private int maxPassengers;
    private int currentPassengers;
    private AudioSystem musicSystem;
    
    public PassengerElevator(int id) {
        super(id, 2000); // 2000 kg capacity
        this.maxPassengers = 15;
        this.currentPassengers = 0;
        this.musicSystem = new AudioSystem();
    }
    
    @Override
    public void loadPassengers() {
        // Passenger-specific loading logic
        if (currentPassengers < maxPassengers) {
            announceFloor();
            playBackgroundMusic();
        }
    }
    
    @Override
    public int getMaxCapacity() {
        return maxPassengers;
    }
    
    private void announceFloor() {
        System.out.println("Floor " + currentFloor);
    }
    
    private void playBackgroundMusic() {
        musicSystem.playTrack("elevator_music.mp3");
    }
}

// Specialized freight elevator
public class FreightElevator extends Elevator {
    private double maxWeight;
    private double currentWeight;
    private int extendedLoadingTime;
    
    public FreightElevator(int id) {
        super(id, 5000); // 5000 kg capacity
        this.maxWeight = 5000.0;
        this.currentWeight = 0.0;
        this.extendedLoadingTime = 30; // seconds
    }
    
    @Override
    public void loadPassengers() {
        // Freight-specific loading logic
        extendDoorOpenTime();
        checkWeightDistribution();
    }
    
    @Override
    public int getMaxCapacity() {
        return (int) maxWeight;
    }
    
    public boolean loadCargo(double weight) {
        if (currentWeight + weight <= maxWeight) {
            currentWeight += weight;
            return true;
        }
        return false;
    }
    
    private void extendDoorOpenTime() {
        // Keep doors open longer for loading
        try {
            Thread.sleep(extendedLoadingTime * 1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}`
        }
      ]
    },
    'Polymorphism': {
      title: 'Polymorphism in Elevator System',
      description: 'Multiple forms of behavior through method overriding and interface implementations.',
      examples: [
        {
          title: 'Polymorphic Elevator Behavior',
          code: `public class ElevatorSystem {
    private List<Elevator> elevators;
    
    public void initializeElevators() {
        elevators = Arrays.asList(
            new PassengerElevator(1),
            new PassengerElevator(2),
            new FreightElevator(3),
            new FreightElevator(4)
        );
    }
    
    // Polymorphic method - works with any Elevator type
    public void operateElevators() {
        for (Elevator elevator : elevators) {
            // Each elevator type implements loadPassengers() differently
            elevator.loadPassengers();
            
            // Common interface, different implementations
            System.out.println("Elevator " + elevator.getId() + 
                             " capacity: " + elevator.getMaxCapacity());
            
            // Polymorphic behavior based on actual type
            if (elevator instanceof PassengerElevator) {
                ((PassengerElevator) elevator).playBackgroundMusic();
            } else if (elevator instanceof FreightElevator) {
                ((FreightElevator) elevator).loadCargo(1000.0);
            }
        }
    }
}

// Interface polymorphism
public interface Sensor {
    boolean detect();
    void calibrate();
}

public class WeightSensor implements Sensor {
    private double currentWeight;
    
    @Override
    public boolean detect() {
        return currentWeight > 0;
    }
    
    @Override
    public void calibrate() {
        currentWeight = 0.0;
        System.out.println("Weight sensor calibrated");
    }
    
    public double getWeight() {
        return currentWeight;
    }
}

public class MotionSensor implements Sensor {
    private boolean motionDetected;
    
    @Override
    public boolean detect() {
        return motionDetected;
    }
    
    @Override
    public void calibrate() {
        motionDetected = false;
        System.out.println("Motion sensor calibrated");
    }
}

// Polymorphic sensor usage
public class Door {
    private List<Sensor> sensors;
    
    public Door() {
        sensors = Arrays.asList(
            new WeightSensor(),
            new MotionSensor()
        );
    }
    
    public boolean canClose() {
        // Polymorphic method calls
        for (Sensor sensor : sensors) {
            if (sensor.detect()) {
                return false; // Something blocking the door
            }
        }
        return true;
    }
    
    public void calibrateAllSensors() {
        sensors.forEach(Sensor::calibrate); // Method reference polymorphism
    }
}`
        }
      ]
    }
  };

  const selectedInfo = conceptInformation[selectedConcept];
  const selectedComponentInfo = umlComponents.find(comp => comp.id === selectedComponent);

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: '#0f172a',
      color: 'white',
      fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif'
    }}>
      <div style={{
        display: 'flex',
        height: '60px',
        backgroundColor: 'rgba(30, 41, 59, 0.95)',
        borderBottom: '1px solid #334155',
        alignItems: 'center',
        gap: '2px',
        padding: '0 20px'
      }}>
        {oopConcepts.map((concept) => (
          <button
            key={concept.id}
            onClick={() => setSelectedConcept(concept.id)}
            style={{
              flex: 1,
              height: '44px',
              backgroundColor: selectedConcept === concept.id ? concept.color : 'transparent',
              border: `2px solid ${concept.color}`,
              borderRadius: '6px',
              color: selectedConcept === concept.id ? 'white' : concept.color,
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '2px',
              margin: '0 1px'
            }}
          >
            <span style={{ fontSize: '13px', fontWeight: '600' }}>
              {concept.title}
            </span>
            <span style={{ fontSize: '10px', opacity: 0.8 }}>
              {concept.subtitle}
            </span>
          </button>
        ))}
      </div>

      <div style={{ display: 'flex', height: 'calc(100vh - 60px)' }}>
        <div style={{ width: '60%', borderRight: '1px solid #334155' }}>
          <div style={{
            height: '50px',
            backgroundColor: 'rgba(51, 65, 81, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderBottom: '1px solid #334155'
          }}>
            <h2 style={{
              margin: 0,
              fontSize: '18px',
              fontWeight: '600',
              color: '#10b981'
            }}>
              Elevator System UML Class Diagram
            </h2>
          </div>
          
          <div style={{ height: 'calc(100% - 50px)', padding: '20px', overflow: 'auto' }}>
            <svg
              ref={svgRef}
              width="900" 
              height="600"
              viewBox="0 0 1200 700"
              style={{ 
                backgroundColor: '#0f172a', 
                borderRadius: '8px',
                border: '1px solid #374151',
                width: '100%',
                cursor: draggedComponent ? 'grabbing' : 'default'
              }}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
            >
              {relationships.map(renderArrow)}
              {umlComponents.map(renderUMLClass)}
            </svg>
          </div>
        </div>

        <div style={{ width: '40%' }}>
          <div style={{
            height: '50px',
            backgroundColor: 'rgba(51, 65, 81, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderBottom: '1px solid #334155'
          }}>
            <h2 style={{
              margin: 0,
              fontSize: '18px',
              fontWeight: '600',
              color: '#10b981'
            }}>
              {selectedInfo.title}
            </h2>
          </div>
          
          <div style={{ 
            height: 'calc(100% - 50px)', 
            overflowY: 'auto',
            padding: '20px'
          }}>
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              borderRadius: '8px',
              padding: '16px',
              marginBottom: '20px',
              border: '1px solid #334155'
            }}>
              <p style={{ 
                color: '#cbd5e1', 
                lineHeight: '1.6',
                margin: '0 0 16px 0'
              }}>
                {selectedInfo.description}
              </p>
              
              {selectedComponentInfo && (
                <div style={{ 
                  padding: '12px',
                  backgroundColor: 'rgba(59, 130, 246, 0.1)',
                  borderRadius: '6px',
                  border: '1px solid #3b82f6',
                  marginTop: '16px'
                }}>
                  <h4 style={{ 
                    color: '#3b82f6', 
                    margin: '0 0 8px 0',
                    fontSize: '14px',
                    fontWeight: '600'
                  }}>
                    Selected: {selectedComponentInfo.name}
                  </h4>
                  <p style={{ 
                    color: '#94a3b8', 
                    fontSize: '13px',
                    margin: 0,
                    lineHeight: '1.4'
                  }}>
                    Demonstrates: {selectedComponentInfo.concepts.join(', ')}
                  </p>
                </div>
              )}
            </div>

            {selectedInfo.examples.map((example, index) => (
              <div key={index} style={{
                marginBottom: '24px',
                backgroundColor: 'rgba(17, 24, 39, 0.8)',
                borderRadius: '8px',
                overflow: 'hidden',
                border: '1px solid #374151'
              }}>
                <div style={{
                  backgroundColor: 'rgba(55, 65, 81, 0.8)',
                  padding: '12px 16px',
                  borderBottom: '1px solid #4b5563'
                }}>
                  <h4 style={{
                    margin: 0,
                    color: '#f3f4f6',
                    fontSize: '14px',
                    fontWeight: '600'
                  }}>
                    {example.title}
                  </h4>
                </div>
                <pre style={{
                  margin: 0,
                  padding: '16px',
                  fontSize: '11px',
                  lineHeight: '1.4',
                  color: '#e2e8f0',
                  backgroundColor: 'rgba(15, 23, 42, 0.8)',
                  overflow: 'auto',
                  fontFamily: 'Monaco, Consolas, "Liberation Mono", "Courier New", monospace'
                }}>
                  <code>{example.code}</code>
                </pre>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ElevatorSystemOOP;
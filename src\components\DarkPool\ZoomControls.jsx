import React from 'react';
import { ZoomIn, ZoomOut, Maximize } from 'lucide-react';

const ZoomControls = ({ zoomLevel, onZoom, onReset }) => {
  return (
    <div className="absolute left-4 top-24 z-20 bg-gray-900 bg-opacity-90 backdrop-blur-sm rounded-lg p-2 space-y-2">
      <button
        onClick={() => onZoom(0.1)}
        className="block p-2 text-white hover:bg-gray-800 rounded transition-colors"
        title="Zoom In"
      >
        <ZoomIn size={20} />
      </button>
      <div className="text-center text-white text-xs py-1">
        {Math.round(zoomLevel * 100)}%
      </div>
      <button
        onClick={() => onZoom(-0.1)}
        className="block p-2 text-white hover:bg-gray-800 rounded transition-colors"
        title="Zoom Out"
      >
        <ZoomOut size={20} />
      </button>
      <button
        onClick={onReset}
        className="block p-2 text-white hover:bg-gray-800 rounded transition-colors"
        title="Reset View"
      >
        <Maximize size={20} />
      </button>
    </div>
  );
};

export default ZoomControls;
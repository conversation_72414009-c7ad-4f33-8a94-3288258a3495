@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 10px rgba(79, 209, 197, 0.5);
  }
  50% {
    box-shadow: 0 0 30px rgba(79, 209, 197, 0.8);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-glow {
  animation: glow 3s ease-in-out infinite;
}
import React, { useState } from 'react';
import { Layers, Activity, TrendingUp, HardDrive, Zap, BarChart3, Cpu, Shield, AlertTriangle } from 'lucide-react';

const JavaCollections = () => {
  const [selectedCategory, setSelectedCategory] = useState('overview');

  const collectionCategories = [
    {
      id: 'overview',
      name: 'Collections Overview',
      icon: <Layers size={20} />,
      color: '#3b82f6',
      content: {
        hierarchy: `Collection Framework Hierarchy:
        
Collection (Interface)
├── List (Interface)
│   ├── ArrayList - Dynamic array, O(1) access
│   ├── LinkedList - Doubly-linked, O(n) access
│   ├── Vector - Synchronized ArrayList
│   └── Stack - LIFO, extends Vector
├── Set (Interface)
│   ├── HashSet - O(1) ops, no order
│   ├── LinkedHashSet - Maintains insertion order
│   └── TreeSet - Sorted, O(log n) ops
├── Queue (Interface)
│   ├── PriorityQueue - Heap-based priority
│   ├── LinkedList - Also implements Queue
│   └── ArrayDeque - Resizable array deque
└── Map (Interface)
    ├── HashMap - O(1) ops, no order
    ├── LinkedHashMap - Maintains insertion order
    ├── TreeMap - Sorted, O(log n) ops
    └── ConcurrentHashMap - Thread-safe`,
        
        bigO: {
          'ArrayList': { 
            access: 'O(1)', 
            search: 'O(n)', 
            insertion: 'O(1) amortized', 
            deletion: 'O(n)',
            memory: 'O(n) contiguous'
          },
          'LinkedList': { 
            access: 'O(n)', 
            search: 'O(n)', 
            insertion: 'O(1)', 
            deletion: 'O(1)',
            memory: 'O(n) non-contiguous'
          },
          'HashMap': { 
            access: 'O(1)', 
            search: 'O(1)', 
            insertion: 'O(1)', 
            deletion: 'O(1)',
            memory: 'O(n) + overhead'
          },
          'TreeMap': { 
            access: 'O(log n)', 
            search: 'O(log n)', 
            insertion: 'O(log n)', 
            deletion: 'O(log n)',
            memory: 'O(n) tree nodes'
          }
        }
      }
    },
    {
      id: 'performance',
      name: 'Performance Characteristics',
      icon: <Activity size={20} />,
      color: '#ef4444',
      content: {
        title: 'Big O Complexity & Memory Analysis',
        details: [
          {
            name: 'ArrayList vs LinkedList',
            comparison: `
// ArrayList - Cache-friendly, contiguous memory
ArrayList<Integer> arrayList = new ArrayList<>();
// Pros: CPU cache locality, fast random access
// Cons: Expensive middle insertions/deletions
// Memory: ~4 bytes per element + ~10% capacity overhead

// LinkedList - Scattered memory, pointer overhead  
LinkedList<Integer> linkedList = new LinkedList<>();
// Pros: O(1) insertion/deletion at known positions
// Cons: Poor cache locality, ~24 bytes per element
// Memory: 3x more than ArrayList (prev/next pointers)

// Performance Test
@Benchmark
public void arrayListIteration() {
    int sum = 0;
    for (Integer val : arrayList) {
        sum += val; // Cache-friendly iteration
    }
}

@Benchmark
public void linkedListIteration() {
    int sum = 0;
    for (Integer val : linkedList) {
        sum += val; // Cache misses on each node
    }
}
// Result: ArrayList 10x faster for iteration`,
            benchmark: {
              'ArrayList Iteration': '12.5 ns/op',
              'LinkedList Iteration': '125.3 ns/op',
              'ArrayList Random Access': '2.1 ns/op',
              'LinkedList Random Access': '890.4 ns/op'
            }
          },
          {
            name: 'HashMap Optimization',
            comparison: `
// Initial capacity and load factor optimization
// Default: 16 buckets, 0.75 load factor
HashMap<String, Double> defaultMap = new HashMap<>();

// Optimized for 10000 elements
int expectedSize = 10000;
int capacity = (int) (expectedSize / 0.75f + 1);
HashMap<String, Double> optimizedMap = new HashMap<>(capacity);

// Prevents rehashing during insertion
// Rehashing is O(n) operation

// JDK 8+ uses tree bins for collisions
// When bucket has >8 elements: converts to red-black tree
// O(n) worst case becomes O(log n)

// Memory footprint per entry:
// 32 bytes (Node) + key size + value size
// Total: ~40-48 bytes for simple key-value`,
            benchmark: {
              'Default HashMap (10k inserts)': '8.2 ms',
              'Optimized HashMap (10k inserts)': '4.1 ms',
              'HashMap Get (no collision)': '15 ns/op',
              'HashMap Get (with collision)': '45 ns/op'
            }
          }
        ]
      }
    },
    {
      id: 'trading',
      name: 'Trading Collections',
      icon: <TrendingUp size={20} />,
      color: '#10b981',
      content: {
        title: 'Specialized Trading Collections',
        implementations: [
          {
            name: 'Chronicle Map (Off-Heap)',
            description: 'Memory-mapped, zero-copy, persisted',
            code: `// Chronicle Map - Off-heap storage for market data
import net.openhft.chronicle.map.ChronicleMap;

ChronicleMap<Long, MarketData> marketDataMap = ChronicleMap
    .of(Long.class, MarketData.class)
    .name("market-data-map")
    .entries(1_000_000)
    .averageValue(marketData)
    .createPersistedTo(new File("/tmp/market-data.dat"));

// Zero-copy, memory-mapped file
// Survives JVM crashes
// No GC overhead
// Latency: <1 microsecond`,
            features: [
              'Off-heap storage (no GC impact)',
              'Memory-mapped files',
              'Concurrent access without locks',
              'Persistence across restarts'
            ]
          },
          {
            name: 'LMAX Disruptor',
            description: 'Ultra-low latency ring buffer',
            code: `// LMAX Disruptor - 10M+ ops/sec
import com.lmax.disruptor.*;

public class OrderEventHandler implements EventHandler<OrderEvent> {
    public void onEvent(OrderEvent event, long sequence, boolean endOfBatch) {
        // Process order with ~50ns latency
        processOrder(event.order);
    }
}

// Ring buffer setup
int bufferSize = 1024 * 64; // Must be power of 2
Disruptor<OrderEvent> disruptor = new Disruptor<>(
    OrderEvent::new,
    bufferSize,
    DaemonThreadFactory.INSTANCE,
    ProducerType.MULTI,
    new YieldingWaitStrategy()
);

disruptor.handleEventsWith(new OrderEventHandler());
RingBuffer<OrderEvent> ringBuffer = disruptor.start();

// Publishing with memory barrier
long sequence = ringBuffer.next();
OrderEvent event = ringBuffer.get(sequence);
event.setOrder(order);
ringBuffer.publish(sequence);`,
            features: [
              'Lock-free multi-producer/consumer',
              'Cache-line padding (no false sharing)',
              'Pre-allocated memory',
              'Mechanical sympathy design'
            ]
          },
          {
            name: 'Koloboke Collections',
            description: 'Primitive specialization, no boxing',
            code: `// Koloboke - Primitive collections without boxing
import com.koloboke.collect.map.hash.*;

// Primitive long->double map (no boxing)
HashLongDoubleMap priceMap = HashLongDoubleMaps.newMutableMap();
priceMap.put(instrumentId, 100.25); // No Long/Double objects

// 3-5x less memory than HashMap<Long, Double>
// 2-3x faster operations

// Object pooling for order book
ObjIntMap<String, Integer> orderBook = HashObjIntMaps.newMutableMap();
orderBook.addValue("AAPL", 100); // Atomic add

// Comparison: HashMap uses 48 bytes per entry
// Koloboke uses 16 bytes per entry`,
            features: [
              'Zero boxing overhead',
              'Specialized primitive maps',
              '60-70% less memory usage',
              'Open addressing (better cache)'
            ]
          }
        ]
      }
    },
    {
      id: 'memory',
      name: 'Memory Optimization',
      icon: <HardDrive size={20} />,
      color: '#f59e0b',
      content: {
        title: 'Memory-Efficient Collections',
        strategies: [
          {
            name: 'Primitive Collections',
            code: `// Eclipse Collections - Primitive lists
import org.eclipse.collections.impl.list.mutable.primitive.*;

// Standard ArrayList<Integer> - 24 bytes per element
List<Integer> boxedList = new ArrayList<>();
boxedList.add(42); // Boxing: int -> Integer object

// Eclipse MutableIntList - 4 bytes per element
MutableIntList primitiveList = new IntArrayList();
primitiveList.add(42); // No boxing

// Memory comparison for 1M integers:
// ArrayList<Integer>: ~24 MB
// MutableIntList: ~4 MB
// 6x memory savings!

// FastUtil alternative
import it.unimi.dsi.fastutil.ints.*;
Int2ObjectOpenHashMap<Order> orderMap = new Int2ObjectOpenHashMap<>();
orderMap.put(orderId, order); // No Integer boxing`,
            savings: '60-85% memory reduction'
          },
          {
            name: 'Object Pooling',
            code: `// Object pool for frequently created objects
public class OrderPool {
    private final Queue<Order> pool = new ConcurrentLinkedQueue<>();
    private final int maxSize = 10000;
    
    public Order borrowOrder() {
        Order order = pool.poll();
        if (order == null) {
            order = new Order();
        }
        return order;
    }
    
    public void returnOrder(Order order) {
        if (pool.size() < maxSize) {
            order.reset(); // Clear state
            pool.offer(order);
        }
    }
}

// Usage - Zero allocation in hot path
OrderPool orderPool = new OrderPool();
Order order = orderPool.borrowOrder();
order.setSymbol("AAPL");
order.setQuantity(100);
processOrder(order);
orderPool.returnOrder(order);`,
            savings: 'Eliminates GC in hot path'
          },
          {
            name: 'Off-Heap Collections',
            code: `// Agrona DirectBuffer for off-heap storage
import org.agrona.concurrent.UnsafeBuffer;
import org.agrona.DirectBuffer;

// Allocate 1MB off-heap
ByteBuffer byteBuffer = ByteBuffer.allocateDirect(1024 * 1024);
UnsafeBuffer buffer = new UnsafeBuffer(byteBuffer);

// Zero-copy message handling
public void writeMarketData(DirectBuffer buffer, int offset) {
    buffer.putLong(offset, System.nanoTime());      // timestamp
    buffer.putInt(offset + 8, instrumentId);        // instrument
    buffer.putDouble(offset + 12, bidPrice);        // bid
    buffer.putDouble(offset + 20, askPrice);        // ask
    buffer.putInt(offset + 28, bidSize);           // bid size
    buffer.putInt(offset + 32, askSize);           // ask size
}

// Memory-mapped file for persistence
RandomAccessFile file = new RandomAccessFile("data.bin", "rw");
MappedByteBuffer mapped = file.getChannel().map(
    FileChannel.MapMode.READ_WRITE, 0, file.length()
);`,
            savings: 'No GC overhead, survives restarts'
          }
        ]
      }
    },
    {
      id: 'lowlatency',
      name: 'Low-Latency Patterns',
      icon: <Zap size={20} />,
      color: '#8b5cf6',
      content: {
        title: 'Lock-Free & Wait-Free Collections',
        patterns: [
          {
            name: 'Lock-Free Queue',
            code: `// ConcurrentLinkedQueue - Lock-free implementation
import java.util.concurrent.ConcurrentLinkedQueue;

public class LockFreeOrderQueue {
    private final ConcurrentLinkedQueue<Order> queue = new ConcurrentLinkedQueue<>();
    
    // Lock-free offer - uses CAS operations
    public void addOrder(Order order) {
        queue.offer(order); // Non-blocking
    }
    
    // Lock-free poll
    public Order getOrder() {
        return queue.poll(); // Non-blocking
    }
}

// Michael & Scott algorithm internally:
// Uses compare-and-swap (CAS) for atomic operations
// No locks, no blocking, no thread contention`,
            latency: '~50-100 nanoseconds'
          },
          {
            name: 'Single Writer Principle',
            code: `// Single writer, multiple readers pattern
public class SingleWriterOrderBook {
    // Padding to prevent false sharing
    private volatile long p1,p2,p3,p4,p5,p6,p7; // 64 bytes
    
    private volatile Order[] orders = new Order[1000];
    private volatile int writeIndex = 0;
    
    private volatile long p8,p9,p10,p11,p12,p13,p14; // 64 bytes
    
    // Only one thread writes (no synchronization needed)
    public void addOrder(Order order) {
        orders[writeIndex] = order;
        writeIndex = (writeIndex + 1) % orders.length;
    }
    
    // Multiple threads can read safely
    public Order readOrder(int index) {
        return orders[index]; // Volatile read
    }
}`,
            latency: '~10-20 nanoseconds'
          },
          {
            name: 'False Sharing Prevention',
            code: `// Padded atomic counter to prevent false sharing
@sun.misc.Contended // JDK 8+ annotation
public class PaddedCounter {
    private volatile long value = 0;
    
    public void increment() {
        value++;
    }
}

// Manual padding for JDK 7
public class ManuallyPaddedCounter {
    private volatile long p1,p2,p3,p4,p5,p6,p7; // 56 bytes
    private volatile long value = 0;            // 8 bytes  
    private volatile long p8,p9,p10,p11,p12,p13,p14; // 56 bytes
    // Total: 128 bytes (2 cache lines)
    
    public void increment() {
        value++;
    }
}

// Performance difference:
// Without padding: 15M ops/sec (false sharing)
// With padding: 150M ops/sec (10x faster!)`,
            latency: 'Prevents 10x slowdown'
          }
        ]
      }
    },
    {
      id: 'orderbook',
      name: 'Order Book Implementation',
      icon: <BarChart3 size={20} />,
      color: '#ec4899',
      content: {
        title: 'High-Performance Order Book',
        implementation: `// Production-grade order book implementation
import java.util.concurrent.ConcurrentSkipListMap;
import java.util.concurrent.atomic.AtomicLong;

public class OrderBook {
    // Price levels sorted by natural ordering
    private final ConcurrentSkipListMap<Double, PriceLevel> bids = 
        new ConcurrentSkipListMap<>((a, b) -> Double.compare(b, a)); // Reverse
    private final ConcurrentSkipListMap<Double, PriceLevel> asks = 
        new ConcurrentSkipListMap<>();
    
    // Price level contains all orders at that price
    private static class PriceLevel {
        private final double price;
        private final AtomicLong totalQuantity = new AtomicLong(0);
        private final ConcurrentLinkedQueue<Order> orders = new ConcurrentLinkedQueue<>();
        
        public void addOrder(Order order) {
            orders.offer(order);
            totalQuantity.addAndGet(order.quantity);
        }
        
        public Order removeOrder() {
            Order order = orders.poll();
            if (order != null) {
                totalQuantity.addAndGet(-order.quantity);
            }
            return order;
        }
    }
    
    // O(log n) order insertion
    public void addOrder(Order order) {
        ConcurrentSkipListMap<Double, PriceLevel> book = 
            order.side == Side.BUY ? bids : asks;
            
        book.compute(order.price, (price, level) -> {
            if (level == null) {
                level = new PriceLevel(price);
            }
            level.addOrder(order);
            return level;
        });
    }
    
    // O(1) best bid/ask retrieval
    public Double getBestBid() {
        Map.Entry<Double, PriceLevel> best = bids.firstEntry();
        return best != null ? best.getKey() : null;
    }
    
    public Double getBestAsk() {
        Map.Entry<Double, PriceLevel> best = asks.firstEntry();
        return best != null ? best.getKey() : null;
    }
    
    // Market data snapshot - lock-free iteration
    public MarketDepth getMarketDepth(int levels) {
        List<PriceLevel> bidLevels = bids.values().stream()
            .limit(levels)
            .collect(Collectors.toList());
            
        List<PriceLevel> askLevels = asks.values().stream()
            .limit(levels)
            .collect(Collectors.toList());
            
        return new MarketDepth(bidLevels, askLevels);
    }
}`,
        features: [
          'Lock-free concurrent access',
          'O(log n) order operations',
          'O(1) top-of-book access',
          'Memory-efficient price aggregation'
        ]
      }
    },
    {
      id: 'benchmarking',
      name: 'Performance Testing',
      icon: <Cpu size={20} />,
      color: '#06b6d4',
      content: {
        title: 'JMH Benchmarking',
        examples: [
          {
            name: 'Collection Benchmark',
            code: `// JMH benchmark for collection performance
import org.openjdk.jmh.annotations.*;
import java.util.concurrent.TimeUnit;

@BenchmarkMode(Mode.AverageTime)
@OutputTimeUnit(TimeUnit.NANOSECONDS)
@State(Scope.Benchmark)
@Fork(value = 2)
@Warmup(iterations = 5)
@Measurement(iterations = 10)
public class CollectionBenchmark {
    
    @Param({"100", "10000", "1000000"})
    private int size;
    
    private List<Integer> arrayList;
    private List<Integer> linkedList;
    private Set<Integer> hashSet;
    private Set<Integer> treeSet;
    
    @Setup
    public void setup() {
        arrayList = new ArrayList<>(size);
        linkedList = new LinkedList<>();
        hashSet = new HashSet<>(size);
        treeSet = new TreeSet<>();
        
        for (int i = 0; i < size; i++) {
            arrayList.add(i);
            linkedList.add(i);
            hashSet.add(i);
            treeSet.add(i);
        }
    }
    
    @Benchmark
    public int arrayListIteration() {
        int sum = 0;
        for (Integer val : arrayList) {
            sum += val;
        }
        return sum;
    }
    
    @Benchmark
    public boolean hashSetContains() {
        return hashSet.contains(size / 2);
    }
    
    @Benchmark
    public boolean treeSetContains() {
        return treeSet.contains(size / 2);
    }
}

// Results (size=10000):
// arrayListIteration   12,450 ns/op
// linkedListIteration  124,892 ns/op  
// hashSetContains          15 ns/op
// treeSetContains         234 ns/op`,
            results: {
              'ArrayList iteration': '12.4 μs',
              'LinkedList iteration': '124.8 μs',
              'HashSet contains': '15 ns',
              'TreeSet contains': '234 ns'
            }
          }
        ]
      }
    },
    {
      id: 'immutable',
      name: 'Immutable Collections',
      icon: <Shield size={20} />,
      color: '#a855f7',
      content: {
        title: 'Immutable & Persistent Collections',
        implementations: [
          {
            name: 'Java 9+ Immutable Collections',
            code: `// Java 9+ factory methods
List<String> immutableList = List.of("AAPL", "GOOGL", "MSFT");
Set<String> immutableSet = Set.of("NYSE", "NASDAQ", "LSE");
Map<String, Double> immutablePrices = Map.of(
    "AAPL", 150.0,
    "GOOGL", 2800.0,
    "MSFT", 300.0
);

// Immutable collectors
List<Order> orders = getOrders();
List<Order> highValueOrders = orders.stream()
    .filter(o -> o.getValue() > 10000)
    .collect(Collectors.toUnmodifiableList());

// Defensive copying
public class Position {
    private final List<Trade> trades;
    
    public Position(List<Trade> trades) {
        this.trades = List.copyOf(trades); // Immutable copy
    }
    
    public List<Trade> getTrades() {
        return trades; // Safe to return, immutable
    }
}`,
            benefits: [
              'Thread-safe by default',
              'No defensive copying needed',
              'Memory efficient (structural sharing)',
              'Prevents bugs from mutation'
            ]
          },
          {
            name: 'Persistent Data Structures',
            code: `// Vavr (formerly Javaslang) persistent collections
import io.vavr.collection.*;

// Persistent vector (like Clojure's vector)
Vector<String> symbols = Vector.of("AAPL", "GOOGL");
Vector<String> moreSymbols = symbols.append("MSFT"); // O(1)
// Original 'symbols' unchanged

// Persistent hash map
HashMap<String, Double> prices = HashMap.of(
    "AAPL", 150.0,
    "GOOGL", 2800.0
);
HashMap<String, Double> updatedPrices = prices.put("MSFT", 300.0);
// Original 'prices' unchanged

// Structural sharing - memory efficient
// Only modified nodes are copied
// Unmodified parts are shared

// Benefits for event sourcing
public class EventStore {
    private Vector<Event> events = Vector.empty();
    
    public EventStore append(Event event) {
        return new EventStore(events.append(event));
    }
    
    public Stream<Event> getEvents() {
        return events.toJavaStream();
    }
}`,
            benefits: [
              'Structural sharing saves memory',
              'Time-travel debugging',
              'Perfect for event sourcing',
              'Undo/redo functionality'
            ]
          }
        ]
      }
    }
  ];

  const getColorForCategory = (category) => {
    const cat = collectionCategories.find(c => c.id === category);
    return cat ? cat.color : '#6b7280';
  };

  const renderContent = () => {
    const category = collectionCategories.find(c => c.id === selectedCategory);
    if (!category) return null;

    if (selectedCategory === 'overview') {
      return (
        <div>
          <h3 style={{ fontSize: '20px', color: 'white', marginBottom: '20px' }}>
            Java Collections Framework
          </h3>
          
          <div style={{
            background: 'rgba(0, 0, 0, 0.3)',
            borderRadius: '8px',
            padding: '16px',
            marginBottom: '20px'
          }}>
            <pre style={{ 
              color: '#10b981', 
              fontSize: '13px',
              lineHeight: '1.6',
              margin: 0
            }}>
              {category.content.hierarchy}
            </pre>
          </div>

          <h4 style={{ fontSize: '16px', color: '#f59e0b', marginBottom: '16px' }}>
            Big O Complexity Comparison
          </h4>
          
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))', gap: '16px' }}>
            {Object.entries(category.content.bigO).map(([name, complexity]) => (
              <div key={name} style={{
                background: 'rgba(59, 130, 246, 0.1)',
                borderRadius: '8px',
                border: '1px solid rgba(59, 130, 246, 0.3)',
                padding: '16px'
              }}>
                <h5 style={{ color: '#3b82f6', marginBottom: '12px', fontSize: '14px' }}>{name}</h5>
                {Object.entries(complexity).map(([op, time]) => (
                  <div key={op} style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between',
                    marginBottom: '4px',
                    fontSize: '12px'
                  }}>
                    <span style={{ color: '#94a3b8' }}>{op}:</span>
                    <span style={{ color: '#10b981', fontFamily: 'monospace' }}>{time}</span>
                  </div>
                ))}
              </div>
            ))}
          </div>
        </div>
      );
    }

    if (selectedCategory === 'performance') {
      return (
        <div>
          <h3 style={{ fontSize: '20px', color: 'white', marginBottom: '20px' }}>
            {category.content.title}
          </h3>
          
          {category.content.details.map((detail, idx) => (
            <div key={idx} style={{ marginBottom: '32px' }}>
              <h4 style={{ color: '#ef4444', marginBottom: '16px' }}>{detail.name}</h4>
              
              <div style={{
                background: 'rgba(0, 0, 0, 0.3)',
                borderRadius: '8px',
                padding: '16px',
                marginBottom: '16px'
              }}>
                <pre style={{ 
                  color: '#e2e8f0', 
                  fontSize: '12px',
                  lineHeight: '1.6',
                  margin: 0,
                  overflow: 'auto'
                }}>
                  <code>{detail.comparison}</code>
                </pre>
              </div>
              
              {detail.benchmark && (
                <div style={{
                  background: 'rgba(239, 68, 68, 0.1)',
                  borderRadius: '8px',
                  border: '1px solid rgba(239, 68, 68, 0.3)',
                  padding: '12px'
                }}>
                  <h5 style={{ color: '#ef4444', marginBottom: '8px', fontSize: '12px' }}>Benchmark Results:</h5>
                  {Object.entries(detail.benchmark).map(([test, result]) => (
                    <div key={test} style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between',
                      fontSize: '11px',
                      marginBottom: '4px'
                    }}>
                      <span style={{ color: '#d1d5db' }}>{test}:</span>
                      <span style={{ color: '#10b981', fontFamily: 'monospace' }}>{result}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      );
    }

    if (selectedCategory === 'trading') {
      return (
        <div>
          <h3 style={{ fontSize: '20px', color: 'white', marginBottom: '20px' }}>
            {category.content.title}
          </h3>
          
          {category.content.implementations.map((impl, idx) => (
            <div key={idx} style={{ marginBottom: '32px' }}>
              <h4 style={{ color: '#10b981', marginBottom: '8px' }}>{impl.name}</h4>
              <p style={{ color: '#94a3b8', fontSize: '13px', marginBottom: '16px' }}>
                {impl.description}
              </p>
              
              <div style={{
                background: 'rgba(0, 0, 0, 0.3)',
                borderRadius: '8px',
                padding: '16px',
                marginBottom: '16px'
              }}>
                <pre style={{ 
                  color: '#e2e8f0', 
                  fontSize: '12px',
                  lineHeight: '1.5',
                  margin: 0,
                  overflow: 'auto'
                }}>
                  <code>{impl.code}</code>
                </pre>
              </div>
              
              <div style={{
                background: 'rgba(16, 185, 129, 0.1)',
                borderRadius: '8px',
                border: '1px solid rgba(16, 185, 129, 0.3)',
                padding: '12px'
              }}>
                <h5 style={{ color: '#10b981', marginBottom: '8px', fontSize: '12px' }}>Key Features:</h5>
                <ul style={{ margin: 0, paddingLeft: '20px' }}>
                  {impl.features.map((feature, fIdx) => (
                    <li key={fIdx} style={{ color: '#d1d5db', fontSize: '11px', marginBottom: '4px' }}>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      );
    }

    if (selectedCategory === 'memory') {
      return (
        <div>
          <h3 style={{ fontSize: '20px', color: 'white', marginBottom: '20px' }}>
            {category.content.title}
          </h3>
          
          {category.content.strategies.map((strategy, idx) => (
            <div key={idx} style={{ marginBottom: '32px' }}>
              <h4 style={{ color: '#f59e0b', marginBottom: '16px' }}>{strategy.name}</h4>
              
              <div style={{
                background: 'rgba(0, 0, 0, 0.3)',
                borderRadius: '8px',
                padding: '16px',
                marginBottom: '16px'
              }}>
                <pre style={{ 
                  color: '#e2e8f0', 
                  fontSize: '12px',
                  lineHeight: '1.5',
                  margin: 0,
                  overflow: 'auto'
                }}>
                  <code>{strategy.code}</code>
                </pre>
              </div>
              
              <div style={{
                background: 'rgba(245, 158, 11, 0.1)',
                borderRadius: '8px',
                border: '1px solid rgba(245, 158, 11, 0.3)',
                padding: '12px',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                <HardDrive size={16} style={{ color: '#f59e0b' }} />
                <span style={{ color: '#fbbf24', fontSize: '12px', fontWeight: '600' }}>
                  Memory Savings: {strategy.savings}
                </span>
              </div>
            </div>
          ))}
        </div>
      );
    }

    if (selectedCategory === 'lowlatency') {
      return (
        <div>
          <h3 style={{ fontSize: '20px', color: 'white', marginBottom: '20px' }}>
            {category.content.title}
          </h3>
          
          {category.content.patterns.map((pattern, idx) => (
            <div key={idx} style={{ marginBottom: '32px' }}>
              <h4 style={{ color: '#8b5cf6', marginBottom: '16px' }}>{pattern.name}</h4>
              
              <div style={{
                background: 'rgba(0, 0, 0, 0.3)',
                borderRadius: '8px',
                padding: '16px',
                marginBottom: '16px'
              }}>
                <pre style={{ 
                  color: '#e2e8f0', 
                  fontSize: '12px',
                  lineHeight: '1.5',
                  margin: 0,
                  overflow: 'auto'
                }}>
                  <code>{pattern.code}</code>
                </pre>
              </div>
              
              <div style={{
                background: 'rgba(139, 92, 246, 0.1)',
                borderRadius: '8px',
                border: '1px solid rgba(139, 92, 246, 0.3)',
                padding: '12px',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                <Zap size={16} style={{ color: '#8b5cf6' }} />
                <span style={{ color: '#a78bfa', fontSize: '12px', fontWeight: '600' }}>
                  Latency: {pattern.latency}
                </span>
              </div>
            </div>
          ))}
        </div>
      );
    }

    if (selectedCategory === 'orderbook') {
      return (
        <div>
          <h3 style={{ fontSize: '20px', color: 'white', marginBottom: '20px' }}>
            {category.content.title}
          </h3>
          
          <div style={{
            background: 'rgba(0, 0, 0, 0.3)',
            borderRadius: '8px',
            padding: '16px',
            marginBottom: '20px'
          }}>
            <pre style={{ 
              color: '#e2e8f0', 
              fontSize: '12px',
              lineHeight: '1.5',
              margin: 0,
              overflow: 'auto'
            }}>
              <code>{category.content.implementation}</code>
            </pre>
          </div>
          
          <div style={{
            background: 'rgba(236, 72, 153, 0.1)',
            borderRadius: '8px',
            border: '1px solid rgba(236, 72, 153, 0.3)',
            padding: '16px'
          }}>
            <h5 style={{ color: '#ec4899', marginBottom: '12px' }}>Performance Characteristics:</h5>
            <ul style={{ margin: 0, paddingLeft: '20px' }}>
              {category.content.features.map((feature, idx) => (
                <li key={idx} style={{ color: '#d1d5db', fontSize: '12px', marginBottom: '4px' }}>
                  {feature}
                </li>
              ))}
            </ul>
          </div>
        </div>
      );
    }

    if (selectedCategory === 'benchmarking') {
      return (
        <div>
          <h3 style={{ fontSize: '20px', color: 'white', marginBottom: '20px' }}>
            {category.content.title}
          </h3>
          
          {category.content.examples.map((example, idx) => (
            <div key={idx} style={{ marginBottom: '32px' }}>
              <h4 style={{ color: '#06b6d4', marginBottom: '16px' }}>{example.name}</h4>
              
              <div style={{
                background: 'rgba(0, 0, 0, 0.3)',
                borderRadius: '8px',
                padding: '16px',
                marginBottom: '16px'
              }}>
                <pre style={{ 
                  color: '#e2e8f0', 
                  fontSize: '12px',
                  lineHeight: '1.5',
                  margin: 0,
                  overflow: 'auto'
                }}>
                  <code>{example.code}</code>
                </pre>
              </div>
              
              {example.results && (
                <div style={{
                  background: 'rgba(6, 182, 212, 0.1)',
                  borderRadius: '8px',
                  border: '1px solid rgba(6, 182, 212, 0.3)',
                  padding: '12px'
                }}>
                  <h5 style={{ color: '#06b6d4', marginBottom: '8px', fontSize: '12px' }}>Results:</h5>
                  {Object.entries(example.results).map(([test, result]) => (
                    <div key={test} style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between',
                      fontSize: '11px',
                      marginBottom: '4px'
                    }}>
                      <span style={{ color: '#d1d5db' }}>{test}:</span>
                      <span style={{ color: '#10b981', fontFamily: 'monospace' }}>{result}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      );
    }

    if (selectedCategory === 'immutable') {
      return (
        <div>
          <h3 style={{ fontSize: '20px', color: 'white', marginBottom: '20px' }}>
            {category.content.title}
          </h3>
          
          {category.content.implementations.map((impl, idx) => (
            <div key={idx} style={{ marginBottom: '32px' }}>
              <h4 style={{ color: '#a855f7', marginBottom: '16px' }}>{impl.name}</h4>
              
              <div style={{
                background: 'rgba(0, 0, 0, 0.3)',
                borderRadius: '8px',
                padding: '16px',
                marginBottom: '16px'
              }}>
                <pre style={{ 
                  color: '#e2e8f0', 
                  fontSize: '12px',
                  lineHeight: '1.5',
                  margin: 0,
                  overflow: 'auto'
                }}>
                  <code>{impl.code}</code>
                </pre>
              </div>
              
              <div style={{
                background: 'rgba(168, 85, 247, 0.1)',
                borderRadius: '8px',
                border: '1px solid rgba(168, 85, 247, 0.3)',
                padding: '12px'
              }}>
                <h5 style={{ color: '#a855f7', marginBottom: '8px', fontSize: '12px' }}>Benefits:</h5>
                <ul style={{ margin: 0, paddingLeft: '20px' }}>
                  {impl.benefits.map((benefit, bIdx) => (
                    <li key={bIdx} style={{ color: '#d1d5db', fontSize: '11px', marginBottom: '4px' }}>
                      {benefit}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      );
    }

    return null;
  };

  return (
    <div style={{
      width: '100%',
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
      color: '#ffffff',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif',
      padding: '40px 20px'
    }}>
      <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
        {/* Header */}
        <div style={{ marginBottom: '40px' }}>
          <h1 style={{ 
            fontSize: '48px', 
            fontWeight: '700', 
            marginBottom: '16px',
            background: 'linear-gradient(135deg, #3b82f6 0%, #10b981 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}>
            Java Collections & Performance
          </h1>
          <p style={{ fontSize: '18px', color: '#94a3b8' }}>
            High-performance collections for low-latency trading systems
          </p>
        </div>

        {/* Category Navigation */}
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '16px',
          marginBottom: '32px'
        }}>
          {collectionCategories.map(category => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              style={{
                padding: '16px',
                background: selectedCategory === category.id 
                  ? `linear-gradient(135deg, ${category.color}40, ${category.color}20)`
                  : 'rgba(30, 41, 59, 0.5)',
                border: selectedCategory === category.id
                  ? `2px solid ${category.color}`
                  : '2px solid rgba(51, 65, 85, 0.5)',
                borderRadius: '12px',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: '8px'
              }}
            >
              <div style={{ color: category.color }}>
                {category.icon}
              </div>
              <span style={{ 
                fontSize: '14px', 
                fontWeight: '600',
                color: selectedCategory === category.id ? 'white' : '#94a3b8'
              }}>
                {category.name}
              </span>
            </button>
          ))}
        </div>

        {/* Content Area */}
        <div style={{
          background: 'rgba(30, 41, 59, 0.5)',
          borderRadius: '16px',
          border: '1px solid rgba(51, 65, 85, 0.5)',
          padding: '32px',
          minHeight: '400px'
        }}>
          {renderContent()}
        </div>

        {/* Performance Tips */}
        <div style={{
          marginTop: '32px',
          padding: '24px',
          background: 'rgba(239, 68, 68, 0.1)',
          borderRadius: '12px',
          border: '1px solid rgba(239, 68, 68, 0.3)'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '16px' }}>
            <AlertTriangle size={24} style={{ color: '#ef4444' }} />
            <h3 style={{ color: '#ef4444', margin: 0 }}>Performance Best Practices</h3>
          </div>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '16px' }}>
            <div>
              <h4 style={{ color: '#f87171', fontSize: '14px', marginBottom: '8px' }}>Size Collections Properly</h4>
              <p style={{ color: '#d1d5db', fontSize: '12px', margin: 0 }}>
                Initialize with expected capacity to avoid resizing overhead
              </p>
            </div>
            <div>
              <h4 style={{ color: '#f87171', fontSize: '14px', marginBottom: '8px' }}>Use Primitive Collections</h4>
              <p style={{ color: '#d1d5db', fontSize: '12px', margin: 0 }}>
                Avoid boxing overhead with specialized primitive collections
              </p>
            </div>
            <div>
              <h4 style={{ color: '#f87171', fontSize: '14px', marginBottom: '8px' }}>Consider Cache Locality</h4>
              <p style={{ color: '#d1d5db', fontSize: '12px', margin: 0 }}>
                ArrayList beats LinkedList for iteration due to cache friendliness
              </p>
            </div>
            <div>
              <h4 style={{ color: '#f87171', fontSize: '14px', marginBottom: '8px' }}>Profile Before Optimizing</h4>
              <p style={{ color: '#d1d5db', fontSize: '12px', margin: 0 }}>
                Use JMH for accurate microbenchmarks, avoid premature optimization
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default JavaCollections;
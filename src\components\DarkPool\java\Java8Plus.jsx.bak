import React, { useState } from 'react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Sparkles, Code, GitBranch, Zap, Package, Activity } from 'lucide-react';

const Java8Plus = () => {
  const [selectedFeature, setSelectedFeature] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [detailsTab, setDetailsTab] = useState('overview');

  import React, { useState } from 'react';
  import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
  import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
  import { Sparkles, Code, GitBranch } from 'lucide-react';

  const Java8Plus = () => {
    const [selectedFeature, setSelectedFeature] = useState(null);
    const [activeTab, setActiveTab] = useState('overview');
    const [detailsTab, setDetailsTab] = useState('overview');

    const features = [
      { id: 'lambdas', name: 'Lambda Expressions', icon: <Code size={20} />, description: 'Functional programming support' },
      { id: 'streams', name: 'Stream API', icon: <GitBranch size={20} />, description: 'Functional data processing' },
      { id: 'optional', name: 'Optional', icon: <Sparkles size={20} />, description: 'Null-safe handling' }
    ];

    const codeExamples = {
      lambdas: `// Example lambda\nRunnable r = () -> System.out.println("hi");`,
      streams: `// Example streams\nlist.stream().filter(x -> x>0).count();`,
      optional: `// Example optional\nOptional.ofNullable(x).ifPresent(System.out::println);`
    };

    return (
      <div style={{ padding: 24 }}>
        <h2>Java8+ (simplified)</h2>
        <div style={{ display: 'flex', gap: 8, marginTop: 12 }}>
          {features.map(f => (
            <div key={f.id} onClick={() => setSelectedFeature(f.id)} style={{ padding: 12, background: '#111827', borderRadius: 6 }}>
              <div style={{ fontWeight: 600 }}>{f.name}</div>
              <div style={{ color: '#94a3b8' }}>{f.description}</div>
            </div>
          ))}
        </div>

        {selectedFeature && (
          <div style={{ marginTop: 12 }}>
            <h3>{selectedFeature}</h3>
            <div style={{ marginTop: 8 }}>
              <button onClick={() => setSelectedFeature(null)}>Close</button>
            </div>
          </div>
        )}
      </div>
    );
  };

  export default Java8Plus;
                  'Type inference reduces compilation time',
                  'Compact strings save 20% memory'
                ].map((benefit, index) => (
                  <li key={index} style={{ padding: '8px 0', color: '#94a3b8', fontSize: '14px' }}>
                    <span style={{ color: '#3b82f6', marginRight: '8px' }}>•</span>
                    {benefit}
                  </li>
                ))}
              </ul>
            </div>
            <div style={{
              padding: '24px',
              backgroundColor: 'rgba(31, 41, 55, 0.5)',
              borderRadius: '12px',
              border: '1px solid #374151'
            }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px', color: '#f59e0b' }}>
                JVM Flags for Java 8+
              </h3>
              <ul style={{ listStyle: 'none', padding: 0 }}>
                {[
                  '-XX:+UseStringDeduplication',
                  '-XX:+UseCompressedStrings',
                  '-XX:+OptimizeStringConcat',
                  '--enable-preview (for preview features)',
                  '-Djdk.virtualThreadScheduler.parallelism=N'
                ].map((flag, index) => (
                  <li key={index} style={{ padding: '8px 0', fontSize: '14px' }}>
                    <code style={{ color: '#60a5fa', fontFamily: 'monospace' }}>{flag}</code>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Java8Plus;
public class DarkPoolAnalytics {
    public Map<String, Double> calculateHiddenLiquidity(List<Order> orders) {
        return orders.parallelStream()
            .filter(Order::isHidden)
            .collect(Collectors.groupingBy(
                Order::getSymbol,
                Collectors.summingDouble(order -> 
                    order.getQuantity() * order.getPrice()
                )
            ));
    }
    
    // Find crossing opportunities
    public List<CrossingOpportunity> findCrossings(List<Order> orders) {
        Map<String, List<Order>> ordersBySymbol = orders.stream()
            .collect(Collectors.groupingBy(Order::getSymbol));
        
        return ordersBySymbol.entrySet().stream()
            .map(entry -> findCrossingForSymbol(entry.getValue()))
            .filter(Optional::isPresent)
            .map(Optional::get)
            .toList();
    }
}

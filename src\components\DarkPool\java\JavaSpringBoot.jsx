import React, { useState } from 'react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Box, Settings, Zap, Activity, Database, Cloud, Server, Globe, RefreshCw, Bell, FileText, Shield, Layers, MessageSquare, Timer, Package } from 'lucide-react';

const JavaSpringBoot = () => {
  const [selectedTopic, setSelectedTopic] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [detailsTab, setDetailsTab] = useState('overview');

  const topics = [
    {
      id: 'auto-configuration',
      name: 'Auto-Configuration',
      icon: <Settings size={20} />,
      description: 'Convention over configuration',
      color: '#ef4444'
    },
    {
      id: 'starters',
      name: 'Spring Boot Starters',
      icon: <Box size={20} />,
      description: 'Dependency management and quick setup',
      color: '#3b82f6'
    },
    {
      id: 'actuator',
      name: 'Spring Boot Actuator',
      icon: <Activity size={20} />,
      description: 'Production monitoring and management',
      color: '#10b981'
    },
    {
      id: 'microservices',
      name: 'Microservices',
      icon: <Cloud size={20} />,
      description: 'Service discovery and communication',
      color: '#f59e0b'
    },
    {
      id: 'data-jpa',
      name: 'Spring Data JPA',
      icon: <Database size={20} />,
      description: 'Repository abstraction and ORM',
      color: '#8b5cf6'
    },
    {
      id: 'testing',
      name: 'Testing',
      icon: <Zap size={20} />,
      description: 'Test slices and integration testing',
      color: '#ec4899'
    },
    {
      id: 'spring-batch',
      name: 'Spring Batch',
      icon: <Activity size={20} />,
      description: 'Batch processing and job execution',
      color: '#67e8f9'
    },
    {
      id: 'spring-cache',
      name: 'Spring Cache',
      icon: <RefreshCw size={20} />,
      description: 'Caching abstraction and optimization',
      color: '#a7f3d0'
    },
    {
      id: 'spring-events',
      name: 'Spring Events',
      icon: <Bell size={20} />,
      description: 'Application event publishing and handling',
      color: '#86efac'
    },
    {
      id: 'spring-websocket',
      name: 'Spring WebSocket',
      icon: <MessageSquare size={20} />,
      description: 'Real-time bidirectional communication',
      color: '#10b981'
    },
    {
      id: 'spring-kafka',
      name: 'Spring Kafka/JMS',
      icon: <Package size={20} />,
      description: 'Message queue integration and streaming',
      color: '#065f46'
    },
    {
      id: 'embedded-servers',
      name: 'Embedded Servers',
      icon: <Server size={20} />,
      description: 'Tomcat, Jetty, and Undertow configuration',
      color: '#1e40af'
    },
    {
      id: 'profiles',
      name: 'Profiles & Properties',
      icon: <Settings size={20} />,
      description: 'Environment-specific configuration',
      color: '#7c3aed'
    },
    {
      id: 'dev-tools',
      name: 'Developer Tools',
      icon: <Timer size={20} />,
      description: 'Hot reloading and development features',
      color: '#dc2626'
    }
  ];

  const codeExamples = {
    'auto-configuration': `// Spring Boot Auto-Configuration for Trading System
@SpringBootApplication
@EnableScheduling
@EnableAsync
@EnableCaching
public class TradingApplication {
    
    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(TradingApplication.class);
        
        // Customize application startup
        app.setBannerMode(Banner.Mode.OFF);
        app.setDefaultProperties(Map.of(
            "server.port", "8080",
            "management.endpoints.web.exposure.include", "health,info,metrics",
            "spring.application.name", "trading-system"
        ));
        
        app.run(args);
    }
    
    // Custom auto-configuration for trading services
    @Configuration
    @ConditionalOnProperty(name = "trading.enabled", havingValue = "true", matchIfMissing = true)
    public static class TradingAutoConfiguration {
        
        @Bean
        @ConditionalOnMissingBean
        public OrderProcessingService orderProcessingService(
                OrderRepository orderRepository,
                RiskService riskService) {
            return new OrderProcessingService(orderRepository, riskService);
        }
        
        @Bean
        @ConditionalOnProperty(name = "trading.risk.enabled", havingValue = "true")
        public RiskService riskService() {
            return new DefaultRiskService();
        }
        
        @Bean
        @ConditionalOnClass(name = "com.bloomberg.api.BloombergApi")
        public MarketDataService bloombergMarketDataService() {
            return new BloombergMarketDataService();
        }
        
        @Bean
        @ConditionalOnMissingBean(MarketDataService.class)
        public MarketDataService defaultMarketDataService() {
            return new MockMarketDataService();
        }
    }
}

// Application Properties Configuration
@ConfigurationProperties(prefix = "trading")
@Data
@Validated
public class TradingProperties {
    
    @NotNull
    private String environment;
    
    @Valid
    private Risk risk = new Risk();
    
    @Valid
    private Market market = new Market();
    
    @Valid
    private Notifications notifications = new Notifications();
    
    @Data
    public static class Risk {
        @DecimalMin("0.0")
        @DecimalMax("1.0")
        private BigDecimal maxPositionRisk = BigDecimal.valueOf(0.05);
        
        @Min(1000)
        @Max(1000000)
        private long maxOrderValue = 100000;
        
        private boolean enableRealTimeChecks = true;
    }
    
    @Data
    public static class Market {
        @NotEmpty
        private List<String> supportedExchanges = List.of("NYSE", "NASDAQ");
        
        private Duration orderTimeout = Duration.ofSeconds(30);
        
        @Min(1)
        @Max(1000)
        private int maxConcurrentOrders = 100;
    }
    
    @Data
    public static class Notifications {
        private boolean emailEnabled = true;
        private boolean slackEnabled = false;
        private String alertThreshold = "HIGH";
    }
}

// Profile-specific Configuration
@Configuration
@Profile("development")
public class DevelopmentConfig {
    
    @Bean
    @Primary
    public MarketDataService mockMarketDataService() {
        return new MockMarketDataService();
    }
    
    @Bean
    public CommandLineRunner loadTestData(OrderRepository orderRepository) {
        return args -> {
            // Load test data for development
            orderRepository.save(Order.builder()
                .symbol("AAPL")
                .quantity(100)
                .price(BigDecimal.valueOf(150.00))
                .build());
        };
    }
}

@Configuration
@Profile("production")
public class ProductionConfig {
    
    @Bean
    public SecurityConfig productionSecurity() {
        return SecurityConfig.builder()
            .httpsOnly(true)
            .strictMode(true)
            .build();
    }
    
    @EventListener
    public void handleContextRefreshedEvent(ContextRefreshedEvent event) {
        log.info("Trading system started in PRODUCTION mode");
        // Send startup notification
        notificationService.sendStartupAlert();
    }
}`,
    starters: `// Spring Boot Starters for Trading System Dependencies
/*
build.gradle dependencies:

// Core Spring Boot starters
implementation 'org.springframework.boot:spring-boot-starter-web'
implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
implementation 'org.springframework.boot:spring-boot-starter-security'
implementation 'org.springframework.boot:spring-boot-starter-validation'
implementation 'org.springframework.boot:spring-boot-starter-actuator'
implementation 'org.springframework.boot:spring-boot-starter-cache'
implementation 'org.springframework.boot:spring-boot-starter-aop'

// Database starters
implementation 'org.springframework.boot:spring-boot-starter-data-redis'
implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'

// Messaging starters
implementation 'org.springframework.boot:spring-boot-starter-amqp'
implementation 'org.springframework.boot:spring-boot-starter-websocket'

// Testing starters
testImplementation 'org.springframework.boot:spring-boot-starter-test'
testImplementation 'org.springframework.security:spring-security-test'
testImplementation 'org.testcontainers:postgresql'
*/

// Custom Trading System Starter
@Configuration
@EnableConfigurationProperties(TradingProperties.class)
@ConditionalOnClass(TradingService.class)
public class TradingSystemAutoConfiguration {
    
    private final TradingProperties tradingProperties;
    
    public TradingSystemAutoConfiguration(TradingProperties tradingProperties) {
        this.tradingProperties = tradingProperties;
    }
    
    @Bean
    @ConditionalOnMissingBean
    public TradingService tradingService(
            OrderRepository orderRepository,
            PortfolioService portfolioService,
            RiskService riskService) {
        
        return TradingService.builder()
            .orderRepository(orderRepository)
            .portfolioService(portfolioService)
            .riskService(riskService)
            .maxConcurrentOrders(tradingProperties.getMarket().getMaxConcurrentOrders())
            .orderTimeout(tradingProperties.getMarket().getOrderTimeout())
            .build();
    }
    
    @Bean
    @ConditionalOnProperty(name = "trading.metrics.enabled", havingValue = "true", matchIfMissing = true)
    public TradingMetricsCollector tradingMetricsCollector(MeterRegistry meterRegistry) {
        return new TradingMetricsCollector(meterRegistry);
    }
    
    @Bean
    @ConditionalOnWebApplication
    public TradingWebSocketConfig tradingWebSocketConfig() {
        return new TradingWebSocketConfig();
    }
}

// META-INF/spring.factories for auto-configuration
/*
org.springframework.boot.autoconfigure.EnableAutoConfiguration=\
com.trading.autoconfigure.TradingSystemAutoConfiguration
*/

// Application Startup Configuration
@Component
public class TradingSystemStartup implements ApplicationRunner {
    
    private final TradingService tradingService;
    private final HealthIndicator tradingHealthIndicator;
    
    public TradingSystemStartup(TradingService tradingService,
                               HealthIndicator tradingHealthIndicator) {
        this.tradingService = tradingService;
        this.tradingHealthIndicator = tradingHealthIndicator;
    }
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("Initializing Trading System...");
        
        // Validate system health
        Health health = tradingHealthIndicator.health();
        if (health.getStatus() != Status.UP) {
            throw new IllegalStateException("Trading system health check failed: " + 
                health.getDetails());
        }
        
        // Initialize market connections
        tradingService.initializeMarketConnections();
        
        // Load trading rules
        tradingService.loadTradingRules();
        
        log.info("Trading System initialized successfully");
    }
}

// External Configuration Management
@Component
@RefreshScope
public class TradingConfigService {
    
    @Value("\${trading.risk.maxPositionRisk:0.05}")
    private BigDecimal maxPositionRisk;
    
    @Value("\${trading.market.orderTimeout:30s}")
    private Duration orderTimeout;
    
    // Configuration can be refreshed without restart using Spring Cloud Config
    @EventListener(RefreshRemoteApplicationEvent.class)
    public void handleConfigRefresh(RefreshRemoteApplicationEvent event) {
        log.info("Configuration refreshed for keys: {}", event.getKeys());
        validateConfiguration();
    }
    
    private void validateConfiguration() {
        if (maxPositionRisk.compareTo(BigDecimal.ONE) > 0) {
            throw new IllegalStateException("Max position risk cannot exceed 100%");
        }
    }
}`,
    actuator: `// Spring Boot Actuator for Trading System Monitoring
@Component
public class TradingSystemHealthIndicator implements HealthIndicator {
    
    private final OrderProcessingService orderService;
    private final MarketDataService marketDataService;
    private final DatabaseHealthService databaseService;
    
    public TradingSystemHealthIndicator(OrderProcessingService orderService,
                                      MarketDataService marketDataService,
                                      DatabaseHealthService databaseService) {
        this.orderService = orderService;
        this.marketDataService = marketDataService;
        this.databaseService = databaseService;
    }
    
    @Override
    public Health health() {
        Health.Builder builder = new Health.Builder();
        
        try {
            // Check order processing status
            OrderProcessingStatus orderStatus = orderService.getStatus();
            builder.withDetail("orderProcessing", Map.of(
                "status", orderStatus.getStatus(),
                "queueSize", orderStatus.getQueueSize(),
                "processedToday", orderStatus.getProcessedToday(),
                "errorRate", orderStatus.getErrorRate()
            ));
            
            // Check market data connectivity
            MarketDataStatus marketStatus = marketDataService.getStatus();
            builder.withDetail("marketData", Map.of(
                "connected", marketStatus.isConnected(),
                "lastUpdate", marketStatus.getLastUpdateTime(),
                "dataLag", marketStatus.getDataLagMs(),
                "exchanges", marketStatus.getConnectedExchanges()
            ));
            
            // Check database health
            DatabaseHealth dbHealth = databaseService.checkHealth();
            builder.withDetail("database", Map.of(
                "responseTime", dbHealth.getResponseTimeMs(),
                "connectionPool", Map.of(
                    "active", dbHealth.getActiveConnections(),
                    "idle", dbHealth.getIdleConnections(),
                    "max", dbHealth.getMaxConnections()
                )
            ));
            
            // Determine overall health
            if (orderStatus.getErrorRate() > 0.05) {
                builder.down().withDetail("reason", "High order error rate");
            } else if (!marketStatus.isConnected()) {
                builder.down().withDetail("reason", "Market data disconnected");
            } else if (dbHealth.getResponseTimeMs() > 1000) {
                builder.down().withDetail("reason", "Database slow response");
            } else {
                builder.up();
            }
            
        } catch (Exception e) {
            builder.down().withException(e);
        }
        
        return builder.build();
    }
}

// Custom Metrics for Trading System
@Component
public class TradingMetrics {
    
    private final Counter ordersSubmitted;
    private final Counter ordersExecuted;
    private final Counter ordersRejected;
    private final Timer orderProcessingTime;
    private final Gauge portfolioValue;
    private final Counter riskViolations;
    
    public TradingMetrics(MeterRegistry meterRegistry) {
        this.ordersSubmitted = Counter.builder("trading.orders.submitted")
            .description("Total number of orders submitted")
            .tag("system", "trading")
            .register(meterRegistry);
            
        this.ordersExecuted = Counter.builder("trading.orders.executed")
            .description("Total number of orders executed")
            .register(meterRegistry);
            
        this.ordersRejected = Counter.builder("trading.orders.rejected")
            .description("Total number of orders rejected")
            .register(meterRegistry);
            
        this.orderProcessingTime = Timer.builder("trading.orders.processing.time")
            .description("Order processing time")
            .register(meterRegistry);
            
        this.portfolioValue = Gauge.builder("trading.portfolio.value")
            .description("Current portfolio value")
            .register(meterRegistry, this, TradingMetrics::getCurrentPortfolioValue);
            
        this.riskViolations = Counter.builder("trading.risk.violations")
            .description("Risk limit violations")
            .register(meterRegistry);
    }
    
    public void recordOrderSubmitted(String orderType) {
        ordersSubmitted.increment(Tags.of("type", orderType));
    }
    
    public void recordOrderExecuted(String symbol, BigDecimal value) {
        ordersExecuted.increment(Tags.of("symbol", symbol));
    }
    
    public void recordOrderRejected(String reason) {
        ordersRejected.increment(Tags.of("reason", reason));
    }
    
    public Timer.Sample startOrderTimer() {
        return Timer.start(orderProcessingTime);
    }
    
    private double getCurrentPortfolioValue() {
        // Implementation to get current portfolio value
        return portfolioService.getCurrentValue().doubleValue();
    }
}

// Custom Actuator Endpoints
@Component
@Endpoint(id = "trading")
public class TradingActuatorEndpoint {
    
    private final TradingService tradingService;
    private final RiskService riskService;
    
    public TradingActuatorEndpoint(TradingService tradingService, RiskService riskService) {
        this.tradingService = tradingService;
        this.riskService = riskService;
    }
    
    @ReadOperation
    public Map<String, Object> tradingInfo() {
        return Map.of(
            "status", tradingService.getStatus(),
            "activeOrders", tradingService.getActiveOrderCount(),
            "todayVolume", tradingService.getTodayVolume(),
            "riskMetrics", riskService.getCurrentRiskMetrics(),
            "timestamp", Instant.now()
        );
    }
    
    @WriteOperation
    public String pauseTrading(@Selector String reason) {
        tradingService.pauseTrading(reason);
        return "Trading paused: " + reason;
    }
    
    @WriteOperation
    public String resumeTrading() {
        tradingService.resumeTrading();
        return "Trading resumed";
    }
    
    @DeleteOperation
    public String cancelAllOrders() {
        int cancelled = tradingService.cancelAllOrders();
        return "Cancelled " + cancelled + " orders";
    }
}

// Configuration for Actuator Security
@Configuration
public class ActuatorSecurityConfig {
    
    @Bean
    public SecurityFilterChain actuatorSecurityFilterChain(HttpSecurity http) throws Exception {
        http.requestMatcher(EndpointRequest.toAnyEndpoint())
            .authorizeHttpRequests(requests -> requests
                .requestMatchers(EndpointRequest.to(HealthEndpoint.class)).permitAll()
                .requestMatchers(EndpointRequest.to(InfoEndpoint.class)).permitAll()
                .anyRequest().hasRole("ACTUATOR")
            )
            .httpBasic(Customizer.withDefaults());
        return http.build();
    }
}`,
    microservices: `// Microservices Architecture with Spring Boot
@SpringBootApplication
@EnableEurekaClient
@EnableCircuitBreaker
@EnableZipkinServer
public class OrderServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(OrderServiceApplication.class, args);
    }
}

// Service Discovery Configuration
@Configuration
public class ServiceDiscoveryConfig {
    
    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
    
    @Bean
    @LoadBalanced
    public WebClient.Builder webClientBuilder() {
        return WebClient.builder();
    }
}

// Inter-Service Communication
@Service
public class PortfolioServiceClient {
    
    private final WebClient webClient;
    private final CircuitBreaker circuitBreaker;
    
    public PortfolioServiceClient(WebClient.Builder webClientBuilder,
                                 CircuitBreakerFactory circuitBreakerFactory) {
        this.webClient = webClientBuilder.build();
        this.circuitBreaker = circuitBreakerFactory.create("portfolio-service");
    }
    
    public Mono<Portfolio> getPortfolio(String accountId) {
        return circuitBreaker.executeSupplier(() ->
            webClient.get()
                .uri("http://portfolio-service/api/v1/portfolios/{accountId}", accountId)
                .retrieve()
                .onStatus(HttpStatus::isError, response -> 
                    Mono.error(new ServiceException("Portfolio service error")))
                .bodyToMono(Portfolio.class)
                .timeout(Duration.ofSeconds(5))
        ).onErrorReturn(Portfolio.empty());
    }
    
    @Retryable(value = {ServiceException.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public CompletableFuture<PortfolioUpdate> updatePortfolioAsync(String accountId, Trade trade) {
        return webClient.put()
            .uri("http://portfolio-service/api/v1/portfolios/{accountId}/trades", accountId)
            .bodyValue(trade)
            .retrieve()
            .bodyToMono(PortfolioUpdate.class)
            .toFuture();
    }
}

// Event-Driven Architecture with Message Queues
@Component
public class TradingEventPublisher {
    
    private final RabbitTemplate rabbitTemplate;
    private final KafkaTemplate<String, Object> kafkaTemplate;
    
    public TradingEventPublisher(RabbitTemplate rabbitTemplate,
                                KafkaTemplate<String, Object> kafkaTemplate) {
        this.rabbitTemplate = rabbitTemplate;
        this.kafkaTemplate = kafkaTemplate;
    }
    
    public void publishOrderEvent(OrderEvent event) {
        // Publish to RabbitMQ for internal processing
        rabbitTemplate.convertAndSend("trading.orders", event);
        
        // Publish to Kafka for analytics and audit
        kafkaTemplate.send("order-events", event.getOrderId(), event);
    }
    
    @EventListener
    @Async
    public void handleTradeExecuted(TradeExecutedEvent event) {
        // Notify portfolio service
        portfolioServiceClient.updatePortfolioAsync(
            event.getAccountId(), event.getTrade());
        
        // Notify risk service
        riskServiceClient.updateRiskMetrics(
            event.getAccountId(), event.getTrade());
        
        // Send external notifications
        notificationService.sendTradeNotification(event);
    }
}

@RabbitListener(queues = "trading.orders")
public class OrderEventProcessor {
    
    private final OrderProcessingService orderService;
    
    public OrderEventProcessor(OrderProcessingService orderService) {
        this.orderService = orderService;
    }
    
    @RabbitHandler
    public void handleOrderSubmitted(OrderSubmittedEvent event) {
        orderService.processOrder(event.getOrder());
    }
    
    @RabbitHandler
    public void handleOrderCancelled(OrderCancelledEvent event) {
        orderService.cancelOrder(event.getOrderId());
    }
}

// API Gateway Configuration
@Configuration
@EnableZuulProxy
public class ApiGatewayConfig {
    
    @Bean
    public RouteLocator customRoutes(RouteLocatorBuilder builder) {
        return builder.routes()
            .route("order-service", r -> r
                .path("/api/v1/orders/**")
                .filters(f -> f
                    .addRequestHeader("X-Gateway", "trading-gateway")
                    .circuitBreaker(config -> config
                        .setName("order-service-cb")
                        .setFallbackUri("forward:/fallback/orders"))
                )
                .uri("http://order-service"))
            .route("portfolio-service", r -> r
                .path("/api/v1/portfolios/**")
                .filters(f -> f.retry(3))
                .uri("http://portfolio-service"))
            .route("risk-service", r -> r
                .path("/api/v1/risk/**")
                .filters(f -> f.requestRateLimiter(config -> config
                    .setRateLimiter(redisRateLimiter())
                    .setKeyResolver(userKeyResolver())))
                .uri("http://risk-service"))
            .build();
    }
    
    @Bean
    public RedisRateLimiter redisRateLimiter() {
        return new RedisRateLimiter(10, 20);
    }
}

// Distributed Configuration
@Configuration
@EnableConfigServer
public class ConfigServerConfig {
    
    @Bean
    public EnvironmentRepository environmentRepository() {
        return new GitEnvironmentRepository(Map.of(
            "uri", "https://github.com/trading-system/config-repo",
            "searchPaths", "trading/{application}",
            "default-label", "main"
        ));
    }
}`,
    'data-jpa': `// Spring Data JPA for Trading System
@Entity
@Table(name = "orders", indexes = {
    @Index(name = "idx_orders_symbol_status", columnList = "symbol, status"),
    @Index(name = "idx_orders_account_created", columnList = "account_id, created_at"),
    @Index(name = "idx_orders_status_created", columnList = "status, created_at")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Order {
    
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "order_seq")
    @SequenceGenerator(name = "order_seq", sequenceName = "order_sequence", allocationSize = 1)
    private Long id;
    
    @Column(nullable = false, length = 10)
    private String symbol;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private OrderType type;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private OrderSide side;
    
    @Column(nullable = false)
    private Integer quantity;
    
    @Column(precision = 19, scale = 4)
    private BigDecimal price;
    
    @Column(name = "account_id", nullable = false)
    private String accountId;
    
    @Enumerated(EnumType.STRING)
    @Builder.Default
    private OrderStatus status = OrderStatus.PENDING;
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    @OneToMany(mappedBy = "order", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Execution> executions = new ArrayList<>();
    
    @Version
    private Long version;
    
    public BigDecimal getTotalValue() {
        if (price == null) return BigDecimal.ZERO;
        return price.multiply(BigDecimal.valueOf(quantity));
    }
}

// Repository with Custom Queries
@Repository
public interface OrderRepository extends JpaRepository<Order, Long>, OrderRepositoryCustom {
    
    // Method name queries
    List<Order> findBySymbolAndStatus(String symbol, OrderStatus status);
    
    List<Order> findByAccountIdOrderByCreatedAtDesc(String accountId);
    
    @Query("SELECT COUNT(o) FROM Order o WHERE o.accountId = ?1 AND o.status = ?2")
    long countByAccountIdAndStatus(String accountId, OrderStatus status);
    
    // Named queries
    @Query(name = "Order.findLargeOrders", nativeQuery = true)
    List<Order> findLargeOrders(@Param("minValue") BigDecimal minValue);
    
    // Custom queries with Pageable
    @Query("SELECT o FROM Order o WHERE o.symbol = ?1 AND o.createdAt BETWEEN ?2 AND ?3")
    Page<Order> findBySymbolAndDateRange(String symbol, LocalDateTime start, 
                                        LocalDateTime end, Pageable pageable);
    
    // Projection for summary data
    @Query("SELECT new com.trading.dto.OrderSummary(o.symbol, COUNT(o), SUM(o.quantity), AVG(o.price)) " +
           "FROM Order o WHERE o.status = 'EXECUTED' GROUP BY o.symbol")
    List<OrderSummary> getOrderSummaryBySymbol();
    
    // Modifying queries
    @Modifying
    @Query("UPDATE Order o SET o.status = 'CANCELLED' WHERE o.id IN ?1")
    int cancelOrdersById(List<Long> orderIds);
    
    @Modifying
    @Query("DELETE FROM Order o WHERE o.status = 'CANCELLED' AND o.createdAt < ?1")
    int deleteOldCancelledOrders(LocalDateTime before);
    
    // Stream for large result sets
    @Query("SELECT o FROM Order o WHERE o.createdAt BETWEEN ?1 AND ?2")
    Stream<Order> streamOrdersByDateRange(LocalDateTime start, LocalDateTime end);
}

// Custom Repository Implementation
@Repository
public class OrderRepositoryImpl implements OrderRepositoryCustom {
    
    @PersistenceContext
    private EntityManager entityManager;
    
    @Override
    public List<Order> findOrdersWithComplexCriteria(OrderSearchCriteria criteria) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Order> query = cb.createQuery(Order.class);
        Root<Order> order = query.from(Order.class);
        
        List<Predicate> predicates = new ArrayList<>();
        
        if (criteria.getSymbol() != null) {
            predicates.add(cb.equal(order.get("symbol"), criteria.getSymbol()));
        }
        
        if (criteria.getAccountId() != null) {
            predicates.add(cb.equal(order.get("accountId"), criteria.getAccountId()));
        }
        
        if (criteria.getMinValue() != null) {
            Expression<BigDecimal> value = cb.prod(order.get("price"), order.get("quantity"));
            predicates.add(cb.ge(value, criteria.getMinValue()));
        }
        
        if (criteria.getDateFrom() != null && criteria.getDateTo() != null) {
            predicates.add(cb.between(order.get("createdAt"), 
                criteria.getDateFrom(), criteria.getDateTo()));
        }
        
        query.where(predicates.toArray(new Predicate[0]));
        query.orderBy(cb.desc(order.get("createdAt")));
        
        return entityManager.createQuery(query)
            .setMaxResults(criteria.getMaxResults())
            .getResultList();
    }
}

// Service Layer with Transaction Management
@Service
@Transactional
public class OrderService {
    
    private final OrderRepository orderRepository;
    private final ApplicationEventPublisher eventPublisher;
    
    public OrderService(OrderRepository orderRepository,
                       ApplicationEventPublisher eventPublisher) {
        this.orderRepository = orderRepository;
        this.eventPublisher = eventPublisher;
    }
    
    @Transactional(readOnly = true)
    public Page<Order> findOrders(OrderSearchCriteria criteria, Pageable pageable) {
        return orderRepository.findAll(OrderSpecifications.withCriteria(criteria), pageable);
    }
    
    public Order submitOrder(Order order) {
        // Validate order
        validateOrder(order);
        
        // Save with optimistic locking
        Order savedOrder = orderRepository.save(order);
        
        // Publish event
        eventPublisher.publishEvent(new OrderSubmittedEvent(savedOrder));
        
        return savedOrder;
    }
    
    @Retryable(value = {OptimisticLockingFailureException.class}, maxAttempts = 3)
    public Order updateOrderStatus(Long orderId, OrderStatus newStatus) {
        return orderRepository.findById(orderId)
            .map(order -> {
                order.setStatus(newStatus);
                return orderRepository.save(order);
            })
            .orElseThrow(() -> new OrderNotFoundException(orderId));
    }
    
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void processOrderBatch(List<Order> orders) {
        orders.forEach(order -> {
            try {
                processOrder(order);
            } catch (Exception e) {
                // Log error but continue with other orders
                log.error("Failed to process order {}", order.getId(), e);
            }
        });
    }
}`,
    testing: `// Spring Boot Testing for Trading System
@SpringBootTest
@ActiveProfiles("test") 
public class OrderServiceIntegrationTest {
    
    @Autowired
    private OrderService orderService;
    
    @MockBean
    private RiskService riskService;
    
    @Test
    public void shouldSubmitValidOrder() {
        Order order = createValidOrder();
        when(riskService.validateOrder(any())).thenReturn(RiskCheckResult.passed());
        
        Order result = orderService.submitOrder(order);
        
        assertThat(result.getId()).isNotNull();
        assertThat(result.getStatus()).isEqualTo(OrderStatus.PENDING);
    }
}

@WebMvcTest(OrderController.class)
public class OrderControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private OrderService orderService;
    
    @Test
    public void shouldSubmitOrderViaREST() throws Exception {
        mockMvc.perform(post("/api/orders")
                .contentType(MediaType.APPLICATION_JSON)
                .content(toJson(createOrderRequest())))
            .andExpected(status().isCreated())
            .andExpect(jsonPath("$.symbol").value("AAPL"));
    }
}`,
    'spring-batch': `// Spring Batch for Trading Settlement
@Configuration
@EnableBatchProcessing
public class TradingBatchConfig {
    
    @Bean
    public Job dailySettlementJob(JobBuilderFactory jobs, StepBuilderFactory steps) {
        return jobs.get("dailySettlementJob")
            .start(loadTradesStep(steps))
            .next(calculateSettlementStep(steps))
            .build();
    }
    
    @Bean
    public Step loadTradesStep(StepBuilderFactory steps) {
        return steps.get("loadTradesStep")
            .<Trade, Trade>chunk(1000)
            .reader(tradeItemReader())
            .processor(tradeProcessor())
            .writer(settledTradeWriter())
            .build();
    }
}`
  };

  return (
    <div style={{
      padding: '40px',
      paddingTop: '100px',
      minHeight: '100vh',
      backgroundColor: '#0f172a',
      color: 'white'
    }}>
      <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
        <div style={{ marginBottom: '40px' }}>
          <h1 style={{ fontSize: '36px', fontWeight: 'bold', marginBottom: '16px', color: '#10b981' }}>
            Spring Boot
          </h1>
          <p style={{ fontSize: '18px', color: '#94a3b8' }}>
            Opinionated framework for building production-ready applications with minimal configuration
          </p>
        </div>

        <div style={{ display: 'flex', gap: '20px', marginBottom: '40px' }}>
          {['overview', 'definition', 'code', 'deployment', 'best-practices'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              style={{
                padding: '12px 24px',
                backgroundColor: activeTab === tab ? '#10b981' : 'transparent',
                border: `2px solid ${activeTab === tab ? '#10b981' : '#374151'}`,
                borderRadius: '8px',
                color: activeTab === tab ? 'white' : '#9ca3af',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                textTransform: 'capitalize',
                transition: 'all 0.2s'
              }}
            >
              {tab}
            </button>
          ))}
        </div>

        {activeTab === 'definition' && (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <div style={{ marginBottom: '20px' }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px' }}>
                {selectedTopic === 'auto-configuration' ? 'Auto-Configuration' :
                 selectedTopic === 'starters' ? 'Spring Boot Starters' :
                 selectedTopic === 'actuator' ? 'Spring Boot Actuator' :
                 selectedTopic === 'microservices' ? 'Microservices' :
                 selectedTopic === 'data-jpa' ? 'Spring Data JPA' :
                 'Testing'} Definition
              </h3>
            </div>
            
            {selectedTopic === 'auto-configuration' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What is Auto-Configuration?</h4>
                <p style={{ marginBottom: '16px' }}>
                  Auto-configuration is Spring Boot's ability to automatically configure application components based on dependencies found in the classpath, reducing the need for explicit configuration. It follows "convention over configuration" principles.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why is it valuable for Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>Rapidly prototype and deploy trading applications</li>
                  <li>Reduce boilerplate configuration for common components</li>
                  <li>Ensure consistent setup across different environments</li>
                  <li>Focus on business logic rather than infrastructure setup</li>
                  <li>Easily override defaults when custom behavior is needed</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to leverage it effectively?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Use @ConditionalOn* annotations for smart configuration</li>
                  <li>Create custom auto-configuration for trading-specific components</li>
                  <li>Externalize configuration through properties files</li>
                  <li>Use profiles for environment-specific settings</li>
                </ul>
              </div>
            )}
            
            {selectedTopic === 'actuator' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What is Spring Boot Actuator?</h4>
                <p style={{ marginBottom: '16px' }}>
                  Spring Boot Actuator provides production-ready features like health checks, metrics, monitoring, and management endpoints. It gives insights into application behavior and enables operational management.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why is it critical for Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>Monitor trading system health and performance in real-time</li>
                  <li>Track business metrics like order volume, execution times</li>
                  <li>Enable operational control (pause trading, cancel orders)</li>
                  <li>Provide audit trails and compliance reporting</li>
                  <li>Support automated alerting and incident response</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to implement comprehensive monitoring?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Create custom health indicators for trading components</li>
                  <li>Define business-specific metrics and counters</li>
                  <li>Implement custom actuator endpoints for trading operations</li>
                  <li>Secure management endpoints appropriately</li>
                </ul>
              </div>
            )}
          </div>
        )}

        {activeTab === 'overview' && (
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>
            {topics.map((topic) => (
              <div
                key={topic.id}
                onClick={() => setSelectedTopic(topic)}
                style={{
                  padding: '24px',
                  backgroundColor: selectedTopic?.id === topic.id ? 'rgba(16, 185, 129, 0.1)' : 'rgba(31, 41, 55, 0.5)',
                  border: `2px solid ${selectedTopic?.id === topic.id ? '#10b981' : 'transparent'}`,
                  borderRadius: '12px',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '12px' }}>
                  <div style={{ color: topic.color }}>{topic.icon}</div>
                  <h3 style={{ fontSize: '20px', fontWeight: '600' }}>{topic.name}</h3>
                </div>
                <p style={{ color: '#94a3b8', fontSize: '14px' }}>{topic.description}</p>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'code' && (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <div style={{ marginBottom: '20px' }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px', textTransform: 'capitalize' }}>
                {selectedTopic ? selectedTopic.replace('-', ' ') : 'Spring Boot'} Example
              </h3>
            </div>
            <SyntaxHighlighter
              language="java"
              style={oneDark}
              customStyle={{
                backgroundColor: '#1e293b',
                padding: '20px',
                borderRadius: '8px',
                fontSize: '14px',
                lineHeight: '1.6'
              }}
            >
              {selectedTopic ? codeExamples[selectedTopic] : '// Select a topic to view code examples'}
            </SyntaxHighlighter>
          </div>
        )}

        {activeTab === 'deployment' && (
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
            <div style={{
              padding: '24px',
              backgroundColor: 'rgba(31, 41, 55, 0.5)',
              borderRadius: '12px',
              border: '1px solid #374151'
            }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px', color: '#10b981' }}>
                Deployment Options
              </h3>
              <ul style={{ listStyle: 'none', padding: 0 }}>
                {[
                  'Executable JAR with embedded server',
                  'Docker containers for microservices',
                  'Kubernetes deployments with health checks',
                  'Traditional WAR deployment',
                  'GraalVM native images for performance',
                  'Cloud-native with Spring Cloud'
                ].map((option, index) => (
                  <li key={index} style={{ padding: '8px 0', color: '#94a3b8', fontSize: '14px' }}>
                    <span style={{ color: '#10b981', marginRight: '8px' }}>•</span>
                    {option}
                  </li>
                ))}
              </ul>
            </div>
            <div style={{
              padding: '24px',
              backgroundColor: 'rgba(31, 41, 55, 0.5)',
              borderRadius: '12px',
              border: '1px solid #374151'
            }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px', color: '#3b82f6' }}>
                Production Considerations
              </h3>
              <ul style={{ listStyle: 'none', padding: 0 }}>
                {[
                  'JVM tuning for trading workloads',
                  'Connection pool optimization',
                  'Circuit breakers for external services',
                  'Monitoring and alerting setup',
                  'Graceful shutdown handling',
                  'Configuration externalization'
                ].map((consideration, index) => (
                  <li key={index} style={{ padding: '8px 0', color: '#94a3b8', fontSize: '14px' }}>
                    <span style={{ color: '#3b82f6', marginRight: '8px' }}>•</span>
                    {consideration}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {activeTab === 'best-practices' && (
          <div style={{
            padding: '24px',
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            border: '1px solid #374151'
          }}>
            <h3 style={{ fontSize: '24px', fontWeight: '600', marginBottom: '20px', color: '#10b981' }}>
              Spring Boot Best Practices
            </h3>
            <div style={{ display: 'grid', gap: '16px' }}>
              {[
                {
                  title: 'Externalize Configuration',
                  description: 'Use application.properties/yml and environment variables for all configuration'
                },
                {
                  title: 'Use Appropriate Starters',
                  description: 'Choose the right starter dependencies and avoid unnecessary ones'
                },
                {
                  title: 'Implement Health Checks',
                  description: 'Create custom health indicators for business-critical components'
                },
                {
                  title: 'Monitor Everything',
                  description: 'Use Actuator endpoints and custom metrics for comprehensive monitoring'
                },
                {
                  title: 'Secure Management Endpoints',
                  description: 'Protect actuator endpoints with proper authentication and authorization'
                }
              ].map((practice, index) => (
                <div key={index} style={{
                  padding: '16px',
                  backgroundColor: '#1e293b',
                  borderRadius: '8px',
                  borderLeft: '4px solid #10b981'
                }}>
                  <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '8px' }}>
                    {practice.title}
                  </h4>
                  <p style={{ color: '#94a3b8', fontSize: '14px' }}>
                    {practice.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      
        {/* Details Panel - Moved to bottom */}
      {selectedTopic && (
        <div style={{
          backgroundColor: 'rgba(31, 41, 55, 0.5)',
          borderRadius: '12px',
          border: '1px solid #374151',
          padding: '24px',
          marginTop: '32px'
                  }}>
          {/* Panel Header */}
          <div style={{
            padding: '20px',
            borderBottom: '1px solid #374151',
            background: 'linear-gradient(135deg, #1f2937 0%, #111827 100%)'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start' }}>
              <div>
                <h2 style={{ fontSize: '18px', fontWeight: 'bold', color: '#fbbf24', margin: '0 0 8px 0' }}>
                  {selectedTopic.name}
                </h2>
                <div style={{ fontSize: '12px', color: '#9ca3af' }}>
                  {selectedTopic.description}
                </div>
              </div>
              <button
                onClick={() => setSelectedTopic(null)}
                style={{
                  background: 'transparent',
                  border: 'none',
                  color: '#9ca3af',
                  fontSize: '24px',
                  cursor: 'pointer',
                  padding: 0,
                  lineHeight: 1
                }}
              >
                ×
              </button>
            </div>
          </div>

          {/* Spring Boot Specific Tabs */}
          <div style={{ display: 'flex', borderBottom: '1px solid #374151' }}>
            {['overview', 'starters', 'properties', 'deployment'].map(tab => (
              <button
                key={tab}
                onClick={() => setDetailsTab(tab)}
                style={{
                  flex: 1,
                  padding: '12px',
                  background: detailsTab === tab ? '#1f2937' : 'transparent',
                  border: 'none',
                  color: detailsTab === tab ? '#fbbf24' : '#9ca3af',
                  fontSize: '12px',
                  fontWeight: '600',
                  textTransform: 'capitalize',
                  cursor: 'pointer'
                }}
              >
                {tab}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div style={{ flex: 1, padding: '20px', overflowY: 'auto' }}>
            {detailsTab === 'overview' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  Spring Boot {selectedTopic.name} Overview
                </h3>
                <div style={{ color: '#d1d5db', fontSize: '13px', lineHeight: '1.8' }}>
                  {selectedTopic.id === 'auto-configuration' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Auto-configuration automatically configures Spring applications based on classpath dependencies, reducing boilerplate and enabling rapid development of trading systems.
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Trading System Benefits:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>Zero-configuration database connections</li>
                        <li>Automatic JPA repository setup</li>
                        <li>Pre-configured security for trading APIs</li>
                        <li>Built-in metrics and monitoring</li>
                      </ul>
                    </>
                  )}
                  {selectedTopic.id === 'starters' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Spring Boot Starters provide curated dependency sets for specific use cases, simplifying dependency management for trading applications.
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Essential Trading Starters:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>spring-boot-starter-web → REST APIs</li>
                        <li>spring-boot-starter-data-jpa → Database access</li>
                        <li>spring-boot-starter-security → Authentication</li>
                        <li>spring-boot-starter-actuator → Monitoring</li>
                        <li>spring-boot-starter-cache → Performance optimization</li>
                      </ul>
                    </>
                  )}
                  {selectedTopic.id === 'actuator' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Spring Boot Actuator provides production-ready features for monitoring and managing trading applications, including health checks and metrics.
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Trading System Monitoring:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>Health checks for market data connections</li>
                        <li>Custom metrics for order processing rates</li>
                        <li>Trade execution latency monitoring</li>
                        <li>Risk limit breach alerting</li>
                      </ul>
                    </>
                  )}
                  {selectedTopic.id === 'microservices' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Spring Boot enables microservices architecture for trading systems, allowing independent scaling and deployment of different trading components.
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Trading Microservices:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>Order Management Service</li>
                        <li>Risk Management Service</li>
                        <li>Market Data Service</li>
                        <li>Position Management Service</li>
                        <li>Reporting and Analytics Service</li>
                      </ul>
                    </>
                  )}
                  {selectedTopic.id === 'data-jpa' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Spring Data JPA provides repository abstractions and query methods, simplifying data access for trading entities like orders, trades, and positions.
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Trading Data Access:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>Repository interfaces for all trading entities</li>
                        <li>Custom query methods for complex searches</li>
                        <li>Pagination support for large datasets</li>
                        <li>Auditing for regulatory compliance</li>
                      </ul>
                    </>
                  )}
                  {selectedTopic.id === 'testing' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Spring Boot provides comprehensive testing support with test slices, enabling focused testing of specific layers in trading applications.
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Trading System Testing:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>@WebMvcTest for REST API testing</li>
                        <li>@DataJpaTest for repository testing</li>
                        <li>@SpringBootTest for integration testing</li>
                        <li>TestContainers for database testing</li>
                      </ul>
                    </>
                  )}
                </div>
              </div>
            )}

            {detailsTab === 'starters' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  Essential Trading System Starters
                </h3>
                <div style={{ display: 'grid', gap: '12px' }}>
                  {[
                    { 
                      starter: 'spring-boot-starter-web',
                      purpose: 'REST APIs for order submission, market data, portfolio queries',
                      includes: 'Spring MVC, Tomcat, Jackson JSON'
                    },
                    { 
                      starter: 'spring-boot-starter-data-jpa',
                      purpose: 'Data persistence for orders, trades, positions, market data',
                      includes: 'Hibernate, Spring Data JPA, HikariCP'
                    },
                    { 
                      starter: 'spring-boot-starter-security',
                      purpose: 'Authentication and authorization for trading functions',
                      includes: 'Spring Security, OAuth2, JWT support'
                    },
                    { 
                      starter: 'spring-boot-starter-actuator',
                      purpose: 'Production monitoring, health checks, metrics',
                      includes: 'Micrometer, health endpoints, metrics collection'
                    },
                    { 
                      starter: 'spring-boot-starter-cache',
                      purpose: 'Performance optimization for market data and positions',
                      includes: 'Spring Cache, Redis, Caffeine'
                    },
                    { 
                      starter: 'spring-boot-starter-validation',
                      purpose: 'Order validation, risk parameter validation',
                      includes: 'Hibernate Validator, Bean Validation'
                    }
                  ].map((item, idx) => (
                    <div key={idx} style={{
                      padding: '16px',
                      background: '#1f2937',
                      borderRadius: '8px',
                      borderLeft: `4px solid ${selectedTopic.color}`
                    }}>
                      <div style={{ color: '#fbbf24', fontSize: '13px', fontWeight: 'bold', fontFamily: 'monospace', marginBottom: '8px' }}>
                        {item.starter}
                      </div>
                      <div style={{ color: '#d1d5db', fontSize: '12px', marginBottom: '6px' }}>
                        <strong>Purpose:</strong> {item.purpose}
                      </div>
                      <div style={{ color: '#9ca3af', fontSize: '11px' }}>
                        <strong>Includes:</strong> {item.includes}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {detailsTab === 'properties' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  Trading System Configuration
                </h3>
                <div style={{
                  background: '#0f172a',
                  padding: '16px',
                  borderRadius: '8px',
                  maxHeight: '600px',
                  overflow: 'auto'
                }}>
                  <SyntaxHighlighter
                    language="yaml"
                    style={oneDark}
                    customStyle={{
                      background: 'transparent',
                      padding: 0,
                      margin: 0,
                      fontSize: '12px',
                      lineHeight: '1.5'
                    }}
                  >
                    {`# application.yml - Trading System Configuration
spring:
  application:
    name: trading-system
  
  datasource:
    url: *******************************************
    username: trading_user
    password: \${TRADING_DB_PASSWORD}
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10
      connection-timeout: 30000
      idle-timeout: 300000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        jdbc:
          batch_size: 50
  
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=10000,expireAfterWrite=5m
  
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://auth.trading-system.com

# Trading-specific configuration
trading:
  risk:
    enabled: true
    max-position-size: 1000000
    daily-var-limit: 100000
    real-time-checks: true
  
  market-data:
    providers:
      - bloomberg
      - reuters
    refresh-interval: 100ms
  
  orders:
    processing:
      batch-size: 100
      timeout: 30s
    validation:
      strict-mode: true

# Actuator configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# Logging configuration
logging:
  level:
    com.trading: INFO
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/trading-system.log`}
                  </SyntaxHighlighter>
                </div>
              </div>
            )}

            {detailsTab === 'deployment' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  Production Deployment Strategy
                </h3>
                <div style={{ display: 'grid', gap: '16px' }}>
                  <div style={{
                    padding: '16px',
                    background: '#1f2937',
                    borderRadius: '8px',
                    borderLeft: '4px solid #10b981'
                  }}>
                    <h4 style={{ color: '#fbbf24', fontSize: '14px', marginBottom: '8px' }}>
                      Container Deployment
                    </h4>
                    <ul style={{ color: '#d1d5db', fontSize: '12px', paddingLeft: '16px' }}>
                      <li>Docker containers for consistent environments</li>
                      <li>Kubernetes orchestration for scaling</li>
                      <li>Health checks and readiness probes</li>
                      <li>Rolling updates for zero-downtime deployment</li>
                    </ul>
                  </div>
                  
                  <div style={{
                    padding: '16px',
                    background: '#1f2937',
                    borderRadius: '8px',
                    borderLeft: '4px solid #3b82f6'
                  }}>
                    <h4 style={{ color: '#fbbf24', fontSize: '14px', marginBottom: '8px' }}>
                      Performance Optimization
                    </h4>
                    <ul style={{ color: '#d1d5db', fontSize: '12px', paddingLeft: '16px' }}>
                      <li>JVM tuning for low-latency trading</li>
                      <li>Connection pooling optimization</li>
                      <li>Caching strategy for market data</li>
                      <li>GC tuning for consistent performance</li>
                    </ul>
                  </div>
                  
                  <div style={{
                    padding: '16px',
                    background: '#1f2937',
                    borderRadius: '8px',
                    borderLeft: '4px solid #f59e0b'
                  }}>
                    <h4 style={{ color: '#fbbf24', fontSize: '14px', marginBottom: '8px' }}>
                      Monitoring & Observability
                    </h4>
                    <ul style={{ color: '#d1d5db', fontSize: '12px', paddingLeft: '16px' }}>
                      <li>Prometheus metrics collection</li>
                      <li>Grafana dashboards for trading metrics</li>
                      <li>Distributed tracing with Zipkin</li>
                      <li>Log aggregation with ELK stack</li>
                    </ul>
                  </div>
                  
                  <div style={{
                    padding: '16px',
                    background: '#1f2937',
                    borderRadius: '8px',
                    borderLeft: '4px solid #ec4899'
                  }}>
                    <h4 style={{ color: '#fbbf24', fontSize: '14px', marginBottom: '8px' }}>
                      Security & Compliance
                    </h4>
                    <ul style={{ color: '#d1d5db', fontSize: '12px', paddingLeft: '16px' }}>
                      <li>SSL/TLS termination at load balancer</li>
                      <li>Secret management with Vault</li>
                      <li>Audit logging for regulatory compliance</li>
                      <li>Network segmentation and firewalls</li>
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default JavaSpringBoot;
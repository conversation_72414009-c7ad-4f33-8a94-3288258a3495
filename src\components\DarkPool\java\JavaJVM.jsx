import React, { useState } from 'react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Gauge, Cpu, Database, Activity, TrendingUp, Settings } from 'lucide-react';

const JavaJVM = () => {
  const [selectedTopic, setSelectedTopic] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [detailsTab, setDetailsTab] = useState('overview');

  const topics = [
    {
      id: 'architecture',
      name: 'JVM Architecture',
      icon: <Cpu size={20} />,
      description: 'Class loading, bytecode, JIT',
      color: '#ef4444'
    },
    {
      id: 'tuning',
      name: 'JVM Tuning',
      icon: <Settings size={20} />,
      description: 'Heap sizing, GC tuning, flags',
      color: '#3b82f6'
    },
    {
      id: 'profiling',
      name: 'Profiling & Monitoring',
      icon: <Activity size={20} />,
      description: 'Tools and techniques',
      color: '#10b981'
    },
    {
      id: 'optimization',
      name: 'Performance Optimization',
      icon: <TrendingUp size={20} />,
      description: 'Hotspot optimizations',
      color: '#f59e0b'
    },
    {
      id: 'latency',
      name: 'Low Latency',
      icon: <Gauge size={20} />,
      description: 'Ultra-low latency techniques',
      color: '#8b5cf6'
    }
  ];

  const codeExamples = {
    architecture: `// JVM Architecture and Class Loading
public class JVMArchitecture {
    
    // Custom ClassLoader for Trading Strategies
    public static class StrategyClassLoader extends ClassLoader {
        private final Map<String, byte[]> classData = new HashMap<>();
        
        @Override
        protected Class<?> findClass(String name) throws ClassNotFoundException {
            byte[] data = classData.get(name);
            if (data == null) {
                throw new ClassNotFoundException(name);
            }
            return defineClass(name, data, 0, data.length);
        }
        
        public void loadStrategyJar(String jarPath) throws IOException {
            try (JarFile jar = new JarFile(jarPath)) {
                Enumeration<JarEntry> entries = jar.entries();
                while (entries.hasMoreElements()) {
                    JarEntry entry = entries.nextElement();
                    if (entry.getName().endsWith(".class")) {
                        byte[] data = jar.getInputStream(entry).readAllBytes();
                        String className = entry.getName()
                            .replace('/', '.')
                            .replace(".class", "");
                        classData.put(className, data);
                    }
                }
            }
        }
    }
    
    // JIT Compilation Hints
    public class HotPathOptimization {
        // Frequently called method - will be JIT compiled
        @HotSpotIntrinsicCandidate  // Hint for intrinsic optimization
        public final double calculatePrice(double bid, double ask) {
            return (bid + ask) / 2.0;  // Simple operation for inlining
        }
        
        // Force compilation with CompileCommand
        // -XX:CompileCommand=compileonly,*HotPathOptimization.processOrder
        public void processOrder(Order order) {
            // Critical path code
        }
    }
    
    // Bytecode generation at runtime
    public class DynamicTradingRule {
        public static byte[] generateRuleClass(String condition) {
            ClassWriter cw = new ClassWriter(ClassWriter.COMPUTE_FRAMES);
            cw.visit(V11, ACC_PUBLIC, "DynamicRule", null, 
                "java/lang/Object", new String[]{"TradingRule"});
            
            // Generate evaluate method
            MethodVisitor mv = cw.visitMethod(
                ACC_PUBLIC, "evaluate", "(LMarketData;)Z", null, null);
            mv.visitCode();
            // ... bytecode generation based on condition
            mv.visitEnd();
            
            return cw.toByteArray();
        }
    }
}`,
    tuning: `// JVM Tuning for Trading Systems
// Startup flags for low-latency trading application:
/*
java -server \
  -Xmx8g -Xms8g \                    # Fixed heap size
  -XX:+UseG1GC \                     # G1 garbage collector
  -XX:MaxGCPauseMillis=10 \          # Target 10ms pauses
  -XX:+ParallelRefProcEnabled \      # Parallel reference processing
  -XX:+UseStringDeduplication \      # Reduce string memory
  -XX:+AlwaysPreTouch \              # Pre-touch memory pages
  -XX:+UseNUMA \                     # NUMA awareness
  -XX:+UseLargePages \               # Large memory pages
  -XX:-UseBiasedLocking \            # Disable biased locking
  -XX:+UseCompressedOops \           # Compressed object pointers
  -XX:CompileThreshold=1000 \        # JIT compile threshold
  -XX:+PrintCompilation \            # Log JIT compilation
  -XX:+UnlockDiagnosticVMOptions \
  -XX:+PrintInlining \               # Log method inlining
  -XX:+LogCompilation \              # Detailed compilation log
  -XX:LogFile=jvm.log \
  TradingApplication
*/

public class JVMTuning {
    
    // NUMA-aware memory allocation
    public class NUMAOptimizedBuffer {
        private final ByteBuffer[] buffers;
        private final int numNodes;
        
        public NUMAOptimizedBuffer(int sizePerNode) {
            this.numNodes = Runtime.getRuntime().availableProcessors() / 8;
            this.buffers = new ByteBuffer[numNodes];
            
            for (int i = 0; i < numNodes; i++) {
                // Allocate on specific NUMA node
                buffers[i] = ByteBuffer.allocateDirect(sizePerNode);
            }
        }
        
        public ByteBuffer getBuffer(int threadId) {
            return buffers[threadId % numNodes];
        }
    }
    
    // Large pages configuration
    public class LargePageBuffer {
        static {
            // Requires: -XX:+UseLargePages -XX:LargePageSizeInBytes=2m
            System.out.println("Large pages enabled: " + 
                ManagementFactory.getOperatingSystemMXBean()
                    .getCommittedVirtualMemorySize());
        }
        
        // Align data structures to page boundaries
        private static final int PAGE_SIZE = 2 * 1024 * 1024; // 2MB
        private final ByteBuffer alignedBuffer;
        
        public LargePageBuffer(int size) {
            int aligned = ((size + PAGE_SIZE - 1) / PAGE_SIZE) * PAGE_SIZE;
            alignedBuffer = ByteBuffer.allocateDirect(aligned);
        }
    }
}`,
    profiling: `// JVM Profiling and Monitoring
public class JVMProfiling {
    
    // JMX Monitoring
    public class TradingSystemMonitor {
        private final MBeanServer mbs = ManagementFactory.getPlatformMBeanServer();
        
        public void registerMonitoring() throws Exception {
            // Register custom MBean
            ObjectName name = new ObjectName("com.trading:type=Performance");
            PerformanceMonitor monitor = new PerformanceMonitor();
            mbs.registerMBean(monitor, name);
        }
        
        @MXBean
        public interface PerformanceMonitorMXBean {
            long getOrdersProcessed();
            double getAverageLatency();
            long getMemoryUsed();
        }
        
        public class PerformanceMonitor implements PerformanceMonitorMXBean {
            private final AtomicLong ordersProcessed = new AtomicLong();
            private final DescriptiveStatistics latencyStats = 
                new DescriptiveStatistics(1000);
            
            @Override
            public long getOrdersProcessed() {
                return ordersProcessed.get();
            }
            
            @Override
            public double getAverageLatency() {
                return latencyStats.getMean();
            }
            
            @Override
            public long getMemoryUsed() {
                return Runtime.getRuntime().totalMemory() - 
                       Runtime.getRuntime().freeMemory();
            }
        }
    }
    
    // Flight Recorder Events
    @Label("Order Processing")
    @Category("Trading")
    @Description("Order processing event")
    public class OrderEvent extends Event {
        @Label("Order ID")
        String orderId;
        
        @Label("Processing Time")
        @Timespan(Timespan.NANOSECONDS)
        long duration;
        
        @Label("Success")
        boolean success;
    }
    
    // Profiling with JFR
    public void profileOrderProcessing(Order order) {
        OrderEvent event = new OrderEvent();
        event.orderId = order.getId();
        event.begin();
        
        try {
            processOrder(order);
            event.success = true;
        } catch (Exception e) {
            event.success = false;
        } finally {
            event.end();
            event.commit();
        }
    }
    
    // Memory profiling
    public void analyzeMemory() {
        List<MemoryPoolMXBean> pools = ManagementFactory.getMemoryPoolMXBeans();
        
        for (MemoryPoolMXBean pool : pools) {
            System.out.printf("%s: Used=%d MB, Max=%d MB%n",
                pool.getName(),
                pool.getUsage().getUsed() / 1024 / 1024,
                pool.getUsage().getMax() / 1024 / 1024);
        }
        
        // Trigger heap dump programmatically
        HotSpotDiagnosticMXBean bean = ManagementFactory.newPlatformMXBeanProxy(
            ManagementFactory.getPlatformMBeanServer(),
            "com.sun.management:type=HotSpotDiagnostic",
            HotSpotDiagnosticMXBean.class);
        
        bean.dumpHeap("/tmp/heap.hprof", true);
    }
}`,
    optimization: `// Performance Optimization Techniques
public class PerformanceOptimization {
    
    // CPU Cache Optimization - False Sharing Prevention
    @Contended  // Requires -XX:-RestrictContended
    public static class PaddedCounter {
        private volatile long value;
        
        public void increment() {
            value++;
        }
        
        public long get() {
            return value;
        }
    }
    
    // Branch Prediction Optimization
    public class BranchOptimized {
        // Sort data to improve branch prediction
        public double calculateTotalValue(List<Order> orders) {
            // Sort by side to minimize branch misprediction
            orders.sort(Comparator.comparing(Order::getSide));
            
            double total = 0;
            for (Order order : orders) {
                // Predictable branch pattern after sorting
                if (order.getSide() == Side.BUY) {
                    total += order.getValue();
                } else {
                    total -= order.getValue();
                }
            }
            return total;
        }
        
        // Branchless computation
        public int branchlessMax(int a, int b) {
            // Avoid if-else for better performance
            return a ^ ((a ^ b) & -(a < b ? 1 : 0));
        }
    }
    
    // Escape Analysis Optimization
    public class EscapeAnalysis {
        // Object doesn't escape - can be stack allocated
        public double calculateMidPrice(double bid, double ask) {
            PricePoint point = new PricePoint(bid, ask);  // Stack allocated
            return point.getMidPrice();
        }
        
        private static class PricePoint {
            private final double bid;
            private final double ask;
            
            PricePoint(double bid, double ask) {
                this.bid = bid;
                this.ask = ask;
            }
            
            double getMidPrice() {
                return (bid + ask) / 2.0;
            }
        }
    }
    
    // Intrinsics and Vectorization
    public class VectorizedOperations {
        // Use vector API (Java 16+ preview)
        public void vectorizedPriceUpdate(double[] prices, double factor) {
            // JVM will auto-vectorize this loop with SIMD instructions
            for (int i = 0; i < prices.length; i++) {
                prices[i] *= factor;
            }
        }
        
        // Manual unrolling for better performance
        public double sumPrices(double[] prices) {
            double sum1 = 0, sum2 = 0, sum3 = 0, sum4 = 0;
            int i;
            
            // Process 4 elements at a time
            for (i = 0; i < prices.length - 3; i += 4) {
                sum1 += prices[i];
                sum2 += prices[i + 1];
                sum3 += prices[i + 2];
                sum4 += prices[i + 3];
            }
            
            // Handle remaining elements
            double sum = sum1 + sum2 + sum3 + sum4;
            for (; i < prices.length; i++) {
                sum += prices[i];
            }
            
            return sum;
        }
    }
}`,
    latency: `// Ultra-Low Latency Techniques
public class UltraLowLatency {
    
    // Lock-free ring buffer for IPC
    public class LockFreeRingBuffer {
        private final int capacity;
        private final Object[] buffer;
        private final AtomicLong writeSequence = new AtomicLong();
        private final AtomicLong readSequence = new AtomicLong();
        
        public LockFreeRingBuffer(int capacity) {
            this.capacity = Integer.highestOneBit(capacity) * 2; // Power of 2
            this.buffer = new Object[this.capacity];
        }
        
        public boolean offer(Order order) {
            long currentWrite = writeSequence.get();
            long nextWrite = currentWrite + 1;
            
            // Check if buffer is full
            if (nextWrite - readSequence.get() > capacity) {
                return false;
            }
            
            // CAS to claim slot
            if (writeSequence.compareAndSet(currentWrite, nextWrite)) {
                int index = (int)(currentWrite & (capacity - 1));
                buffer[index] = order;
                return true;
            }
            return false;
        }
    }
    
    // Memory-mapped file for zero-copy
    public class MemoryMappedJournal {
        private final RandomAccessFile file;
        private final MappedByteBuffer buffer;
        
        public MemoryMappedJournal(String path, long size) throws IOException {
            file = new RandomAccessFile(path, "rw");
            buffer = file.getChannel().map(
                FileChannel.MapMode.READ_WRITE, 0, size);
        }
        
        public void writeOrder(Order order) {
            // Direct memory write, no system call
            buffer.putLong(order.getId());
            buffer.putDouble(order.getPrice());
            buffer.putInt(order.getQuantity());
            buffer.force(); // Ensure write to disk
        }
    }
    
    // Busy spinning for ultra-low latency
    public class BusySpinProcessor {
        private volatile Order pendingOrder;
        
        public void processOrders() {
            Thread.currentThread().setPriority(Thread.MAX_PRIORITY);
            
            // Pin to CPU core (requires JNI or external library)
            // Affinity.setAffinity(1 << coreId);
            
            while (!Thread.currentThread().isInterrupted()) {
                // Busy wait - no context switching
                Order order = pendingOrder;
                if (order != null) {
                    processOrder(order);
                    pendingOrder = null;
                } else {
                    // CPU pause instruction to reduce power consumption
                    Thread.onSpinWait(); // Java 9+
                }
            }
        }
    }
    
    // Off-heap memory to avoid GC
    public class OffHeapOrderBook {
        private static final int ORDER_SIZE = 32; // bytes per order
        private final ByteBuffer buffer;
        private final Unsafe unsafe;
        
        public OffHeapOrderBook(int capacity) {
            buffer = ByteBuffer.allocateDirect(capacity * ORDER_SIZE);
            
            // Get Unsafe instance (requires --add-opens)
            Field f = Unsafe.class.getDeclaredField("theUnsafe");
            f.setAccessible(true);
            unsafe = (Unsafe) f.get(null);
        }
        
        public void putOrder(int index, Order order) {
            long address = ((DirectBuffer)buffer).address();
            long offset = address + (index * ORDER_SIZE);
            
            // Direct memory access
            unsafe.putLong(offset, order.getId());
            unsafe.putDouble(offset + 8, order.getPrice());
            unsafe.putInt(offset + 16, order.getQuantity());
        }
    }
}`
  };

  return (
    <div style={{
      padding: '40px',
      paddingTop: '100px',
      minHeight: '100vh',
      backgroundColor: '#0f172a',
      color: 'white'
    }}>
      <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
        <div style={{ marginBottom: '40px' }}>
          <h1 style={{ fontSize: '36px', fontWeight: 'bold', marginBottom: '16px', color: '#10b981' }}>
            JVM Internals & Performance
          </h1>
          <p style={{ fontSize: '18px', color: '#94a3b8' }}>
            JVM architecture, tuning, profiling, and ultra-low latency techniques
          </p>
        </div>

        <div style={{ display: 'flex', gap: '20px', marginBottom: '40px' }}>
          {['overview', 'definition', 'code', 'flags', 'tools'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              style={{
                padding: '12px 24px',
                backgroundColor: activeTab === tab ? '#10b981' : 'transparent',
                border: `2px solid ${activeTab === tab ? '#10b981' : '#374151'}`,
                borderRadius: '8px',
                color: activeTab === tab ? 'white' : '#9ca3af',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                textTransform: 'capitalize',
                transition: 'all 0.2s'
              }}
            >
              {tab}
            </button>
          ))}
        </div>

        {activeTab === 'definition' && (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <div style={{ marginBottom: '20px' }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px' }}>
                {selectedTopic === 'architecture' ? 'JVM Architecture' :
                 selectedTopic === 'tuning' ? 'JVM Tuning' :
                 selectedTopic === 'profiling' ? 'Profiling & Monitoring' :
                 selectedTopic === 'optimization' ? 'Performance Optimization' :
                 'Low Latency'} Definition
              </h3>
            </div>
            
            {selectedTopic === 'architecture' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What is JVM Architecture?</h4>
                <p style={{ marginBottom: '16px' }}>
                  The JVM (Java Virtual Machine) architecture consists of class loaders, runtime data areas (heap, stack, method area), execution engine with JIT compiler, and native method interfaces. It provides platform independence and memory management.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why is it critical for Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>Understanding helps optimize performance for low-latency trading</li>
                  <li>JIT compilation can dramatically improve hot code paths</li>
                  <li>Memory layout affects cache performance and GC behavior</li>
                  <li>Class loading strategies impact startup time and memory usage</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to leverage it effectively?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Warm up critical code paths for JIT optimization</li>
                  <li>Use profilers to understand JIT compilation behavior</li>
                  <li>Configure class loading for dynamic strategy deployment</li>
                  <li>Monitor and tune different memory areas independently</li>
                </ul>
              </div>
            )}
            
            {selectedTopic === 'tuning' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What is JVM Tuning?</h4>
                <p style={{ marginBottom: '16px' }}>
                  JVM tuning involves configuring various parameters to optimize performance, memory usage, and garbage collection behavior for specific application requirements and hardware configurations.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why is it essential for Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>Minimize GC pauses that can cause trade execution delays</li>
                  <li>Optimize memory allocation for high-volume order processing</li>
                  <li>Configure for predictable low-latency performance</li>
                  <li>Maximize throughput while maintaining response times</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to tune it systematically?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Profile application to identify bottlenecks first</li>
                  <li>Choose appropriate GC algorithm for workload</li>
                  <li>Set heap sizes based on memory requirements</li>
                  <li>Test changes under realistic load conditions</li>
                </ul>
              </div>
            )}
          </div>
        )}

        {activeTab === 'overview' && (
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>
            {topics.map((topic) => (
              <div
                key={topic.id}
                onClick={() => setSelectedTopic(topic.id)}
                style={{
                  padding: '24px',
                  backgroundColor: selectedTopic === topic.id ? 'rgba(16, 185, 129, 0.1)' : 'rgba(31, 41, 55, 0.5)',
                  border: `2px solid ${selectedTopic === topic.id ? '#10b981' : 'transparent'}`,
                  borderRadius: '12px',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '12px' }}>
                  <div style={{ color: topic.color }}>{topic.icon}</div>
                  <h3 style={{ fontSize: '20px', fontWeight: '600' }}>{topic.name}</h3>
                </div>
                <p style={{ color: '#94a3b8', fontSize: '14px' }}>{topic.description}</p>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'code' && (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <div style={{ marginBottom: '20px' }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px', textTransform: 'capitalize' }}>
                {selectedTopic} Example
              </h3>
            </div>
            <SyntaxHighlighter
              language="java"
              style={oneDark}
              customStyle={{
                backgroundColor: '#1e293b',
                padding: '20px',
                borderRadius: '8px',
                fontSize: '14px',
                lineHeight: '1.6'
              }}
            >
              {codeExamples[selectedTopic]}
            </SyntaxHighlighter>
          </div>
        )}

        {activeTab === 'flags' && (
          <div style={{ display: 'grid', gap: '20px' }}>
            <div style={{
              padding: '24px',
              backgroundColor: 'rgba(31, 41, 55, 0.5)',
              borderRadius: '12px',
              border: '1px solid #374151'
            }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px', color: '#10b981' }}>
                Essential JVM Flags for Trading Systems
              </h3>
              <div style={{ display: 'grid', gap: '12px' }}>
                {[
                  { flag: '-XX:+UseG1GC', desc: 'G1 garbage collector for predictable pauses' },
                  { flag: '-XX:MaxGCPauseMillis=10', desc: 'Target maximum GC pause time' },
                  { flag: '-XX:+AlwaysPreTouch', desc: 'Pre-touch memory pages at startup' },
                  { flag: '-XX:+UseLargePages', desc: 'Use OS large memory pages' },
                  { flag: '-XX:+UseNUMA', desc: 'Enable NUMA-aware memory allocation' },
                  { flag: '-XX:-UseBiasedLocking', desc: 'Disable biased locking for low latency' },
                  { flag: '-XX:+UseStringDeduplication', desc: 'Reduce memory for duplicate strings' },
                  { flag: '-XX:CompileThreshold=1000', desc: 'JIT compilation threshold' }
                ].map((item, index) => (
                  <div key={index} style={{
                    padding: '12px',
                    backgroundColor: '#1e293b',
                    borderRadius: '6px',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}>
                    <code style={{ color: '#60a5fa', fontFamily: 'monospace' }}>{item.flag}</code>
                    <span style={{ color: '#94a3b8', fontSize: '14px', marginLeft: '16px' }}>{item.desc}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'tools' && (
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
            <div style={{
              padding: '24px',
              backgroundColor: 'rgba(31, 41, 55, 0.5)',
              borderRadius: '12px',
              border: '1px solid #374151'
            }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px', color: '#3b82f6' }}>
                Profiling Tools
              </h3>
              <ul style={{ listStyle: 'none', padding: 0 }}>
                {[
                  'JDK Flight Recorder (JFR)',
                  'JDK Mission Control',
                  'async-profiler',
                  'JProfiler',
                  'YourKit',
                  'Intel VTune'
                ].map((tool, index) => (
                  <li key={index} style={{ padding: '8px 0', color: '#94a3b8', fontSize: '14px' }}>
                    <span style={{ color: '#3b82f6', marginRight: '8px' }}>•</span>
                    {tool}
                  </li>
                ))}
              </ul>
            </div>
            <div style={{
              padding: '24px',
              backgroundColor: 'rgba(31, 41, 55, 0.5)',
              borderRadius: '12px',
              border: '1px solid #374151'
            }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px', color: '#f59e0b' }}>
                Monitoring Commands
              </h3>
              <ul style={{ listStyle: 'none', padding: 0 }}>
                {[
                  'jstat -gc <pid> - GC statistics',
                  'jmap -heap <pid> - Heap summary',
                  'jstack <pid> - Thread dump',
                  'jcmd <pid> GC.run - Force GC',
                  'jinfo -flags <pid> - JVM flags',
                  'jconsole - GUI monitoring'
                ].map((cmd, index) => (
                  <li key={index} style={{ padding: '8px 0', fontSize: '14px' }}>
                    <code style={{ color: '#60a5fa', fontFamily: 'monospace', fontSize: '12px' }}>{cmd}</code>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}
      </div>
      
        {/* Details Panel - Moved to bottom */}
        {selectedTopic && (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            border: '1px solid #374151',
            padding: '24px',
            marginTop: '32px'
          }}>
            <div style={{ marginBottom: '20px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                <h2 style={{ fontSize: '24px', fontWeight: 'bold', color: '#10b981' }}>
                  {topics.find(t => t.id === selectedTopic)?.name}
                </h2>
                <button
                  onClick={() => setSelectedTopic(null)}
                  style={{
                    background: 'none',
                    border: 'none',
                    color: '#9ca3af',
                    fontSize: '24px',
                    cursor: 'pointer'
                  }}
                >
                  ×
                </button>
              </div>
              
              {/* JVM Specific Tabs */}
              <div style={{ display: 'flex', borderBottom: '1px solid #374151' }}>
                {['overview', 'configuration', 'debugging', 'optimization'].map(tab => (
                  <button
                    key={tab}
                    onClick={() => setDetailsTab(tab)}
                    style={{
                      padding: '8px 16px',
                      backgroundColor: detailsTab === tab ? '#10b981' : 'transparent',
                      color: detailsTab === tab ? 'white' : '#9ca3af',
                      border: 'none',
                      borderBottom: detailsTab === tab ? '2px solid #10b981' : '2px solid transparent',
                      cursor: 'pointer',
                      fontSize: '14px',
                      textTransform: 'capitalize',
                      transition: 'all 0.2s'
                    }}
                  >
                    {tab}
                  </button>
                ))}
              </div>
            </div>
            
            {detailsTab === 'overview' && (
              <div>
                <div style={{ marginBottom: '24px' }}>
                  <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '12px', color: '#10b981' }}>
                    JVM for High-Frequency Trading
                  </h3>
                  <p style={{ color: '#e2e8f0', lineHeight: '1.6' }}>
                    {selectedTopic === 'architecture' ? (
                      "Understanding JVM architecture is crucial for trading systems where microseconds matter. The JIT compiler, memory layout, and class loading mechanisms directly impact order processing latency and throughput."
                    ) : selectedTopic === 'tuning' ? (
                      "JVM tuning for trading systems focuses on minimizing garbage collection pauses, optimizing memory allocation patterns, and ensuring predictable performance under high load conditions."
                    ) : selectedTopic === 'profiling' ? (
                      "Profiling and monitoring trading applications requires specialized techniques to identify performance bottlenecks without impacting production performance. Real-time monitoring is essential for maintaining SLAs."
                    ) : selectedTopic === 'optimization' ? (
                      "Performance optimization involves leveraging CPU cache patterns, avoiding allocations in hot paths, and utilizing JIT compiler optimizations to achieve ultra-low latency requirements."
                    ) : (
                      "Ultra-low latency techniques include lock-free programming, off-heap memory management, memory mapping, and direct hardware interaction to minimize processing delays to nanosecond precision."
                    )}
                  </p>
                </div>
                
                <div style={{ marginBottom: '24px' }}>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Trading System Impact</h4>
                  <ul style={{ color: '#e2e8f0', paddingLeft: '20px' }}>
                    {selectedTopic === 'architecture' && [
                      'JIT compilation optimizes hot trading algorithms',
                      'Memory layout affects cache performance for order books',
                      'Class loading enables dynamic strategy deployment',
                      'Runtime data areas impact concurrent trade processing'
                    ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                    
                    {selectedTopic === 'tuning' && [
                      'GC tuning eliminates unpredictable pause times',
                      'Heap sizing prevents out-of-memory failures',
                      'Thread pool configuration optimizes throughput',
                      'NUMA awareness improves multi-core performance'
                    ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                    
                    {selectedTopic === 'profiling' && [
                      'Identifies bottlenecks in order processing pipelines',
                      'Memory leak detection prevents system failures',
                      'Thread contention analysis improves concurrency',
                      'Real-time monitoring enables proactive optimization'
                    ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                    
                    {selectedTopic === 'optimization' && [
                      'CPU cache optimization reduces memory access latency',
                      'Branch prediction improvements speed up algorithms',
                      'Escape analysis enables stack allocation',
                      'Vectorization accelerates bulk calculations'
                    ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                    
                    {selectedTopic === 'latency' && [
                      'Nanosecond precision for market making strategies',
                      'Zero-copy techniques eliminate memory overhead',
                      'Lock-free data structures prevent blocking',
                      'Direct memory access bypasses JVM overhead'
                    ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                  </ul>
                </div>
                
                <div>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#ef4444' }}>Implementation Example</h4>
                  <div style={{
                    backgroundColor: '#1e293b',
                    padding: '16px',
                    borderRadius: '8px',
                    border: '1px solid #374151'
                  }}>
                    <SyntaxHighlighter
                      language="java"
                      style={oneDark}
                      customStyle={{
                        backgroundColor: 'transparent',
                        padding: 0,
                        margin: 0,
                        fontSize: '12px'
                      }}
                    >
                      {selectedTopic === 'architecture' ? `// JIT Optimization for Trading Algorithms
public class OptimizedTradingAlgorithm {
    // Mark methods for aggressive optimization
    @HotSpotIntrinsicCandidate
    public final double calculateMidPrice(double bid, double ask) {
        // Simple operation - will be inlined by JIT
        return (bid + ask) * 0.5;
    }
    
    // Force early compilation
    // -XX:CompileCommand=compileonly,*OptimizedTradingAlgorithm.processOrder
    public void processOrder(Order order) {
        // Critical path - compile immediately
        double midPrice = calculateMidPrice(order.getBid(), order.getAsk());
        executeOrder(order, midPrice);
    }
    
    // Warmup for JIT compilation
    public void warmupAlgorithm() {
        Order dummyOrder = new Order(100.0, 100.1, 1000);
        for (int i = 0; i < 10000; i++) {
            processOrder(dummyOrder); // Trigger JIT compilation
        }
    }
}` : selectedTopic === 'tuning' ? `// Production JVM Flags for Trading System
/*
java -server \
  -Xmx16g -Xms16g \                    # Fixed heap - prevent dynamic sizing
  -XX:+UseG1GC \                       # G1 for predictable pauses
  -XX:MaxGCPauseMillis=5 \             # Target sub-5ms pauses
  -XX:+UseStringDeduplication \        # Reduce string memory usage
  -XX:+AlwaysPreTouch \                # Pre-allocate memory pages
  -XX:+UseTransparentHugePages \       # Use huge pages for heap
  -XX:+UseLargePages \                 # OS large pages
  -XX:LargePageSizeInBytes=2m \        # 2MB pages
  -XX:+UseNUMA \                       # NUMA-aware allocation
  -XX:-UseBiasedLocking \              # Disable for low latency
  -XX:CompileThreshold=500 \           # Aggressive JIT compilation
  -XX:+UnlockExperimentalVMOptions \   # Enable experimental features
  -XX:+UseZGC \                        # Ultra-low latency GC (Java 11+)
  -XX:+UncommonTrapDeoptimizes \       # Aggressive deoptimization
  TradingApplication
*/` : selectedTopic === 'profiling' ? `// Real-time Trading System Monitoring
public class TradingSystemMonitor {
    private final MBeanServer mbs = ManagementFactory.getPlatformMBeanServer();
    
    @MXBean
    public interface TradingMetricsMXBean {
        long getOrdersPerSecond();
        double getAverageLatencyMicros();
        long getHeapUsedMB();
        int getActiveThreadCount();
    }
    
    public class TradingMetrics implements TradingMetricsMXBean {
        private final AtomicLong orderCount = new AtomicLong();
        private final HistogramMetric latency = new HistogramMetric();
        
        @Override
        public long getOrdersPerSecond() {
            return orderCount.get() / TimeUnit.SECONDS.toSeconds(
                System.currentTimeMillis() - startTime);
        }
        
        @Override
        public double getAverageLatencyMicros() {
            return latency.getMean() / 1000.0; // Convert ns to μs
        }
    }
    
    // JFR Custom Events
    @Label("Order Execution")
    public static class OrderExecutionEvent extends Event {
        @Label("Order ID")
        String orderId;
        
        @Label("Execution Time")
        @Timespan(Timespan.NANOSECONDS)
        long duration;
    }
}` : selectedTopic === 'optimization' ? `// CPU Cache-Friendly Data Structures
public class CacheOptimizedOrderBook {
    // Prevent false sharing with @Contended
    @Contended("group1")
    private volatile long bidSequence;
    
    @Contended("group2") 
    private volatile long askSequence;
    
    // Pack data for cache line efficiency
    private static class PriceLevel {
        final double price;      // 8 bytes
        final int quantity;      // 4 bytes
        final int orderCount;    // 4 bytes
        // 16 bytes total - half cache line
        
        PriceLevel(double price, int quantity, int orderCount) {
            this.price = price;
            this.quantity = quantity;
            this.orderCount = orderCount;
        }
    }
    
    // Array of structs layout for better cache usage
    private final PriceLevel[] bidLevels = new PriceLevel[1000];
    private final PriceLevel[] askLevels = new PriceLevel[1000];
    
    // Branchless comparison for better performance
    public double getBestPrice(boolean isBuy) {
        PriceLevel[] levels = isBuy ? bidLevels : askLevels;
        return levels[0].price;
    }
}` : `// Ultra-Low Latency Techniques
public class UltraLowLatencyTrader {
    // Off-heap order storage to avoid GC
    private final ByteBuffer orderBuffer = 
        ByteBuffer.allocateDirect(1024 * 1024); // 1MB direct
    
    // Memory-mapped file for persistence
    private final MappedByteBuffer journal;
    
    public UltraLowLatencyTrader() throws IOException {
        RandomAccessFile file = new RandomAccessFile("orders.journal", "rw");
        journal = file.getChannel().map(
            FileChannel.MapMode.READ_WRITE, 0, 100 * 1024 * 1024);
    }
    
    // Lock-free single producer/consumer queue
    private final AtomicReferenceArray<Order> queue = 
        new AtomicReferenceArray<>(8192);
    private volatile long writeIndex = 0;
    private volatile long readIndex = 0;
    
    // Busy-wait processing loop
    public void processingLoop() {
        Thread.currentThread().setPriority(Thread.MAX_PRIORITY);
        
        while (isRunning) {
            long currentRead = readIndex;
            Order order = queue.get((int)(currentRead & 8191));
            
            if (order != null) {
                processOrder(order); // < 1μs processing time
                queue.set((int)(currentRead & 8191), null);
                readIndex = currentRead + 1;
            } else {
                Thread.onSpinWait(); // CPU pause instruction
            }
        }
    }
}`}
                    </SyntaxHighlighter>
                  </div>
                </div>
              </div>
            )}
            
            {detailsTab === 'configuration' && (
              <div>
                <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px', color: '#10b981' }}>JVM Configuration</h3>
                
                <div style={{ marginBottom: '24px' }}>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Memory Configuration</h4>
                  <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px' }}>
                    <div style={{ display: 'grid', gap: '8px', fontSize: '12px' }}>
                      <div>
                        <span style={{ color: '#3b82f6', fontFamily: 'monospace' }}>-Xms8g -Xmx8g</span>
                        <span style={{ color: '#e2e8f0', marginLeft: '12px' }}>Fixed heap size (no dynamic resizing)</span>
                      </div>
                      <div>
                        <span style={{ color: '#3b82f6', fontFamily: 'monospace' }}>-XX:NewRatio=1</span>
                        <span style={{ color: '#e2e8f0', marginLeft: '12px' }}>Equal young/old generation sizes</span>
                      </div>
                      <div>
                        <span style={{ color: '#3b82f6', fontFamily: 'monospace' }}>-XX:MaxDirectMemorySize=4g</span>
                        <span style={{ color: '#e2e8f0', marginLeft: '12px' }}>Off-heap memory limit</span>
                      </div>
                      <div>
                        <span style={{ color: '#3b82f6', fontFamily: 'monospace' }}>-XX:+AlwaysPreTouch</span>
                        <span style={{ color: '#e2e8f0', marginLeft: '12px' }}>Pre-allocate all memory at startup</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div style={{ marginBottom: '24px' }}>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Garbage Collection</h4>
                  <div style={{ display: 'grid', gap: '12px' }}>
                    <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', borderLeft: '3px solid #10b981' }}>
                      <p style={{ color: '#10b981', fontSize: '14px', fontWeight: 'bold' }}>G1GC (Recommended)</p>
                      <div style={{ color: '#e2e8f0', fontSize: '12px', marginTop: '4px' }}>
                        <code>-XX:+UseG1GC -XX:MaxGCPauseMillis=10</code>
                      </div>
                    </div>
                    <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', borderLeft: '3px solid #3b82f6' }}>
                      <p style={{ color: '#3b82f6', fontSize: '14px', fontWeight: 'bold' }}>ZGC (Ultra Low Latency)</p>
                      <div style={{ color: '#e2e8f0', fontSize: '12px', marginTop: '4px' }}>
                        <code>-XX:+UseZGC -XX:+UnlockExperimentalVMOptions</code>
                      </div>
                    </div>
                    <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', borderLeft: '3px solid #f59e0b' }}>
                      <p style={{ color: '#f59e0b', fontSize: '14px', fontWeight: 'bold' }}>Parallel GC (High Throughput)</p>
                      <div style={{ color: '#e2e8f0', fontSize: '12px', marginTop: '4px' }}>
                        <code>-XX:+UseParallelGC -XX:+UseParallelOldGC</code>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>JIT Compilation</h4>
                  <ul style={{ color: '#e2e8f0', paddingLeft: '20px' }}>
                    <li style={{ marginBottom: '8px' }}><code>-XX:CompileThreshold=1000</code> - Lower threshold for faster compilation</li>
                    <li style={{ marginBottom: '8px' }}><code>-XX:+TieredCompilation</code> - Enable tiered compilation</li>
                    <li style={{ marginBottom: '8px' }}><code>-XX:+AggressiveOpts</code> - Aggressive optimizations</li>
                    <li style={{ marginBottom: '8px' }}><code>-XX:+OptimizeStringConcat</code> - String concatenation optimization</li>
                  </ul>
                </div>
              </div>
            )}
            
            {detailsTab === 'debugging' && (
              <div>
                <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px', color: '#10b981' }}>Debugging & Diagnostics</h3>
                
                <div style={{ marginBottom: '24px' }}>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Essential Commands</h4>
                  <div style={{ display: 'grid', gap: '8px' }}>
                    {[
                      { cmd: 'jstat -gc <pid> 1s', desc: 'GC statistics every second' },
                      { cmd: 'jmap -heap <pid>', desc: 'Heap memory summary' },
                      { cmd: 'jstack <pid>', desc: 'Thread dump for deadlock analysis' },
                      { cmd: 'jcmd <pid> VM.classloader_stats', desc: 'Class loader statistics' },
                      { cmd: 'jinfo -flags <pid>', desc: 'JVM flags in use' },
                      { cmd: 'jcmd <pid> GC.run_finalization', desc: 'Force finalization' }
                    ].map((item, idx) => (
                      <div key={idx} style={{ backgroundColor: '#1e293b', padding: '8px', borderRadius: '4px' }}>
                        <div style={{ color: '#60a5fa', fontFamily: 'monospace', fontSize: '12px' }}>{item.cmd}</div>
                        <div style={{ color: '#9ca3af', fontSize: '11px', marginTop: '2px' }}>{item.desc}</div>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div style={{ marginBottom: '24px' }}>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Flight Recorder</h4>
                  <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px' }}>
                    <div style={{ color: '#e2e8f0', fontSize: '14px', marginBottom: '8px' }}>Enable JFR:</div>
                    <code style={{ color: '#10b981', fontSize: '12px' }}>
                      -XX:+FlightRecorder -XX:StartFlightRecording=duration=60s,filename=trading.jfr
                    </code>
                    
                    <div style={{ color: '#e2e8f0', fontSize: '14px', marginBottom: '8px', marginTop: '12px' }}>Custom Events:</div>
                    <code style={{ color: '#10b981', fontSize: '12px' }}>
                      jcmd &lt;pid&gt; JFR.start name=trading duration=30s filename=orders.jfr
                    </code>
                  </div>
                </div>
                
                <div>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#ef4444' }}>Common Issues</h4>
                  <div style={{ display: 'grid', gap: '12px' }}>
                    <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', borderLeft: '3px solid #ef4444' }}>
                      <p style={{ color: '#ef4444', fontSize: '14px', fontWeight: 'bold' }}>OutOfMemoryError</p>
                      <p style={{ color: '#e2e8f0', fontSize: '12px' }}>Check heap dump with jmap, increase heap size, or fix memory leaks</p>
                    </div>
                    <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', borderLeft: '3px solid #f59e0b' }}>
                      <p style={{ color: '#f59e0b', fontSize: '14px', fontWeight: 'bold' }}>High GC Overhead</p>
                      <p style={{ color: '#e2e8f0', fontSize: '12px' }}>Tune GC parameters, increase heap size, or optimize allocation patterns</p>
                    </div>
                    <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', borderLeft: '3px solid #8b5cf6' }}>
                      <p style={{ color: '#8b5cf6', fontSize: '14px', fontWeight: 'bold' }}>Thread Contention</p>
                      <p style={{ color: '#e2e8f0', fontSize: '12px' }}>Analyze thread dumps, optimize locking, use lock-free algorithms</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            {detailsTab === 'optimization' && (
              <div>
                <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px', color: '#10b981' }}>Performance Optimization</h3>
                
                <div style={{ marginBottom: '24px' }}>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Allocation Optimization</h4>
                  <ul style={{ color: '#e2e8f0', paddingLeft: '20px' }}>
                    <li style={{ marginBottom: '8px' }}>Use object pools for frequently created objects</li>
                    <li style={{ marginBottom: '8px' }}>Avoid allocations in hot paths (use primitives)</li>
                    <li style={{ marginBottom: '8px' }}>Leverage escape analysis for stack allocation</li>
                    <li style={{ marginBottom: '8px' }}>Pre-size collections to avoid resizing</li>
                  </ul>
                </div>
                
                <div style={{ marginBottom: '24px' }}>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>CPU Optimization</h4>
                  <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px' }}>
                    <div style={{ marginBottom: '8px' }}>
                      <span style={{ color: '#10b981', fontSize: '14px' }}>Cache-Friendly Code:</span>
                      <p style={{ color: '#e2e8f0', fontSize: '12px', marginTop: '4px' }}>
                        Structure data for sequential access, minimize pointer chasing, use @Contended annotation
                      </p>
                    </div>
                    <div style={{ marginBottom: '8px' }}>
                      <span style={{ color: '#10b981', fontSize: '14px' }}>Branch Prediction:</span>
                      <p style={{ color: '#e2e8f0', fontSize: '12px', marginTop: '4px' }}>
                        Sort data to make branches predictable, use branchless algorithms where possible
                      </p>
                    </div>
                    <div>
                      <span style={{ color: '#10b981', fontSize: '14px' }}>SIMD Utilization:</span>
                      <p style={{ color: '#e2e8f0', fontSize: '12px', marginTop: '4px' }}>
                        Write vectorizable loops, use bulk operations, consider Vector API (Java 16+)
                      </p>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>JIT Optimization Tips</h4>
                  <ul style={{ color: '#e2e8f0', paddingLeft: '20px' }}>
                    <li style={{ marginBottom: '8px' }}>Warm up critical code paths during startup</li>
                    <li style={{ marginBottom: '8px' }}>Keep methods small for better inlining</li>
                    <li style={{ marginBottom: '8px' }}>Use final methods and classes when possible</li>
                    <li style={{ marginBottom: '8px' }}>Monitor compilation logs to verify optimizations</li>
                  </ul>
                </div>
              </div>
            )}
          </div>
        )}
    </div>
  );
};

export default JavaJVM;
import React, { useState } from 'react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Package, Database, GitBranch, Layers, Grid, Hash } from 'lucide-react';

const JavaCollections = () => {
  const [selectedCollection, setSelectedCollection] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [detailsTab, setDetailsTab] = useState('definition');

  const collections = [
    {
      id: 'list',
      name: 'Lists',
      icon: <Layers size={20} />,
      description: 'ArrayList, LinkedList, Vector',
      color: '#ef4444'
    },
    {
      id: 'set',
      name: 'Sets',
      icon: <Grid size={20} />,
      description: 'HashSet, TreeSet, LinkedHashSet',
      color: '#3b82f6'
    },
    {
      id: 'map',
      name: 'Maps',
      icon: <Hash size={20} />,
      description: 'HashMap, TreeMap, ConcurrentHashMap',
      color: '#10b981'
    },
    {
      id: 'queue',
      name: 'Queues',
      icon: <GitBranch size={20} />,
      description: 'PriorityQueue, BlockingQueue, Deque',
      color: '#f59e0b'
    },
    {
      id: 'concurrent',
      name: 'Concurrent Collections',
      icon: <Database size={20} />,
      description: 'Thread-safe collections',
      color: '#8b5cf6'
    }
  ];

  const codeExamples = {
    list: `// High-Performance Order List Implementation
public class OrderBook {
    // ArrayList for fast random access
    private final List<Order> buyOrders = new ArrayList<>(10000);
    private final List<Order> sellOrders = new ArrayList<>(10000);
    
    // LinkedList for frequent insertions/deletions
    private final LinkedList<Order> pendingOrders = new LinkedList<>();
    
    public void addOrder(Order order) {
        if (order.isPending()) {
            pendingOrders.addFirst(order); // O(1) insertion
        } else if (order.isBuy()) {
            insertSorted(buyOrders, order); // Binary search insertion
        } else {
            insertSorted(sellOrders, order);
        }
    }
    
    private void insertSorted(List<Order> orders, Order order) {
        int pos = Collections.binarySearch(orders, order, 
            Comparator.comparing(Order::getPrice).reversed());
        if (pos < 0) pos = -pos - 1;
        orders.add(pos, order);
    }
}`,
    set: `// Duplicate Detection using Sets
public class TradeValidator {
    // HashSet for O(1) lookups
    private final Set<String> processedTradeIds = new HashSet<>();
    
    // TreeSet for maintaining sorted unique prices
    private final TreeSet<Double> uniquePriceLevels = new TreeSet<>();
    
    // LinkedHashSet preserves insertion order
    private final Set<String> executionSequence = new LinkedHashSet<>();
    
    public boolean validateTrade(Trade trade) {
        // Check for duplicate trade
        if (!processedTradeIds.add(trade.getId())) {
            return false; // Duplicate detected
        }
        
        // Track unique price levels
        uniquePriceLevels.add(trade.getPrice());
        
        // Maintain execution order
        executionSequence.add(trade.getId());
        
        return true;
    }
    
    public NavigableSet<Double> getPriceRange(double min, double max) {
        return uniquePriceLevels.subSet(min, true, max, true);
    }
}`,
    map: `// Market Data Cache using Maps
public class MarketDataCache {
    // HashMap for fast lookups
    private final Map<String, MarketData> symbolCache = new HashMap<>();
    
    // TreeMap for sorted data by timestamp
    private final TreeMap<Long, List<MarketData>> timeSeriesData = new TreeMap<>();
    
    // ConcurrentHashMap for thread-safe operations
    private final ConcurrentHashMap<String, AtomicDouble> lastPrices = 
        new ConcurrentHashMap<>();
    
    public void updateMarketData(String symbol, MarketData data) {
        // Update symbol cache
        symbolCache.put(symbol, data);
        
        // Add to time series
        timeSeriesData.computeIfAbsent(data.getTimestamp(), 
            k -> new ArrayList<>()).add(data);
        
        // Update last price atomically
        lastPrices.computeIfAbsent(symbol, k -> new AtomicDouble())
                  .set(data.getPrice());
    }
    
    public Map<Long, List<MarketData>> getDataInRange(long start, long end) {
        return timeSeriesData.subMap(start, true, end, true);
    }
}`,
    queue: `// Order Processing Queue System
public class OrderProcessor {
    // Priority queue for order prioritization
    private final PriorityQueue<Order> priorityOrders = new PriorityQueue<>(
        Comparator.comparing(Order::getPriority).reversed()
    );
    
    // BlockingQueue for producer-consumer pattern
    private final BlockingQueue<Order> processingQueue = 
        new LinkedBlockingQueue<>(10000);
    
    // Deque for two-ended processing
    private final Deque<Order> retryQueue = new ArrayDeque<>();
    
    public void submitOrder(Order order) throws InterruptedException {
        if (order.isHighPriority()) {
            priorityOrders.offer(order);
        } else {
            processingQueue.put(order); // Blocks if full
        }
    }
    
    public void processOrders() throws InterruptedException {
        // Process high priority first
        Order priorityOrder = priorityOrders.poll();
        if (priorityOrder != null) {
            process(priorityOrder);
        }
        
        // Then process regular queue
        Order order = processingQueue.take(); // Blocks if empty
        try {
            process(order);
        } catch (Exception e) {
            retryQueue.addLast(order); // Add to retry queue
        }
    }
}`,
    concurrent: `// Thread-Safe Trading System Collections
public class ConcurrentTradingSystem {
    // Thread-safe list
    private final List<Trade> trades = 
        Collections.synchronizedList(new ArrayList<>());
    
    // ConcurrentHashMap for lock-free reads
    private final ConcurrentHashMap<String, Position> positions = 
        new ConcurrentHashMap<>();
    
    // ConcurrentLinkedQueue for lock-free queue
    private final ConcurrentLinkedQueue<Order> orderQueue = 
        new ConcurrentLinkedQueue<>();
    
    // CopyOnWriteArrayList for mostly-read scenarios
    private final CopyOnWriteArrayList<TradingRule> rules = 
        new CopyOnWriteArrayList<>();
    
    // ConcurrentSkipListMap for sorted concurrent access
    private final ConcurrentSkipListMap<Double, List<Order>> ordersByPrice = 
        new ConcurrentSkipListMap<>();
    
    public void updatePosition(String symbol, double quantity, double price) {
        positions.compute(symbol, (k, existingPosition) -> {
            if (existingPosition == null) {
                return new Position(symbol, quantity, price);
            } else {
                return existingPosition.add(quantity, price);
            }
        });
    }
    
    public void addOrder(Order order) {
        orderQueue.offer(order);
        ordersByPrice.computeIfAbsent(order.getPrice(), 
            k -> new CopyOnWriteArrayList<>()).add(order);
    }
}`
  };

  return (
    <div style={{
      padding: '40px',
      paddingTop: '100px',
      minHeight: '100vh',
      backgroundColor: '#0f172a',
      color: 'white'
    }}>
      <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
        <div style={{ marginBottom: '40px' }}>
          <h1 style={{ fontSize: '36px', fontWeight: 'bold', marginBottom: '16px', color: '#10b981' }}>
            Java Collections Framework
          </h1>
          <p style={{ fontSize: '18px', color: '#94a3b8' }}>
            Efficient data structures for high-performance trading systems
          </p>
        </div>

        <div style={{ display: 'flex', gap: '20px', marginBottom: '40px' }}>
          {['overview', 'definition', 'code', 'performance', 'best-practices'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              style={{
                padding: '12px 24px',
                backgroundColor: activeTab === tab ? '#10b981' : 'transparent',
                border: `2px solid ${activeTab === tab ? '#10b981' : '#374151'}`,
                borderRadius: '8px',
                color: activeTab === tab ? 'white' : '#9ca3af',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                textTransform: 'capitalize',
                transition: 'all 0.2s'
              }}
            >
              {tab.replace('-', ' ')}
            </button>
          ))}
        </div>

        {activeTab === 'definition' && (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <div style={{ marginBottom: '20px' }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px' }}>
                {selectedCollection === 'list' ? 'Lists' :
                 selectedCollection === 'set' ? 'Sets' :
                 selectedCollection === 'map' ? 'Maps' :
                 selectedCollection === 'queue' ? 'Queues' :
                 'Concurrent Collections'} Definition
              </h3>
            </div>
            
            {selectedCollection === 'list' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What are Lists?</h4>
                <p style={{ marginBottom: '16px' }}>
                  Lists are ordered collections that allow duplicate elements. They provide indexed access to elements and maintain insertion order. Common implementations include ArrayList, LinkedList, and Vector.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why are they essential for Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>Store ordered sequences of trades, orders, or price updates</li>
                  <li>Maintain chronological order of market events</li>
                  <li>Enable fast random access to historical data</li>
                  <li>Support iteration over trading records</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to use them effectively?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Use ArrayList for frequent random access operations</li>
                  <li>Use LinkedList for frequent insertions/deletions</li>
                  <li>Pre-size collections to avoid resizing overhead</li>
                  <li>Consider CopyOnWriteArrayList for read-heavy scenarios</li>
                </ul>
              </div>
            )}
            
            {selectedCollection === 'map' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What are Maps?</h4>
                <p style={{ marginBottom: '16px' }}>
                  Maps are key-value pair collections that provide fast lookups based on keys. They don't allow duplicate keys and are perfect for caching and indexing data.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why are they crucial for Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>Cache market data by symbol for O(1) lookups</li>
                  <li>Store positions indexed by instrument</li>
                  <li>Maintain order books with price-level mappings</li>
                  <li>Index trading strategies by identifier</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to use them effectively?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Use HashMap for general-purpose key-value storage</li>
                  <li>Use TreeMap for sorted keys (price levels)</li>
                  <li>Use ConcurrentHashMap for thread-safe operations</li>
                  <li>Consider load factor and initial capacity</li>
                </ul>
              </div>
            )}
          </div>
        )}

        {activeTab === 'overview' && (
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>
            {collections.map((collection) => (
              <div
                key={collection.id}
                onClick={() => setSelectedCollection(collection)}
                style={{
                  padding: '24px',
                  backgroundColor: selectedCollection?.id === collection.id ? 'rgba(16, 185, 129, 0.1)' : 'rgba(31, 41, 55, 0.5)',
                  border: `2px solid ${selectedCollection?.id === collection.id ? '#10b981' : 'transparent'}`,
                  borderRadius: '12px',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '12px' }}>
                  <div style={{ color: collection.color }}>{collection.icon}</div>
                  <h3 style={{ fontSize: '20px', fontWeight: '600' }}>{collection.name}</h3>
                </div>
                <p style={{ color: '#94a3b8', fontSize: '14px' }}>{collection.description}</p>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'code' && (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <div style={{ marginBottom: '20px' }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px', textTransform: 'capitalize' }}>
                {selectedCollection} Example
              </h3>
            </div>
            <SyntaxHighlighter
              language="java"
              style={oneDark}
              customStyle={{
                backgroundColor: '#1e293b',
                padding: '20px',
                borderRadius: '8px',
                fontSize: '14px',
                lineHeight: '1.6'
              }}
            >
              {codeExamples[selectedCollection]}
            </SyntaxHighlighter>
          </div>
        )}

        {activeTab === 'performance' && (
          <div style={{
            padding: '24px',
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            border: '1px solid #374151'
          }}>
            <h3 style={{ fontSize: '24px', fontWeight: '600', marginBottom: '20px', color: '#10b981' }}>
              Collection Performance Comparison
            </h3>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ borderBottom: '2px solid #374151' }}>
                  <th style={{ padding: '12px', textAlign: 'left', color: '#10b981' }}>Collection</th>
                  <th style={{ padding: '12px', textAlign: 'left', color: '#10b981' }}>Add</th>
                  <th style={{ padding: '12px', textAlign: 'left', color: '#10b981' }}>Remove</th>
                  <th style={{ padding: '12px', textAlign: 'left', color: '#10b981' }}>Get</th>
                  <th style={{ padding: '12px', textAlign: 'left', color: '#10b981' }}>Contains</th>
                </tr>
              </thead>
              <tbody>
                {[
                  ['ArrayList', 'O(1)*', 'O(n)', 'O(1)', 'O(n)'],
                  ['LinkedList', 'O(1)', 'O(1)', 'O(n)', 'O(n)'],
                  ['HashSet', 'O(1)', 'O(1)', 'N/A', 'O(1)'],
                  ['TreeSet', 'O(log n)', 'O(log n)', 'N/A', 'O(log n)'],
                  ['HashMap', 'O(1)', 'O(1)', 'O(1)', 'O(1)'],
                  ['TreeMap', 'O(log n)', 'O(log n)', 'O(log n)', 'O(log n)']
                ].map((row, index) => (
                  <tr key={index} style={{ borderBottom: '1px solid #1f2937' }}>
                    <td style={{ padding: '12px', color: '#e2e8f0' }}>{row[0]}</td>
                    <td style={{ padding: '12px', color: '#94a3b8' }}>{row[1]}</td>
                    <td style={{ padding: '12px', color: '#94a3b8' }}>{row[2]}</td>
                    <td style={{ padding: '12px', color: '#94a3b8' }}>{row[3]}</td>
                    <td style={{ padding: '12px', color: '#94a3b8' }}>{row[4]}</td>
                  </tr>
                ))}
              </tbody>
            </table>
            <p style={{ marginTop: '16px', fontSize: '12px', color: '#6b7280' }}>
              * Amortized time complexity. May require array resizing.
            </p>
          </div>
        )}

        {activeTab === 'best-practices' && (
          <div style={{ display: 'grid', gap: '20px' }}>
            {[
              {
                title: 'Choose the Right Collection',
                tips: [
                  'Use ArrayList for fast random access',
                  'Use LinkedList for frequent insertions/deletions',
                  'Use HashSet/HashMap for fast lookups',
                  'Use TreeSet/TreeMap for sorted data'
                ]
              },
              {
                title: 'Thread Safety',
                tips: [
                  'Use ConcurrentHashMap instead of Hashtable',
                  'Consider CopyOnWriteArrayList for mostly-read scenarios',
                  'Use BlockingQueue for producer-consumer patterns',
                  'Avoid Collections.synchronized* in high-concurrency'
                ]
              },
              {
                title: 'Performance Optimization',
                tips: [
                  'Initialize collections with expected capacity',
                  'Use primitive collections (Trove, Eclipse Collections) for better performance',
                  'Consider immutable collections for thread safety',
                  'Profile and measure before optimizing'
                ]
              }
            ].map((section, index) => (
              <div key={index} style={{
                padding: '24px',
                backgroundColor: 'rgba(31, 41, 55, 0.5)',
                borderRadius: '12px',
                border: '1px solid #374151'
              }}>
                <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px', color: '#10b981' }}>
                  {section.title}
                </h3>
                <ul style={{ listStyle: 'none', padding: 0 }}>
                  {section.tips.map((tip, tipIndex) => (
                    <li key={tipIndex} style={{ padding: '8px 0', color: '#94a3b8', fontSize: '14px' }}>
                      <span style={{ color: '#10b981', marginRight: '8px' }}>•</span>
                      {tip}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        )}
      </div>
      
      {/* Details Panel - Matching Dark Pool Style */}
      {selectedCollection && (
        <div style={{
          position: 'fixed',
          right: 0,
          top: 0,
          width: '500px',
          height: '100vh',
          backgroundColor: 'rgba(17, 24, 39, 0.95)',
          backdropFilter: 'blur(12px)',
          borderLeft: '1px solid #374151',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          zIndex: 1000
        }}>
          {/* Panel Header */}
          <div style={{
            padding: '20px',
            borderBottom: '1px solid #374151',
            background: 'linear-gradient(135deg, #1f2937 0%, #111827 100%)'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start' }}>
              <div>
                <h2 style={{ fontSize: '18px', fontWeight: 'bold', color: '#fbbf24', margin: '0 0 8px 0' }}>
                  {selectedCollection.name}
                </h2>
                <div style={{ fontSize: '12px', color: '#9ca3af' }}>
                  {selectedCollection.description}
                </div>
              </div>
              <button
                onClick={() => setSelectedCollection(null)}
                style={{
                  background: 'transparent',
                  border: 'none',
                  color: '#9ca3af',
                  fontSize: '24px',
                  cursor: 'pointer',
                  padding: 0,
                  lineHeight: 1
                }}
              >
                ×
              </button>
            </div>
          </div>

          {/* Tabs */}
          <div style={{ display: 'flex', borderBottom: '1px solid #374151' }}>
            {['definition', 'implementations', 'code', 'performance', 'use-cases'].map(tab => (
              <button
                key={tab}
                onClick={() => setDetailsTab(tab)}
                style={{
                  flex: 1,
                  padding: '12px',
                  background: detailsTab === tab ? '#1f2937' : 'transparent',
                  border: 'none',
                  color: detailsTab === tab ? '#fbbf24' : '#9ca3af',
                  fontSize: '12px',
                  fontWeight: '600',
                  textTransform: 'capitalize',
                  cursor: 'pointer'
                }}
              >
                {tab.replace('-', ' ')}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div style={{ flex: 1, padding: '20px', overflowY: 'auto' }}>
            {detailsTab === 'definition' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  What are {selectedCollection.name}?
                </h3>
                <div style={{ color: '#d1d5db', fontSize: '13px', lineHeight: '1.8' }}>
                  {selectedCollection.id === 'list' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Lists are ordered collections that allow duplicate elements and provide indexed access to elements. They maintain insertion order and support dynamic resizing.
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Types:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>ArrayList - Resizable array implementation</li>
                        <li>LinkedList - Doubly-linked list implementation</li>
                        <li>Vector - Synchronized resizable array</li>
                      </ul>
                    </>
                  )}
                  {selectedCollection.id === 'set' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Sets are collections that do not allow duplicate elements. They are used when you need to ensure uniqueness and fast membership testing.
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Types:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>HashSet - Hash table based implementation</li>
                        <li>TreeSet - Sorted set implementation</li>
                        <li>LinkedHashSet - Hash set with insertion order</li>
                      </ul>
                    </>
                  )}
                  {selectedCollection.id === 'map' && (
                    <>
                      <p style={{ marginBottom: '16px' }}>
                        Maps store key-value pairs and provide fast lookups based on keys. They don't allow duplicate keys and are essential for caching and indexing.
                      </p>
                      <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Types:</h4>
                      <ul style={{ paddingLeft: '20px' }}>
                        <li>HashMap - Hash table based implementation</li>
                        <li>TreeMap - Sorted map implementation</li>
                        <li>ConcurrentHashMap - Thread-safe hash map</li>
                      </ul>
                    </>
                  )}
                </div>
              </div>
            )}

            {detailsTab === 'implementations' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  {selectedCollection.name} Implementations
                </h3>
                
                {selectedCollection.id === 'list' && (
                  <div style={{ display: 'grid', gap: '16px' }}>
                    {/* ArrayList */}
                    <div style={{
                      padding: '16px',
                      background: '#1f2937',
                      borderRadius: '8px',
                      borderLeft: '4px solid #ef4444'
                    }}>
                      <h4 style={{ color: '#ef4444', fontSize: '13px', fontWeight: '600', marginBottom: '8px' }}>
                        ArrayList&lt;T&gt;
                      </h4>
                      <p style={{ color: '#d1d5db', fontSize: '12px', marginBottom: '12px' }}>
                        Dynamic array implementation. Best for random access and when size changes infrequently.
                      </p>
                      <SyntaxHighlighter language="java" style={oneDark} customStyle={{ fontSize: '11px', margin: 0, background: '#0f172a', padding: '12px', borderRadius: '6px' }}>
{`// Order book using ArrayList for fast random access
ArrayList<Order> orders = new ArrayList<>();
orders.add(new Order("ORD001", 150.0, 100));
orders.add(new Order("ORD002", 149.5, 200));

// Fast access by index - O(1)
Order firstOrder = orders.get(0);

// Efficient for appending orders
orders.add(new Order("ORD003", 151.0, 150));`}
                      </SyntaxHighlighter>
                      <div style={{ marginTop: '8px', fontSize: '11px', color: '#9ca3af' }}>
                        <strong>Use cases:</strong> Order books, price history, account portfolios
                      </div>
                    </div>

                    {/* LinkedList */}
                    <div style={{
                      padding: '16px',
                      background: '#1f2937',
                      borderRadius: '8px',
                      borderLeft: '4px solid #ef4444'
                    }}>
                      <h4 style={{ color: '#ef4444', fontSize: '13px', fontWeight: '600', marginBottom: '8px' }}>
                        LinkedList&lt;T&gt;
                      </h4>
                      <p style={{ color: '#d1d5db', fontSize: '12px', marginBottom: '12px' }}>
                        Doubly-linked list. Best for frequent insertions/deletions at both ends.
                      </p>
                      <SyntaxHighlighter language="java" style={oneDark} customStyle={{ fontSize: '11px', margin: 0, background: '#0f172a', padding: '12px', borderRadius: '6px' }}>
{`// Trade execution queue using LinkedList
LinkedList<Trade> tradeQueue = new LinkedList<>();

// Efficient insertions at both ends - O(1)
tradeQueue.addFirst(urgentTrade);  // High priority
tradeQueue.addLast(normalTrade);   // Normal priority

// Process trades from front
Trade nextTrade = tradeQueue.removeFirst();`}
                      </SyntaxHighlighter>
                      <div style={{ marginTop: '8px', fontSize: '11px', color: '#9ca3af' }}>
                        <strong>Use cases:</strong> Trade queues, transaction logs, undo operations
                      </div>
                    </div>

                    {/* Vector */}
                    <div style={{
                      padding: '16px',
                      background: '#1f2937',
                      borderRadius: '8px',
                      borderLeft: '4px solid #ef4444'
                    }}>
                      <h4 style={{ color: '#ef4444', fontSize: '13px', fontWeight: '600', marginBottom: '8px' }}>
                        Vector&lt;T&gt;
                      </h4>
                      <p style={{ color: '#d1d5db', fontSize: '12px', marginBottom: '12px' }}>
                        Thread-safe dynamic array. Similar to ArrayList but synchronized.
                      </p>
                      <SyntaxHighlighter language="java" style={oneDark} customStyle={{ fontSize: '11px', margin: 0, background: '#0f172a', padding: '12px', borderRadius: '6px' }}>
{`// Thread-safe position tracking
Vector<Position> positions = new Vector<>();

// Safe concurrent access from multiple threads
synchronized(positions) {
    positions.add(new Position("AAPL", 100));
    double totalValue = positions.stream()
        .mapToDouble(Position::getValue)
        .sum();
}`}
                      </SyntaxHighlighter>
                      <div style={{ marginTop: '8px', fontSize: '11px', color: '#9ca3af' }}>
                        <strong>Use cases:</strong> Legacy thread-safe collections, simple concurrent scenarios
                      </div>
                    </div>

                    {/* CopyOnWriteArrayList */}
                    <div style={{
                      padding: '16px',
                      background: '#1f2937',
                      borderRadius: '8px',
                      borderLeft: '4px solid #ef4444'
                    }}>
                      <h4 style={{ color: '#ef4444', fontSize: '13px', fontWeight: '600', marginBottom: '8px' }}>
                        CopyOnWriteArrayList&lt;T&gt;
                      </h4>
                      <p style={{ color: '#d1d5db', fontSize: '12px', marginBottom: '12px' }}>
                        Thread-safe list optimized for scenarios with many readers, few writers.
                      </p>
                      <SyntaxHighlighter language="java" style={oneDark} customStyle={{ fontSize: '11px', margin: 0, background: '#0f172a', padding: '12px', borderRadius: '6px' }}>
{`// Market data subscribers - many readers, few writers
CopyOnWriteArrayList<MarketDataListener> subscribers = 
    new CopyOnWriteArrayList<>();

// Adding subscribers is infrequent but expensive
subscribers.add(new RiskManagerListener());

// Reading is frequent and lock-free
for (MarketDataListener listener : subscribers) {
    listener.onPriceUpdate(price);
}`}
                      </SyntaxHighlighter>
                      <div style={{ marginTop: '8px', fontSize: '11px', color: '#9ca3af' }}>
                        <strong>Use cases:</strong> Event listeners, observer patterns, configuration lists
                      </div>
                    </div>
                  </div>
                )}

                {selectedCollection.id === 'map' && (
                  <div style={{ display: 'grid', gap: '16px' }}>
                    {/* HashMap */}
                    <div style={{
                      padding: '16px',
                      background: '#1f2937',
                      borderRadius: '8px',
                      borderLeft: '4px solid #10b981'
                    }}>
                      <h4 style={{ color: '#10b981', fontSize: '13px', fontWeight: '600', marginBottom: '8px' }}>
                        HashMap&lt;K,V&gt;
                      </h4>
                      <p style={{ color: '#d1d5db', fontSize: '12px', marginBottom: '12px' }}>
                        Hash table implementation. Best general-purpose map with O(1) average performance.
                      </p>
                      <SyntaxHighlighter language="java" style={oneDark} customStyle={{ fontSize: '11px', margin: 0, background: '#0f172a', padding: '12px', borderRadius: '6px' }}>
{`// Portfolio positions by symbol
HashMap<String, Position> portfolio = new HashMap<>();
portfolio.put("AAPL", new Position("AAPL", 100, 150.0));
portfolio.put("GOOGL", new Position("GOOGL", 50, 2800.0));

// Fast lookup - O(1) average
Position applePosition = portfolio.get("AAPL");

// Check if position exists
if (portfolio.containsKey("MSFT")) {
    // Handle MSFT position
}`}
                      </SyntaxHighlighter>
                      <div style={{ marginTop: '8px', fontSize: '11px', color: '#9ca3af' }}>
                        <strong>Use cases:</strong> Portfolios, price caches, account lookups
                      </div>
                    </div>

                    {/* TreeMap */}
                    <div style={{
                      padding: '16px',
                      background: '#1f2937',
                      borderRadius: '8px',
                      borderLeft: '4px solid #10b981'
                    }}>
                      <h4 style={{ color: '#10b981', fontSize: '13px', fontWeight: '600', marginBottom: '8px' }}>
                        TreeMap&lt;K,V&gt;
                      </h4>
                      <p style={{ color: '#d1d5db', fontSize: '12px', marginBottom: '12px' }}>
                        Red-black tree implementation. Maintains sorted order, O(log n) operations.
                      </p>
                      <SyntaxHighlighter language="java" style={oneDark} customStyle={{ fontSize: '11px', margin: 0, background: '#0f172a', padding: '12px', borderRadius: '6px' }}>
{`// Order book with price-level sorting
TreeMap<Double, List<Order>> bidBook = new TreeMap<>(Collections.reverseOrder());
TreeMap<Double, List<Order>> askBook = new TreeMap<>();

// Orders automatically sorted by price
bidBook.computeIfAbsent(150.0, k -> new ArrayList<>())
       .add(new Order("BUY", 150.0, 100));

// Get best bid (highest price)
Double bestBid = bidBook.firstKey();
Double bestAsk = askBook.firstKey();
                <div style={{ display: 'grid', gap: '12px' }}>
                  {selectedCollection.id === 'list' && [
                    { operation: 'Access by index', complexity: 'ArrayList: O(1), LinkedList: O(n)' },
                    { operation: 'Add/Remove at end', complexity: 'ArrayList: O(1)*, LinkedList: O(1)' },
                    { operation: 'Add/Remove at beginning', complexity: 'ArrayList: O(n), LinkedList: O(1)' },
                    { operation: 'Search', complexity: 'Both: O(n)' }
                  ].map((item, idx) => (
                    <div key={idx} style={{
                      padding: '12px',
                      background: '#1f2937',
                      borderRadius: '6px',
                      borderLeft: '3px solid #10b981'
                    }}>
                      <div style={{ color: '#fbbf24', fontSize: '13px', fontWeight: 'bold' }}>
                        {item.operation}
                      </div>
                      <div style={{ color: '#d1d5db', fontSize: '12px', marginTop: '4px' }}>
                        {item.complexity}
                      </div>
                    </div>
                  ))}
                  {selectedCollection.id === 'map' && [
                    { operation: 'Get/Put/Remove', complexity: 'HashMap: O(1)*, TreeMap: O(log n)' },
                    { operation: 'Contains key', complexity: 'HashMap: O(1)*, TreeMap: O(log n)' },
                    { operation: 'Iteration', complexity: 'HashMap: O(n), TreeMap: O(n) sorted' },
                    { operation: 'Memory overhead', complexity: 'HashMap: Higher, TreeMap: Lower' }
                  ].map((item, idx) => (
                    <div key={idx} style={{
                      padding: '12px',
                      background: '#1f2937',
                      borderRadius: '6px',
                      borderLeft: '3px solid #10b981'
                    }}>
                      <div style={{ color: '#fbbf24', fontSize: '13px', fontWeight: 'bold' }}>
                        {item.operation}
                      </div>
                      <div style={{ color: '#d1d5db', fontSize: '12px', marginTop: '4px' }}>
                        {item.complexity}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {detailsTab === 'use-cases' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  Trading System Use Cases
                </h3>
                <div style={{ display: 'grid', gap: '12px' }}>
                  {[
                    'Order queue management with PriorityQueue',
                    'Position caching with HashMap for fast lookups',
                    'Maintaining unique symbols with HashSet',
                    'Price level storage using TreeMap for sorted prices',
                    'Trade history with ArrayList for chronological order',
                    'Concurrent order processing with BlockingQueue'
                  ].map((useCase, idx) => (
                    <div key={idx} style={{
                      padding: '12px',
                      background: '#1f2937',
                      borderRadius: '6px',
                      borderLeft: '3px solid #10b981',
                      color: '#d1d5db',
                      fontSize: '13px'
                    }}>
                      {useCase}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default JavaCollections;
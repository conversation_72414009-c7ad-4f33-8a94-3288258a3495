import React, { useState } from 'react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { TrendingUp, BarChart3, AlertTriangle, Calculator, Activity, Database, Clock, Shield, Zap, Settings } from 'lucide-react';

const VarCvarSystem = () => {
  const [selectedComponent, setSelectedComponent] = useState(null);
  const [activeTab, setActiveTab] = useState('details');

  const components = [
    {
      id: 'market-data',
      name: 'Market Data Engine',
      position: { left: 80, top: 100 },
      category: 'data',
      tech: ['Real-time Feeds', 'Historical Data', 'Tick Processing'],
      color: '#3b82f6'
    },
    {
      id: 'portfolio-manager',
      name: 'Portfolio Manager',
      position: { left: 280, top: 100 },
      category: 'core',
      tech: ['Position Tracking', 'P&L Calculation', 'Asset Allocation'],
      color: '#ef4444'
    },
    {
      id: 'risk-calculator',
      name: 'Risk Calculator',
      position: { left: 480, top: 100 },
      category: 'processing',
      tech: ['Monte Carlo', 'Historical Simulation', 'Parametric VaR'],
      color: '#10b981'
    },
    {
      id: 'scenario-engine',
      name: 'Scenario Engine',
      position: { left: 680, top: 100 },
      category: 'analytics',
      tech: ['Stress Testing', 'What-if Analysis', 'Tail Risk'],
      color: '#f59e0b'
    },
    {
      id: 'reporting-system',
      name: 'Reporting System',
      position: { left: 180, top: 280 },
      category: 'output',
      tech: ['Risk Reports', 'Dashboards', 'Alerts'],
      color: '#8b5cf6'
    },
    {
      id: 'calibration-engine',
      name: 'Model Calibration',
      position: { left: 380, top: 280 },
      category: 'analytics',
      tech: ['Parameter Estimation', 'Backtesting', 'Model Validation'],
      color: '#ec4899'
    },
    {
      id: 'limit-monitor',
      name: 'Limit Monitor',
      position: { left: 580, top: 280 },
      category: 'compliance',
      tech: ['Threshold Alerts', 'Breach Detection', 'Escalation'],
      color: '#f97316'
    }
  ];

  const getComponentDetails = (componentId) => {
    const details = {
      'market-data': {
        functions: [
          'Real-time market data ingestion from multiple feeds',
          'Historical price and volatility data management',
          'Tick-by-tick data processing and normalization',
          'Corporate actions and dividend adjustments',
          'Market microstructure data handling',
          'Cross-asset price correlation calculations',
          'Volatility surface construction and interpolation',
          'Reference data management and validation',
          'Data quality monitoring and gap detection',
          'Multi-currency and time zone handling'
        ],
        workflow: `1. Ingest real-time market data from multiple vendors
2. Normalize and validate incoming data feeds
3. Store tick data in time-series database
4. Calculate derived metrics (returns, volatilities)
5. Build correlation matrices across assets
6. Construct volatility surfaces for options
7. Handle corporate actions and adjustments
8. Monitor data quality and detect gaps
9. Provide clean data to risk engines
10. Archive historical data for backtesting`,
        code: `@Service
@Slf4j
public class MarketDataEngine {
    
    private final Map<String, MarketDataFeed> feedProviders;
    private final TimeSeriesDatabase timeSeriesDB;
    private final VolatilityCalculator volatilityCalc;
    private final CorrelationEngine correlationEngine;
    private final ReferenceDataService refDataService;
    private final DataQualityMonitor qualityMonitor;
    private final ExecutorService processingPool;
    
    @Autowired
    public MarketDataEngine(TimeSeriesDatabase tsDB, 
                           VolatilityCalculator volCalc,
                           CorrelationEngine corrEngine) {
        this.timeSeriesDB = tsDB;
        this.volatilityCalc = volCalc;
        this.correlationEngine = corrEngine;
        this.feedProviders = new ConcurrentHashMap<>();
        this.processingPool = Executors.newFixedThreadPool(16);
        
        initializeFeedProviders();
    }
    
    @EventListener
    @Async("marketDataPool")
    public void processMarketDataTick(MarketDataTick tick) {
        long startTime = System.nanoTime();
        
        try {
            // Validate and normalize tick data
            NormalizedTick normalizedTick = normalizeAndValidate(tick);
            
            // Store in time-series database
            timeSeriesDB.insertTick(normalizedTick);
            
            // Calculate real-time volatility
            double instantVol = volatilityCalc.calculateInstantaneousVolatility(
                normalizedTick.getSymbol(), normalizedTick.getPrice());
            
            // Update correlation matrices
            correlationEngine.updateCorrelation(normalizedTick);
            
            // Trigger downstream risk calculations
            publishRiskDataUpdate(normalizedTick, instantVol);
            
            // Monitor processing latency
            long processingTime = System.nanoTime() - startTime;
            if (processingTime > 1_000_000) { // > 1ms
                log.warn("Slow tick processing: {}μs for {}", 
                        processingTime / 1000, tick.getSymbol());
            }
            
        } catch (Exception e) {
            log.error("Failed to process tick for {}: {}", 
                     tick.getSymbol(), e.getMessage());
            qualityMonitor.recordProcessingError(tick.getSymbol(), e);
        }
    }
    
    public VolatilitySurface buildVolatilitySurface(String underlyingSymbol, 
                                                   LocalDate valueDate) {
        List<OptionQuote> optionQuotes = getOptionQuotes(underlyingSymbol, valueDate);
        
        // Sort by expiry and strike
        Map<LocalDate, List<OptionQuote>> quotesByExpiry = optionQuotes.stream()
            .collect(Collectors.groupingBy(OptionQuote::getExpiry));
        
        Map<LocalDate, Map<Double, Double>> volSurfaceData = new HashMap<>();
        
        for (Map.Entry<LocalDate, List<OptionQuote>> entry : quotesByExpiry.entrySet()) {
            LocalDate expiry = entry.getKey();
            List<OptionQuote> quotes = entry.getValue();
            
            Map<Double, Double> strikeToVol = quotes.stream()
                .filter(q -> q.getImpliedVolatility() > 0)
                .collect(Collectors.toMap(
                    OptionQuote::getStrike,
                    OptionQuote::getImpliedVolatility,
                    (v1, v2) -> (v1 + v2) / 2.0 // Average if duplicate
                ));
            
            volSurfaceData.put(expiry, strikeToVol);
        }
        
        return new VolatilitySurface(underlyingSymbol, valueDate, volSurfaceData);
    }
    
    @Scheduled(fixedRate = 30000) // Every 30 seconds
    public void calculateRollingVolatilities() {
        List<String> activeSymbols = refDataService.getActiveSymbols();
        
        activeSymbols.parallelStream().forEach(symbol -> {
            try {
                // 30-day rolling volatility
                double vol30d = volatilityCalc.calculateRollingVolatility(
                    symbol, Duration.ofDays(30));
                
                // 90-day rolling volatility
                double vol90d = volatilityCalc.calculateRollingVolatility(
                    symbol, Duration.ofDays(90));
                
                // GARCH volatility forecast
                double garchVol = volatilityCalc.calculateGARCHVolatility(symbol);
                
                // Store volatility metrics
                VolatilityMetrics metrics = VolatilityMetrics.builder()
                    .symbol(symbol)
                    .vol30d(vol30d)
                    .vol90d(vol90d)
                    .garchVol(garchVol)
                    .timestamp(LocalDateTime.now())
                    .build();
                
                timeSeriesDB.storeVolatilityMetrics(metrics);
                
            } catch (Exception e) {
                log.error("Failed to calculate volatility for {}: {}", 
                         symbol, e.getMessage());
            }
        });
    }
    
    private NormalizedTick normalizeAndValidate(MarketDataTick tick) {
        // Price validation
        if (tick.getPrice() <= 0) {
            throw new InvalidTickException("Invalid price: " + tick.getPrice());
        }
        
        // Volume validation
        if (tick.getVolume() < 0) {
            throw new InvalidTickException("Invalid volume: " + tick.getVolume());
        }
        
        // Time validation
        if (tick.getTimestamp().isAfter(LocalDateTime.now().plusMinutes(1))) {
            throw new InvalidTickException("Future timestamp: " + tick.getTimestamp());
        }
        
        // Corporate actions adjustment
        double adjustedPrice = refDataService.adjustForCorporateActions(
            tick.getSymbol(), tick.getPrice(), tick.getTimestamp());
        
        return NormalizedTick.builder()
            .symbol(tick.getSymbol())
            .price(adjustedPrice)
            .volume(tick.getVolume())
            .timestamp(tick.getTimestamp())
            .exchange(tick.getExchange())
            .build();
    }
    
    private void publishRiskDataUpdate(NormalizedTick tick, double volatility) {
        RiskDataUpdate update = RiskDataUpdate.builder()
            .symbol(tick.getSymbol())
            .price(tick.getPrice())
            .volatility(volatility)
            .timestamp(tick.getTimestamp())
            .build();
            
        applicationEventPublisher.publishEvent(
            new RiskDataUpdateEvent(this, update));
    }
}`,
        performance: {
          'Tick Processing Latency': '< 500μs per tick',
          'Throughput': '100K+ ticks/second',
          'Data Quality': '99.99% clean data rate',
          'Correlation Update': '< 1ms matrix update',
          'Storage Efficiency': '90%+ compression ratio'
        },
        technologies: {
          'Database': 'InfluxDB time-series, Redis cache',
          'Feeds': 'Bloomberg, Reuters, IEX',
          'Processing': 'Java 17 Virtual Threads',
          'Analytics': 'Apache Commons Math, EJML',
          'Monitoring': 'Micrometer, Grafana'
        }
      },
      
      'portfolio-manager': {
        functions: [
          'Real-time position tracking and P&L calculation',
          'Multi-asset portfolio management (equity, bonds, derivatives)',
          'Currency exposure and hedging analysis',
          'Sector and geographic allocation monitoring',
          'Performance attribution and factor decomposition',
          'Benchmark comparison and tracking error calculation',
          'Liquidity analysis and days-to-liquidate estimation',
          'Concentration risk measurement and limits',
          'Portfolio optimization and rebalancing suggestions',
          'Trade impact analysis and execution cost estimation'
        ],
        workflow: `1. Receive trade confirmations from execution systems
2. Update position inventory in real-time
3. Calculate mark-to-market P&L using latest prices
4. Compute portfolio exposures by asset class/sector
5. Analyze currency exposures and hedge ratios
6. Calculate portfolio-level risk metrics
7. Monitor concentration limits and diversification
8. Generate performance attribution reports
9. Provide risk data to VaR calculation engines
10. Alert on significant position or P&L changes`,
        code: `@Component
@Transactional
public class PortfolioManager {
    
    private final PositionRepository positionRepo;
    private final PricingService pricingService;
    private final RiskMetricsCalculator riskCalc;
    private final BenchmarkService benchmarkService;
    private final CurrencyService currencyService;
    private final Map<String, Portfolio> portfolioCache;
    
    @EventListener
    @Async("portfolioPool")
    public void handleTradeExecution(TradeExecutionEvent event) {
        Trade trade = event.getTrade();
        String portfolioId = trade.getPortfolioId();
        
        try {
            // Update position atomically
            Position updatedPosition = updatePosition(trade);
            
            // Recalculate portfolio metrics
            Portfolio portfolio = getPortfolio(portfolioId);
            PortfolioMetrics metrics = calculatePortfolioMetrics(portfolio);
            
            // Check concentration limits
            checkConcentrationLimits(portfolio, updatedPosition);
            
            // Calculate incremental VaR impact
            double incrementalVaR = calculateIncrementalVaR(portfolio, trade);
            
            // Publish portfolio update event
            publishPortfolioUpdate(portfolioId, metrics, incrementalVaR);
            
            log.info("Portfolio {} updated: P&L={}, VaR impact={}",
                    portfolioId, metrics.getUnrealizedPnL(), incrementalVaR);
                    
        } catch (Exception e) {
            log.error("Failed to update portfolio {} for trade {}: {}",
                     portfolioId, trade.getTradeId(), e.getMessage());
        }
    }
    
    public PortfolioMetrics calculatePortfolioMetrics(Portfolio portfolio) {
        List<Position> positions = portfolio.getPositions();
        
        // Calculate total market value
        double totalMarketValue = positions.stream()
            .mapToDouble(this::calculatePositionValue)
            .sum();
        
        // Calculate unrealized P&L
        double unrealizedPnL = positions.stream()
            .mapToDouble(this::calculateUnrealizedPnL)
            .sum();
        
        // Calculate sector exposures
        Map<Sector, Double> sectorExposures = calculateSectorExposures(positions);
        
        // Calculate currency exposures
        Map<Currency, Double> currencyExposures = calculateCurrencyExposures(positions);
        
        // Calculate portfolio volatility
        double portfolioVolatility = calculatePortfolioVolatility(positions);
        
        // Calculate tracking error vs benchmark
        double trackingError = calculateTrackingError(portfolio);
        
        // Calculate maximum concentration
        double maxConcentration = positions.stream()
            .mapToDouble(pos -> Math.abs(calculatePositionValue(pos)) / totalMarketValue)
            .max()
            .orElse(0.0);
        
        return PortfolioMetrics.builder()
            .portfolioId(portfolio.getId())
            .totalMarketValue(totalMarketValue)
            .unrealizedPnL(unrealizedPnL)
            .sectorExposures(sectorExposures)
            .currencyExposures(currencyExposures)
            .volatility(portfolioVolatility)
            .trackingError(trackingError)
            .maxConcentration(maxConcentration)
            .timestamp(LocalDateTime.now())
            .build();
    }
    
    private double calculatePortfolioVolatility(List<Position> positions) {
        int n = positions.size();
        RealMatrix covarianceMatrix = new Array2DRowRealMatrix(n, n);
        RealVector weights = new ArrayRealVector(n);
        
        // Build weights vector
        double totalValue = positions.stream()
            .mapToDouble(this::calculatePositionValue)
            .sum();
            
        for (int i = 0; i < n; i++) {
            weights.setEntry(i, calculatePositionValue(positions.get(i)) / totalValue);
        }
        
        // Build covariance matrix
        for (int i = 0; i < n; i++) {
            for (int j = 0; j < n; j++) {
                String symbol1 = positions.get(i).getSymbol();
                String symbol2 = positions.get(j).getSymbol();
                
                double covariance = riskCalc.getCovariance(symbol1, symbol2, 
                                                          Duration.ofDays(252));
                covarianceMatrix.setEntry(i, j, covariance);
            }
        }
        
        // Calculate portfolio variance: w^T * Σ * w
        RealVector temp = covarianceMatrix.operate(weights);
        double portfolioVariance = weights.dotProduct(temp);
        
        return Math.sqrt(portfolioVariance * 252); // Annualized volatility
    }
    
    public double calculateIncrementalVaR(Portfolio portfolio, Trade trade) {
        // Calculate portfolio VaR before trade
        double varBefore = riskCalc.calculatePortfolioVaR(portfolio, 0.99, 1);
        
        // Simulate portfolio with new trade
        Portfolio simulatedPortfolio = portfolio.clone();
        simulatedPortfolio.addTrade(trade);
        
        // Calculate portfolio VaR after trade
        double varAfter = riskCalc.calculatePortfolioVaR(simulatedPortfolio, 0.99, 1);
        
        return varAfter - varBefore;
    }
    
    private void checkConcentrationLimits(Portfolio portfolio, Position position) {
        double positionValue = Math.abs(calculatePositionValue(position));
        double portfolioValue = calculatePortfolioValue(portfolio);
        double concentration = positionValue / portfolioValue;
        
        // Check position concentration limit (e.g., 5%)
        if (concentration > 0.05) {
            ConcentrationAlert alert = ConcentrationAlert.builder()
                .portfolioId(portfolio.getId())
                .symbol(position.getSymbol())
                .concentration(concentration)
                .limit(0.05)
                .severity(AlertSeverity.HIGH)
                .build();
                
            alertService.sendAlert(alert);
        }
        
        // Check sector concentration
        Sector sector = refDataService.getSector(position.getSymbol());
        double sectorConcentration = calculateSectorConcentration(portfolio, sector);
        
        if (sectorConcentration > 0.20) { // 20% sector limit
            SectorConcentrationAlert alert = SectorConcentrationAlert.builder()
                .portfolioId(portfolio.getId())
                .sector(sector)
                .concentration(sectorConcentration)
                .limit(0.20)
                .severity(AlertSeverity.MEDIUM)
                .build();
                
            alertService.sendAlert(alert);
        }
    }
    
    @Scheduled(fixedRate = 60000) // Every minute
    public void updateAllPortfolioMetrics() {
        List<String> activePortfolios = getActivePortfolioIds();
        
        activePortfolios.parallelStream().forEach(portfolioId -> {
            try {
                Portfolio portfolio = getPortfolio(portfolioId);
                PortfolioMetrics metrics = calculatePortfolioMetrics(portfolio);
                
                // Store metrics for historical analysis
                metricsRepository.save(metrics);
                
                // Update real-time dashboard
                dashboardService.updatePortfolioMetrics(metrics);
                
            } catch (Exception e) {
                log.error("Failed to update metrics for portfolio {}: {}",
                         portfolioId, e.getMessage());
            }
        });
    }
}`
      },
      
      'risk-calculator': {
        functions: [
          'Value at Risk (VaR) calculation using multiple methodologies',
          'Conditional Value at Risk (CVaR/ES) computation',
          'Monte Carlo simulation for complex portfolios',
          'Historical simulation with scenario generation',
          'Parametric VaR using covariance matrices',
          'Multi-horizon risk projections (1-day to 1-year)',
          'Component VaR and marginal VaR attribution',
          'Coherent risk measure implementation',
          'Tail risk analysis and extreme value theory',
          'Portfolio optimization under risk constraints'
        ],
        workflow: `1. Receive portfolio positions and market data
2. Select appropriate VaR methodology based on portfolio
3. Generate or retrieve historical scenarios
4. Run Monte Carlo simulations for complex instruments
5. Calculate parametric VaR using covariance approach
6. Compute CVaR as conditional expectation of tail losses
7. Perform component and marginal VaR attribution
8. Validate results using backtesting procedures
9. Generate risk reports and visualizations
10. Update risk limits and trigger alerts if breached`,
        code: `@Service
@Slf4j
public class RiskCalculator {
    
    private final MonteCarloEngine monteCarloEngine;
    private final HistoricalSimulation historicalSim;
    private final CovarianceEstimator covarianceEstimator;
    private final BacktestingEngine backtestEngine;
    private final ExecutorService riskCalculationPool;
    
    // VaR calculation using multiple methods
    public VaRResult calculateVaR(Portfolio portfolio, double confidence, int horizon) {
        long startTime = System.nanoTime();
        
        try {
            // Method 1: Historical Simulation
            double historicalVaR = calculateHistoricalVaR(portfolio, confidence, horizon);
            
            // Method 2: Monte Carlo Simulation
            double monteCarloVaR = calculateMonteCarloVaR(portfolio, confidence, horizon);
            
            // Method 3: Parametric (Variance-Covariance)
            double parametricVaR = calculateParametricVaR(portfolio, confidence, horizon);
            
            // Ensemble method - weighted average
            double ensembleVaR = calculateEnsembleVaR(historicalVaR, monteCarloVaR, parametricVaR);
            
            // Calculate CVaR (Expected Shortfall)
            double cvar = calculateCVaR(portfolio, confidence, horizon);
            
            // Component VaR attribution
            Map<String, Double> componentVaR = calculateComponentVaR(portfolio, confidence, horizon);
            
            // Marginal VaR for incremental analysis
            Map<String, Double> marginalVaR = calculateMarginalVaR(portfolio, confidence, horizon);
            
            long calculationTime = System.nanoTime() - startTime;
            
            return VaRResult.builder()
                .portfolioId(portfolio.getId())
                .confidence(confidence)
                .horizon(horizon)
                .historicalVaR(historicalVaR)
                .monteCarloVaR(monteCarloVaR)
                .parametricVaR(parametricVaR)
                .ensembleVaR(ensembleVaR)
                .conditionalVaR(cvar)
                .componentVaR(componentVaR)
                .marginalVaR(marginalVaR)
                .calculationTimeNanos(calculationTime)
                .timestamp(LocalDateTime.now())
                .build();
                
        } catch (Exception e) {
            log.error("VaR calculation failed for portfolio {}: {}", 
                     portfolio.getId(), e.getMessage());
            throw new RiskCalculationException("VaR calculation failed", e);
        }
    }
    
    private double calculateHistoricalVaR(Portfolio portfolio, double confidence, int horizon) {
        // Get historical scenarios (e.g., 2 years of daily returns)
        List<ScenarioResult> scenarios = historicalSim.generateScenarios(portfolio, 504, horizon);
        
        // Calculate P&L for each scenario
        double[] scenarioPnLs = scenarios.stream()
            .mapToDouble(ScenarioResult::getPnL)
            .sorted()
            .toArray();
        
        // Find percentile corresponding to confidence level
        int percentileIndex = (int) Math.ceil((1 - confidence) * scenarioPnLs.length) - 1;
        percentileIndex = Math.max(0, Math.min(percentileIndex, scenarioPnLs.length - 1));
        
        return -scenarioPnLs[percentileIndex]; // VaR is positive for losses
    }
    
    private double calculateMonteCarloVaR(Portfolio portfolio, double confidence, int horizon) {
        int numSimulations = 10000;
        
        // Generate correlated random returns using Cholesky decomposition
        List<Position> positions = portfolio.getPositions();
        int numAssets = positions.size();
        
        RealMatrix correlationMatrix = buildCorrelationMatrix(positions);
        CholeskyDecomposition cholesky = new CholeskyDecomposition(correlationMatrix);
        RealMatrix choleskyL = cholesky.getL();
        
        double[] simulatedPnLs = new double[numSimulations];
        Random random = new Random();
        
        for (int sim = 0; sim < numSimulations; sim++) {
            RealVector randomVector = new ArrayRealVector(numAssets);
            
            // Generate independent standard normal random variables
            for (int i = 0; i < numAssets; i++) {
                randomVector.setEntry(i, random.nextGaussian());
            }
            
            // Apply correlation structure
            RealVector correlatedReturns = choleskyL.operate(randomVector);
            
            // Calculate portfolio P&L for this simulation
            double portfolioPnL = 0.0;
            for (int i = 0; i < numAssets; i++) {
                Position position = positions.get(i);
                double return_i = correlatedReturns.getEntry(i);
                double volatility = getVolatility(position.getSymbol());
                
                // Scale by volatility and time horizon
                double scaledReturn = return_i * volatility * Math.sqrt(horizon);
                double positionPnL = position.getMarketValue() * scaledReturn;
                
                portfolioPnL += positionPnL;
            }
            
            simulatedPnLs[sim] = portfolioPnL;
        }
        
        // Sort and find VaR
        Arrays.sort(simulatedPnLs);
        int varIndex = (int) Math.ceil((1 - confidence) * numSimulations) - 1;
        
        return -simulatedPnLs[varIndex];
    }
    
    private double calculateParametricVaR(Portfolio portfolio, double confidence, int horizon) {
        List<Position> positions = portfolio.getPositions();
        int numPositions = positions.size();
        
        // Build portfolio weights vector
        double portfolioValue = portfolio.getTotalMarketValue();
        RealVector weights = new ArrayRealVector(numPositions);
        
        for (int i = 0; i < numPositions; i++) {
            weights.setEntry(i, positions.get(i).getMarketValue() / portfolioValue);
        }
        
        // Build covariance matrix
        RealMatrix covarianceMatrix = covarianceEstimator.estimateCovariance(positions);
        
        // Calculate portfolio variance: w^T * Σ * w
        RealVector temp = covarianceMatrix.operate(weights);
        double portfolioVariance = weights.dotProduct(temp);
        
        // Scale by time horizon
        double portfolioStdDev = Math.sqrt(portfolioVariance * horizon);
        
        // Calculate VaR using normal distribution assumption
        NormalDistribution normalDist = new NormalDistribution();
        double zScore = normalDist.inverseCumulativeProbability(1 - confidence);
        
        return portfolioValue * portfolioStdDev * Math.abs(zScore);
    }
    
    private double calculateCVaR(Portfolio portfolio, double confidence, int horizon) {
        // Use Monte Carlo method for CVaR calculation
        int numSimulations = 100000;
        double[] simulatedPnLs = monteCarloEngine.simulatePortfolioPnL(
            portfolio, numSimulations, horizon);
        
        Arrays.sort(simulatedPnLs);
        
        // Find VaR threshold
        int varIndex = (int) Math.ceil((1 - confidence) * numSimulations) - 1;
        double varThreshold = simulatedPnLs[varIndex];
        
        // Calculate expected value of losses beyond VaR
        double tailSum = 0.0;
        int tailCount = 0;
        
        for (int i = 0; i <= varIndex; i++) {
            tailSum += simulatedPnLs[i];
            tailCount++;
        }
        
        return tailCount > 0 ? -tailSum / tailCount : 0.0;
    }
    
    private Map<String, Double> calculateComponentVaR(Portfolio portfolio, double confidence, int horizon) {
        double portfolioVaR = calculateParametricVaR(portfolio, confidence, horizon);
        Map<String, Double> componentVaR = new HashMap<>();
        
        List<Position> positions = portfolio.getPositions();
        
        for (Position position : positions) {
            // Create portfolio without this position
            Portfolio portfolioWithoutPosition = portfolio.clone();
            portfolioWithoutPosition.removePosition(position.getSymbol());
            
            double varWithoutPosition = calculateParametricVaR(portfolioWithoutPosition, confidence, horizon);
            double diversificationBenefit = portfolioVaR - varWithoutPosition;
            
            // Component VaR = Stand-alone VaR - Diversification benefit
            Portfolio singleAssetPortfolio = Portfolio.builder()
                .positions(Arrays.asList(position))
                .build();
            double standaloneVaR = calculateParametricVaR(singleAssetPortfolio, confidence, horizon);
            
            double compVaR = standaloneVaR - diversificationBenefit;
            componentVaR.put(position.getSymbol(), compVaR);
        }
        
        return componentVaR;
    }
    
    @Scheduled(fixedRate = 300000) // Every 5 minutes
    public void performBacktesting() {
        List<String> portfolioIds = getActivePortfolioIds();
        
        portfolioIds.forEach(portfolioId -> {
            try {
                BacktestResult result = backtestEngine.performBacktest(
                    portfolioId, LocalDate.now().minusMonths(6), LocalDate.now());
                
                // Check for model violations
                if (result.getViolationRate() > 0.05) { // More than 5% violations
                    log.warn("VaR model violations for portfolio {}: {}%", 
                            portfolioId, result.getViolationRate() * 100);
                    
                    // Trigger model recalibration
                    triggerModelRecalibration(portfolioId);
                }
                
            } catch (Exception e) {
                log.error("Backtesting failed for portfolio {}: {}", 
                         portfolioId, e.getMessage());
            }
        });
    }
}`
      },
      
      'scenario-engine': {
        functions: [
          'Stress testing scenarios with extreme market conditions',
          'What-if analysis for portfolio sensitivity',
          'Tail risk scenarios and black swan events',
          'Interest rate shock scenarios',
          'Credit spread widening simulations',
          'Market crash scenario generation',
          'Liquidity crisis modeling',
          'Correlation breakdown analysis',
          'Model parameter sensitivity testing',
          'Regulatory stress test compliance'
        ],
        workflow: `1. Define stress test scenarios based on historical events
2. Generate what-if analysis parameters
3. Apply stress scenarios to portfolio positions
4. Calculate P&L impact under stress conditions
5. Measure tail risk and extreme quantiles
6. Test correlation breakdown scenarios
7. Assess liquidity impact under stress
8. Generate regulatory stress test reports
9. Validate scenario robustness
10. Update scenario library with new patterns`,
        code: `@Service
@Slf4j
public class ScenarioEngine {
    
    private final MonteCarloEngine monteCarloEngine;
    private final HistoricalDataService historicalData;
    private final CorrelationService correlationService;
    private final VolatilityService volatilityService;
    private final Map<String, StressScenario> scenarioLibrary;
    
    @PostConstruct
    public void initializeScenarios() {
        // Load predefined stress scenarios
        scenarioLibrary.put("2008_CRISIS", createFinancialCrisisScenario());
        scenarioLibrary.put("COVID_SHOCK", createCovidShockScenario());
        scenarioLibrary.put("DOTCOM_CRASH", createDotcomCrashScenario());
        scenarioLibrary.put("RATE_SHOCK_UP", createRateShockUpScenario());
        scenarioLibrary.put("RATE_SHOCK_DOWN", createRateShockDownScenario());
        scenarioLibrary.put("CREDIT_CRISIS", createCreditCrisisScenario());
        scenarioLibrary.put("LIQUIDITY_CRISIS", createLiquidityCrisisScenario());
    }
    
    public StressTestResult runStressTest(Portfolio portfolio, String scenarioId) {
        StressScenario scenario = scenarioLibrary.get(scenarioId);
        if (scenario == null) {
            throw new IllegalArgumentException("Unknown scenario: " + scenarioId);
        }
        
        long startTime = System.nanoTime();
        
        try {
            // Apply scenario shocks to market data
            MarketDataSnapshot stressedData = applyScenarioShocks(scenario);
            
            // Calculate stressed portfolio value
            double baseValue = calculatePortfolioValue(portfolio);
            double stressedValue = calculateStressedValue(portfolio, stressedData);
            double pnlImpact = stressedValue - baseValue;
            
            // Component-wise stress impact
            Map<String, Double> componentStress = new HashMap<>();
            for (Position position : portfolio.getPositions()) {
                double positionStress = calculatePositionStress(position, stressedData);
                componentStress.put(position.getSymbol(), positionStress);
            }
            
            // Liquidity impact analysis
            LiquidityImpact liquidityImpact = assessLiquidityImpact(portfolio, scenario);
            
            // Correlation breakdown analysis
            CorrelationBreakdown corrBreakdown = analyzeCorrelationBreakdown(portfolio, scenario);
            
            long calculationTime = System.nanoTime() - startTime;
            
            return StressTestResult.builder()
                .portfolioId(portfolio.getId())
                .scenarioId(scenarioId)
                .scenarioName(scenario.getName())
                .baseValue(baseValue)
                .stressedValue(stressedValue)
                .pnlImpact(pnlImpact)
                .relativeImpact(pnlImpact / baseValue)
                .componentStress(componentStress)
                .liquidityImpact(liquidityImpact)
                .correlationBreakdown(corrBreakdown)
                .calculationTimeNanos(calculationTime)
                .timestamp(LocalDateTime.now())
                .build();
                
        } catch (Exception e) {
            log.error("Stress test failed for portfolio {} with scenario {}: {}", 
                     portfolio.getId(), scenarioId, e.getMessage());
            throw new StressTestException("Stress test execution failed", e);
        }
    }
    
    public WhatIfResult performWhatIfAnalysis(Portfolio portfolio, WhatIfScenario whatIf) {
        // Clone portfolio for what-if analysis
        Portfolio testPortfolio = portfolio.clone();
        
        // Apply what-if changes
        for (TradeAction action : whatIf.getActions()) {
            switch (action.getType()) {
                case ADD_POSITION:
                    testPortfolio.addPosition(action.getPosition());
                    break;
                case REMOVE_POSITION:
                    testPortfolio.removePosition(action.getSymbol());
                    break;
                case MODIFY_POSITION:
                    testPortfolio.updatePosition(action.getSymbol(), action.getNewQuantity());
                    break;
                case HEDGE_POSITION:
                    Position hedge = createHedgePosition(action.getPosition(), action.getHedgeRatio());
                    testPortfolio.addPosition(hedge);
                    break;
            }
        }
        
        // Calculate impact metrics
        double originalVaR = calculatePortfolioVaR(portfolio, 0.99, 1);
        double newVaR = calculatePortfolioVaR(testPortfolio, 0.99, 1);
        double incrementalVaR = newVaR - originalVaR;
        
        double originalCVaR = calculatePortfolioCVaR(portfolio, 0.99, 1);
        double newCVaR = calculatePortfolioCVaR(testPortfolio, 0.99, 1);
        double incrementalCVaR = newCVaR - originalCVaR;
        
        // Portfolio composition analysis
        PortfolioMetrics originalMetrics = calculatePortfolioMetrics(portfolio);
        PortfolioMetrics newMetrics = calculatePortfolioMetrics(testPortfolio);
        
        return WhatIfResult.builder()
            .portfolioId(portfolio.getId())
            .scenario(whatIf)
            .originalVaR(originalVaR)
            .newVaR(newVaR)
            .incrementalVaR(incrementalVaR)
            .originalCVaR(originalCVaR)
            .newCVaR(newCVaR)
            .incrementalCVaR(incrementalCVaR)
            .originalMetrics(originalMetrics)
            .newMetrics(newMetrics)
            .riskContribution(calculateRiskContribution(testPortfolio))
            .timestamp(LocalDateTime.now())
            .build();
    }
    
    private StressScenario createFinancialCrisisScenario() {
        return StressScenario.builder()
            .name("2008 Financial Crisis")
            .description("Replicate 2008 financial crisis conditions")
            .equityShock(-0.45) // 45% equity decline
            .creditSpreadShock(0.0500) // 500bp credit spread widening
            .interestRateShock(-0.02) // 200bp rate cut
            .volatilityShock(2.5) // 250% volatility increase
            .correlationShock(0.85) // High correlation in stress
            .liquidityShock(0.7) // 70% liquidity reduction
            .currencyShocks(Map.of(
                "USD", 0.0,
                "EUR", -0.15,
                "GBP", -0.12,
                "JPY", 0.08
            ))
            .build();
    }
    
    private TailRiskMetrics calculateTailRisk(Portfolio portfolio, double threshold) {
        // Generate large number of scenarios for tail analysis
        int numSimulations = 100000;
        List<Double> scenarioPnLs = new ArrayList<>();
        
        for (int i = 0; i < numSimulations; i++) {
            MarketDataSnapshot scenario = monteCarloEngine.generateScenario();
            double pnl = calculateScenarioPnL(portfolio, scenario);
            scenarioPnLs.add(pnl);
        }
        
        // Sort P&Ls for tail analysis
        scenarioPnLs.sort(Double::compareTo);
        
        // Calculate tail statistics
        int tailIndex = (int) (threshold * scenarioPnLs.size());
        List<Double> tailPnLs = scenarioPnLs.subList(0, tailIndex);
        
        double tailMean = tailPnLs.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double tailStd = calculateStandardDeviation(tailPnLs);
        double extremeTail = scenarioPnLs.get(Math.max(0, tailIndex / 10)); // 0.1% tail
        
        return TailRiskMetrics.builder()
            .threshold(threshold)
            .tailMean(tailMean)
            .tailStandardDeviation(tailStd)
            .extremeTailValue(extremeTail)
            .tailRatio(Math.abs(tailMean / extremeTail))
            .build();
    }
    
    @Scheduled(cron = "0 0 2 * * MON") // Weekly on Monday at 2 AM
    public void runRegularStressTests() {
        List<String> activePortfolios = getActivePortfolioIds();
        
        for (String portfolioId : activePortfolios) {
            try {
                Portfolio portfolio = getPortfolio(portfolioId);
                
                // Run all predefined scenarios
                for (String scenarioId : scenarioLibrary.keySet()) {
                    StressTestResult result = runStressTest(portfolio, scenarioId);
                    storeStressTestResult(result);
                    
                    // Check for excessive risk
                    if (Math.abs(result.getRelativeImpact()) > 0.20) { // 20% loss threshold
                        triggerStressTestAlert(result);
                    }
                }
                
                // Custom tail risk analysis
                TailRiskMetrics tailRisk = calculateTailRisk(portfolio, 0.01); // 1% tail
                storeTailRiskMetrics(portfolioId, tailRisk);
                
            } catch (Exception e) {
                log.error("Regular stress test failed for portfolio {}: {}", 
                         portfolioId, e.getMessage());
            }
        }
    }
}`,
        performance: {
          scenarioExecutionTime: '< 500ms',
          concurrentScenarios: '20+ parallel',
          memoryUsage: '< 2GB per test',
          throughput: '1000+ scenarios/min'
        },
        technologies: {
          'Simulation Engine': 'Custom Monte Carlo with variance reduction',
          'Scenario Storage': 'Redis for fast scenario caching',
          'Parallel Processing': 'Java 17 Virtual Threads',
          'Statistical Computing': 'Apache Commons Math'
        }
      },
      
      'reporting-system': {
        functions: [
          'Real-time risk dashboard generation',
          'Regulatory reporting automation',
          'Executive risk summary reports',
          'Detailed portfolio analytics reports',
          'Limit breach notifications and alerts',
          'Historical risk trend analysis',
          'Peer benchmark comparisons',
          'Custom report template engine',
          'Multi-format export capabilities',
          'Automated report distribution'
        ],
        workflow: `1. Collect risk metrics from all calculation engines
2. Aggregate data across portfolios and time horizons
3. Apply report templates and formatting rules
4. Generate interactive charts and visualizations
5. Validate data quality and completeness
6. Apply user access controls and permissions
7. Schedule and distribute reports automatically
8. Archive reports for regulatory compliance
9. Generate alert notifications for breaches
10. Maintain audit trails for all reporting activities`,
        code: `@Service
@Slf4j
public class ReportingSystem {
    
    private final ReportTemplateEngine templateEngine;
    private final ChartGenerationService chartService;
    private final NotificationService notificationService;
    private final ReportRepository reportRepository;
    private final UserPermissionService permissionService;
    private final ScheduledTaskService taskService;
    
    @Autowired
    public ReportingSystem(ReportTemplateEngine templateEngine,
                          ChartGenerationService chartService,
                          NotificationService notificationService) {
        this.templateEngine = templateEngine;
        this.chartService = chartService;
        this.notificationService = notificationService;
    }
    
    public GeneratedReport generateRiskReport(ReportRequest request) {
        long startTime = System.nanoTime();
        
        try {
            // Validate user permissions
            if (!permissionService.hasReportAccess(request.getUserId(), request.getReportType())) {
                throw new UnauthorizedAccessException("Insufficient permissions for report type");
            }
            
            // Collect data based on report parameters
            ReportData data = collectReportData(request);
            
            // Generate charts and visualizations
            List<Chart> charts = generateCharts(data, request.getChartTypes());
            
            // Apply report template
            ReportTemplate template = templateEngine.getTemplate(request.getTemplateId());
            String reportContent = templateEngine.render(template, data, charts);
            
            // Create report metadata
            ReportMetadata metadata = ReportMetadata.builder()
                .reportId(UUID.randomUUID().toString())
                .reportType(request.getReportType())
                .generatedBy(request.getUserId())
                .generatedAt(LocalDateTime.now())
                .parameters(request.getParameters())
                .dataAsOf(data.getDataAsOf())
                .build();
            
            // Export in requested formats
            Map<String, byte[]> exports = new HashMap<>();
            for (ReportFormat format : request.getFormats()) {
                byte[] exportData = exportReport(reportContent, format, charts);
                exports.put(format.toString(), exportData);
            }
            
            // Store report for future reference
            GeneratedReport report = GeneratedReport.builder()
                .metadata(metadata)
                .content(reportContent)
                .charts(charts)
                .exports(exports)
                .generationTimeNanos(System.nanoTime() - startTime)
                .build();
            
            reportRepository.save(report);
            
            // Send notifications if configured
            if (request.isNotifyOnCompletion()) {
                sendReportNotification(report, request.getNotificationRecipients());
            }
            
            return report;
            
        } catch (Exception e) {
            log.error("Report generation failed for request {}: {}", 
                     request.getReportId(), e.getMessage());
            throw new ReportGenerationException("Failed to generate report", e);
        }
    }
    
    private ReportData collectReportData(ReportRequest request) {
        ReportDataBuilder builder = ReportData.builder();
        
        switch (request.getReportType()) {
            case DAILY_RISK_SUMMARY:
                builder.portfolioMetrics(getPortfolioMetrics(request.getPortfolioIds()))
                       .varMetrics(getVaRMetrics(request.getPortfolioIds()))
                       .stressTestResults(getLatestStressTests(request.getPortfolioIds()))
                       .limitBreaches(getLimitBreaches(request.getDateRange()));
                break;
                
            case REGULATORY_BASEL:
                builder.capitalRequirements(getCapitalRequirements(request.getPortfolioIds()))
                       .riskWeightedAssets(getRiskWeightedAssets(request.getPortfolioIds()))
                       .leverageRatios(getLeverageRatios(request.getPortfolioIds()))
                       .liquidityRatios(getLiquidityRatios(request.getPortfolioIds()));
                break;
                
            case EXECUTIVE_SUMMARY:
                builder.keyRiskMetrics(getKeyRiskMetrics(request.getPortfolioIds()))
                       .peerBenchmarks(getPeerBenchmarks())
                       .trendAnalysis(getTrendAnalysis(request.getDateRange()))
                       .topRisks(getTopRisks(request.getPortfolioIds()));
                break;
                
            case PORTFOLIO_ATTRIBUTION:
                builder.componentVaR(getComponentVaR(request.getPortfolioIds()))
                       .sectorAllocation(getSectorAllocation(request.getPortfolioIds()))
                       .performanceAttribution(getPerformanceAttribution(request.getDateRange()))
                       .riskContribution(getRiskContribution(request.getPortfolioIds()));
                break;
        }
        
        return builder.dataAsOf(LocalDateTime.now()).build();
    }
    
    private List<Chart> generateCharts(ReportData data, List<ChartType> chartTypes) {
        List<Chart> charts = new ArrayList<>();
        
        for (ChartType type : chartTypes) {
            switch (type) {
                case VAR_TREND:
                    Chart varChart = chartService.createTimeSeriesChart(
                        "VaR Trend", data.getVarMetrics(), "date", "var");
                    charts.add(varChart);
                    break;
                    
                case RISK_HEATMAP:
                    Chart heatmap = chartService.createHeatmapChart(
                        "Risk Heatmap", data.getComponentVaR());
                    charts.add(heatmap);
                    break;
                    
                case SECTOR_ALLOCATION:
                    Chart pieChart = chartService.createPieChart(
                        "Sector Allocation", data.getSectorAllocation());
                    charts.add(pieChart);
                    break;
                    
                case STRESS_TEST_RESULTS:
                    Chart barChart = chartService.createBarChart(
                        "Stress Test Results", data.getStressTestResults());
                    charts.add(barChart);
                    break;
                    
                case LIMIT_UTILIZATION:
                    Chart gaugeChart = chartService.createGaugeChart(
                        "Limit Utilization", data.getLimitUtilization());
                    charts.add(gaugeChart);
                    break;
            }
        }
        
        return charts;
    }
    
    @Scheduled(cron = "0 30 7 * * MON-FRI") // Weekdays at 7:30 AM
    public void generateDailyRiskReports() {
        List<String> activePortfolios = getActivePortfolioIds();
        
        // Generate daily risk summary for each business unit
        Map<String, List<String>> businessUnits = groupPortfoliosByBusinessUnit(activePortfolios);
        
        for (Map.Entry<String, List<String>> entry : businessUnits.entrySet()) {
            try {
                String businessUnit = entry.getKey();
                List<String> portfolios = entry.getValue();
                
                ReportRequest request = ReportRequest.builder()
                    .reportType(ReportType.DAILY_RISK_SUMMARY)
                    .portfolioIds(portfolios)
                    .userId("SYSTEM_SCHEDULER")
                    .templateId("daily_risk_template")
                    .formats(Arrays.asList(ReportFormat.PDF, ReportFormat.EXCEL))
                    .chartTypes(Arrays.asList(
                        ChartType.VAR_TREND,
                        ChartType.RISK_HEATMAP,
                        ChartType.LIMIT_UTILIZATION
                    ))
                    .notifyOnCompletion(true)
                    .notificationRecipients(getRiskManagerEmails(businessUnit))
                    .build();
                
                GeneratedReport report = generateRiskReport(request);
                
                // Auto-distribute to risk committee if high risk detected
                if (detectHighRiskConditions(report)) {
                    escalateToRiskCommittee(report, businessUnit);
                }
                
            } catch (Exception e) {
                log.error("Failed to generate daily risk report for business unit {}: {}", 
                         entry.getKey(), e.getMessage());
            }
        }
    }
    
    public void generateRegulatoryReport(RegulatoryReportRequest request) {
        // Basel III/IV regulatory reporting
        if (request.getRegulation() == Regulation.BASEL_III) {
            generateBaselReport(request);
        }
        
        // CCAR stress testing
        else if (request.getRegulation() == Regulation.CCAR) {
            generateCCARReport(request);
        }
        
        // FRTB market risk
        else if (request.getRegulation() == Regulation.FRTB) {
            generateFRTBReport(request);
        }
    }
    
    private void generateBaselReport(RegulatoryReportRequest request) {
        // Calculate regulatory capital requirements
        Map<String, Double> capitalRequirements = calculateCapitalRequirements(request.getPortfolioIds());
        
        // Risk-weighted assets calculation
        Map<String, Double> riskWeightedAssets = calculateRiskWeightedAssets(request.getPortfolioIds());
        
        // Capital ratios
        CapitalRatios ratios = calculateCapitalRatios(capitalRequirements, riskWeightedAssets);
        
        // Generate regulatory XML/CSV formats
        String xmlReport = generateBaselXML(capitalRequirements, riskWeightedAssets, ratios);
        String csvReport = generateBaselCSV(capitalRequirements, riskWeightedAssets, ratios);
        
        // Submit to regulatory systems
        submitRegulatoryReport(xmlReport, ReportFormat.XML, request);
        submitRegulatoryReport(csvReport, ReportFormat.CSV, request);
    }
    
    private boolean detectHighRiskConditions(GeneratedReport report) {
        // Check for VaR breaches
        boolean varBreach = report.getData().getVarMetrics().values().stream()
            .anyMatch(var -> var > getVarThreshold());
        
        // Check for limit breaches
        boolean limitBreach = !report.getData().getLimitBreaches().isEmpty();
        
        // Check for stress test failures
        boolean stressFailure = report.getData().getStressTestResults().values().stream()
            .anyMatch(result -> Math.abs(result) > 0.15); // 15% threshold
        
        return varBreach || limitBreach || stressFailure;
    }
}`,
        performance: {
          reportGenerationTime: '< 10 seconds',
          concurrentReports: '50+ simultaneous',
          chartRenderTime: '< 2 seconds',
          memoryFootprint: '< 1GB per report'
        },
        technologies: {
          'Template Engine': 'Apache FreeMarker with custom extensions',
          'Chart Generation': 'JFreeChart with D3.js integration',
          'Export Formats': 'PDF (iText), Excel (Apache POI), CSV',
          'Notification System': 'Apache Kafka + Email/SMS gateways'
        }
      },
      
      'calibration-engine': {
        functions: [
          'Model parameter estimation and optimization',
          'Backtesting framework for model validation',
          'Model performance monitoring and drift detection',
          'Automated model recalibration triggers',
          'Cross-validation and out-of-sample testing',
          'Model comparison and champion/challenger framework',
          'Statistical significance testing',
          'Model documentation and lineage tracking',
          'Regulatory model validation compliance',
          'A/B testing framework for model improvements'
        ],
        workflow: `1. Monitor model performance metrics continuously
2. Detect model drift and performance degradation
3. Trigger recalibration when thresholds exceeded
4. Collect fresh training data from market feeds
5. Estimate new model parameters using ML techniques
6. Validate new models using cross-validation
7. Compare new vs existing model performance
8. Run champion/challenger testing framework
9. Deploy approved models to production
10. Update model documentation and audit trails`,
        code: `@Service
@Slf4j
public class ModelCalibrationEngine {
    
    private final ModelRepository modelRepository;
    private final BacktestingEngine backtestEngine;
    private final StatisticalTestService statisticalTests;
    private final ModelValidationService validationService;
    private final MarketDataService marketData;
    private final MLModelService mlModelService;
    
    // Model performance monitoring
    @Scheduled(fixedRate = 3600000) // Every hour
    public void monitorModelPerformance() {
        List<RiskModel> activeModels = modelRepository.findActiveModels();
        
        for (RiskModel model : activeModels) {
            try {
                ModelPerformanceMetrics metrics = calculatePerformanceMetrics(model);
                
                // Check for drift
                if (detectModelDrift(model, metrics)) {
                    log.warn("Model drift detected for model {}: {}", 
                            model.getModelId(), metrics.getDriftScore());
                    scheduleRecalibration(model, RecalibrationReason.MODEL_DRIFT);
                }
                
                // Check accuracy degradation
                if (metrics.getAccuracy() < model.getMinAccuracyThreshold()) {
                    log.warn("Accuracy degradation for model {}: {} < {}", 
                            model.getModelId(), metrics.getAccuracy(), 
                            model.getMinAccuracyThreshold());
                    scheduleRecalibration(model, RecalibrationReason.ACCURACY_DEGRADATION);
                }
                
                // Store performance metrics
                storePerformanceMetrics(model.getModelId(), metrics);
                
            } catch (Exception e) {
                log.error("Performance monitoring failed for model {}: {}", 
                         model.getModelId(), e.getMessage());
            }
        }
    }
    
    public CalibrationResult recalibrateModel(String modelId) {
        long startTime = System.nanoTime();
        
        try {
            RiskModel currentModel = modelRepository.findById(modelId);
            if (currentModel == null) {
                throw new ModelNotFoundException("Model not found: " + modelId);
            }
            
            // Collect training data
            TrainingDataset dataset = collectTrainingData(currentModel);
            
            // Prepare data for training
            ProcessedDataset processedData = preprocessTrainingData(dataset);
            
            // Split data for cross-validation
            DatasetSplit dataSplit = createTrainValidationSplit(processedData, 0.8);
            
            // Train new model parameters
            ModelParameters newParameters = trainModel(currentModel, dataSplit.getTrainingSet());
            
            // Create candidate model
            RiskModel candidateModel = currentModel.cloneWithNewParameters(newParameters);
            candidateModel.setModelId(generateCandidateModelId(modelId));
            candidateModel.setCalibrationDate(LocalDateTime.now());
            
            // Validate candidate model
            ValidationResult validation = validateModel(candidateModel, dataSplit.getValidationSet());
            
            // Backtest candidate model
            BacktestResult backtest = backtestEngine.runBacktest(candidateModel, 
                                                               LocalDate.now().minusMonths(12),
                                                               LocalDate.now().minusDays(1));
            
            // Compare with current model
            ModelComparison comparison = compareModels(currentModel, candidateModel, 
                                                      dataSplit.getValidationSet());
            
            // Statistical significance test
            SignificanceTestResult significance = statisticalTests.testModelImprovement(
                currentModel, candidateModel, dataSplit.getValidationSet());
            
            CalibrationResult result = CalibrationResult.builder()
                .originalModelId(modelId)
                .candidateModelId(candidateModel.getModelId())
                .validationResult(validation)
                .backtestResult(backtest)
                .modelComparison(comparison)
                .significanceTest(significance)
                .calibrationTimeNanos(System.nanoTime() - startTime)
                .recommendedAction(determineRecommendedAction(validation, backtest, 
                                                            comparison, significance))
                .build();
            
            // Auto-approve if improvement is significant and validation passed
            if (shouldAutoApprove(result)) {
                approveModelCandidate(candidateModel);
            } else {
                // Submit for manual review
                submitForReview(result);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("Model recalibration failed for model {}: {}", modelId, e.getMessage());
            throw new CalibrationException("Model recalibration failed", e);
        }
    }
    
    private ModelParameters trainModel(RiskModel baseModel, TrainingData trainingData) {
        switch (baseModel.getModelType()) {
            case GARCH_VOLATILITY:
                return trainGARCHModel(trainingData);
                
            case MONTE_CARLO_VAR:
                return trainMonteCarloParameters(trainingData);
                
            case COPULA_CORRELATION:
                return trainCopulaModel(trainingData);
                
            case MACHINE_LEARNING:
                return trainMLModel(baseModel, trainingData);
                
            default:
                throw new UnsupportedOperationException("Model type not supported: " + 
                                                       baseModel.getModelType());
        }
    }
    
    private ModelParameters trainGARCHModel(TrainingData data) {
        // GARCH(1,1) parameter estimation using MLE
        double[] returns = data.getReturns();
        
        // Initial parameter guess
        double omega = 0.001;
        double alpha = 0.1;
        double beta = 0.8;
        
        // Maximum likelihood estimation
        GarchOptimizer optimizer = new GarchOptimizer();
        GarchParameters params = optimizer.estimateParameters(returns, omega, alpha, beta);
        
        // Validate parameter constraints
        if (!isValidGarchParameters(params)) {
            throw new CalibrationException("Invalid GARCH parameters estimated");
        }
        
        return ModelParameters.builder()
            .parameterType(ParameterType.GARCH)
            .omega(params.getOmega())
            .alpha(params.getAlpha())
            .beta(params.getBeta())
            .logLikelihood(params.getLogLikelihood())
            .build();
    }
    
    private ModelParameters trainMLModel(RiskModel baseModel, TrainingData data) {
        // Feature engineering
        FeatureMatrix features = createFeatures(data);
        
        // Model selection based on performance
        MLModelType bestModelType = selectBestMLModel(features);
        
        switch (bestModelType) {
            case RANDOM_FOREST:
                return trainRandomForestModel(features);
                
            case GRADIENT_BOOSTING:
                return trainGradientBoostingModel(features);
                
            case NEURAL_NETWORK:
                return trainNeuralNetworkModel(features);
                
            case LINEAR_REGRESSION:
                return trainLinearRegressionModel(features);
                
            default:
                throw new UnsupportedOperationException("ML model type not supported");
        }
    }
    
    private ValidationResult validateModel(RiskModel model, ValidationData validationData) {
        List<ValidationTest> tests = Arrays.asList(
            new AccuracyTest(),
            new CoverageTest(),
            new IndependenceTest(),
            new UnconditionalCoverageTest(),
            new ConditionalCoverageTest(),
            new DurationBasedTest(),
            new MagnitudeTest()
        );
        
        Map<String, TestResult> testResults = new HashMap<>();
        boolean overallPass = true;
        
        for (ValidationTest test : tests) {
            try {
                TestResult result = test.runTest(model, validationData);
                testResults.put(test.getTestName(), result);
                
                if (!result.isPassed()) {
                    overallPass = false;
                    log.warn("Model validation failed test {}: {}", 
                            test.getTestName(), result.getFailureReason());
                }
                
            } catch (Exception e) {
                log.error("Validation test {} failed with exception: {}", 
                         test.getTestName(), e.getMessage());
                overallPass = false;
                testResults.put(test.getTestName(), TestResult.failed(e.getMessage()));
            }
        }
        
        return ValidationResult.builder()
            .modelId(model.getModelId())
            .overallPass(overallPass)
            .testResults(testResults)
            .validationDate(LocalDateTime.now())
            .build();
    }
    
    private ModelComparison compareModels(RiskModel currentModel, RiskModel candidateModel, 
                                        ValidationData data) {
        // Calculate metrics for both models
        ModelMetrics currentMetrics = calculateModelMetrics(currentModel, data);
        ModelMetrics candidateMetrics = calculateModelMetrics(candidateModel, data);
        
        // Compare key performance indicators
        double accuracyImprovement = candidateMetrics.getAccuracy() - currentMetrics.getAccuracy();
        double logLikelihoodImprovement = candidateMetrics.getLogLikelihood() - 
                                        currentMetrics.getLogLikelihood();
        double aicImprovement = currentMetrics.getAic() - candidateMetrics.getAic(); // Lower is better
        double bicImprovement = currentMetrics.getBic() - candidateMetrics.getBic(); // Lower is better
        
        return ModelComparison.builder()
            .currentModelId(currentModel.getModelId())
            .candidateModelId(candidateModel.getModelId())
            .currentMetrics(currentMetrics)
            .candidateMetrics(candidateMetrics)
            .accuracyImprovement(accuracyImprovement)
            .logLikelihoodImprovement(logLikelihoodImprovement)
            .aicImprovement(aicImprovement)
            .bicImprovement(bicImprovement)
            .overallImprovement(calculateOverallImprovement(accuracyImprovement,
                                                           logLikelihoodImprovement,
                                                           aicImprovement,
                                                           bicImprovement))
            .build();
    }
    
    @EventListener
    public void handleModelApproval(ModelApprovalEvent event) {
        try {
            RiskModel newModel = event.getApprovedModel();
            RiskModel oldModel = modelRepository.findById(event.getOriginalModelId());
            
            // Deploy new model
            deployModelToProduction(newModel);
            
            // Archive old model
            archiveModel(oldModel);
            
            // Update model lineage
            updateModelLineage(oldModel, newModel);
            
            // Notify stakeholders
            notifyModelDeployment(newModel, oldModel);
            
            log.info("Successfully deployed new model {} replacing {}", 
                    newModel.getModelId(), oldModel.getModelId());
                    
        } catch (Exception e) {
            log.error("Failed to deploy approved model {}: {}", 
                     event.getApprovedModel().getModelId(), e.getMessage());
            // Rollback if deployment fails
            rollbackModelDeployment(event);
        }
    }
}`,
        performance: {
          calibrationTime: '< 30 minutes',
          backtestingSpeed: '1M+ scenarios/hour',
          memoryUsage: '< 4GB during training',
          modelValidationTime: '< 5 minutes'
        },
        technologies: {
          'ML Framework': 'TensorFlow/PyTorch via JNI',
          'Optimization': 'Apache Commons Math, COLT',
          'Statistical Tests': 'Custom implementation + R integration',
          'Model Storage': 'MongoDB with versioning'
        }
      },
      
      'limit-monitor': {
        functions: [
          'Real-time limit monitoring and breach detection',
          'Multi-level escalation workflows',
          'Dynamic limit adjustment based on market conditions',
          'Cross-portfolio limit aggregation',
          'Limit utilization forecasting',
          'Regulatory limit compliance monitoring',
          'Automated position blocking for limit breaches',
          'Limit exception approval workflows',
          'Historical limit utilization analysis',
          'Stress test limit validation'
        ],
        workflow: `1. Monitor portfolio positions and exposures in real-time
2. Calculate current limit utilization across all limit types
3. Detect limit breaches and near-breach conditions
4. Trigger immediate alerts to risk management
5. Execute automated risk controls (position blocking)
6. Initiate escalation workflows for severe breaches
7. Log all limit events for audit and compliance
8. Generate limit utilization reports
9. Update limit forecasts based on trading activity
10. Validate limits against regulatory requirements`,
        code: `@Service
@Slf4j
public class LimitMonitor {
    
    private final LimitRepository limitRepository;
    private final PositionService positionService;
    private final AlertService alertService;
    private final EscalationService escalationService;
    private final AuditService auditService;
    private final RiskControlService riskControlService;
    
    // Real-time position monitoring
    @EventListener
    public void handlePositionUpdate(PositionUpdateEvent event) {
        String portfolioId = event.getPortfolioId();
        Position updatedPosition = event.getPosition();
        
        try {
            // Get all limits applicable to this portfolio
            List<RiskLimit> applicableLimits = limitRepository.findByPortfolioId(portfolioId);
            
            for (RiskLimit limit : applicableLimits) {
                // Calculate current utilization
                double currentUtilization = calculateLimitUtilization(limit, portfolioId);
                
                // Update limit utilization cache
                updateLimitUtilization(limit.getLimitId(), currentUtilization);
                
                // Check for breaches
                LimitStatus status = evaluateLimitStatus(limit, currentUtilization);
                
                if (status.isBreach() || status.isNearBreach()) {
                    handleLimitEvent(limit, status, currentUtilization, updatedPosition);
                }
            }
            
        } catch (Exception e) {
            log.error("Limit monitoring failed for position update in portfolio {}: {}", 
                     portfolioId, e.getMessage());
        }
    }
    
    private void handleLimitEvent(RiskLimit limit, LimitStatus status, 
                                 double utilization, Position triggeringPosition) {
        
        LimitEvent event = LimitEvent.builder()
            .eventId(UUID.randomUUID().toString())
            .limitId(limit.getLimitId())
            .portfolioId(limit.getPortfolioId())
            .limitType(limit.getLimitType())
            .limitValue(limit.getLimitValue())
            .currentUtilization(utilization)
            .utilizationPercent(utilization / limit.getLimitValue() * 100)
            .status(status)
            .triggeringPosition(triggeringPosition)
            .timestamp(LocalDateTime.now())
            .build();
        
        // Log for audit
        auditService.logLimitEvent(event);
        
        if (status.isBreach()) {
            handleLimitBreach(event, limit);
        } else if (status.isNearBreach()) {
            handleNearBreach(event, limit);
        }
    }
    
    private void handleLimitBreach(LimitEvent event, RiskLimit limit) {
        log.error("LIMIT BREACH: {} for portfolio {} - Utilization: {}/{}",
                 limit.getLimitType(), limit.getPortfolioId(),
                 event.getCurrentUtilization(), limit.getLimitValue());
        
        // Execute immediate risk controls
        if (limit.isAutoBlock()) {
            riskControlService.blockNewPositions(limit.getPortfolioId(), 
                                                limit.getLimitType(),
                                                "Automatic block due to limit breach");
        }
        
        // Send immediate alerts
        LimitBreachAlert alert = LimitBreachAlert.builder()
            .limitEvent(event)
            .severity(AlertSeverity.CRITICAL)
            .requiresImmedateAction(true)
            .build();
        
        alertService.sendImmediateAlert(alert);
        
        // Start escalation workflow
        EscalationWorkflow workflow = EscalationWorkflow.builder()
            .eventId(event.getEventId())
            .escalationLevel(EscalationLevel.IMMEDIATE)
            .recipientGroups(Arrays.asList("risk_managers", "traders", "senior_management"))
            .timeoutMinutes(5) // Escalate after 5 minutes if not acknowledged
            .build();
        
        escalationService.startEscalation(workflow);
        
        // Check for aggregated limits across portfolios
        checkAggregatedLimits(limit);
    }
    
    private void handleNearBreach(LimitEvent event, RiskLimit limit) {
        log.warn("Near limit breach: {} for portfolio {} - Utilization: {}/{}",
                limit.getLimitType(), limit.getPortfolioId(),
                event.getCurrentUtilization(), limit.getLimitValue());
        
        // Send warning alert
        LimitWarningAlert alert = LimitWarningAlert.builder()
            .limitEvent(event)
            .severity(AlertSeverity.WARNING)
            .warningThreshold(limit.getWarningThreshold())
            .build();
        
        alertService.sendAlert(alert);
        
        // Optional position size reduction recommendations
        if (limit.isAutoRecommendReduction()) {
            generatePositionReductionRecommendations(event, limit);
        }
    }
    
    public double calculateLimitUtilization(RiskLimit limit, String portfolioId) {
        switch (limit.getLimitType()) {
            case VAR_LIMIT:
                return calculateVarUtilization(limit, portfolioId);
                
            case NOTIONAL_LIMIT:
                return calculateNotionalUtilization(limit, portfolioId);
                
            case SECTOR_CONCENTRATION:
                return calculateSectorConcentration(limit, portfolioId);
                
            case SINGLE_NAME_CONCENTRATION:
                return calculateSingleNameConcentration(limit, portfolioId);
                
            case LEVERAGE_LIMIT:
                return calculateLeverageUtilization(limit, portfolioId);
                
            case CREDIT_EXPOSURE:
                return calculateCreditExposure(limit, portfolioId);
                
            case COUNTRY_EXPOSURE:
                return calculateCountryExposure(limit, portfolioId);
                
            case CURRENCY_EXPOSURE:
                return calculateCurrencyExposure(limit, portfolioId);
                
            default:
                throw new UnsupportedOperationException("Limit type not supported: " + 
                                                       limit.getLimitType());
        }
    }
    
    private double calculateVarUtilization(RiskLimit limit, String portfolioId) {
        // Get latest VaR calculation for the portfolio
        VaRResult varResult = varCalculationService.getLatestVaR(portfolioId);
        
        if (varResult == null) {
            log.warn("No VaR result available for portfolio {}", portfolioId);
            return 0.0;
        }
        
        // Use the confidence level and horizon specified in the limit
        return varResult.getVarByConfidenceAndHorizon(
            limit.getConfidenceLevel(), limit.getHorizonDays());
    }
    
    private double calculateNotionalUtilization(RiskLimit limit, String portfolioId) {
        Portfolio portfolio = positionService.getPortfolio(portfolioId);
        
        if (limit.getInstrumentFilter() != null) {
            // Filter positions based on limit criteria
            return portfolio.getPositions().stream()
                .filter(pos -> matchesFilter(pos, limit.getInstrumentFilter()))
                .mapToDouble(pos -> Math.abs(calculateNotionalValue(pos)))
                .sum();
        } else {
            // Total portfolio notional
            return calculateTotalNotional(portfolio);
        }
    }
    
    private double calculateSectorConcentration(RiskLimit limit, String portfolioId) {
        Portfolio portfolio = positionService.getPortfolio(portfolioId);
        String targetSector = limit.getSectorFilter();
        
        double sectorValue = portfolio.getPositions().stream()
            .filter(pos -> getSector(pos.getSymbol()).equals(targetSector))
            .mapToDouble(this::calculatePositionValue)
            .sum();
            
        double totalValue = calculateTotalPortfolioValue(portfolio);
        
        return Math.abs(sectorValue / totalValue);
    }
    
    @Scheduled(fixedRate = 30000) // Every 30 seconds
    public void performScheduledLimitChecks() {
        List<RiskLimit> allLimits = limitRepository.findAllActiveLimits();
        
        allLimits.parallelStream().forEach(limit -> {
            try {
                double utilization = calculateLimitUtilization(limit, limit.getPortfolioId());
                LimitStatus status = evaluateLimitStatus(limit, utilization);
                
                // Update utilization cache
                updateLimitUtilization(limit.getLimitId(), utilization);
                
                // Handle any status changes
                if (status.isStatusChange()) {
                    handleLimitStatusChange(limit, status, utilization);
                }
                
            } catch (Exception e) {
                log.error("Scheduled limit check failed for limit {}: {}", 
                         limit.getLimitId(), e.getMessage());
            }
        });
    }
    
    public LimitForecast generateLimitForecast(String limitId, Duration forecastHorizon) {
        RiskLimit limit = limitRepository.findById(limitId);
        if (limit == null) {
            throw new LimitNotFoundException("Limit not found: " + limitId);
        }
        
        // Get historical utilization data
        List<UtilizationHistory> history = getLimitUtilizationHistory(limitId, 
                                                                     Duration.ofDays(30));
        
        // Analyze current trading patterns
        TradingPattern pattern = analyzeTradingPattern(limit.getPortfolioId());
        
        // Generate forecast scenarios
        List<ForecastScenario> scenarios = Arrays.asList(
            createBullishScenario(limit, pattern),
            createBearishScenario(limit, pattern),
            createVolatilityScenario(limit, pattern),
            createBaselineScenario(limit, pattern)
        );
        
        // Calculate probability-weighted forecast
        double forecastUtilization = scenarios.stream()
            .mapToDouble(s -> s.getUtilization() * s.getProbability())
            .sum();
        
        // Calculate breach probability
        double breachProbability = scenarios.stream()
            .filter(s -> s.getUtilization() > limit.getLimitValue())
            .mapToDouble(ForecastScenario::getProbability)
            .sum();
        
        return LimitForecast.builder()
            .limitId(limitId)
            .currentUtilization(calculateLimitUtilization(limit, limit.getPortfolioId()))
            .forecastUtilization(forecastUtilization)
            .forecastHorizon(forecastHorizon)
            .breachProbability(breachProbability)
            .scenarios(scenarios)
            .generatedAt(LocalDateTime.now())
            .build();
    }
    
    @EventListener
    public void handleMarketStress(MarketStressEvent stressEvent) {
        // During market stress, dynamically adjust certain limits
        if (stressEvent.getStressLevel() == StressLevel.HIGH) {
            
            List<RiskLimit> volatilityLimits = limitRepository.findByType(LimitType.VAR_LIMIT);
            
            for (RiskLimit limit : volatilityLimits) {
                if (limit.isDynamicAdjustment()) {
                    // Temporarily tighten VaR limits during high stress
                    double stressAdjustment = 0.8; // 20% tighter
                    double adjustedLimit = limit.getLimitValue() * stressAdjustment;
                    
                    applyTemporaryLimitAdjustment(limit.getLimitId(), adjustedLimit,
                                                "Automatic stress adjustment", 
                                                Duration.ofHours(24));
                    
                    log.info("Applied stress adjustment to limit {}: {} -> {}", 
                            limit.getLimitId(), limit.getLimitValue(), adjustedLimit);
                }
            }
        }
    }
}`,
        performance: {
          responseTime: '< 100ms per check',
          throughput: '10K+ positions/second',
          alertLatency: '< 1 second',
          memoryUsage: '< 500MB'
        },
        technologies: {
          'Real-time Processing': 'Apache Kafka Streams',
          'Cache Layer': 'Redis for fast limit lookups',
          'Alert System': 'WebSocket + SMS/Email gateways',
          'Workflow Engine': 'Zeebe for escalation management'
        }
      }
    };
    
    return details[componentId] || {
      functions: ['Component details coming soon...'],
      workflow: 'Detailed workflow will be provided.',
      code: '// Implementation details will be added'
    };
  };

  const getCategoryStyle = (category) => {
    const styles = {
      data: { color: '#3b82f6', bg: 'rgba(59, 130, 246, 0.1)' },
      core: { color: '#ef4444', bg: 'rgba(239, 68, 68, 0.1)' },
      processing: { color: '#10b981', bg: 'rgba(16, 185, 129, 0.1)' },
      analytics: { color: '#f59e0b', bg: 'rgba(245, 158, 11, 0.1)' },
      output: { color: '#8b5cf6', bg: 'rgba(139, 92, 246, 0.1)' },
      compliance: { color: '#f97316', bg: 'rgba(249, 115, 22, 0.1)' }
    };
    return styles[category] || styles.processing;
  };

  const buttonStyle = (isActive, isDisabled) => ({
    width: '100%',
    padding: '12px',
    marginBottom: '8px',
    backgroundColor: isActive ? 'rgba(16, 185, 129, 0.2)' : 'transparent',
    border: isActive ? '2px solid #10b981' : '2px solid transparent',
    borderRadius: '8px',
    cursor: isDisabled ? 'not-allowed' : 'pointer',
    opacity: isDisabled ? 0.4 : 1,
    transition: 'all 0.2s',
    textAlign: 'left',
    display: 'flex',
    flexDirection: 'column',
    gap: '4px'
  });

  return (
    <div style={{ 
      padding: '40px',
      backgroundColor: '#0f172a',
      minHeight: '100vh',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      <div style={{
        maxWidth: '1400px',
        margin: '0 auto'
      }}>
        {/* Header */}
        <div style={{ marginBottom: '40px', textAlign: 'center' }}>
          <div style={{
            display: 'inline-flex',
            alignItems: 'center',
            gap: '16px',
            backgroundColor: 'rgba(239, 68, 68, 0.1)',
            padding: '16px 32px',
            borderRadius: '16px',
            border: '1px solid rgba(239, 68, 68, 0.3)'
          }}>
            <TrendingUp size={32} color="#ef4444" />
            <h1 style={{
              fontSize: '28px',
              fontWeight: '700',
              color: 'white',
              margin: 0
            }}>
              VaR/CVaR Risk Analytics System
            </h1>
          </div>
          <p style={{
            fontSize: '16px',
            color: '#94a3b8',
            marginTop: '16px',
            maxWidth: '800px',
            margin: '16px auto 0'
          }}>
            Advanced Value at Risk and Conditional Value at Risk calculation engine with real-time 
            portfolio monitoring, multi-methodology risk assessment, and comprehensive backtesting
          </p>
        </div>

        <div style={{ display: 'flex', gap: '32px' }}>
          {/* Architecture Diagram */}
          <div style={{ flex: 1, position: 'relative', padding: '40px' }}>
            <h2 style={{ color: 'white', marginBottom: '32px', fontSize: '24px' }}>
              VaR/CVaR System Architecture
            </h2>
            
            {/* Components Grid */}
            <div style={{ position: 'relative', width: '800px', height: '400px' }}>
              {components.map(component => {
                const categoryStyle = getCategoryStyle(component.category);
                const isSelected = selectedComponent?.id === component.id;
                
                return (
                  <div
                    key={component.id}
                    onClick={() => setSelectedComponent(component)}
                    style={{
                      position: 'absolute',
                      top: component.position.top,
                      left: component.position.left,
                      width: '180px',
                      padding: '16px',
                      background: isSelected ? categoryStyle.color : categoryStyle.bg,
                      border: `2px solid ${categoryStyle.color}`,
                      borderRadius: '12px',
                      cursor: 'pointer',
                      transition: 'all 0.3s',
                      transform: isSelected ? 'scale(1.05)' : 'scale(1)'
                    }}
                  >
                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '8px' }}>
                      <div style={{ color: isSelected ? 'white' : categoryStyle.color }}>
                        {component.id === 'market-data' && <Database size={18} />}
                        {component.id === 'portfolio-manager' && <BarChart3 size={18} />}
                        {component.id === 'risk-calculator' && <Calculator size={18} />}
                        {component.id === 'scenario-engine' && <Activity size={18} />}
                        {component.id === 'reporting-system' && <TrendingUp size={18} />}
                        {component.id === 'calibration-engine' && <Settings size={18} />}
                        {component.id === 'limit-monitor' && <AlertTriangle size={18} />}
                      </div>
                      <h3 style={{ 
                        fontSize: '13px', 
                        fontWeight: 'bold', 
                        color: isSelected ? 'white' : 'white',
                        margin: 0
                      }}>
                        {component.name}
                      </h3>
                    </div>
                    <div style={{ fontSize: '11px', color: isSelected ? '#e5e7eb' : '#9ca3af' }}>
                      {component.tech.join(' • ')}
                    </div>
                  </div>
                );
              })}
              
              {/* Connection Lines for Data Flow */}
              <svg style={{ position: 'absolute', inset: 0, pointerEvents: 'none' }}>
                <defs>
                  <marker id="arrowhead-var" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="#ef4444" />
                  </marker>
                </defs>
                {/* Market Data to Portfolio Manager */}
                <line x1="200" y1="120" x2="280" y2="120" stroke="#ef4444" strokeWidth="2" markerEnd="url(#arrowhead-var)" />
                {/* Portfolio Manager to Risk Calculator */}
                <line x1="380" y1="120" x2="480" y2="120" stroke="#ef4444" strokeWidth="2" markerEnd="url(#arrowhead-var)" />
                {/* Risk Calculator to Scenario Engine */}
                <line x1="580" y1="120" x2="680" y2="120" stroke="#ef4444" strokeWidth="2" markerEnd="url(#arrowhead-var)" />
                {/* Risk Calculator to Reporting */}
                <line x1="520" y1="180" x2="260" y2="280" stroke="#f59e0b" strokeWidth="2" markerEnd="url(#arrowhead-var)" />
                {/* Risk Calculator to Calibration */}
                <line x1="520" y1="180" x2="440" y2="280" stroke="#f59e0b" strokeWidth="2" markerEnd="url(#arrowhead-var)" />
                {/* Risk Calculator to Limit Monitor */}
                <line x1="560" y1="180" x2="620" y2="280" stroke="#f59e0b" strokeWidth="2" markerEnd="url(#arrowhead-var)" />
              </svg>
            </div>
          </div>

          {/* Details Panel */}
          {selectedComponent && (
            <div style={{
              width: '600px',
              background: 'rgba(0, 0, 0, 0.9)',
              border: '1px solid #333',
              margin: '20px 20px 20px 0',
              borderRadius: '10px',
              overflow: 'hidden',
              backdropFilter: 'blur(10px)'
            }}>
              {/* Panel Header */}
              <div style={{
                background: `linear-gradient(135deg, ${selectedComponent.color}, ${selectedComponent.color}cc)`,
                padding: '20px',
                borderBottom: '1px solid #333'
              }}>
                <h3 style={{
                  margin: '0 0 10px 0',
                  fontSize: '20px',
                  fontWeight: 'bold',
                  color: '#ffffff'
                }}>
                  {selectedComponent.name}
                </h3>
                <div style={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: '8px'
                }}>
                  {selectedComponent.tech.map((tech, i) => (
                    <span
                      key={i}
                      style={{
                        background: 'rgba(255, 255, 255, 0.2)',
                        padding: '4px 10px',
                        borderRadius: '15px',
                        fontSize: '11px',
                        color: '#ffffff',
                        fontWeight: '500'
                      }}
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>

              {/* Tabs */}
              <div style={{
                display: 'flex',
                borderBottom: '1px solid #333'
              }}>
                {['details', 'workflow', 'code', 'performance'].map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab)}
                    style={{
                      flex: 1,
                      padding: '12px',
                      background: activeTab === tab ? '#1f2937' : 'transparent',
                      border: 'none',
                      color: activeTab === tab ? '#ef4444' : '#9ca3af',
                      fontSize: '12px',
                      fontWeight: '600',
                      textTransform: 'capitalize',
                      cursor: 'pointer'
                    }}
                  >
                    {tab}
                  </button>
                ))}
              </div>

              {/* Tab Content */}
              <div style={{
                padding: activeTab === 'code' ? '0' : '20px',
                maxHeight: 'calc(100vh - 300px)',
                overflowY: activeTab === 'code' ? 'hidden' : 'auto',
                overflowX: activeTab === 'code' ? 'hidden' : 'auto'
              }}>
                {activeTab === 'details' && (
                  <div>
                    <h4 style={{ margin: '0 0 15px 0', color: '#ef4444', fontSize: '16px' }}>
                      Key Functions
                    </h4>
                    <ul style={{ margin: '0', padding: '0 0 0 18px', fontSize: '13px', lineHeight: '1.7' }}>
                      {getComponentDetails(selectedComponent.id).functions.map((func, i) => (
                        <li key={i} style={{ marginBottom: '10px', color: '#e0e0e0' }}>
                          {func}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {activeTab === 'workflow' && (
                  <div>
                    <h4 style={{ margin: '0 0 15px 0', color: '#ef4444', fontSize: '16px' }}>
                      Process Flow
                    </h4>
                    <div style={{
                      background: 'rgba(0, 0, 0, 0.6)',
                      padding: '15px',
                      borderRadius: '8px',
                      fontSize: '12px',
                      lineHeight: '1.8',
                      color: '#e5e5e5',
                      border: '1px solid #333',
                      fontFamily: 'monospace'
                    }}>
                      {(() => {
                        const workflow = getComponentDetails(selectedComponent.id).workflow;
                        const formattedWorkflow = workflow
                          .replace(/(\d+)\.\s+/g, '\n$1. ')
                          .trim()
                          .split('\n')
                          .filter(item => item.trim());
                        
                        return formattedWorkflow.map((item, index) => {
                          const match = item.match(/^(\d+\.)\s*(.*)$/);
                          if (match) {
                            return (
                              <div key={index} style={{ 
                                marginTop: index === 0 ? '0' : '8px',
                                display: 'flex',
                                alignItems: 'flex-start'
                              }}>
                                <span style={{ 
                                  color: '#ef4444', 
                                  fontWeight: 'bold',
                                  marginRight: '8px',
                                  flexShrink: 0,
                                  minWidth: '30px'
                                }}>
                                  {match[1]}
                                </span>
                                <span style={{ flex: 1 }}>
                                  {match[2].trim()}
                                </span>
                              </div>
                            );
                          }
                          return (
                            <div key={index} style={{ marginTop: '8px' }}>
                              {item.trim()}
                            </div>
                          );
                        });
                      })()}
                    </div>
                  </div>
                )}

                {activeTab === 'code' && (
                  <div style={{ 
                    height: 'calc(100vh - 300px)',
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%'
                  }}>
                    <h4 style={{ 
                      margin: '0 0 10px 0', 
                      color: '#ef4444', 
                      fontSize: '16px',
                      flexShrink: 0,
                      padding: '15px 20px 0 20px'
                    }}>
                      Implementation Example
                    </h4>
                    <div style={{
                      flex: 1,
                      padding: '0 20px 20px 20px',
                      overflow: 'hidden'
                    }}>
                      <SyntaxHighlighter
                        language="java"
                        style={oneDark}
                        customStyle={{
                          borderRadius: '8px',
                          fontSize: '11px',
                          lineHeight: '1.5',
                          height: '100%',
                          margin: '0',
                          padding: '20px',
                          overflowX: 'auto',
                          overflowY: 'auto'
                        }}
                        wrapLongLines={false}
                      >
                        {getComponentDetails(selectedComponent.id).code}
                      </SyntaxHighlighter>
                    </div>
                  </div>
                )}

                {activeTab === 'performance' && getComponentDetails(selectedComponent.id).performance && (
                  <div>
                    <h4 style={{ margin: '0 0 15px 0', color: '#ef4444', fontSize: '16px' }}>
                      Performance Metrics
                    </h4>
                    {Object.entries(getComponentDetails(selectedComponent.id).performance).map(([key, value]) => (
                      <div key={key} style={{ marginBottom: '16px' }}>
                        <div style={{ color: '#9ca3af', fontSize: '11px', textTransform: 'uppercase', marginBottom: '4px' }}>
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </div>
                        <div style={{ color: '#ef4444', fontSize: '16px', fontWeight: 'bold' }}>
                          {value}
                        </div>
                      </div>
                    ))}
                    
                    {getComponentDetails(selectedComponent.id).technologies && (
                      <div style={{ marginTop: '24px' }}>
                        <h4 style={{ color: '#ef4444', fontSize: '16px', marginBottom: '12px' }}>
                          Technology Stack
                        </h4>
                        {Object.entries(getComponentDetails(selectedComponent.id).technologies).map(([key, value]) => (
                          <div key={key} style={{ marginBottom: '8px' }}>
                            <span style={{ color: '#9ca3af', fontSize: '12px' }}>{key}: </span>
                            <span style={{ color: '#d1d5db', fontSize: '12px' }}>{value}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default VarCvarSystem;
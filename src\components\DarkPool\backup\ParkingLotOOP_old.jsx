import React, { useState } from 'react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Car, Truck, Bike, Shield, CreditCard, Timer, MapPin, Users, DollarSign, BarChart3, Lock, AlertCircle, Layers } from 'lucide-react';

const ParkingLotOOP = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedConcept, setSelectedConcept] = useState('abstraction');
  const [selectedClass, setSelectedClass] = useState(null);
  const [hoveredClass, setHoveredClass] = useState(null);

  // UML Class definitions for interactive diagram
  const umlClasses = [
    {
      id: 'parkable',
      name: 'Parkable',
      type: 'interface',
      position: { x: 400, y: 50 },
      methods: ['+park()', '+unpark()', '+isParked()'],
      attributes: [],
      color: '#8b5cf6',
      relationships: [
        { target: 'vehicle', type: 'implements', points: [400, 100, 200, 180] }
      ]
    },
    {
      id: 'chargeable',
      name: 'Chargeable',
      type: 'interface',
      position: { x: 600, y: 50 },
      methods: ['+startCharging()', '+getBatteryLevel()', '+setBatteryLevel()'],
      attributes: [],
      color: '#10b981',
      relationships: [
        { target: 'electric-car', type: 'implements', points: [600, 100, 500, 380] }
      ]
    },
    {
      id: 'vehicle',
      name: 'Vehicle',
      type: 'abstract',
      position: { x: 100, y: 180 },
      methods: ['+calculateParkingFee()', '+canFitInSpot()', '+getRequiredSpots()', '+generateTicket()'],
      attributes: ['#licensePlate: String', '#color: String', '#type: VehicleType', '#entryTime: DateTime'],
      color: '#ef4444',
      relationships: [
        { target: 'car', type: 'extends', points: [150, 280, 100, 350] },
        { target: 'motorcycle', type: 'extends', points: [200, 280, 200, 350] },
        { target: 'truck', type: 'extends', points: [250, 280, 300, 350] },
        { target: 'electric-car', type: 'extends', points: [300, 280, 450, 350] }
      ]
    },
    {
      id: 'car',
      name: 'Car',
      type: 'class',
      position: { x: 50, y: 380 },
      methods: ['+park()', '+unpark()', '+calculateFee()'],
      attributes: ['-doors: int', '-electric: boolean'],
      color: '#3b82f6'
    },
    {
      id: 'motorcycle',
      name: 'Motorcycle',
      type: 'class',
      position: { x: 170, y: 380 },
      methods: ['+park()', '+unpark()', '+calculateFee()'],
      attributes: ['-sidecar: boolean'],
      color: '#f59e0b'
    },
    {
      id: 'truck',
      name: 'Truck',
      type: 'class',
      position: { x: 290, y: 380 },
      methods: ['+park()', '+unpark()', '+calculateFee()'],
      attributes: ['-capacity: double', '-axles: int'],
      color: '#ec4899'
    },
    {
      id: 'electric-car',
      name: 'ElectricCar',
      type: 'class',
      position: { x: 420, y: 380 },
      methods: ['+startCharging()', '+stopCharging()', '+calculateFee()'],
      attributes: ['-batteryLevel: double', '-chargingRate: double'],
      color: '#06b6d4'
    },
    {
      id: 'parking-lot',
      name: 'ParkingLot',
      type: 'class',
      position: { x: 700, y: 180 },
      methods: ['+parkVehicle()', '+exitVehicle()', '+getAvailableSpots()', '+calculateFee()'],
      attributes: ['-lotId: String', '-name: String', '-address: Address', '-floors: Map', '-dailyRevenue: double'],
      color: '#84cc16',
      relationships: [
        { target: 'parking-floor', type: 'composition', points: [800, 250, 900, 300] },
        { target: 'payment-system', type: 'aggregation', points: [750, 280, 750, 500] }
      ]
    },
    {
      id: 'parking-floor',
      name: 'ParkingFloor',
      type: 'class',
      position: { x: 850, y: 320 },
      methods: ['+getAvailableSpots()', '+updateDisplayBoard()', '+decommission()'],
      attributes: ['-floorNumber: int', '-spots: Map', '-displayBoard: DisplayBoard'],
      color: '#f97316',
      relationships: [
        { target: 'parking-spot', type: 'composition', points: [900, 420, 900, 500] }
      ]
    },
    {
      id: 'parking-spot',
      name: 'ParkingSpot',
      type: 'abstract',
      position: { x: 850, y: 520 },
      methods: ['+canAccommodate()', '+assignVehicle()', '+removeVehicle()', '+isAvailable()'],
      attributes: ['#spotId: String', '#floor: int', '#status: SpotStatus', '#vehicle: Vehicle'],
      color: '#8b5cf6',
      relationships: [
        { target: 'compact-spot', type: 'extends', points: [800, 620, 700, 680] },
        { target: 'regular-spot', type: 'extends', points: [850, 620, 800, 680] },
        { target: 'large-spot', type: 'extends', points: [900, 620, 900, 680] },
        { target: 'electric-spot', type: 'extends', points: [950, 620, 1000, 680] }
      ]
    },
    {
      id: 'compact-spot',
      name: 'CompactSpot',
      type: 'class',
      position: { x: 650, y: 700 },
      methods: ['+canAccommodate()'],
      attributes: [],
      color: '#06b6d4'
    },
    {
      id: 'regular-spot',
      name: 'RegularSpot',
      type: 'class',
      position: { x: 750, y: 700 },
      methods: ['+canAccommodate()'],
      attributes: [],
      color: '#10b981'
    },
    {
      id: 'large-spot',
      name: 'LargeSpot',
      type: 'class',
      position: { x: 850, y: 700 },
      methods: ['+canAccommodate()'],
      attributes: [],
      color: '#f59e0b'
    },
    {
      id: 'electric-spot',
      name: 'ElectricSpot',
      type: 'class',
      position: { x: 950, y: 700 },
      methods: ['+canAccommodate()', '+provideCharging()'],
      attributes: ['-chargingStation: ChargingStation'],
      color: '#84cc16'
    },
    {
      id: 'payment-system',
      name: 'PaymentSystem',
      type: 'class',
      position: { x: 650, y: 520 },
      methods: ['+processPayment()', '+validatePayment()', '+issueRefund()'],
      attributes: ['-processors: List<PaymentProcessor>', '-transactions: Queue<Transaction>'],
      color: '#ec4899'
    }
  ];

  const getClassDetails = (classId) => {
    const classDetails = {
      'parkable': {
        description: 'Interface defining basic parking operations that all parkable entities must implement',
        responsibilities: ['Define parking contract', 'Ensure consistent parking behavior', 'Support polymorphism'],
        relationships: 'Implemented by Vehicle and its subclasses'
      },
      'chargeable': {
        description: 'Interface for electric vehicles that support charging functionality',
        responsibilities: ['Define charging contract', 'Manage battery state', 'Control charging process'],
        relationships: 'Implemented by ElectricCar and other electric vehicles'
      },
      'vehicle': {
        description: 'Abstract base class representing all vehicles in the parking system',
        responsibilities: ['Common vehicle properties', 'Template method for ticket generation', 'Abstract parking operations'],
        relationships: 'Base class for Car, Motorcycle, Truck, and ElectricCar'
      },
      'car': {
        description: 'Concrete implementation of a standard car vehicle',
        responsibilities: ['Car-specific parking logic', 'Door count management', 'Standard fee calculation'],
        relationships: 'Extends Vehicle, implements Parkable'
      },
      'motorcycle': {
        description: 'Concrete implementation of a motorcycle vehicle',
        responsibilities: ['Motorcycle-specific parking', 'Sidecar accommodation', 'Reduced fee calculation'],
        relationships: 'Extends Vehicle, implements Parkable'
      },
      'truck': {
        description: 'Concrete implementation of a truck vehicle requiring large spaces',
        responsibilities: ['Truck-specific parking', 'Capacity-based validation', 'Premium fee calculation'],
        relationships: 'Extends Vehicle, implements Parkable'
      },
      'electric-car': {
        description: 'Electric car with charging capabilities',
        responsibilities: ['Electric vehicle parking', 'Charging management', 'Battery monitoring', 'Environmental fee calculation'],
        relationships: 'Extends Vehicle, implements Parkable and Chargeable'
      },
      'parking-lot': {
        description: 'Main system class managing the entire parking facility',
        responsibilities: ['Overall lot management', 'Vehicle entry/exit', 'Revenue tracking', 'System coordination'],
        relationships: 'Aggregates ParkingFloor and PaymentSystem'
      },
      'parking-floor': {
        description: 'Represents a single floor within the parking lot',
        responsibilities: ['Floor-level management', 'Spot allocation', 'Display board updates', 'Floor maintenance'],
        relationships: 'Composed by ParkingLot, aggregates ParkingSpots'
      },
      'parking-spot': {
        description: 'Abstract base class for all parking spot types',
        responsibilities: ['Spot state management', 'Vehicle assignment', 'Availability tracking', 'Type-specific validation'],
        relationships: 'Base class for all spot types'
      },
      'compact-spot': {
        description: 'Small parking spot for compact vehicles',
        responsibilities: ['Compact vehicle accommodation', 'Size validation'],
        relationships: 'Extends ParkingSpot'
      },
      'regular-spot': {
        description: 'Standard parking spot for regular-sized vehicles',
        responsibilities: ['Regular vehicle accommodation', 'Standard size validation'],
        relationships: 'Extends ParkingSpot'
      },
      'large-spot': {
        description: 'Large parking spot for trucks and oversized vehicles',
        responsibilities: ['Large vehicle accommodation', 'Size and weight validation'],
        relationships: 'Extends ParkingSpot'
      },
      'electric-spot': {
        description: 'Specialized spot with charging capabilities for electric vehicles',
        responsibilities: ['Electric vehicle accommodation', 'Charging station management', 'Power monitoring'],
        relationships: 'Extends ParkingSpot'
      },
      'payment-system': {
        description: 'Handles all payment processing and financial transactions',
        responsibilities: ['Payment processing', 'Transaction management', 'Refund handling', 'Financial reporting'],
        relationships: 'Used by ParkingLot for billing operations'
      }
    };
    
    return classDetails[classId] || {
      description: 'Class description not available',
      responsibilities: ['Details coming soon...'],
      relationships: 'Relationships not defined'
    };
  };

  const oopConcepts = [
    {
      id: 'abstraction',
      title: 'Abstraction & Interfaces',
      icon: <Shield size={20} />,
      color: '#8b5cf6',
      highlightClasses: ['parkable', 'chargeable', 'vehicle', 'parking-spot'],
      overview: 'Abstract classes and interfaces define contracts for vehicle types, parking spots, and payment systems, hiding implementation complexity while exposing essential operations.',
      concepts: 'Abstraction separates the "what" from the "how", defining abstract vehicle behaviors, parking spot types, and payment processing interfaces that concrete implementations must fulfill.',
      code: `// Abstract Vehicle class demonstrating abstraction
public abstract class Vehicle {
    protected String licensePlate;
    protected String color;
    protected VehicleType type;
    protected LocalDateTime entryTime;
    private static final AtomicInteger idGenerator = new AtomicInteger(1000);
    
    // Constructor - protected for inheritance
    protected Vehicle(String licensePlate, String color, VehicleType type) {
        this.licensePlate = licensePlate;
        this.color = color;
        this.type = type;
        this.entryTime = LocalDateTime.now();
    }
    
    // Abstract methods that subclasses must implement
    public abstract double calculateParkingFee(Duration parkingDuration);
    public abstract boolean canFitInSpot(ParkingSpot spot);
    public abstract int getRequiredSpots();
    
    // Concrete methods available to all vehicles
    public Duration getParkingDuration() {
        return Duration.between(entryTime, LocalDateTime.now());
    }
    
    // Template method pattern
    public final ParkingTicket generateTicket(ParkingSpot spot) {
        validateVehicle();
        double estimatedFee = calculateParkingFee(Duration.ofHours(1));
        return new ParkingTicket(this, spot, estimatedFee);
    }
    
    protected void validateVehicle() {
        if (licensePlate == null || licensePlate.isEmpty()) {
            throw new IllegalStateException("Vehicle must have a license plate");
        }
    }
    
    // Getters with encapsulation
    public String getLicensePlate() { return licensePlate; }
    public VehicleType getType() { return type; }
    public LocalDateTime getEntryTime() { return entryTime; }
}

// Interface for parkable entities
public interface Parkable {
    boolean park(ParkingSpot spot);
    boolean unpark();
    ParkingSpot getCurrentSpot();
    boolean isParked();
}

// Interface for payment processing
public interface PaymentProcessor {
    PaymentResult processPayment(double amount, PaymentMethod method);
    boolean validatePaymentMethod(PaymentMethod method);
    void issueRefund(String transactionId, double amount);
    PaymentHistory getPaymentHistory(String customerId);
}

// Abstract parking spot with common behavior
public abstract class ParkingSpot {
    protected final String spotId;
    protected final int floor;
    protected final int row;
    protected final int number;
    protected SpotType type;
    protected SpotStatus status;
    protected Vehicle currentVehicle;
    protected final ReentrantLock spotLock;
    
    protected ParkingSpot(String spotId, int floor, int row, int number, SpotType type) {
        this.spotId = spotId;
        this.floor = floor;
        this.row = row;
        this.number = number;
        this.type = type;
        this.status = SpotStatus.AVAILABLE;
        this.spotLock = new ReentrantLock();
    }
    
    // Abstract methods for spot-specific behavior
    public abstract boolean canAccommodateVehicle(Vehicle vehicle);
    public abstract double getHourlyRate();
    public abstract Duration getMaxParkingDuration();
    
    // Template method for parking
    public final boolean assignVehicle(Vehicle vehicle) {
        spotLock.lock();
        try {
            if (!isAvailable() || !canAccommodateVehicle(vehicle)) {
                return false;
            }
            
            this.currentVehicle = vehicle;
            this.status = SpotStatus.OCCUPIED;
            onVehicleAssigned(vehicle);
            return true;
        } finally {
            spotLock.unlock();
        }
    }
    
    // Hook method for subclasses
    protected void onVehicleAssigned(Vehicle vehicle) {
        // Subclasses can override for specific behavior
    }
    
    public final Vehicle removeVehicle() {
        spotLock.lock();
        try {
            Vehicle vehicle = this.currentVehicle;
            this.currentVehicle = null;
            this.status = SpotStatus.AVAILABLE;
            onVehicleRemoved(vehicle);
            return vehicle;
        } finally {
            spotLock.unlock();
        }
    }
    
    protected void onVehicleRemoved(Vehicle vehicle) {
        // Subclasses can override
    }
    
    public boolean isAvailable() {
        return status == SpotStatus.AVAILABLE && currentVehicle == null;
    }
}`
    },
    {
      title: 'Inheritance & Polymorphism',
      icon: <Car size={20} />,
      overview: 'Vehicle hierarchy demonstrates inheritance with Car, Motorcycle, Truck extending Vehicle class. Polymorphism allows treating different vehicles uniformly while maintaining specific behaviors.',
      concepts: 'Inheritance creates an "is-a" relationship where specialized vehicles inherit common properties. Polymorphism enables dynamic method dispatch based on actual object type at runtime.',
      code: `// Concrete vehicle implementations showing inheritance
public class Car extends Vehicle implements Parkable, Chargeable {
    private final int numberOfDoors;
    private final boolean isElectric;
    private double batteryLevel;
    private ParkingSpot currentSpot;
    
    public Car(String licensePlate, String color, int numberOfDoors, boolean isElectric) {
        super(licensePlate, color, VehicleType.CAR);
        this.numberOfDoors = numberOfDoors;
        this.isElectric = isElectric;
        this.batteryLevel = isElectric ? 50.0 : 0.0;
    }
    
    @Override
    public double calculateParkingFee(Duration parkingDuration) {
        double hours = Math.ceil(parkingDuration.toMinutes() / 60.0);
        double baseFee = hours * 5.0; // $5 per hour
        
        // Electric vehicles get 20% discount
        if (isElectric) {
            baseFee *= 0.8;
        }
        
        // Add charging fees if applicable
        if (isElectric && currentSpot instanceof ElectricSpot) {
            baseFee += calculateChargingFee(parkingDuration);
        }
        
        return Math.min(baseFee, 50.0); // Daily max $50
    }
    
    @Override
    public boolean canFitInSpot(ParkingSpot spot) {
        return spot.getType() == SpotType.COMPACT || 
               spot.getType() == SpotType.LARGE ||
               (isElectric && spot.getType() == SpotType.ELECTRIC);
    }
    
    @Override
    public int getRequiredSpots() {
        return 1;
    }
    
    // Parkable interface implementation
    @Override
    public boolean park(ParkingSpot spot) {
        if (isParked() || !canFitInSpot(spot)) {
            return false;
        }
        this.currentSpot = spot;
        return true;
    }
    
    @Override
    public boolean unpark() {
        if (!isParked()) {
            return false;
        }
        this.currentSpot = null;
        return true;
    }
    
    @Override
    public ParkingSpot getCurrentSpot() {
        return currentSpot;
    }
    
    @Override
    public boolean isParked() {
        return currentSpot != null;
    }
    
    // Chargeable interface for electric cars
    @Override
    public void startCharging(ChargingStation station) {
        if (isElectric && batteryLevel < 100) {
            station.beginCharging(this);
        }
    }
    
    @Override
    public double getBatteryLevel() {
        return batteryLevel;
    }
    
    @Override
    public void setBatteryLevel(double level) {
        this.batteryLevel = Math.min(100, Math.max(0, level));
    }
    
    private double calculateChargingFee(Duration duration) {
        double kWhUsed = duration.toMinutes() * 0.5; // 0.5 kWh per minute
        return kWhUsed * 0.15; // $0.15 per kWh
    }
}

public class Motorcycle extends Vehicle implements Parkable {
    private final boolean hasSidecar;
    private ParkingSpot currentSpot;
    
    public Motorcycle(String licensePlate, String color, boolean hasSidecar) {
        super(licensePlate, color, VehicleType.MOTORCYCLE);
        this.hasSidecar = hasSidecar;
    }
    
    @Override
    public double calculateParkingFee(Duration parkingDuration) {
        double hours = Math.ceil(parkingDuration.toMinutes() / 60.0);
        double baseFee = hours * 2.0; // $2 per hour for motorcycles
        
        if (hasSidecar) {
            baseFee *= 1.5; // 50% more for sidecar
        }
        
        return Math.min(baseFee, 20.0); // Daily max $20
    }
    
    @Override
    public boolean canFitInSpot(ParkingSpot spot) {
        // Motorcycles can fit in any spot type
        return true;
    }
    
    @Override
    public int getRequiredSpots() {
        return hasSidecar ? 1 : 0; // Regular motorcycles can share spots
    }
    
    @Override
    public boolean park(ParkingSpot spot) {
        if (isParked()) {
            return false;
        }
        this.currentSpot = spot;
        return true;
    }
    
    @Override
    public boolean unpark() {
        if (!isParked()) {
            return false;
        }
        this.currentSpot = null;
        return true;
    }
    
    @Override
    public ParkingSpot getCurrentSpot() {
        return currentSpot;
    }
    
    @Override
    public boolean isParked() {
        return currentSpot != null;
    }
}

public class Truck extends Vehicle implements Parkable, CommercialVehicle {
    private final double cargoCapacity;
    private final int numberOfAxles;
    private List<ParkingSpot> occupiedSpots;
    private String commercialLicense;
    
    public Truck(String licensePlate, String color, double cargoCapacity, int numberOfAxles) {
        super(licensePlate, color, VehicleType.TRUCK);
        this.cargoCapacity = cargoCapacity;
        this.numberOfAxles = numberOfAxles;
        this.occupiedSpots = new ArrayList<>();
    }
    
    @Override
    public double calculateParkingFee(Duration parkingDuration) {
        double hours = Math.ceil(parkingDuration.toMinutes() / 60.0);
        double baseFee = hours * 10.0 * getRequiredSpots(); // $10 per hour per spot
        
        // Additional fee for heavy trucks
        if (numberOfAxles > 2) {
            baseFee *= 1.2;
        }
        
        // Commercial vehicle surcharge
        baseFee += getCommercialSurcharge();
        
        return Math.min(baseFee, 200.0); // Daily max $200
    }
    
    @Override
    public boolean canFitInSpot(ParkingSpot spot) {
        return spot.getType() == SpotType.LARGE;
    }
    
    @Override
    public int getRequiredSpots() {
        if (numberOfAxles <= 2) return 2;
        if (numberOfAxles <= 4) return 3;
        return 5; // Large trucks need 5 spots
    }
    
    @Override
    public boolean park(ParkingSpot spot) {
        // Trucks need multiple consecutive spots
        return false; // Simplified - would need complex logic
    }
    
    @Override
    public boolean unpark() {
        if (!isParked()) {
            return false;
        }
        occupiedSpots.clear();
        return true;
    }
    
    @Override
    public ParkingSpot getCurrentSpot() {
        return occupiedSpots.isEmpty() ? null : occupiedSpots.get(0);
    }
    
    @Override
    public boolean isParked() {
        return !occupiedSpots.isEmpty();
    }
    
    // CommercialVehicle interface methods
    @Override
    public void setCommercialLicense(String license) {
        this.commercialLicense = license;
    }
    
    @Override
    public String getCommercialLicense() {
        return commercialLicense;
    }
    
    @Override
    public double getCommercialSurcharge() {
        return 5.0; // $5 commercial vehicle surcharge
    }
    
    @Override
    public boolean requiresSpecialPermit() {
        return numberOfAxles > 3 || cargoCapacity > 10000;
    }
}

// Demonstration of polymorphism
public class ParkingLotManager {
    private List<Vehicle> vehicles = new ArrayList<>();
    private Map<String, ParkingSpot> spots = new HashMap<>();
    
    // Polymorphic method - works with any Vehicle type
    public ParkingTicket processVehicleEntry(Vehicle vehicle, ParkingSpot spot) {
        // Runtime polymorphism - actual method called depends on vehicle type
        double estimatedFee = vehicle.calculateParkingFee(Duration.ofHours(1));
        boolean canPark = vehicle.canFitInSpot(spot);
        
        if (!canPark) {
            throw new InvalidParkingException("Vehicle cannot fit in selected spot");
        }
        
        // Polymorphic behavior
        if (vehicle instanceof Parkable) {
            Parkable parkable = (Parkable) vehicle;
            parkable.park(spot);
        }
        
        // Different behavior for different vehicle types
        if (vehicle instanceof Car && ((Car) vehicle).isElectric) {
            assignChargingStation((Car) vehicle);
        } else if (vehicle instanceof Truck) {
            validateCommercialPermit((Truck) vehicle);
        }
        
        vehicles.add(vehicle);
        return vehicle.generateTicket(spot);
    }
    
    // Covariant return type example
    public Vehicle findVehicle(String licensePlate) {
        return vehicles.stream()
            .filter(v -> v.getLicensePlate().equals(licensePlate))
            .findFirst()
            .orElse(null);
    }
    
    // Method overloading - compile-time polymorphism
    public double calculateTotalRevenue() {
        return calculateTotalRevenue(vehicles);
    }
    
    public double calculateTotalRevenue(List<Vehicle> vehicleList) {
        return vehicleList.stream()
            .mapToDouble(v -> v.calculateParkingFee(v.getParkingDuration()))
            .sum();
    }
    
    public double calculateTotalRevenue(LocalDate date) {
        return vehicles.stream()
            .filter(v -> v.getEntryTime().toLocalDate().equals(date))
            .mapToDouble(v -> v.calculateParkingFee(v.getParkingDuration()))
            .sum();
    }
}`
    },
    {
      title: 'Encapsulation & Data Hiding',
      icon: <Lock size={20} />,
      overview: 'Encapsulation bundles data and methods together while hiding internal implementation details. Private fields with public getters/setters ensure controlled access and maintain object integrity.',
      concepts: 'Encapsulation protects object state by making fields private and providing controlled access through public methods. This ensures data consistency and allows implementation changes without affecting external code.',
      code: `// ParkingLot class demonstrating encapsulation
public class ParkingLot {
    // Private fields - hidden from external access
    private final String lotId;
    private final String name;
    private final Address address;
    private final int totalFloors;
    private final Map<Integer, ParkingFloor> floors;
    private final Map<String, ParkingSpot> allSpots;
    private final Queue<ParkingTicket> activeTickets;
    private final PaymentSystem paymentSystem;
    private final SecuritySystem securitySystem;
    
    // Private state management
    private volatile int availableSpots;
    private volatile int occupiedSpots;
    private final AtomicLong dailyRevenue;
    private final ReentrantReadWriteLock lotLock;
    
    // Encapsulated configuration
    private ParkingLotConfig config;
    private final Map<VehicleType, Integer> vehicleCapacity;
    private final Map<SpotType, Double> spotPricing;
    
    // Private constructor - controlled instantiation
    private ParkingLot(ParkingLotBuilder builder) {
        this.lotId = builder.lotId;
        this.name = builder.name;
        this.address = builder.address;
        this.totalFloors = builder.totalFloors;
        this.floors = new ConcurrentHashMap<>();
        this.allSpots = new ConcurrentHashMap<>();
        this.activeTickets = new ConcurrentLinkedQueue<>();
        this.paymentSystem = builder.paymentSystem;
        this.securitySystem = builder.securitySystem;
        this.dailyRevenue = new AtomicLong(0);
        this.lotLock = new ReentrantReadWriteLock();
        this.vehicleCapacity = new HashMap<>();
        this.spotPricing = new HashMap<>();
        
        initializeFloors();
        initializeCapacity();
        initializePricing();
    }
    
    // Private initialization methods
    private void initializeFloors() {
        for (int i = 1; i <= totalFloors; i++) {
            ParkingFloor floor = new ParkingFloor(i, 100); // 100 spots per floor
            floors.put(i, floor);
            floor.getAllSpots().forEach(spot -> allSpots.put(spot.getSpotId(), spot));
        }
        this.availableSpots = allSpots.size();
        this.occupiedSpots = 0;
    }
    
    private void initializeCapacity() {
        vehicleCapacity.put(VehicleType.MOTORCYCLE, 50);
        vehicleCapacity.put(VehicleType.CAR, 200);
        vehicleCapacity.put(VehicleType.TRUCK, 20);
        vehicleCapacity.put(VehicleType.BUS, 10);
    }
    
    private void initializePricing() {
        spotPricing.put(SpotType.MOTORCYCLE, 2.0);
        spotPricing.put(SpotType.COMPACT, 5.0);
        spotPricing.put(SpotType.LARGE, 10.0);
        spotPricing.put(SpotType.HANDICAPPED, 0.0);
        spotPricing.put(SpotType.ELECTRIC, 7.0);
    }
    
    // Public methods providing controlled access
    public boolean parkVehicle(Vehicle vehicle) {
        lotLock.writeLock().lock();
        try {
            // Validate before parking
            if (!canAccommodate(vehicle)) {
                return false;
            }
            
            ParkingSpot spot = findAvailableSpot(vehicle);
            if (spot == null) {
                return false;
            }
            
            // Encapsulated parking logic
            boolean parked = assignVehicleToSpot(vehicle, spot);
            if (parked) {
                updateOccupancy(1);
                ParkingTicket ticket = generateTicket(vehicle, spot);
                activeTickets.offer(ticket);
                logParkingEvent(vehicle, spot, "ENTRY");
            }
            
            return parked;
        } finally {
            lotLock.writeLock().unlock();
        }
    }
    
    public ParkingTicket exitVehicle(String ticketId, PaymentMethod paymentMethod) {
        lotLock.writeLock().lock();
        try {
            ParkingTicket ticket = findTicket(ticketId);
            if (ticket == null) {
                throw new TicketNotFoundException("Invalid ticket: " + ticketId);
            }
            
            // Calculate fee using encapsulated logic
            double fee = calculateParkingFee(ticket);
            
            // Process payment through encapsulated system
            PaymentResult payment = processPayment(fee, paymentMethod);
            if (!payment.isSuccessful()) {
                throw new PaymentFailedException("Payment failed: " + payment.getError());
            }
            
            // Update internal state
            releaseSpot(ticket.getSpot());
            updateOccupancy(-1);
            updateRevenue(fee);
            ticket.markAsPaid(payment.getTransactionId());
            activeTickets.remove(ticket);
            logParkingEvent(ticket.getVehicle(), ticket.getSpot(), "EXIT");
            
            return ticket;
        } finally {
            lotLock.writeLock().unlock();
        }
    }
    
    // Private helper methods - hidden implementation
    private boolean canAccommodate(Vehicle vehicle) {
        int currentCount = getVehicleCount(vehicle.getType());
        int capacity = vehicleCapacity.getOrDefault(vehicle.getType(), 0);
        return currentCount < capacity && availableSpots >= vehicle.getRequiredSpots();
    }
    
    private ParkingSpot findAvailableSpot(Vehicle vehicle) {
        return allSpots.values().stream()
            .filter(spot -> spot.isAvailable() && vehicle.canFitInSpot(spot))
            .min(Comparator.comparingInt(spot -> spot.getFloor()))
            .orElse(null);
    }
    
    private boolean assignVehicleToSpot(Vehicle vehicle, ParkingSpot spot) {
        return spot.assignVehicle(vehicle);
    }
    
    private void updateOccupancy(int delta) {
        this.occupiedSpots += delta;
        this.availableSpots -= delta;
    }
    
    private void updateRevenue(double amount) {
        dailyRevenue.addAndGet((long)(amount * 100)); // Store in cents
    }
    
    private double calculateParkingFee(ParkingTicket ticket) {
        Duration duration = Duration.between(ticket.getEntryTime(), LocalDateTime.now());
        Vehicle vehicle = ticket.getVehicle();
        ParkingSpot spot = ticket.getSpot();
        
        double baseFee = vehicle.calculateParkingFee(duration);
        double spotRate = spotPricing.getOrDefault(spot.getType(), 5.0);
        
        return baseFee + applyDiscounts(baseFee, ticket);
    }
    
    private double applyDiscounts(double fee, ParkingTicket ticket) {
        // Member discount
        if (ticket.isMember()) {
            fee *= 0.9;
        }
        // Weekend discount
        if (isWeekend()) {
            fee *= 0.8;
        }
        return fee;
    }
    
    // Public getters with defensive copying
    public String getLotId() {
        return lotId;
    }
    
    public String getName() {
        return name;
    }
    
    public Address getAddress() {
        // Return defensive copy to prevent external modification
        return new Address(address);
    }
    
    public int getAvailableSpots() {
        lotLock.readLock().lock();
        try {
            return availableSpots;
        } finally {
            lotLock.readLock().unlock();
        }
    }
    
    public int getOccupiedSpots() {
        lotLock.readLock().lock();
        try {
            return occupiedSpots;
        } finally {
            lotLock.readLock().unlock();
        }
    }
    
    public double getDailyRevenue() {
        return dailyRevenue.get() / 100.0; // Convert from cents
    }
    
    // Immutable view of internal collections
    public List<ParkingSpot> getAllSpots() {
        return Collections.unmodifiableList(new ArrayList<>(allSpots.values()));
    }
    
    public Map<Integer, ParkingFloor> getFloors() {
        return Collections.unmodifiableMap(new HashMap<>(floors));
    }
    
    // Builder pattern for controlled object creation
    public static class ParkingLotBuilder {
        private String lotId;
        private String name;
        private Address address;
        private int totalFloors;
        private PaymentSystem paymentSystem;
        private SecuritySystem securitySystem;
        
        public ParkingLotBuilder(String lotId) {
            this.lotId = lotId;
        }
        
        public ParkingLotBuilder name(String name) {
            this.name = name;
            return this;
        }
        
        public ParkingLotBuilder address(Address address) {
            this.address = address;
            return this;
        }
        
        public ParkingLotBuilder totalFloors(int floors) {
            this.totalFloors = floors;
            return this;
        }
        
        public ParkingLotBuilder paymentSystem(PaymentSystem system) {
            this.paymentSystem = system;
            return this;
        }
        
        public ParkingLotBuilder securitySystem(SecuritySystem system) {
            this.securitySystem = system;
            return this;
        }
        
        public ParkingLot build() {
            validateBuilder();
            return new ParkingLot(this);
        }
        
        private void validateBuilder() {
            if (lotId == null || name == null || address == null) {
                throw new IllegalStateException("Required fields missing");
            }
            if (totalFloors <= 0) {
                throw new IllegalArgumentException("Invalid floor count");
            }
        }
    }
}`
    },
    {
      title: 'Composition & Aggregation',
      icon: <Layers size={20} />,
      overview: 'ParkingLot "has-a" relationship with ParkingFloors, ParkingSpots, and PaymentSystem. Composition creates strong ownership while aggregation allows shared references.',
      concepts: 'Composition represents a strong "has-a" relationship where components cannot exist without the container. Aggregation is a weaker relationship where components can exist independently.',
      code: `// Demonstration of Composition and Aggregation relationships
public class ParkingFloor {
    // Composition - ParkingFloor owns ParkingSpots
    private final int floorNumber;
    private final Map<String, ParkingSpot> spots;
    private final Map<SpotType, List<ParkingSpot>> spotsByType;
    private final DisplayBoard displayBoard; // Composition
    private final List<ParkingSensor> sensors; // Composition
    
    // Aggregation - ParkingFloor uses but doesn't own
    private SecurityCamera securityCamera; // Can exist independently
    private MaintenanceTeam maintenanceTeam; // Shared across floors
    
    public ParkingFloor(int floorNumber, int spotsPerFloor) {
        this.floorNumber = floorNumber;
        this.spots = new HashMap<>();
        this.spotsByType = new EnumMap<>(SpotType.class);
        
        // Composition - Creating owned objects
        this.displayBoard = new DisplayBoard(floorNumber);
        this.sensors = new ArrayList<>();
        
        initializeSpots(spotsPerFloor);
        initializeSensors();
    }
    
    private void initializeSpots(int count) {
        // Creating spots - Composition relationship
        for (int i = 1; i <= count; i++) {
            SpotType type = determineSpotType(i);
            String spotId = String.format("F%d-R%d-S%d", floorNumber, (i-1)/10 + 1, i);
            
            ParkingSpot spot = createSpot(spotId, type, i);
            spots.put(spotId, spot);
            
            spotsByType.computeIfAbsent(type, k -> new ArrayList<>()).add(spot);
        }
    }
    
    private ParkingSpot createSpot(String id, SpotType type, int number) {
        // Factory method for spot creation
        switch (type) {
            case COMPACT:
                return new CompactSpot(id, floorNumber, number);
            case LARGE:
                return new LargeSpot(id, floorNumber, number);
            case HANDICAPPED:
                return new HandicappedSpot(id, floorNumber, number);
            case ELECTRIC:
                return new ElectricSpot(id, floorNumber, number, new ChargingStation());
            case MOTORCYCLE:
                return new MotorcycleSpot(id, floorNumber, number);
            default:
                return new StandardSpot(id, floorNumber, number);
        }
    }
    
    private void initializeSensors() {
        // Composition - sensors are created and owned by floor
        spots.values().forEach(spot -> {
            ParkingSensor sensor = new ParkingSensor(spot.getSpotId());
            sensor.attachToSpot(spot);
            sensors.add(sensor);
        });
    }
    
    // Aggregation - setting external references
    public void setSecurityCamera(SecurityCamera camera) {
        this.securityCamera = camera;
        camera.addMonitoredFloor(this);
    }
    
    public void assignMaintenanceTeam(MaintenanceTeam team) {
        this.maintenanceTeam = team;
        team.addServicedFloor(this);
    }
    
    // When floor is destroyed, spots are destroyed (Composition)
    public void decommission() {
        // Composed objects are destroyed
        spots.clear();
        sensors.forEach(ParkingSensor::deactivate);
        sensors.clear();
        displayBoard.turnOff();
        
        // Aggregated objects are just dereferenced
        if (securityCamera != null) {
            securityCamera.removeMonitoredFloor(this);
            securityCamera = null;
        }
        if (maintenanceTeam != null) {
            maintenanceTeam.removeServicedFloor(this);
            maintenanceTeam = null;
        }
    }
    
    public List<ParkingSpot> getAvailableSpots() {
        return spots.values().stream()
            .filter(ParkingSpot::isAvailable)
            .collect(Collectors.toList());
    }
    
    public void updateDisplayBoard() {
        Map<SpotType, Integer> availability = new HashMap<>();
        spotsByType.forEach((type, spotList) -> {
            int available = (int) spotList.stream()
                .filter(ParkingSpot::isAvailable)
                .count();
            availability.put(type, available);
        });
        
        displayBoard.updateAvailability(availability);
    }
}

// Component class - exists only within ParkingFloor
class DisplayBoard {
    private final int floorNumber;
    private Map<SpotType, Integer> currentAvailability;
    private LocalDateTime lastUpdated;
    private boolean isActive;
    
    public DisplayBoard(int floorNumber) {
        this.floorNumber = floorNumber;
        this.currentAvailability = new HashMap<>();
        this.isActive = true;
        this.lastUpdated = LocalDateTime.now();
    }
    
    public void updateAvailability(Map<SpotType, Integer> availability) {
        if (!isActive) {
            throw new IllegalStateException("Display board is not active");
        }
        this.currentAvailability = new HashMap<>(availability);
        this.lastUpdated = LocalDateTime.now();
        display();
    }
    
    private void display() {
        System.out.println("Floor " + floorNumber + " Availability:");
        currentAvailability.forEach((type, count) -> 
            System.out.println(type + ": " + count + " spots"));
    }
    
    public void turnOff() {
        this.isActive = false;
        this.currentAvailability.clear();
    }
}

// Component class for sensors
class ParkingSensor {
    private final String sensorId;
    private String attachedSpotId;
    private boolean isActive;
    private SensorStatus status;
    private LocalDateTime lastReading;
    
    public ParkingSensor(String sensorId) {
        this.sensorId = sensorId;
        this.isActive = true;
        this.status = SensorStatus.VACANT;
    }
    
    public void attachToSpot(ParkingSpot spot) {
        this.attachedSpotId = spot.getSpotId();
    }
    
    public boolean detectVehicle() {
        if (!isActive) {
            return false;
        }
        
        lastReading = LocalDateTime.now();
        // Simulate vehicle detection logic
        return status == SensorStatus.OCCUPIED;
    }
    
    public void updateStatus(SensorStatus newStatus) {
        this.status = newStatus;
        this.lastReading = LocalDateTime.now();
    }
    
    public void deactivate() {
        this.isActive = false;
        this.status = SensorStatus.OFFLINE;
    }
}

// Aggregated class - can exist independently
class SecurityCamera {
    private final String cameraId;
    private final Set<ParkingFloor> monitoredFloors;
    private boolean isRecording;
    private LocalDateTime installationDate;
    
    public SecurityCamera(String cameraId) {
        this.cameraId = cameraId;
        this.monitoredFloors = new HashSet<>();
        this.isRecording = true;
        this.installationDate = LocalDateTime.now();
    }
    
    public void addMonitoredFloor(ParkingFloor floor) {
        monitoredFloors.add(floor);
    }
    
    public void removeMonitoredFloor(ParkingFloor floor) {
        monitoredFloors.remove(floor);
    }
    
    public void startRecording() {
        this.isRecording = true;
        monitoredFloors.forEach(floor -> 
            System.out.println("Recording floor: " + floor.getFloorNumber()));
    }
    
    public void stopRecording() {
        this.isRecording = false;
    }
    
    public List<SecurityFootage> getFootage(LocalDateTime from, LocalDateTime to) {
        // Return security footage for the time period
        return new ArrayList<>();
    }
}

// Another aggregated class - shared service
class MaintenanceTeam {
    private final String teamId;
    private final List<MaintenanceWorker> workers;
    private final Set<ParkingFloor> servicedFloors;
    private final Queue<MaintenanceRequest> requests;
    
    public MaintenanceTeam(String teamId) {
        this.teamId = teamId;
        this.workers = new ArrayList<>();
        this.servicedFloors = new HashSet<>();
        this.requests = new PriorityQueue<>();
    }
    
    public void addServicedFloor(ParkingFloor floor) {
        servicedFloors.add(floor);
    }
    
    public void removeServicedFloor(ParkingFloor floor) {
        servicedFloors.remove(floor);
    }
    
    public void handleMaintenance(MaintenanceRequest request) {
        requests.offer(request);
        assignWorker(request);
    }
    
    private void assignWorker(MaintenanceRequest request) {
        MaintenanceWorker availableWorker = workers.stream()
            .filter(MaintenanceWorker::isAvailable)
            .findFirst()
            .orElse(null);
            
        if (availableWorker != null) {
            availableWorker.assignTask(request);
        }
    }
}`
    },
    {
      title: 'SOLID Principles',
      icon: <Shield size={20} />,
      overview: 'Single Responsibility, Open/Closed, Liskov Substitution, Interface Segregation, and Dependency Inversion principles ensure maintainable, extensible object-oriented design.',
      concepts: 'SOLID principles guide OOP design: SRP keeps classes focused, OCP allows extension without modification, LSP ensures proper inheritance, ISP prevents fat interfaces, DIP decouples dependencies.',
      code: `// Single Responsibility Principle (SRP)
// Each class has one reason to change

// Good: Separated responsibilities
public class ParkingFeeCalculator {
    // Only responsible for fee calculation
    public double calculateFee(Vehicle vehicle, Duration duration) {
        double baseFee = calculateBaseFee(vehicle.getType(), duration);
        double discounts = calculateDiscounts(vehicle, duration);
        double surcharges = calculateSurcharges(vehicle, duration);
        return Math.max(0, baseFee - discounts + surcharges);
    }
    
    private double calculateBaseFee(VehicleType type, Duration duration) {
        double hourlyRate = getHourlyRate(type);
        double hours = Math.ceil(duration.toMinutes() / 60.0);
        return hourlyRate * hours;
    }
    
    private double getHourlyRate(VehicleType type) {
        switch (type) {
            case MOTORCYCLE: return 2.0;
            case CAR: return 5.0;
            case TRUCK: return 10.0;
            default: return 5.0;
        }
    }
    
    private double calculateDiscounts(Vehicle vehicle, Duration duration) {
        double discount = 0;
        if (vehicle instanceof ElectricVehicle) {
            discount += 0.2 * calculateBaseFee(vehicle.getType(), duration);
        }
        if (duration.toHours() > 8) {
            discount += 5.0; // Daily parking discount
        }
        return discount;
    }
    
    private double calculateSurcharges(Vehicle vehicle, Duration duration) {
        double surcharge = 0;
        if (vehicle instanceof CommercialVehicle) {
            surcharge += 5.0;
        }
        if (isOvernight(duration)) {
            surcharge += 10.0;
        }
        return surcharge;
    }
    
    private boolean isOvernight(Duration duration) {
        return duration.toHours() > 12;
    }
}

// Open/Closed Principle (OCP)
// Open for extension, closed for modification

// Base payment method - closed for modification
public abstract class PaymentMethod {
    protected final String accountId;
    protected final String customerName;
    
    protected PaymentMethod(String accountId, String customerName) {
        this.accountId = accountId;
        this.customerName = customerName;
    }
    
    public abstract PaymentResult processPayment(double amount);
    public abstract boolean validate();
    public abstract PaymentType getType();
}

// Extended without modifying base class
public class CreditCardPayment extends PaymentMethod {
    private final String cardNumber;
    private final String cvv;
    private final LocalDate expiryDate;
    
    public CreditCardPayment(String accountId, String customerName, 
                            String cardNumber, String cvv, LocalDate expiryDate) {
        super(accountId, customerName);
        this.cardNumber = cardNumber;
        this.cvv = cvv;
        this.expiryDate = expiryDate;
    }
    
    @Override
    public PaymentResult processPayment(double amount) {
        if (!validate()) {
            return PaymentResult.failed("Invalid credit card");
        }
        
        // Process credit card payment
        String transactionId = processWithBank(amount);
        return PaymentResult.success(transactionId, amount);
    }
    
    @Override
    public boolean validate() {
        return validateCardNumber() && validateCVV() && !isExpired();
    }
    
    @Override
    public PaymentType getType() {
        return PaymentType.CREDIT_CARD;
    }
    
    private boolean validateCardNumber() {
        return cardNumber != null && cardNumber.matches("\\d{16}");
    }
    
    private boolean validateCVV() {
        return cvv != null && cvv.matches("\\d{3,4}");
    }
    
    private boolean isExpired() {
        return LocalDate.now().isAfter(expiryDate);
    }
    
    private String processWithBank(double amount) {
        // Simulate bank processing
        return "TXN-" + System.currentTimeMillis();
    }
}

// New payment method - extension without modification
public class DigitalWalletPayment extends PaymentMethod {
    private final String walletId;
    private final WalletProvider provider;
    
    public DigitalWalletPayment(String accountId, String customerName, 
                                String walletId, WalletProvider provider) {
        super(accountId, customerName);
        this.walletId = walletId;
        this.provider = provider;
    }
    
    @Override
    public PaymentResult processPayment(double amount) {
        if (!validate()) {
            return PaymentResult.failed("Invalid digital wallet");
        }
        
        String transactionId = provider.processTransaction(walletId, amount);
        return PaymentResult.success(transactionId, amount);
    }
    
    @Override
    public boolean validate() {
        return walletId != null && provider.validateWallet(walletId);
    }
    
    @Override
    public PaymentType getType() {
        return PaymentType.DIGITAL_WALLET;
    }
}

// Liskov Substitution Principle (LSP)
// Subtypes must be substitutable for their base types

// Good LSP implementation
public abstract class ParkingSpot {
    protected String spotId;
    protected boolean isOccupied;
    
    public boolean canAccommodate(Vehicle vehicle) {
        return !isOccupied && vehicle.canFitInSpot(this);
    }
    
    public boolean park(Vehicle vehicle) {
        if (canAccommodate(vehicle)) {
            isOccupied = true;
            return true;
        }
        return false;
    }
    
    public void unpark() {
        isOccupied = false;
    }
}

public class HandicappedSpot extends ParkingSpot {
    @Override
    public boolean canAccommodate(Vehicle vehicle) {
        // LSP preserved - more restrictive but compatible
        return super.canAccommodate(vehicle) && 
               vehicle.getDriver().hasHandicappedPermit();
    }
    
    // Does not violate LSP - behavior is consistent with parent
    @Override
    public boolean park(Vehicle vehicle) {
        if (vehicle.getDriver().hasHandicappedPermit()) {
            return super.park(vehicle);
        }
        return false;
    }
}

// Interface Segregation Principle (ISP)
// Clients shouldn't depend on interfaces they don't use

// Bad: Fat interface
interface BadVehicleOperations {
    void startEngine();
    void stopEngine();
    void chargeBattery();
    void refuel();
    void openTrunk();
    void foldSeats();
    void engageAutopilot();
}

// Good: Segregated interfaces
interface Driveable {
    void startEngine();
    void stopEngine();
    void accelerate(double speed);
    void brake();
}

interface ElectricVehicle {
    void chargeBattery(double kWh);
    double getBatteryLevel();
    double getRange();
}

interface FuelVehicle {
    void refuel(double liters);
    double getFuelLevel();
    double getFuelEfficiency();
}

interface CargoCapable {
    void openTrunk();
    void loadCargo(Cargo cargo);
    double getCargoCapacity();
}

// Classes implement only what they need
public class ElectricCar implements Driveable, ElectricVehicle, CargoCapable {
    private double batteryLevel;
    private boolean isRunning;
    private double speed;
    private Trunk trunk;
    
    @Override
    public void startEngine() {
        if (batteryLevel > 0) {
            isRunning = true;
        }
    }
    
    @Override
    public void stopEngine() {
        isRunning = false;
        speed = 0;
    }
    
    @Override
    public void accelerate(double speed) {
        if (isRunning && batteryLevel > 0) {
            this.speed = Math.min(speed, 200);
            batteryLevel -= speed * 0.01;
        }
    }
    
    @Override
    public void brake() {
        speed = Math.max(0, speed - 10);
        batteryLevel += 0.5; // Regenerative braking
    }
    
    @Override
    public void chargeBattery(double kWh) {
        batteryLevel = Math.min(100, batteryLevel + kWh);
    }
    
    @Override
    public double getBatteryLevel() {
        return batteryLevel;
    }
    
    @Override
    public double getRange() {
        return batteryLevel * 5; // 5 km per kWh
    }
    
    @Override
    public void openTrunk() {
        trunk.open();
    }
    
    @Override
    public void loadCargo(Cargo cargo) {
        trunk.load(cargo);
    }
    
    @Override
    public double getCargoCapacity() {
        return trunk.getCapacity();
    }
}

// Dependency Inversion Principle (DIP)
// Depend on abstractions, not concretions

// High-level module
public class ParkingLotController {
    // Depends on abstractions, not concrete implementations
    private final ParkingRepository repository;
    private final NotificationService notificationService;
    private final PaymentProcessor paymentProcessor;
    private final Logger logger;
    
    // Constructor injection of dependencies
    public ParkingLotController(ParkingRepository repository,
                                NotificationService notificationService,
                                PaymentProcessor paymentProcessor,
                                Logger logger) {
        this.repository = repository;
        this.notificationService = notificationService;
        this.paymentProcessor = paymentProcessor;
        this.logger = logger;
    }
    
    public ParkingReceipt parkVehicle(Vehicle vehicle, String spotId) {
        try {
            ParkingSpot spot = repository.findSpot(spotId);
            if (spot == null) {
                throw new SpotNotFoundException("Spot not found: " + spotId);
            }
            
            if (!spot.canAccommodate(vehicle)) {
                throw new InvalidParkingException("Vehicle cannot fit");
            }
            
            spot.park(vehicle);
            repository.updateSpot(spot);
            
            ParkingTicket ticket = new ParkingTicket(vehicle, spot);
            repository.saveTicket(ticket);
            
            notificationService.sendParkingConfirmation(vehicle.getOwnerEmail(), ticket);
            logger.log("Vehicle parked: " + vehicle.getLicensePlate());
            
            return new ParkingReceipt(ticket);
            
        } catch (Exception e) {
            logger.error("Parking failed", e);
            notificationService.sendParkingFailure(vehicle.getOwnerEmail(), e.getMessage());
            throw e;
        }
    }
    
    public PaymentReceipt exitParking(String ticketId, PaymentMethod paymentMethod) {
        ParkingTicket ticket = repository.findTicket(ticketId);
        if (ticket == null) {
            throw new TicketNotFoundException("Invalid ticket");
        }
        
        double fee = calculateFee(ticket);
        PaymentResult result = paymentProcessor.processPayment(fee, paymentMethod);
        
        if (result.isSuccessful()) {
            ParkingSpot spot = ticket.getSpot();
            spot.unpark();
            repository.updateSpot(spot);
            repository.completeTicket(ticket);
            
            notificationService.sendPaymentConfirmation(
                ticket.getVehicle().getOwnerEmail(), result);
            logger.log("Vehicle exited: " + ticket.getVehicle().getLicensePlate());
            
            return new PaymentReceipt(result, ticket);
        } else {
            throw new PaymentFailedException("Payment failed: " + result.getError());
        }
    }
    
    private double calculateFee(ParkingTicket ticket) {
        Duration duration = Duration.between(ticket.getEntryTime(), LocalDateTime.now());
        return ticket.getVehicle().calculateParkingFee(duration);
    }
}

// Abstraction interfaces
interface ParkingRepository {
    ParkingSpot findSpot(String spotId);
    void updateSpot(ParkingSpot spot);
    void saveTicket(ParkingTicket ticket);
    ParkingTicket findTicket(String ticketId);
    void completeTicket(ParkingTicket ticket);
}

interface NotificationService {
    void sendParkingConfirmation(String email, ParkingTicket ticket);
    void sendParkingFailure(String email, String reason);
    void sendPaymentConfirmation(String email, PaymentResult result);
}

interface Logger {
    void log(String message);
    void error(String message, Exception e);
    void debug(String message);
}`
    }
  ];

  const tabStyle = (isActive) => ({
    padding: '12px 24px',
    backgroundColor: isActive ? 'rgba(139, 92, 246, 0.2)' : 'rgba(55, 65, 81, 0.3)',
    border: 'none',
    borderRadius: '8px',
    color: isActive ? '#a78bfa' : '#9ca3af',
    cursor: 'pointer',
    fontWeight: isActive ? '600' : '500',
    transition: 'all 0.2s',
    fontSize: '14px'
  });

  return (
    <div style={{ 
      padding: '40px',
      backgroundColor: '#0f172a',
      minHeight: '100vh',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      <div style={{
        maxWidth: '1400px',
        margin: '0 auto'
      }}>
        {/* Header */}
        <div style={{ marginBottom: '40px', textAlign: 'center' }}>
          <div style={{
            display: 'inline-flex',
            alignItems: 'center',
            gap: '16px',
            backgroundColor: 'rgba(139, 92, 246, 0.1)',
            padding: '16px 32px',
            borderRadius: '16px',
            border: '1px solid rgba(139, 92, 246, 0.3)'
          }}>
            <Car size={32} color="#a78bfa" />
            <h1 style={{
              fontSize: '28px',
              fontWeight: '700',
              color: 'white',
              margin: 0
            }}>
              Parking Lot OOP Design System
            </h1>
          </div>
          <p style={{
            fontSize: '16px',
            color: '#94a3b8',
            marginTop: '16px',
            maxWidth: '800px',
            margin: '16px auto 0'
          }}>
            Comprehensive Object-Oriented Programming demonstration using a Parking Lot Management System
            showcasing all four pillars of OOP: Abstraction, Inheritance, Encapsulation, and Polymorphism
          </p>
        </div>

        {/* Tab Navigation */}
        <div style={{
          display: 'flex',
          gap: '8px',
          marginBottom: '32px',
          justifyContent: 'center'
        }}>
          {['overview', 'concepts', 'code', 'uml'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              style={tabStyle(activeTab === tab)}
              onMouseEnter={(e) => {
                if (activeTab !== tab) {
                  e.currentTarget.style.backgroundColor = 'rgba(139, 92, 246, 0.1)';
                }
              }}
              onMouseLeave={(e) => {
                if (activeTab !== tab) {
                  e.currentTarget.style.backgroundColor = 'rgba(55, 65, 81, 0.3)';
                }
              }}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </button>
          ))}
        </div>

        <div style={{ display: 'flex', gap: '32px' }}>
          {/* Concepts Sidebar */}
          <div style={{
            width: '320px',
            backgroundColor: 'rgba(30, 41, 59, 0.5)',
            borderRadius: '12px',
            padding: '24px',
            height: 'fit-content',
            border: '1px solid rgba(51, 65, 85, 0.8)'
          }}>
            <h3 style={{
              fontSize: '18px',
              fontWeight: '600',
              color: '#a78bfa',
              marginBottom: '20px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <Shield size={20} />
              OOP Concepts
            </h3>
            
            {oopConcepts.map((concept, index) => (
              <button
                key={index}
                onClick={() => setSelectedConcept(index)}
                style={{
                  width: '100%',
                  padding: '16px',
                  marginBottom: '12px',
                  backgroundColor: selectedConcept === index ? 'rgba(139, 92, 246, 0.2)' : 'transparent',
                  border: selectedConcept === index ? '2px solid #a78bfa' : '2px solid transparent',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  textAlign: 'left',
                  transition: 'all 0.2s',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px'
                }}
                onMouseEnter={(e) => {
                  if (selectedConcept !== index) {
                    e.currentTarget.style.backgroundColor = 'rgba(139, 92, 246, 0.1)';
                    e.currentTarget.style.borderColor = '#a78bfa';
                  }
                }}
                onMouseLeave={(e) => {
                  if (selectedConcept !== index) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.borderColor = 'transparent';
                  }
                }}
              >
                <div style={{ color: selectedConcept === index ? '#a78bfa' : '#94a3b8' }}>
                  {concept.icon}
                </div>
                <span style={{
                  color: selectedConcept === index ? '#a78bfa' : 'white',
                  fontWeight: selectedConcept === index ? '600' : '500',
                  fontSize: '14px'
                }}>
                  {concept.title}
                </span>
              </button>
            ))}
          </div>

          {/* Content Area */}
          <div style={{ flex: 1 }}>
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              borderRadius: '12px',
              padding: '32px',
              border: '1px solid rgba(51, 65, 85, 0.8)'
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                marginBottom: '24px'
              }}>
                <div style={{ color: '#a78bfa' }}>
                  {oopConcepts[selectedConcept].icon}
                </div>
                <h2 style={{
                  fontSize: '24px',
                  fontWeight: '700',
                  color: 'white',
                  margin: 0
                }}>
                  {oopConcepts[selectedConcept].title}
                </h2>
              </div>

              <div style={{
                fontSize: '16px',
                lineHeight: '1.7',
                color: '#e2e8f0'
              }}>
                {activeTab === 'overview' && (
                  <div>
                    <p>{oopConcepts[selectedConcept].overview}</p>
                  </div>
                )}

                {activeTab === 'concepts' && (
                  <div>
                    <p>{oopConcepts[selectedConcept].concepts}</p>
                  </div>
                )}

                {activeTab === 'code' && (
                  <div style={{ marginTop: '20px' }}>
                    <SyntaxHighlighter
                      language="java"
                      style={oneDark}
                      customStyle={{
                        borderRadius: '8px',
                        fontSize: '13px',
                        lineHeight: '1.5',
                        maxHeight: 'calc(100vh - 400px)',
                        overflow: 'auto'
                      }}
                    >
                      {oopConcepts[selectedConcept].code}
                    </SyntaxHighlighter>
                  </div>
                )}

                {activeTab === 'uml' && (
                  <div style={{ marginTop: '20px' }}>
                    <div style={{ 
                      display: 'flex',
                      gap: '20px',
                      height: 'calc(100vh - 300px)'
                    }}>
                      {/* Interactive UML Diagram */}
                      <div style={{ 
                        flex: 2,
                        backgroundColor: '#1e293b',
                        borderRadius: '8px',
                        padding: '20px',
                        overflow: 'auto',
                        position: 'relative'
                      }}>
                        <h4 style={{ 
                          color: '#a78bfa', 
                          marginBottom: '20px', 
                          textAlign: 'center',
                          fontSize: '16px'
                        }}>
                          Interactive UML Class Diagram
                        </h4>
                        
                        <svg 
                          width="1100" 
                          height="800" 
                          style={{ 
                            backgroundColor: '#0f172a', 
                            borderRadius: '8px',
                            border: '1px solid #374151'
                          }}
                        >
                          {/* Relationship Lines */}
                          {umlClasses.filter(cls => cls.relationships).map(cls => 
                            cls.relationships.map((rel, idx) => {
                              const target = umlClasses.find(c => c.id === rel.target);
                              if (!target) return null;
                              
                              const [x1, y1, x2, y2] = rel.points;
                              return (
                                <g key={`${cls.id}-${idx}`}>
                                  <line
                                    x1={x1}
                                    y1={y1}
                                    x2={x2}
                                    y2={y2}
                                    stroke="#64748b"
                                    strokeWidth="2"
                                    strokeDasharray={rel.type === 'implements' ? '5,5' : '0'}
                                    markerEnd="url(#arrow)"
                                  />
                                  <text
                                    x={(x1 + x2) / 2}
                                    y={(y1 + y2) / 2 - 5}
                                    fill="#94a3b8"
                                    fontSize="10"
                                    textAnchor="middle"
                                  >
                                    {rel.type}
                                  </text>
                                </g>
                              );
                            })
                          )}
                          
                          <defs>
                            <marker
                              id="arrow"
                              markerWidth="10"
                              markerHeight="10"
                              refX="9"
                              refY="3"
                              orient="auto"
                              markerUnits="strokeWidth"
                            >
                              <path d="M0,0 L0,6 L9,3 z" fill="#64748b" />
                            </marker>
                          </defs>
                          
                          {/* Class boxes */}
                          {umlClasses.map(cls => {
                            const isSelected = selectedClass?.id === cls.id;
                            const isHovered = hoveredClass === cls.id;
                            const boxHeight = 50 + (cls.attributes.length * 12) + (cls.methods.length * 12);
                            
                            return (
                              <g 
                                key={cls.id}
                                transform={`translate(${cls.position.x}, ${cls.position.y})`}
                                style={{ cursor: 'pointer' }}
                                onClick={() => setSelectedClass(cls)}
                                onMouseEnter={() => setHoveredClass(cls.id)}
                                onMouseLeave={() => setHoveredClass(null)}
                              >
                                <rect
                                  width="140"
                                  height={boxHeight}
                                  fill={isSelected || isHovered ? cls.color : 'rgba(30, 41, 59, 0.9)'}
                                  stroke={cls.color}
                                  strokeWidth={isSelected ? "3" : "1.5"}
                                  rx="4"
                                />
                                
                                <text
                                  x="70"
                                  y="15"
                                  fill={isSelected || isHovered ? "white" : cls.color}
                                  fontSize="12"
                                  fontWeight="bold"
                                  textAnchor="middle"
                                >
                                  {cls.type !== 'class' && `<<${cls.type}>>`}
                                </text>
                                <text
                                  x="70"
                                  y={cls.type !== 'class' ? "28" : "15"}
                                  fill={isSelected || isHovered ? "white" : "#e2e8f0"}
                                  fontSize="11"
                                  fontWeight="bold"
                                  textAnchor="middle"
                                >
                                  {cls.name}
                                </text>
                                
                                <line
                                  x1="5"
                                  y1={cls.type !== 'class' ? "35" : "22"}
                                  x2="135"
                                  y2={cls.type !== 'class' ? "35" : "22"}
                                  stroke={cls.color}
                                  strokeWidth="1"
                                />
                                
                                {cls.attributes.map((attr, idx) => (
                                  <text
                                    key={idx}
                                    x="10"
                                    y={cls.type !== 'class' ? 48 + idx * 12 : 35 + idx * 12}
                                    fill={isSelected || isHovered ? "#e2e8f0" : "#94a3b8"}
                                    fontSize="9"
                                  >
                                    {attr}
                                  </text>
                                ))}
                                
                                {cls.methods.length > 0 && (
                                  <line
                                    x1="5"
                                    y1={cls.type !== 'class' ? 35 + cls.attributes.length * 12 + 8 : 22 + cls.attributes.length * 12 + 8}
                                    x2="135"
                                    y2={cls.type !== 'class' ? 35 + cls.attributes.length * 12 + 8 : 22 + cls.attributes.length * 12 + 8}
                                    stroke={cls.color}
                                    strokeWidth="1"
                                  />
                                )}
                                
                                {cls.methods.map((method, idx) => (
                                  <text
                                    key={idx}
                                    x="10"
                                    y={cls.type !== 'class' ? 48 + cls.attributes.length * 12 + idx * 12 : 35 + cls.attributes.length * 12 + idx * 12}
                                    fill={isSelected || isHovered ? "#e2e8f0" : "#94a3b8"}
                                    fontSize="9"
                                  >
                                    {method}
                                  </text>
                                ))}
                              </g>
                            );
                          })}
                        </svg>
                      </div>
                      
                      {/* Class Details Panel */}
                      <div style={{ 
                        flex: 1,
                        backgroundColor: 'rgba(30, 41, 59, 0.5)',
                        borderRadius: '8px',
                        padding: '20px',
                        border: '1px solid #374151'
                      }}>
                        <h4 style={{ 
                          color: '#a78bfa', 
                          marginBottom: '16px',
                          fontSize: '16px'
                        }}>
                          Class Details
                        </h4>
                        
                        {selectedClass ? (
                          <div>
                            <div style={{
                              backgroundColor: selectedClass.color,
                              color: 'white',
                              padding: '12px',
                              borderRadius: '6px',
                              marginBottom: '16px',
                              textAlign: 'center'
                            }}>
                              <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                                {selectedClass.name}
                              </div>
                              <div style={{ fontSize: '12px', opacity: 0.9, textTransform: 'capitalize' }}>
                                {selectedClass.type}
                              </div>
                            </div>
                            
                            <div style={{ marginBottom: '16px' }}>
                              <h5 style={{ color: '#10b981', fontSize: '13px', marginBottom: '8px' }}>
                                Description
                              </h5>
                              <p style={{ color: '#d1d5db', fontSize: '12px', lineHeight: '1.5' }}>
                                {getClassDetails(selectedClass.id).description}
                              </p>
                            </div>
                            
                            <div style={{ marginBottom: '16px' }}>
                              <h5 style={{ color: '#10b981', fontSize: '13px', marginBottom: '8px' }}>
                                Responsibilities
                              </h5>
                              <ul style={{ margin: 0, paddingLeft: '16px', color: '#d1d5db', fontSize: '12px' }}>
                                {getClassDetails(selectedClass.id).responsibilities.map((resp, idx) => (
                                  <li key={idx} style={{ marginBottom: '4px' }}>{resp}</li>
                                ))}
                              </ul>
                            </div>
                            
                            <div style={{ marginBottom: '16px' }}>
                              <h5 style={{ color: '#10b981', fontSize: '13px', marginBottom: '8px' }}>
                                Relationships
                              </h5>
                              <p style={{ color: '#d1d5db', fontSize: '12px', lineHeight: '1.5' }}>
                                {getClassDetails(selectedClass.id).relationships}
                              </p>
                            </div>
                            
                            {selectedClass.attributes && selectedClass.attributes.length > 0 && (
                              <div style={{ marginBottom: '16px' }}>
                                <h5 style={{ color: '#10b981', fontSize: '13px', marginBottom: '8px' }}>
                                  Attributes ({selectedClass.attributes.length})
                                </h5>
                                <div style={{ backgroundColor: 'rgba(0, 0, 0, 0.3)', padding: '8px', borderRadius: '4px' }}>
                                  {selectedClass.attributes.map((attr, idx) => (
                                    <div key={idx} style={{ color: '#94a3b8', fontSize: '11px', fontFamily: 'monospace' }}>
                                      {attr}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                            
                            {selectedClass.methods && selectedClass.methods.length > 0 && (
                              <div>
                                <h5 style={{ color: '#10b981', fontSize: '13px', marginBottom: '8px' }}>
                                  Methods ({selectedClass.methods.length})
                                </h5>
                                <div style={{ backgroundColor: 'rgba(0, 0, 0, 0.3)', padding: '8px', borderRadius: '4px' }}>
                                  {selectedClass.methods.map((method, idx) => (
                                    <div key={idx} style={{ color: '#94a3b8', fontSize: '11px', fontFamily: 'monospace' }}>
                                      {method}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        ) : (
                          <div style={{ 
                            textAlign: 'center', 
                            color: '#94a3b8', 
                            fontSize: '14px',
                            paddingTop: '40px'
                          }}>
                            <div style={{ marginBottom: '16px', color: '#64748b' }}>
                              <Layers size={48} />
                            </div>
                            Click on a class in the diagram to view detailed information about its structure, responsibilities, and relationships.
                            
                            <div style={{ 
                              marginTop: '24px', 
                              fontSize: '12px',
                              backgroundColor: 'rgba(139, 92, 246, 0.1)',
                              padding: '12px',
                              borderRadius: '6px',
                              border: '1px solid rgba(139, 92, 246, 0.3)'
                            }}>
                              <strong style={{ color: '#a78bfa' }}>Interactive Features:</strong>
                              <ul style={{ textAlign: 'left', marginTop: '8px', paddingLeft: '16px' }}>
                                <li>Hover over classes to highlight</li>
                                <li>Click to view detailed information</li>
                                <li>Relationship lines show inheritance and implementation</li>
                                <li>Color coding by class type and functionality</li>
                              </ul>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ParkingLotOOP;

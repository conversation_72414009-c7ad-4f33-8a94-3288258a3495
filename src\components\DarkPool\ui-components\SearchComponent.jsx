import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Search, X, Filter, Clock, Tag, Code2, Star, ArrowRight, Zap, History } from 'lucide-react';

const SearchComponent = ({ 
  components, 
  onSearch, 
  onSelectComponent,
  recentSearches = [],
  onAddRecentSearch,
  searchHistory = [],
  popularComponents = []
}) => {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [activeIndex, setActiveIndex] = useState(-1);
  const [searchMode, setSearchMode] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    category: 'all',
    technology: 'all',
    difficulty: 'all',
    type: 'all'
  });
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const searchRef = useRef(null);
  const inputRef = useRef(null);

  const searchModes = [
    { id: 'all', label: 'All', icon: <Search size={14} />, description: 'Search everywhere' },
    { id: 'name', label: 'Names', icon: <Tag size={14} />, description: 'Component names only' },
    { id: 'tech', label: 'Tech', icon: <Code2 size={14} />, description: 'Technologies & frameworks' },
    { id: 'keywords', label: 'Keywords', icon: <Zap size={14} />, description: 'Keywords & concepts' }
  ];

  const categories = ['all', 'trading', 'oop-designs', 'patterns', 'ui-components', 'java', 'sql', 'messaging'];
  const technologies = ['all', 'React', 'Java', 'Spring', 'SQL', 'Kafka', 'JavaScript', 'CSS'];
  const difficulties = ['all', 'beginner', 'intermediate', 'advanced', 'expert'];
  const types = ['all', 'component', 'system', 'pattern', 'utility'];

  const performSearch = useCallback((searchQuery, searchFilters = filters) => {
    if (!searchQuery && searchFilters.category === 'all' && searchFilters.technology === 'all' && 
        searchFilters.difficulty === 'all' && searchFilters.type === 'all') {
      return [];
    }

    return components.filter(comp => {
      const searchLower = searchQuery.toLowerCase();
      let matchesQuery = true;
      
      if (searchQuery) {
        switch (searchMode) {
          case 'name':
            matchesQuery = comp.name.toLowerCase().includes(searchLower);
            break;
          case 'tech':
            matchesQuery = comp.tech?.some(t => t.toLowerCase().includes(searchLower));
            break;
          case 'keywords':
            matchesQuery = comp.keywords?.some(k => k.toLowerCase().includes(searchLower));
            break;
          default:
            matchesQuery = comp.name.toLowerCase().includes(searchLower) ||
              comp.tech?.some(t => t.toLowerCase().includes(searchLower)) ||
              comp.keywords?.some(k => k.toLowerCase().includes(searchLower)) ||
              comp.description?.toLowerCase().includes(searchLower);
        }
      }

      const matchesCategory = searchFilters.category === 'all' || comp.category === searchFilters.category;
      const matchesTechnology = searchFilters.technology === 'all' || comp.tech?.includes(searchFilters.technology);
      const matchesDifficulty = searchFilters.difficulty === 'all' || comp.difficulty === searchFilters.difficulty;
      const matchesType = searchFilters.type === 'all' || comp.type === searchFilters.type;

      return matchesQuery && matchesCategory && matchesTechnology && matchesDifficulty && matchesType;
    });
  }, [components, searchMode, filters]);

  const getSearchScore = (comp, searchQuery) => {
    if (!searchQuery) return 0;
    
    const searchLower = searchQuery.toLowerCase();
    let score = 0;

    if (comp.name.toLowerCase().startsWith(searchLower)) score += 100;
    else if (comp.name.toLowerCase().includes(searchLower)) score += 50;

    if (comp.keywords?.some(k => k.toLowerCase().startsWith(searchLower))) score += 30;
    if (comp.tech?.some(t => t.toLowerCase().includes(searchLower))) score += 20;

    if (comp.description?.toLowerCase().includes(searchLower)) score += 10;

    if (popularComponents.includes(comp.id)) score += 5;
    if (recentSearches.includes(comp.id)) score += 3;

    return score;
  };

  useEffect(() => {
    const searchResults = performSearch(query);
    const sortedResults = searchResults
      .map(comp => ({ ...comp, searchScore: getSearchScore(comp, query) }))
      .sort((a, b) => b.searchScore - a.searchScore)
      .slice(0, 10);

    setSuggestions(sortedResults);
    setShowSuggestions(query.length > 0 || isSearchFocused);
    setActiveIndex(-1);
  }, [query, filters, searchMode, performSearch]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setShowSuggestions(false);
        setIsSearchFocused(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleKeyDown = (e) => {
    if (!showSuggestions) return;

    const totalItems = suggestions.length + (query === '' ? recentSearches.length : 0);

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setActiveIndex(prev => (prev + 1) % totalItems);
        break;
      case 'ArrowUp':
        e.preventDefault();
        setActiveIndex(prev => prev <= 0 ? totalItems - 1 : prev - 1);
        break;
      case 'Enter':
        e.preventDefault();
        if (activeIndex >= 0) {
          if (query === '' && activeIndex < recentSearches.length) {
            const recentComp = components.find(c => c.id === recentSearches[activeIndex]);
            if (recentComp) handleSelectComponent(recentComp);
          } else {
            const suggestionIndex = query === '' ? activeIndex - recentSearches.length : activeIndex;
            if (suggestions[suggestionIndex]) {
              handleSelectComponent(suggestions[suggestionIndex]);
            }
          }
        } else if (suggestions.length > 0) {
          handleSelectComponent(suggestions[0]);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setIsSearchFocused(false);
        inputRef.current?.blur();
        break;
    }
  };

  const handleSelectComponent = (comp) => {
    setQuery(comp.name);
    setShowSuggestions(false);
    setIsSearchFocused(false);
    onSelectComponent(comp);
    if (onAddRecentSearch) {
      onAddRecentSearch(comp.id);
    }
  };

  const clearSearch = () => {
    setQuery('');
    setShowSuggestions(false);
    onSearch('');
    inputRef.current?.focus();
  };

  const handleInputChange = (e) => {
    const newQuery = e.target.value;
    setQuery(newQuery);
    onSearch(newQuery);
  };

  const handleFilterChange = (filterType, value) => {
    const newFilters = { ...filters, [filterType]: value };
    setFilters(newFilters);
    
    const results = performSearch(query, newFilters);
    setSuggestions(results.slice(0, 10));
  };

  const getHighlightedText = (text, highlight) => {
    if (!highlight) return text;
    
    const parts = text.split(new RegExp(`(${highlight})`, 'gi'));
    return parts.map((part, index) => (
      <span 
        key={index} 
        style={{ 
          backgroundColor: part.toLowerCase() === highlight.toLowerCase() ? '#fbbf24' : 'transparent',
          color: part.toLowerCase() === highlight.toLowerCase() ? '#000' : 'inherit',
          borderRadius: '2px',
          padding: '0 2px'
        }}
      >
        {part}
      </span>
    ));
  };

  const getTechBadgeColor = (tech) => {
    const techColors = {
      'React': '#61dafb', 'Java': '#ed8b00', 'Spring': '#6db33f',
      'SQL': '#336791', 'Kafka': '#231f20', 'JavaScript': '#f7df1e',
      'CSS': '#1572b6', 'HTML': '#e34f26'
    };
    return techColors[tech] || '#6b7280';
  };

  const inputStyle = {
    paddingLeft: '44px',
    paddingRight: query ? '44px' : '16px',
    paddingTop: '12px',
    paddingBottom: '12px',
    width: '320px',
    backgroundColor: isSearchFocused ? '#111827' : '#1f2937',
    color: 'white',
    borderRadius: '12px',
    border: `2px solid ${isSearchFocused ? '#3b82f6' : '#374151'}`,
    outline: 'none',
    fontSize: '14px',
    fontWeight: '500',
    transition: 'all 0.2s ease-in-out',
    boxShadow: isSearchFocused ? '0 0 0 3px rgba(59, 130, 246, 0.1)' : 'none'
  };

  return (
    <div ref={searchRef} style={{ position: 'relative', display: 'flex', flexDirection: 'column', gap: '8px' }}>
      {/* Search Input */}
      <div style={{ position: 'relative', display: 'flex', alignItems: 'center', gap: '8px' }}>
        <div style={{ position: 'relative', flex: 1 }}>
          <Search 
            style={{ 
              position: 'absolute', 
              left: '14px', 
              top: '50%', 
              transform: 'translateY(-50%)', 
              color: isSearchFocused ? '#3b82f6' : '#9ca3af',
              transition: 'color 0.2s'
            }} 
            size={20} 
          />
          <input
            ref={inputRef}
            type="text"
            placeholder={`Search components${searchMode !== 'all' ? ` by ${searchMode}` : ''}...`}
            value={query}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onFocus={() => {
              setIsSearchFocused(true);
              setShowSuggestions(true);
            }}
            style={inputStyle}
          />
          {query && (
            <button
              onClick={clearSearch}
              style={{ 
                position: 'absolute', 
                right: '14px', 
                top: '50%', 
                transform: 'translateY(-50%)', 
                color: '#9ca3af', 
                cursor: 'pointer', 
                background: 'none', 
                border: 'none',
                padding: '4px',
                borderRadius: '4px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                transition: 'all 0.2s'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#374151';
                e.currentTarget.style.color = '#fff';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = '#9ca3af';
              }}
            >
              <X size={16} />
            </button>
          )}
        </div>

        <button
          onClick={() => setShowFilters(!showFilters)}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            padding: '12px 16px',
            backgroundColor: showFilters ? '#3b82f6' : '#374151',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: '500',
            transition: 'all 0.2s'
          }}
          onMouseEnter={(e) => {
            if (!showFilters) {
              e.currentTarget.style.backgroundColor = '#4b5563';
            }
          }}
          onMouseLeave={(e) => {
            if (!showFilters) {
              e.currentTarget.style.backgroundColor = '#374151';
            }
          }}
        >
          <Filter size={16} />
          Filters
        </button>
      </div>

      {/* Search Mode Tabs */}
      <div style={{ display: 'flex', gap: '4px', marginBottom: '8px' }}>
        {searchModes.map(mode => (
          <button
            key={mode.id}
            onClick={() => setSearchMode(mode.id)}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
              padding: '6px 12px',
              backgroundColor: searchMode === mode.id ? '#3b82f6' : 'rgba(55, 65, 81, 0.5)',
              color: searchMode === mode.id ? 'white' : '#d1d5db',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '12px',
              fontWeight: '500',
              transition: 'all 0.2s'
            }}
            onMouseEnter={(e) => {
              if (searchMode !== mode.id) {
                e.currentTarget.style.backgroundColor = '#374151';
                e.currentTarget.style.color = 'white';
              }
            }}
            onMouseLeave={(e) => {
              if (searchMode !== mode.id) {
                e.currentTarget.style.backgroundColor = 'rgba(55, 65, 81, 0.5)';
                e.currentTarget.style.color = '#d1d5db';
              }
            }}
            title={mode.description}
          >
            {mode.icon}
            {mode.label}
          </button>
        ))}
      </div>

      {/* Advanced Filters */}
      {showFilters && (
        <div style={{
          backgroundColor: '#1f2937',
          border: '1px solid #374151',
          borderRadius: '12px',
          padding: '16px',
          display: 'grid',
          gridTemplateColumns: 'repeat(2, 1fr)',
          gap: '12px'
        }}>
          <div>
            <label style={{ color: '#d1d5db', fontSize: '12px', fontWeight: '500', marginBottom: '4px', display: 'block' }}>
              Category
            </label>
            <select
              value={filters.category}
              onChange={(e) => handleFilterChange('category', e.target.value)}
              style={{
                width: '100%',
                padding: '6px 8px',
                backgroundColor: '#374151',
                color: 'white',
                border: '1px solid #4b5563',
                borderRadius: '6px',
                fontSize: '12px'
              }}
            >
              {categories.map(cat => (
                <option key={cat} value={cat}>
                  {cat === 'all' ? 'All Categories' : cat.charAt(0).toUpperCase() + cat.slice(1)}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label style={{ color: '#d1d5db', fontSize: '12px', fontWeight: '500', marginBottom: '4px', display: 'block' }}>
              Technology
            </label>
            <select
              value={filters.technology}
              onChange={(e) => handleFilterChange('technology', e.target.value)}
              style={{
                width: '100%',
                padding: '6px 8px',
                backgroundColor: '#374151',
                color: 'white',
                border: '1px solid #4b5563',
                borderRadius: '6px',
                fontSize: '12px'
              }}
            >
              {technologies.map(tech => (
                <option key={tech} value={tech}>
                  {tech === 'all' ? 'All Technologies' : tech}
                </option>
              ))}
            </select>
          </div>
        </div>
      )}

      {/* Search Results */}
      {showSuggestions && (
        <div style={{ 
          position: 'absolute', 
          top: showFilters ? '200px' : '120px',
          left: 0,
          right: 0,
          backgroundColor: '#1f2937', 
          borderRadius: '12px', 
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.5)', 
          border: '1px solid #374151', 
          maxHeight: '400px', 
          overflowY: 'auto', 
          zIndex: 50,
          backdropFilter: 'blur(8px)'
        }}>
          {/* Recent Searches */}
          {query === '' && recentSearches.length > 0 && (
            <div style={{ padding: '12px 16px', borderBottom: '1px solid #374151' }}>
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: '8px', 
                color: '#9ca3af', 
                fontSize: '12px', 
                fontWeight: '600',
                marginBottom: '8px'
              }}>
                <History size={14} />
                RECENT SEARCHES
              </div>
              {recentSearches.slice(0, 5).map((compId, index) => {
                const comp = components.find(c => c.id === compId);
                if (!comp) return null;
                
                return (
                  <button
                    key={comp.id}
                    onClick={() => handleSelectComponent(comp)}
                    style={{
                      width: '100%',
                      padding: '8px 0',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '12px',
                      backgroundColor: activeIndex === index ? '#374151' : 'transparent',
                      border: 'none',
                      cursor: 'pointer',
                      borderRadius: '6px',
                      transition: 'background-color 0.2s'
                    }}
                    onMouseEnter={() => setActiveIndex(index)}
                  >
                    <Clock size={14} style={{ color: '#6b7280' }} />
                    <div style={{ color: '#d1d5db', fontSize: '14px', fontWeight: '500' }}>
                      {comp.name}
                    </div>
                  </button>
                );
              })}
            </div>
          )}

          {/* Search Suggestions */}
          {suggestions.length > 0 && (
            <div>
              {suggestions.map((comp, index) => {
                const actualIndex = query === '' ? index + recentSearches.length : index;
                return (
                  <button
                    key={comp.id}
                    onClick={() => handleSelectComponent(comp)}
                    style={{
                      width: '100%',
                      padding: '12px 16px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '12px',
                      backgroundColor: activeIndex === actualIndex ? '#374151' : 'transparent',
                      border: 'none',
                      cursor: 'pointer',
                      transition: 'background-color 0.2s',
                      textAlign: 'left'
                    }}
                    onMouseEnter={() => setActiveIndex(actualIndex)}
                  >
                    <div style={{ 
                      width: '32px', 
                      height: '32px', 
                      borderRadius: '6px', 
                      backgroundColor: '#374151',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: '#9ca3af'
                    }}>
                      <Code2 size={16} />
                    </div>
                    
                    <div style={{ flex: 1 }}>
                      <div style={{ 
                        color: 'white', 
                        fontWeight: '500',
                        fontSize: '14px',
                        marginBottom: '2px'
                      }}>
                        {getHighlightedText(comp.name, query)}
                      </div>
                      
                      {comp.description && (
                        <div style={{ 
                          color: '#9ca3af', 
                          fontSize: '12px',
                          marginBottom: '4px',
                          lineHeight: '1.3'
                        }}>
                          {getHighlightedText(comp.description.slice(0, 100), query)}
                          {comp.description.length > 100 && '...'}
                        </div>
                      )}
                      
                      {comp.tech && comp.tech.length > 0 && (
                        <div style={{ display: 'flex', gap: '4px', flexWrap: 'wrap' }}>
                          {comp.tech.slice(0, 3).map(tech => (
                            <span
                              key={tech}
                              style={{
                                backgroundColor: getTechBadgeColor(tech),
                                color: tech === 'JavaScript' ? '#000' : 'white',
                                fontSize: '10px',
                                fontWeight: '500',
                                padding: '2px 6px',
                                borderRadius: '4px',
                                textTransform: 'uppercase',
                                letterSpacing: '0.5px'
                              }}
                            >
                              {tech}
                            </span>
                          ))}
                          {comp.tech.length > 3 && (
                            <span style={{ color: '#6b7280', fontSize: '10px' }}>
                              +{comp.tech.length - 3}
                            </span>
                          )}
                        </div>
                      )}
                    </div>

                    <ArrowRight size={16} style={{ color: '#6b7280' }} />
                  </button>
                );
              })}
            </div>
          )}

          {/* No Results */}
          {query && suggestions.length === 0 && (
            <div style={{ 
              padding: '32px 16px',
              textAlign: 'center',
              color: '#6b7280'
            }}>
              <Search size={48} style={{ margin: '0 auto 16px', opacity: 0.5 }} />
              <div style={{ fontSize: '16px', fontWeight: '500', marginBottom: '8px' }}>
                No components found
              </div>
              <div style={{ fontSize: '14px' }}>
                Try adjusting your search or filters
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SearchComponent;
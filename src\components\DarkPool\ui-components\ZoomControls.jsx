import React, { useState, useCallback, useRef, useEffect } from 'react';
import { ZoomIn, ZoomOut, Maximize, Minimize, RotateCcw, Move, Settings, Target, MousePointer } from 'lucide-react';

const ZoomControls = ({ 
  zoomLevel, 
  onZoom, 
  onReset, 
  containerRef, 
  onPan,
  position = { x: 0, y: 0 },
  bounds = { width: 0, height: 0 },
  onPositionChange 
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [dragMode, setDragMode] = useState(false);
  const [isPanning, setIsPanning] = useState(false);
  const [lastPanPoint, setLastPanPoint] = useState({ x: 0, y: 0 });
  const [zoomPreset, setZoomPreset] = useState('custom');
  const panRef = useRef(null);

  const zoomPresets = [
    { id: 'fit', label: 'Fit to Screen', value: 0.8, description: 'Fit entire view' },
    { id: 'actual', label: 'Actual Size', value: 1.0, description: '100% zoom level' },
    { id: 'large', label: 'Large View', value: 1.5, description: '150% zoom level' },
    { id: 'detail', label: 'Detail View', value: 2.0, description: '200% zoom level' },
    { id: 'ultra', label: 'Ultra Detail', value: 3.0, description: '300% zoom level' }
  ];

  const quickZoomLevels = [0.25, 0.5, 0.75, 1.0, 1.25, 1.5, 2.0, 2.5, 3.0];

  useEffect(() => {
    const currentPreset = zoomPresets.find(preset => Math.abs(preset.value - zoomLevel) < 0.05);
    setZoomPreset(currentPreset ? currentPreset.id : 'custom');
  }, [zoomLevel]);

  const handleZoomIn = useCallback(() => {
    const currentIndex = quickZoomLevels.findIndex(level => level >= zoomLevel);
    const nextLevel = currentIndex < quickZoomLevels.length - 1 
      ? quickZoomLevels[currentIndex + 1] 
      : Math.min(zoomLevel + 0.1, 5.0);
    onZoom(nextLevel - zoomLevel);
  }, [zoomLevel, onZoom, quickZoomLevels]);

  const handleZoomOut = useCallback(() => {
    const currentIndex = quickZoomLevels.findIndex(level => level >= zoomLevel);
    const prevLevel = currentIndex > 0 
      ? quickZoomLevels[currentIndex - 1] 
      : Math.max(zoomLevel - 0.1, 0.1);
    onZoom(prevLevel - zoomLevel);
  }, [zoomLevel, onZoom, quickZoomLevels]);

  const handlePresetZoom = (presetId) => {
    const preset = zoomPresets.find(p => p.id === presetId);
    if (preset) {
      onZoom(preset.value - zoomLevel);
      setZoomPreset(presetId);
    }
  };

  const handleFitToScreen = () => {
    if (containerRef?.current && bounds.width && bounds.height) {
      const container = containerRef.current;
      const containerWidth = container.clientWidth;
      const containerHeight = container.clientHeight;
      
      const scaleX = containerWidth / bounds.width;
      const scaleY = containerHeight / bounds.height;
      const optimalZoom = Math.min(scaleX, scaleY) * 0.9;
      
      onZoom(optimalZoom - zoomLevel);
      if (onPositionChange) {
        onPositionChange({ x: 0, y: 0 });
      }
    }
  };

  const handlePanStart = (e) => {
    if (!dragMode) return;
    
    setIsPanning(true);
    const rect = containerRef?.current?.getBoundingClientRect();
    if (rect) {
      setLastPanPoint({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
    }
  };

  const handlePanMove = useCallback((e) => {
    if (!isPanning || !dragMode || !onPositionChange) return;

    const rect = containerRef?.current?.getBoundingClientRect();
    if (rect) {
      const currentPoint = {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      };
      
      const deltaX = currentPoint.x - lastPanPoint.x;
      const deltaY = currentPoint.y - lastPanPoint.y;
      
      onPositionChange({
        x: position.x + deltaX / zoomLevel,
        y: position.y + deltaY / zoomLevel
      });
      
      setLastPanPoint(currentPoint);
    }
  }, [isPanning, dragMode, lastPanPoint, position, zoomLevel, onPositionChange]);

  const handlePanEnd = () => {
    setIsPanning(false);
  };

  useEffect(() => {
    if (dragMode && containerRef?.current) {
      const container = containerRef.current;
      container.addEventListener('mousemove', handlePanMove);
      container.addEventListener('mouseup', handlePanEnd);
      container.addEventListener('mouseleave', handlePanEnd);
      
      return () => {
        container.removeEventListener('mousemove', handlePanMove);
        container.removeEventListener('mouseup', handlePanEnd);
        container.removeEventListener('mouseleave', handlePanEnd);
      };
    }
  }, [dragMode, handlePanMove]);

  const buttonStyle = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '8px',
    color: 'white',
    backgroundColor: 'transparent',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
    transition: 'all 0.2s',
    position: 'relative'
  };

  const activeButtonStyle = {
    ...buttonStyle,
    backgroundColor: '#3b82f6',
    color: 'white'
  };

  const getZoomColor = () => {
    if (zoomLevel < 0.5) return '#ef4444';
    if (zoomLevel < 1.0) return '#f59e0b';
    if (zoomLevel === 1.0) return '#10b981';
    if (zoomLevel <= 2.0) return '#3b82f6';
    return '#8b5cf6';
  };

  return (
    <div style={{ 
      position: 'absolute', 
      left: '16px', 
      top: '96px', 
      zIndex: 20, 
      display: 'flex',
      flexDirection: 'column',
      gap: '8px'
    }}>
      {/* Main Controls */}
      <div style={{
        backgroundColor: 'rgba(17, 24, 39, 0.95)', 
        backdropFilter: 'blur(8px)', 
        borderRadius: '12px', 
        padding: '12px', 
        display: 'flex', 
        flexDirection: 'column', 
        gap: '8px',
        border: '1px solid rgba(55, 65, 81, 0.5)',
        boxShadow: '0 8px 25px rgba(0, 0, 0, 0.3)'
      }}>
        <button
          onClick={handleZoomIn}
          style={buttonStyle}
          onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#1f2937'}
          onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
          title={`Zoom In (${Math.round((quickZoomLevels.find(l => l > zoomLevel) || zoomLevel + 0.1) * 100)}%)`}
          disabled={zoomLevel >= 5.0}
        >
          <ZoomIn size={20} />
        </button>
        
        <div style={{ 
          textAlign: 'center', 
          color: getZoomColor(), 
          fontSize: '13px', 
          fontWeight: '600',
          padding: '6px 8px',
          backgroundColor: 'rgba(17, 24, 39, 0.8)',
          borderRadius: '6px',
          border: `1px solid ${getZoomColor()}20`,
          minWidth: '60px'
        }}>
          {Math.round(zoomLevel * 100)}%
        </div>
        
        <button
          onClick={handleZoomOut}
          style={buttonStyle}
          onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#1f2937'}
          onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
          title={`Zoom Out (${Math.round((quickZoomLevels.find(l => l < zoomLevel) || Math.max(zoomLevel - 0.1, 0.1)) * 100)}%)`}
          disabled={zoomLevel <= 0.1}
        >
          <ZoomOut size={20} />
        </button>

        <div style={{ height: '1px', backgroundColor: '#374151', margin: '4px 0' }} />

        <button
          onClick={handleFitToScreen}
          style={buttonStyle}
          onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#059669'}
          onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
          title="Fit to Screen"
        >
          <Target size={20} />
        </button>

        <button
          onClick={() => setDragMode(!dragMode)}
          style={dragMode ? activeButtonStyle : buttonStyle}
          onMouseEnter={(e) => !dragMode && (e.currentTarget.style.backgroundColor = '#1f2937')}
          onMouseLeave={(e) => !dragMode && (e.currentTarget.style.backgroundColor = 'transparent')}
          title={dragMode ? "Exit Pan Mode" : "Enable Pan Mode"}
        >
          {dragMode ? <MousePointer size={20} /> : <Move size={20} />}
        </button>

        <button
          onClick={onReset}
          style={buttonStyle}
          onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#7c3aed'}
          onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
          title="Reset View (100%, Center)"
        >
          <RotateCcw size={20} />
        </button>

        <div style={{ height: '1px', backgroundColor: '#374151', margin: '4px 0' }} />

        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          style={showAdvanced ? activeButtonStyle : buttonStyle}
          onMouseEnter={(e) => !showAdvanced && (e.currentTarget.style.backgroundColor = '#1f2937')}
          onMouseLeave={(e) => !showAdvanced && (e.currentTarget.style.backgroundColor = 'transparent')}
          title="Zoom Options"
        >
          <Settings size={18} />
        </button>
      </div>

      {/* Advanced Controls Panel */}
      {showAdvanced && (
        <div style={{
          backgroundColor: 'rgba(17, 24, 39, 0.98)',
          backdropFilter: 'blur(12px)',
          borderRadius: '12px',
          padding: '16px',
          border: '1px solid rgba(55, 65, 81, 0.5)',
          boxShadow: '0 12px 35px rgba(0, 0, 0, 0.4)',
          minWidth: '240px',
          maxHeight: '400px',
          overflowY: 'auto'
        }}>
          <h4 style={{ 
            color: '#f9fafb', 
            margin: '0 0 16px 0', 
            fontSize: '15px', 
            fontWeight: '600',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            <Settings size={16} />
            Zoom Controls
          </h4>

          {/* Quick Zoom Presets */}
          <div style={{ marginBottom: '20px' }}>
            <label style={{ 
              display: 'block', 
              color: '#d1d5db', 
              fontSize: '13px', 
              fontWeight: '500',
              marginBottom: '10px' 
            }}>
              Quick Zoom Levels
            </label>
            <div style={{ 
              display: 'grid', 
              gridTemplateColumns: 'repeat(3, 1fr)', 
              gap: '6px' 
            }}>
              {zoomPresets.map(preset => (
                <button
                  key={preset.id}
                  onClick={() => handlePresetZoom(preset.id)}
                  style={{
                    padding: '8px 6px',
                    backgroundColor: zoomPreset === preset.id ? '#3b82f6' : 'rgba(55, 65, 81, 0.5)',
                    color: zoomPreset === preset.id ? 'white' : '#d1d5db',
                    border: zoomPreset === preset.id ? '1px solid #60a5fa' : '1px solid #374151',
                    borderRadius: '6px',
                    fontSize: '11px',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'all 0.2s',
                    textAlign: 'center'
                  }}
                  onMouseEnter={(e) => {
                    if (zoomPreset !== preset.id) {
                      e.currentTarget.style.backgroundColor = '#374151';
                      e.currentTarget.style.borderColor = '#4b5563';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (zoomPreset !== preset.id) {
                      e.currentTarget.style.backgroundColor = 'rgba(55, 65, 81, 0.5)';
                      e.currentTarget.style.borderColor = '#374151';
                    }
                  }}
                  title={preset.description}
                >
                  {Math.round(preset.value * 100)}%
                </button>
              ))}
            </div>
          </div>

          {/* Custom Zoom Levels */}
          <div style={{ marginBottom: '20px' }}>
            <label style={{ 
              display: 'block', 
              color: '#d1d5db', 
              fontSize: '13px', 
              fontWeight: '500',
              marginBottom: '10px' 
            }}>
              Zoom Increments
            </label>
            <div style={{ 
              display: 'flex', 
              flexWrap: 'wrap',
              gap: '4px' 
            }}>
              {quickZoomLevels.map(level => {
                const isActive = Math.abs(level - zoomLevel) < 0.05;
                return (
                  <button
                    key={level}
                    onClick={() => onZoom(level - zoomLevel)}
                    style={{
                      padding: '4px 8px',
                      backgroundColor: isActive ? getZoomColor() : 'rgba(55, 65, 81, 0.3)',
                      color: isActive ? 'white' : '#9ca3af',
                      border: isActive ? `1px solid ${getZoomColor()}` : '1px solid #374151',
                      borderRadius: '4px',
                      fontSize: '10px',
                      fontWeight: isActive ? '600' : '400',
                      cursor: 'pointer',
                      transition: 'all 0.2s'
                    }}
                    onMouseEnter={(e) => {
                      if (!isActive) {
                        e.currentTarget.style.backgroundColor = '#374151';
                        e.currentTarget.style.color = '#d1d5db';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (!isActive) {
                        e.currentTarget.style.backgroundColor = 'rgba(55, 65, 81, 0.3)';
                        e.currentTarget.style.color = '#9ca3af';
                      }
                    }}
                  >
                    {Math.round(level * 100)}%
                  </button>
                );
              })}
            </div>
          </div>

          {/* Pan Controls Info */}
          <div style={{ marginBottom: '16px' }}>
            <label style={{ 
              display: 'block', 
              color: '#d1d5db', 
              fontSize: '13px', 
              fontWeight: '500',
              marginBottom: '8px' 
            }}>
              Navigation
            </label>
            <div style={{
              padding: '8px',
              backgroundColor: 'rgba(59, 130, 246, 0.1)',
              borderRadius: '6px',
              border: '1px solid rgba(59, 130, 246, 0.2)'
            }}>
              <div style={{ fontSize: '11px', color: '#93c5fd', lineHeight: '1.4' }}>
                • Click <Move size={12} style={{display: 'inline', marginLeft: '2px', marginRight: '2px'}} /> to enable pan mode<br/>
                • Use mouse wheel to zoom<br/>
                • Pan: drag while in pan mode<br/>
                • Double-click: reset to center
              </div>
            </div>
          </div>

          {/* Current Status */}
          <div style={{
            padding: '8px',
            backgroundColor: 'rgba(17, 24, 39, 0.8)',
            borderRadius: '6px',
            border: '1px solid #374151'
          }}>
            <div style={{ fontSize: '11px', color: '#6b7280', marginBottom: '4px' }}>
              Current Status:
            </div>
            <div style={{ fontSize: '12px', color: '#d1d5db' }}>
              Zoom: <span style={{ color: getZoomColor(), fontWeight: '600' }}>
                {Math.round(zoomLevel * 100)}%
              </span>
              {position && (
                <>
                  <br/>Position: ({Math.round(position.x)}, {Math.round(position.y)})
                </>
              )}
              {dragMode && (
                <>
                  <br/><span style={{ color: '#3b82f6' }}>Pan Mode Active</span>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ZoomControls;
import React, { useState, useEffect, useRef } from 'react';
import { Search, X } from 'lucide-react';

const SearchComponent = ({ components, onSearch, onSelectComponent }) => {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const searchRef = useRef(null);

  useEffect(() => {
    if (query.length > 0) {
      const filtered = components.filter(comp => {
        const searchLower = query.toLowerCase();
        return (
          comp.name.toLowerCase().includes(searchLower) ||
          comp.tech.some(t => t.toLowerCase().includes(searchLower)) ||
          comp.keywords.some(k => k.includes(searchLower))
        );
      });
      setSuggestions(filtered);
      setShowSuggestions(true);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  }, [query, components]);

  return (
    <div ref={searchRef} className="relative">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
        <input
          type="text"
          placeholder="Search components..."
          value={query}
          onChange={(e) => {
            setQuery(e.target.value);
            onSearch(e.target.value);
          }}
          className="pl-10 pr-4 py-2 w-64 bg-gray-800 text-white rounded-lg border border-gray-700 focus:border-blue-500 focus:outline-none"
        />
        {query && (
          <button
            onClick={() => {
              setQuery('');
              onSearch('');
            }}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
          >
            <X size={16} />
          </button>
        )}
      </div>

      {showSuggestions && suggestions.length > 0 && (
        <div className="absolute top-full mt-2 w-full bg-gray-800 rounded-lg shadow-xl border border-gray-700 max-h-64 overflow-y-auto z-50">
          {suggestions.map((comp) => (
            <button
              key={comp.id}
              onClick={() => {
                setQuery(comp.name);
                setShowSuggestions(false);
                onSelectComponent(comp);
              }}
              className="w-full px-4 py-3 text-left hover:bg-gray-700 transition-colors"
            >
              <div className="text-white font-medium">{comp.name}</div>
              <div className="text-gray-400 text-sm">{comp.tech[0]}</div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default SearchComponent;
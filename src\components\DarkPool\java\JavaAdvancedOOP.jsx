import React, { useState } from 'react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Settings, Code, Layers, GitBranch, Zap, Shield } from 'lucide-react';

const JavaAdvancedOOP = () => {
  const [selectedTopic, setSelectedTopic] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [detailsTab, setDetailsTab] = useState('overview');

  const topics = [
    {
      id: 'generics',
      name: 'Generics',
      icon: <Code size={20} />,
      description: 'Type-safe collections and methods',
      color: '#ef4444'
    },
    {
      id: 'reflection',
      name: 'Reflection API',
      icon: <Layers size={20} />,
      description: 'Runtime class inspection and manipulation',
      color: '#3b82f6'
    },
    {
      id: 'annotations',
      name: 'Annotations',
      icon: <Settings size={20} />,
      description: 'Metadata and compile-time processing',
      color: '#10b981'
    },
    {
      id: 'enum',
      name: 'Enums',
      icon: <GitBranch size={20} />,
      description: 'Type-safe constants and behavior',
      color: '#f59e0b'
    },
    {
      id: 'nested-classes',
      name: 'Nested Classes',
      icon: <Zap size={20} />,
      description: 'Inner, static, local, and anonymous classes',
      color: '#8b5cf6'
    },
    {
      id: 'immutability',
      name: 'Immutable Objects',
      icon: <Shield size={20} />,
      description: 'Thread-safe immutable design patterns',
      color: '#ec4899'
    }
  ];

  const codeExamples = {
    generics: `// Generics in Trading Systems
public class OrderBook<T extends Order> {
    private final List<T> orders = new ArrayList<>();
    private final Comparator<T> comparator;
    
    public OrderBook(Comparator<T> comparator) {
        this.comparator = Objects.requireNonNull(comparator);
    }
    
    public void addOrder(T order) {
        orders.add(order);
        orders.sort(comparator);
    }
    
    public Optional<T> getBestOrder() {
        return orders.isEmpty() ? Optional.empty() : Optional.of(orders.get(0));
    }
    
    public List<T> getOrdersByPrice(double price) {
        return orders.stream()
            .filter(order -> order.getPrice() == price)
            .collect(Collectors.toList());
    }
}

// Generic Trading Strategy Interface
public interface TradingStrategy<T extends Instrument> {
    Decision<T> evaluate(MarketData<T> marketData, Portfolio<T> portfolio);
    double calculateRisk(Position<T> position);
    
    // Generic method with bounded wildcard
    default <U extends T> boolean canTrade(U instrument, double minVolume) {
        return instrument.getDailyVolume() >= minVolume;
    }
}

// Bounded type parameters for different instrument types
public class EquityStrategy implements TradingStrategy<Equity> {
    @Override
    public Decision<Equity> evaluate(MarketData<Equity> data, Portfolio<Equity> portfolio) {
        Equity equity = data.getInstrument();
        double pe = equity.getPeRatio();
        return pe < 15 ? Decision.buy(equity) : Decision.hold(equity);
    }
    
    @Override
    public double calculateRisk(Position<Equity> position) {
        return position.getValue() * position.getInstrument().getBeta();
    }
}

// Generic utility class for trading calculations
public class TradingUtils {
    
    // Generic method with multiple type parameters
    public static <T extends Instrument, R extends Number> 
    Map<T, R> calculateMetrics(List<Position<T>> positions, 
                              Function<Position<T>, R> calculator) {
        return positions.stream()
            .collect(Collectors.toMap(
                Position::getInstrument,
                calculator
            ));
    }
    
    // Wildcard generics for flexibility
    public static double calculateTotalValue(List<? extends Position<?>> positions) {
        return positions.stream()
            .mapToDouble(Position::getValue)
            .sum();
    }
    
    // Generic method with type erasure considerations
    @SuppressWarnings("unchecked")
    public static <T> T[] toArray(List<T> list, Class<T> type) {
        return list.toArray((T[]) Array.newInstance(type, list.size()));
    }
}`,
    reflection: `// Reflection API in Trading Systems
public class TradingSystemInspector {
    
    // Dynamically load and instantiate trading strategies
    @SuppressWarnings("unchecked")
    public <T extends TradingStrategy> T loadStrategy(String className) 
            throws ReflectiveOperationException {
        Class<?> clazz = Class.forName(className);
        
        // Check if it implements TradingStrategy
        if (!TradingStrategy.class.isAssignableFrom(clazz)) {
            throw new IllegalArgumentException("Class must implement TradingStrategy");
        }
        
        Constructor<?> constructor = clazz.getDeclaredConstructor();
        constructor.setAccessible(true);
        return (T) constructor.newInstance();
    }
    
    // Inspect strategy configuration
    public StrategyConfig inspectStrategy(TradingStrategy strategy) {
        Class<?> clazz = strategy.getClass();
        StrategyConfig config = new StrategyConfig();
        
        // Get class-level annotations
        ConfigurableStrategy annotation = clazz.getAnnotation(ConfigurableStrategy.class);
        if (annotation != null) {
            config.setName(annotation.name());
            config.setRiskLevel(annotation.riskLevel());
        }
        
        // Inspect fields
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(Parameter.class)) {
                Parameter param = field.getAnnotation(Parameter.class);
                field.setAccessible(true);
                try {
                    Object value = field.get(strategy);
                    config.addParameter(param.name(), value);
                } catch (IllegalAccessException e) {
                    // Handle access error
                }
            }
        }
        
        return config;
    }
    
    // Dynamic method invocation
    public Object executeMethod(Object target, String methodName, Object... args) 
            throws ReflectiveOperationException {
        Class<?> clazz = target.getClass();
        Class<?>[] paramTypes = Arrays.stream(args)
            .map(Object::getClass)
            .toArray(Class[]::new);
            
        Method method = clazz.getDeclaredMethod(methodName, paramTypes);
        method.setAccessible(true);
        return method.invoke(target, args);
    }
    
    // Generic method finder with parameter type matching
    public Method findMethod(Class<?> clazz, String name, Class<?>... paramTypes) {
        try {
            return clazz.getDeclaredMethod(name, paramTypes);
        } catch (NoSuchMethodException e) {
            // Try with superclass
            Class<?> superclass = clazz.getSuperclass();
            if (superclass != null) {
                return findMethod(superclass, name, paramTypes);
            }
            throw new RuntimeException("Method not found: " + name, e);
        }
    }
}

// Custom annotation for strategy configuration
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface ConfigurableStrategy {
    String name() default "";
    RiskLevel riskLevel() default RiskLevel.MEDIUM;
    String description() default "";
}

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface Parameter {
    String name();
    String description() default "";
    double min() default Double.MIN_VALUE;
    double max() default Double.MAX_VALUE;
}

// Example usage with annotations
@ConfigurableStrategy(name = "Momentum", riskLevel = RiskLevel.HIGH)
public class MomentumStrategy implements TradingStrategy<Equity> {
    
    @Parameter(name = "lookbackPeriod", min = 1, max = 252)
    private int lookbackPeriod = 20;
    
    @Parameter(name = "threshold", min = 0.01, max = 0.5)
    private double threshold = 0.05;
    
    @Override
    public Decision<Equity> evaluate(MarketData<Equity> data, Portfolio<Equity> portfolio) {
        double momentum = calculateMomentum(data, lookbackPeriod);
        return momentum > threshold ? Decision.buy(data.getInstrument()) : Decision.hold(data.getInstrument());
    }
}`,
    annotations: `// Custom Annotations for Trading Systems
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.TYPE})
public @interface Auditable {
    AuditLevel level() default AuditLevel.INFO;
    String value() default "";
    boolean includeParameters() default true;
    boolean includeReturnValue() default false;
}

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface RiskCheck {
    double maxPositionSize() default Double.MAX_VALUE;
    RiskLevel maxRiskLevel() default RiskLevel.HIGH;
    String[] requiredApprovals() default {};
}

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.PARAMETER)
public @interface Valid {
    String message() default "Invalid parameter";
    Class<?>[] groups() default {};
}

// Annotation processor using reflection
public class TradingAnnotationProcessor {
    
    public void processAuditableMethod(Method method, Object[] args, Object result) {
        Auditable auditable = method.getAnnotation(Auditable.class);
        if (auditable != null) {
            AuditEvent event = new AuditEvent();
            event.setMethodName(method.getName());
            event.setLevel(auditable.level());
            event.setDescription(auditable.value());
            
            if (auditable.includeParameters()) {
                event.setParameters(Arrays.toString(args));
            }
            
            if (auditable.includeReturnValue()) {
                event.setReturnValue(result);
            }
            
            auditLogger.log(event);
        }
    }
    
    public void validateRiskCheck(Method method, Object[] args) throws RiskViolationException {
        RiskCheck riskCheck = method.getAnnotation(RiskCheck.class);
        if (riskCheck != null) {
            // Validate position size
            for (Object arg : args) {
                if (arg instanceof Order) {
                    Order order = (Order) arg;
                    double positionSize = order.getValue();
                    if (positionSize > riskCheck.maxPositionSize()) {
                        throw new RiskViolationException(
                            "Position size exceeds limit: " + positionSize);
                    }
                }
            }
            
            // Check approvals
            String[] requiredApprovals = riskCheck.requiredApprovals();
            if (requiredApprovals.length > 0) {
                validateApprovals(requiredApprovals);
            }
        }
    }
}

// AOP-style proxy for annotation processing
public class TradingServiceProxy implements InvocationHandler {
    private final Object target;
    private final TradingAnnotationProcessor processor;
    
    public TradingServiceProxy(Object target) {
        this.target = target;
        this.processor = new TradingAnnotationProcessor();
    }
    
    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        // Pre-processing
        processor.validateRiskCheck(method, args);
        
        try {
            // Execute original method
            Object result = method.invoke(target, args);
            
            // Post-processing
            processor.processAuditableMethod(method, args, result);
            
            return result;
        } catch (InvocationTargetException e) {
            throw e.getCause();
        }
    }
    
    @SuppressWarnings("unchecked")
    public static <T> T createProxy(T target, Class<T> interfaceType) {
        return (T) Proxy.newProxyInstance(
            interfaceType.getClassLoader(),
            new Class<?>[]{interfaceType},
            new TradingServiceProxy(target)
        );
    }
}

// Example service with annotations
public class OrderService {
    
    @Auditable(level = AuditLevel.WARN, value = "High-value order submitted")
    @RiskCheck(maxPositionSize = 1000000.0, requiredApprovals = {"RISK_MANAGER", "TRADER"})
    public OrderResult submitOrder(@Valid Order order) {
        // Method implementation
        return processOrder(order);
    }
    
    @Auditable(includeReturnValue = true)
    public Portfolio getPortfolio(String accountId) {
        return portfolioService.getPortfolio(accountId);
    }
}`,
    enum: `// Advanced Enum Usage in Trading Systems
public enum OrderType {
    MARKET("MKT", 0, true) {
        @Override
        public double calculateExecutionPrice(MarketData data) {
            return data.getCurrentPrice();
        }
        
        @Override
        public boolean canExecute(MarketData data) {
            return data.isMarketOpen();
        }
    },
    
    LIMIT("LMT", 1, false) {
        @Override
        public double calculateExecutionPrice(MarketData data) {
            // Limit price would be set separately
            return 0.0; // Placeholder
        }
        
        @Override
        public boolean canExecute(MarketData data) {
            return data.isMarketOpen() && data.hasLiquidity();
        }
    },
    
    STOP_LOSS("STP", 2, false) {
        @Override
        public double calculateExecutionPrice(MarketData data) {
            return data.getCurrentPrice(); // Execute at market when triggered
        }
        
        @Override
        public boolean canExecute(MarketData data) {
            return data.isMarketOpen(); // Additional stop trigger logic needed
        }
    };
    
    private final String code;
    private final int priority;
    private final boolean immediateExecution;
    
    OrderType(String code, int priority, boolean immediateExecution) {
        this.code = code;
        this.priority = priority;
        this.immediateExecution = immediateExecution;
    }
    
    // Abstract methods that each enum constant must implement
    public abstract double calculateExecutionPrice(MarketData data);
    public abstract boolean canExecute(MarketData data);
    
    // Common methods for all enum constants
    public String getCode() { return code; }
    public int getPriority() { return priority; }
    public boolean isImmediateExecution() { return immediateExecution; }
    
    // Static methods for enum utilities
    public static OrderType fromCode(String code) {
        return Arrays.stream(values())
            .filter(type -> type.code.equals(code))
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("Unknown order type: " + code));
    }
    
    public static List<OrderType> getByPriority() {
        return Arrays.stream(values())
            .sorted(Comparator.comparingInt(OrderType::getPriority))
            .collect(Collectors.toList());
    }
}

// Complex enum with nested interfaces and classes
public enum TradingStrategy {
    MOMENTUM(new MomentumCalculator()),
    MEAN_REVERSION(new MeanReversionCalculator()),
    ARBITRAGE(new ArbitrageCalculator());
    
    private final StrategyCalculator calculator;
    
    TradingStrategy(StrategyCalculator calculator) {
        this.calculator = calculator;
    }
    
    public Decision evaluate(MarketData data, Portfolio portfolio) {
        return calculator.calculate(data, portfolio);
    }
    
    // Nested interface for strategy calculations
    public interface StrategyCalculator {
        Decision calculate(MarketData data, Portfolio portfolio);
    }
    
    // Nested classes for each strategy implementation
    private static class MomentumCalculator implements StrategyCalculator {
        @Override
        public Decision calculate(MarketData data, Portfolio portfolio) {
            double momentum = data.calculateMomentum(20); // 20-day momentum
            return momentum > 0.05 ? Decision.BUY : 
                   momentum < -0.05 ? Decision.SELL : Decision.HOLD;
        }
    }
    
    private static class MeanReversionCalculator implements StrategyCalculator {
        @Override
        public Decision calculate(MarketData data, Portfolio portfolio) {
            double zscore = data.calculateZScore(50); // 50-day mean reversion
            return zscore > 2.0 ? Decision.SELL :
                   zscore < -2.0 ? Decision.BUY : Decision.HOLD;
        }
    }
    
    private static class ArbitrageCalculator implements StrategyCalculator {
        @Override
        public Decision calculate(MarketData data, Portfolio portfolio) {
            double spread = data.calculateArbitrageSpread();
            return spread > 0.01 ? Decision.BUY : Decision.HOLD;
        }
    }
}

// Enum with complex state management
public enum MarketState {
    PRE_MARKET("Pre-Market", false, false),
    OPENING("Opening", true, false),
    REGULAR("Regular Hours", true, true),
    CLOSING("Closing", true, false),
    AFTER_HOURS("After Hours", false, true),
    CLOSED("Closed", false, false);
    
    private final String description;
    private final boolean allowsTrading;
    private final boolean allowsLimitOrders;
    
    MarketState(String description, boolean allowsTrading, boolean allowsLimitOrders) {
        this.description = description;
        this.allowsTrading = allowsTrading;
        this.allowsLimitOrders = allowsLimitOrders;
    }
    
    public boolean canSubmitOrder(OrderType orderType) {
        if (!allowsTrading) return false;
        if (orderType == OrderType.LIMIT && !allowsLimitOrders) return false;
        return true;
    }
    
    public String getDescription() { return description; }
}`,
    'nested-classes': `// Nested Classes in Trading Systems

public class TradingEngine {
    private final OrderProcessor orderProcessor;
    private final RiskManager riskManager;
    
    public TradingEngine() {
        this.orderProcessor = new OrderProcessor();
        this.riskManager = new RiskManager();
    }
    
    // Static Nested Class - Utility class that doesn't need outer instance
    public static class OrderBuilder {
        private String symbol;
        private double price;
        private int quantity;
        private OrderType type = OrderType.MARKET;
        
        public OrderBuilder symbol(String symbol) {
            this.symbol = symbol;
            return this;
        }
        
        public OrderBuilder price(double price) {
            this.price = price;
            return this;
        }
        
        public OrderBuilder quantity(int quantity) {
            this.quantity = quantity;
            return this;
        }
        
        public OrderBuilder type(OrderType type) {
            this.type = type;
            return this;
        }
        
        public Order build() {
            validateOrder();
            return new Order(symbol, price, quantity, type);
        }
        
        private void validateOrder() {
            if (symbol == null || symbol.isEmpty()) {
                throw new IllegalArgumentException("Symbol is required");
            }
            if (quantity <= 0) {
                throw new IllegalArgumentException("Quantity must be positive");
            }
        }
    }
    
    // Non-static Inner Class - Has access to outer class instance
    public class OrderProcessor {
        private final Map<String, Order> processingOrders = new HashMap<>();
        
        public OrderResult processOrder(Order order) {
            // Can access outer class fields and methods
            if (!riskManager.validateOrder(order)) {
                return OrderResult.rejected("Risk check failed");
            }
            
            processingOrders.put(order.getId(), order);
            return executeOrder(order);
        }
        
        public void cancelOrder(String orderId) {
            Order order = processingOrders.remove(orderId);
            if (order != null) {
                // Access outer class method
                notifyOrderCancellation(order);
            }
        }
        
        // Inner class can have its own inner class
        private class OrderTracker {
            private final long startTime = System.currentTimeMillis();
            
            public long getProcessingTime() {
                return System.currentTimeMillis() - startTime;
            }
        }
    }
    
    // Method with Local Inner Class
    public List<Order> filterOrders(List<Order> orders, double minValue) {
        // Local class - only visible within this method
        class OrderFilter {
            private final double threshold;
            
            OrderFilter(double threshold) {
                this.threshold = threshold;
            }
            
            boolean accept(Order order) {
                return order.getValue() >= threshold;
            }
        }
        
        OrderFilter filter = new OrderFilter(minValue);
        return orders.stream()
            .filter(filter::accept)
            .collect(Collectors.toList());
    }
    
    // Method using Anonymous Inner Class
    public void scheduleOrderExecution(Order order, int delaySeconds) {
        Timer timer = new Timer();
        
        // Anonymous inner class extending TimerTask
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                try {
                    orderProcessor.processOrder(order);
                } catch (Exception e) {
                    handleExecutionError(order, e);
                } finally {
                    timer.cancel();
                }
            }
        }, delaySeconds * 1000);
    }
    
    // Method using Lambda instead of anonymous class (Java 8+)
    public void scheduleWithLambda(Runnable task, int delaySeconds) {
        ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
        scheduler.schedule(() -> {
            try {
                task.run();
            } finally {
                scheduler.shutdown();
            }
        }, delaySeconds, TimeUnit.SECONDS);
    }
    
    // Anonymous class implementing interface with multiple methods
    public EventListener createOrderEventListener() {
        return new EventListener() {
            private final AtomicInteger eventCount = new AtomicInteger();
            
            @Override
            public void onOrderSubmitted(Order order) {
                eventCount.incrementAndGet();
                logEvent("Order submitted: " + order.getId());
            }
            
            @Override
            public void onOrderExecuted(Order order, Trade trade) {
                eventCount.incrementAndGet();
                logEvent("Order executed: " + order.getId() + " -> " + trade.getId());
            }
            
            @Override
            public void onOrderRejected(Order order, String reason) {
                eventCount.incrementAndGet();
                logEvent("Order rejected: " + order.getId() + " - " + reason);
            }
            
            public int getEventCount() {
                return eventCount.get();
            }
        };
    }
    
    // Private methods accessible by inner classes
    private void notifyOrderCancellation(Order order) {
        System.out.println("Order cancelled: " + order.getId());
    }
    
    private void handleExecutionError(Order order, Exception e) {
        System.err.println("Execution error for order " + order.getId() + ": " + e.getMessage());
    }
    
    private void logEvent(String message) {
        System.out.println(LocalDateTime.now() + ": " + message);
    }
    
    private OrderResult executeOrder(Order order) {
        // Implementation details
        return OrderResult.success(order.getId());
    }
}

// Usage examples
public class TradingEngineUsage {
    public void demonstrateUsage() {
        TradingEngine engine = new TradingEngine();
        
        // Using static nested class (Builder pattern)
        Order order = TradingEngine.OrderBuilder
            .symbol("AAPL")
            .price(150.0)
            .quantity(100)
            .type(OrderType.LIMIT)
            .build();
        
        // Using non-static inner class
        TradingEngine.OrderProcessor processor = engine.new OrderProcessor();
        OrderResult result = processor.processOrder(order);
        
        // Anonymous class usage
        EventListener listener = engine.createOrderEventListener();
        listener.onOrderSubmitted(order);
    }
}`
  };

  return (
    <div style={{
      padding: '40px',
      paddingTop: '100px',
      minHeight: '100vh',
      backgroundColor: '#0f172a',
      color: 'white'
    }}>
      <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
        <div style={{ marginBottom: '40px' }}>
          <h1 style={{ fontSize: '36px', fontWeight: 'bold', marginBottom: '16px', color: '#10b981' }}>
            Advanced Java OOP Concepts
          </h1>
          <p style={{ fontSize: '18px', color: '#94a3b8' }}>
            Advanced object-oriented programming concepts, generics, reflection, and design patterns
          </p>
        </div>

        <div style={{ display: 'flex', gap: '20px', marginBottom: '40px' }}>
          {['overview', 'definition', 'code', 'best-practices'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              style={{
                padding: '12px 24px',
                backgroundColor: activeTab === tab ? '#10b981' : 'transparent',
                border: `2px solid ${activeTab === tab ? '#10b981' : '#374151'}`,
                borderRadius: '8px',
                color: activeTab === tab ? 'white' : '#9ca3af',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                textTransform: 'capitalize',
                transition: 'all 0.2s'
              }}
            >
              {tab.replace('-', ' ')}
            </button>
          ))}
        </div>

        {activeTab === 'definition' && (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <div style={{ marginBottom: '20px' }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px' }}>
                {selectedTopic === 'generics' ? 'Generics' :
                 selectedTopic === 'reflection' ? 'Reflection API' :
                 selectedTopic === 'annotations' ? 'Annotations' :
                 selectedTopic === 'enum' ? 'Enums' :
                 selectedTopic === 'nested-classes' ? 'Nested Classes' :
                 'Immutable Objects'} Definition
              </h3>
            </div>
            
            {selectedTopic === 'generics' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What are Generics?</h4>
                <p style={{ marginBottom: '16px' }}>
                  Generics provide compile-time type safety by allowing you to parameterize types in classes, interfaces, and methods. They eliminate the need for casting and enable you to create reusable code that works with different types while maintaining type safety.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why are they crucial for Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>Type-safe collections for orders, trades, and positions</li>
                  <li>Generic trading strategies that work with different instruments</li>
                  <li>Eliminate ClassCastException errors in critical trading code</li>
                  <li>Create reusable components for different market data types</li>
                  <li>Improve code readability and maintainability</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to use them effectively?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Use bounded type parameters with extends/super wildcards</li>
                  <li>Apply PECS principle (Producer Extends, Consumer Super)</li>
                  <li>Create generic utility classes for trading calculations</li>
                  <li>Use generic interfaces for pluggable trading components</li>
                </ul>
              </div>
            )}
            
            {selectedTopic === 'reflection' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What is the Reflection API?</h4>
                <p style={{ marginBottom: '16px' }}>
                  The Reflection API allows programs to examine and modify their own structure and behavior at runtime. It provides the ability to inspect classes, interfaces, fields, and methods without knowing their names at compile time.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why is it valuable for Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>Dynamic loading of trading strategies from configuration</li>
                  <li>Runtime inspection of strategy parameters and settings</li>
                  <li>Generic serialization/deserialization of trading objects</li>
                  <li>Building configurable and extensible trading frameworks</li>
                  <li>Creating annotation-based processing for audit trails</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to implement it safely?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Cache reflection objects to improve performance</li>
                  <li>Handle security restrictions properly</li>
                  <li>Use reflection sparingly in performance-critical paths</li>
                  <li>Validate types and handle exceptions gracefully</li>
                </ul>
              </div>
            )}
          </div>
        )}

        {activeTab === 'overview' && (
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>
            {topics.map((topic) => (
              <div
                key={topic.id}
                onClick={() => setSelectedTopic(topic.id)}
                style={{
                  padding: '24px',
                  backgroundColor: selectedTopic === topic.id ? 'rgba(16, 185, 129, 0.1)' : 'rgba(31, 41, 55, 0.5)',
                  border: `2px solid ${selectedTopic === topic.id ? '#10b981' : 'transparent'}`,
                  borderRadius: '12px',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '12px' }}>
                  <div style={{ color: topic.color }}>{topic.icon}</div>
                  <h3 style={{ fontSize: '20px', fontWeight: '600' }}>{topic.name}</h3>
                </div>
                <p style={{ color: '#94a3b8', fontSize: '14px' }}>{topic.description}</p>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'code' && (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <div style={{ marginBottom: '20px' }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px', textTransform: 'capitalize' }}>
                {selectedTopic ? selectedTopic.replace('-', ' ') : 'Advanced OOP'} Example
              </h3>
            </div>
            <SyntaxHighlighter
              language="java"
              style={oneDark}
              customStyle={{
                backgroundColor: '#1e293b',
                padding: '20px',
                borderRadius: '8px',
                fontSize: '14px',
                lineHeight: '1.6'
              }}
            >
              {selectedTopic ? codeExamples[selectedTopic] : '// Select a topic to view code examples'}
            </SyntaxHighlighter>
          </div>
        )}

        {activeTab === 'best-practices' && (
          <div style={{
            padding: '24px',
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            border: '1px solid #374151'
          }}>
            <h3 style={{ fontSize: '24px', fontWeight: '600', marginBottom: '20px', color: '#10b981' }}>
              Advanced OOP Best Practices
            </h3>
            <div style={{ display: 'grid', gap: '16px' }}>
              {[
                {
                  title: 'Use Generics Wisely',
                  description: 'Apply bounded wildcards and PECS principle for flexible yet type-safe APIs'
                },
                {
                  title: 'Minimize Reflection Usage',
                  description: 'Use reflection only when necessary and cache reflected objects for performance'
                },
                {
                  title: 'Design Custom Annotations',
                  description: 'Create domain-specific annotations for configuration and metadata'
                },
                {
                  title: 'Leverage Enum Capabilities',
                  description: 'Use enums for type-safe constants with behavior and state'
                },
                {
                  title: 'Choose Appropriate Nesting',
                  description: 'Use static nested classes for utilities, inner classes for tight coupling'
                }
              ].map((practice, index) => (
                <div key={index} style={{
                  padding: '16px',
                  backgroundColor: '#1e293b',
                  borderRadius: '8px',
                  borderLeft: '4px solid #10b981'
                }}>
                  <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '8px' }}>
                    {practice.title}
                  </h4>
                  <p style={{ color: '#94a3b8', fontSize: '14px' }}>
                    {practice.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      
        {/* Details Panel - Moved to bottom */}
        {selectedTopic && (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            border: '1px solid #374151',
            padding: '24px',
            marginTop: '32px'
          }}>
            <div style={{ marginBottom: '20px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                <h2 style={{ fontSize: '24px', fontWeight: 'bold', color: '#10b981' }}>
                  {topics.find(t => t.id === selectedTopic)?.name}
                </h2>
                <button
                  onClick={() => setSelectedTopic(null)}
                  style={{
                    background: 'none',
                    border: 'none',
                    color: '#9ca3af',
                    fontSize: '24px',
                    cursor: 'pointer'
                  }}
                >
                  ×
                </button>
              </div>
              
              {/* Advanced OOP Tabs */}
              <div style={{ display: 'flex', borderBottom: '1px solid #374151' }}>
                {['overview', 'patterns', 'examples', 'pitfalls'].map(tab => (
                  <button
                    key={tab}
                    onClick={() => setDetailsTab(tab)}
                    style={{
                      padding: '8px 16px',
                      backgroundColor: detailsTab === tab ? '#10b981' : 'transparent',
                      color: detailsTab === tab ? 'white' : '#9ca3af',
                      border: 'none',
                      borderBottom: detailsTab === tab ? '2px solid #10b981' : '2px solid transparent',
                      cursor: 'pointer',
                      fontSize: '14px',
                      textTransform: 'capitalize',
                      transition: 'all 0.2s'
                    }}
                  >
                    {tab}
                  </button>
                ))}
              </div>
            </div>
            
            {detailsTab === 'overview' && (
              <div>
                <div style={{ marginBottom: '24px' }}>
                  <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '12px', color: '#10b981' }}>
                    Advanced OOP for Trading Systems
                  </h3>
                  <p style={{ color: '#e2e8f0', lineHeight: '1.6' }}>
                    {selectedTopic === 'generics' ? (
                      "Generics provide compile-time type safety essential for trading systems handling multiple instrument types, currencies, and data structures. They eliminate ClassCastException risks and enable reusable, type-safe trading components."
                    ) : selectedTopic === 'reflection' ? (
                      "Reflection enables dynamic configuration and extensibility crucial for trading platforms. It allows runtime strategy loading, parameter inspection, and building flexible trading frameworks while maintaining type safety where possible."
                    ) : selectedTopic === 'annotations' ? (
                      "Annotations provide metadata-driven programming essential for trading systems requiring audit trails, risk checks, and configuration management. They enable declarative programming patterns that improve code clarity and reduce boilerplate."
                    ) : selectedTopic === 'enum' ? (
                      "Enums offer more than simple constants - they provide type safety, behavior encapsulation, and state management ideal for trading systems with complex business rules and state machines."
                    ) : selectedTopic === 'nested-classes' ? (
                      "Nested classes provide logical grouping and encapsulation patterns essential for complex trading systems. They offer different levels of access and coupling appropriate for various architectural needs."
                    ) : (
                      "Immutable objects are crucial for thread safety and data integrity in concurrent trading systems. They prevent accidental mutations, simplify reasoning about state, and enable safe sharing across threads."
                    )}
                  </p>
                </div>
                
                <div style={{ marginBottom: '24px' }}>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Trading System Benefits</h4>
                  <ul style={{ color: '#e2e8f0', paddingLeft: '20px' }}>
                    {selectedTopic === 'generics' && [
                      'Type-safe order books for different instrument types',
                      'Generic trading strategies working across asset classes',
                      'Eliminates ClassCastException in critical trading paths',
                      'Reusable components for market data processing',
                      'Better IDE support with auto-completion and refactoring'
                    ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                    
                    {selectedTopic === 'reflection' && [
                      'Dynamic strategy loading from configuration files',
                      'Runtime parameter inspection and validation',
                      'Generic object serialization for persistence',
                      'Flexible plugin architectures for trading components',
                      'Annotation-based processing for cross-cutting concerns'
                    ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                    
                    {selectedTopic === 'annotations' && [
                      'Declarative risk management and validation rules',
                      'Automated audit trail generation',
                      'Configuration-driven behavior modification',
                      'Cleaner separation of concerns',
                      'Reduced boilerplate code for cross-cutting concerns'
                    ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                    
                    {selectedTopic === 'enum' && [
                      'Type-safe order states and transitions',
                      'Complex business rule encapsulation',
                      'Strategy pattern implementation with enums',
                      'Immutable constants with associated behavior',
                      'Better performance than string-based constants'
                    ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                    
                    {selectedTopic === 'nested-classes' && [
                      'Logical grouping of related trading components',
                      'Access control and encapsulation management',
                      'Reduced namespace pollution in complex systems',
                      'Event handling with inner class listeners',
                      'Builder patterns with static nested classes'
                    ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                    
                    {selectedTopic === 'immutability' && [
                      'Thread-safe data sharing in concurrent systems',
                      'Simplified reasoning about state changes',
                      'Prevention of accidental data corruption',
                      'Easier debugging and testing',
                      'Functional programming compatibility'
                    ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                  </ul>
                </div>
                
                <div>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#ef4444' }}>Implementation Example</h4>
                  <div style={{
                    backgroundColor: '#1e293b',
                    padding: '16px',
                    borderRadius: '8px',
                    border: '1px solid #374151'
                  }}>
                    <SyntaxHighlighter
                      language="java"
                      style={oneDark}
                      customStyle={{
                        backgroundColor: 'transparent',
                        padding: 0,
                        margin: 0,
                        fontSize: '12px'
                      }}
                    >
                      {selectedTopic === 'generics' ? `// Generic Trading Components
public class Portfolio<T extends Instrument> {
    private final Map<T, Position<T>> positions = new ConcurrentHashMap<>();
    
    public void addPosition(Position<T> position) {
        positions.put(position.getInstrument(), position);
    }
    
    public Optional<Position<T>> getPosition(T instrument) {
        return Optional.ofNullable(positions.get(instrument));
    }
    
    // Generic method with bounded wildcards
    public double calculateValue(PriceProvider<? super T> priceProvider) {
        return positions.values().stream()
            .mapToDouble(pos -> pos.getQuantity() * 
                priceProvider.getPrice(pos.getInstrument()))
            .sum();
    }
}

// PECS principle example
public class TradingUtils {
    // Producer Extends - reading from collection
    public static double calculateTotalRisk(
            Collection<? extends Position<?>> positions) {
        return positions.stream()
            .mapToDouble(Position::getRisk)
            .sum();
    }
    
    // Consumer Super - writing to collection
    public static void addAllOrders(
            Collection<? super Order> destination,
            Collection<? extends Order> source) {
        destination.addAll(source);
    }
}` : selectedTopic === 'reflection' ? `// Dynamic Strategy Loading
public class StrategyLoader {
    private final Map<String, Class<? extends TradingStrategy>> 
        strategyCache = new ConcurrentHashMap<>();
    
    @SuppressWarnings("unchecked")
    public TradingStrategy loadStrategy(String className, 
                                      Map<String, Object> parameters) 
            throws ReflectiveOperationException {
        
        Class<? extends TradingStrategy> clazz = strategyCache.computeIfAbsent(
            className, this::loadStrategyClass);
        
        TradingStrategy strategy = clazz.getDeclaredConstructor().newInstance();
        configureStrategy(strategy, parameters);
        return strategy;
    }
    
    @SuppressWarnings("unchecked")
    private Class<? extends TradingStrategy> loadStrategyClass(String className) {
        try {
            Class<?> clazz = Class.forName(className);
            if (!TradingStrategy.class.isAssignableFrom(clazz)) {
                throw new IllegalArgumentException("Not a trading strategy: " + className);
            }
            return (Class<? extends TradingStrategy>) clazz;
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("Strategy not found: " + className, e);
        }
    }
    
    private void configureStrategy(TradingStrategy strategy, 
                                 Map<String, Object> parameters) 
            throws ReflectiveOperationException {
        
        Class<?> clazz = strategy.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            if (field.isAnnotationPresent(Configurable.class)) {
                Configurable config = field.getAnnotation(Configurable.class);
                String paramName = config.value().isEmpty() ? 
                    field.getName() : config.value();
                
                if (parameters.containsKey(paramName)) {
                    field.setAccessible(true);
                    field.set(strategy, parameters.get(paramName));
                }
            }
        }
    }
}` : selectedTopic === 'annotations' ? `// Risk Management Annotations
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface RiskCheck {
    double maxValue() default Double.MAX_VALUE;
    String[] requiredApprovals() default {};
    RiskLevel level() default RiskLevel.MEDIUM;
}

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.PARAMETER)
public @interface ValidInstrument {
    String[] allowedExchanges() default {};
    boolean requiresLiquidity() default true;
}

// Annotation-driven order validation
public class OrderService {
    @RiskCheck(maxValue = 1_000_000, level = RiskLevel.HIGH,
               requiredApprovals = {"RISK_MANAGER", "TRADER"})
    public OrderResult submitLargeOrder(
            @ValidInstrument(allowedExchanges = {"NYSE", "NASDAQ"}) 
            Order order) {
        
        // Method implementation
        return processOrder(order);
    }
    
    // AOP-style processing
    @Around("@annotation(riskCheck)")
    public Object processRiskCheck(ProceedingJoinPoint pjp, 
                                 RiskCheck riskCheck) throws Throwable {
        Object[] args = pjp.getArgs();
        validateRisk(args, riskCheck);
        
        try {
            Object result = pjp.proceed();
            auditExecution(pjp.getSignature(), args, result);
            return result;
        } catch (Exception e) {
            handleRiskViolation(pjp.getSignature(), args, e);
            throw e;
        }
    }
}` : selectedTopic === 'enum' ? `// Advanced Enum with State Machine
public enum OrderState {
    NEW("New", false, Set.of(SUBMITTED, CANCELLED)) {
        @Override
        public OrderState process(Order order, OrderEvent event) {
            return event == OrderEvent.SUBMIT ? SUBMITTED : 
                   event == OrderEvent.CANCEL ? CANCELLED : this;
        }
    },
    
    SUBMITTED("Submitted", true, Set.of(FILLED, PARTIALLY_FILLED, CANCELLED)) {
        @Override
        public OrderState process(Order order, OrderEvent event) {
            switch (event) {
                case FILL: return order.isFullyFilled() ? FILLED : PARTIALLY_FILLED;
                case CANCEL: return CANCELLED;
                default: return this;
            }
        }
    },
    
    PARTIALLY_FILLED("Partially Filled", true, Set.of(FILLED, CANCELLED)) {
        @Override
        public OrderState process(Order order, OrderEvent event) {
            return event == OrderEvent.FILL && order.isFullyFilled() ? FILLED :
                   event == OrderEvent.CANCEL ? CANCELLED : this;
        }
    },
    
    FILLED("Filled", false, Set.of()),
    CANCELLED("Cancelled", false, Set.of());
    
    private final String description;
    private final boolean canTransition;
    private final Set<OrderState> validTransitions;
    
    OrderState(String description, boolean canTransition, 
              Set<OrderState> validTransitions) {
        this.description = description;
        this.canTransition = canTransition;
        this.validTransitions = validTransitions;
    }
    
    public abstract OrderState process(Order order, OrderEvent event);
    
    public boolean canTransitionTo(OrderState newState) {
        return validTransitions.contains(newState);
    }
}` : selectedTopic === 'nested-classes' ? `// Comprehensive Nested Classes Example
public class TradingSystem {
    private final OrderManager orderManager = new OrderManager();
    private final EventBus eventBus = new EventBus();
    
    // Static nested class - utility builder
    public static class OrderBuilder {
        private String symbol;
        private double price;
        private int quantity;
        
        public OrderBuilder symbol(String symbol) {
            this.symbol = symbol;
            return this;
        }
        
        public Order build() {
            return new Order(symbol, price, quantity);
        }
    }
    
    // Non-static inner class with access to outer instance
    public class OrderManager {
        private final Map<String, Order> activeOrders = new HashMap<>();
        
        public void submitOrder(Order order) {
            activeOrders.put(order.getId(), order);
            // Access outer class field
            eventBus.publish(new OrderSubmittedEvent(order));
        }
        
        // Local class within method
        public List<Order> filterOrders(Predicate<Order> filter) {
            class OrderFilter {
                List<Order> filter(Collection<Order> orders) {
                    return orders.stream()
                        .filter(filter)
                        .collect(Collectors.toList());
                }
            }
            
            return new OrderFilter().filter(activeOrders.values());
        }
    }
    
    // Anonymous class for event handling
    public void setupEventHandlers() {
        eventBus.subscribe(new EventHandler() {
            @Override
            public void handle(Event event) {
                if (event instanceof OrderSubmittedEvent) {
                    OrderSubmittedEvent orderEvent = (OrderSubmittedEvent) event;
                    logOrderSubmission(orderEvent.getOrder());
                }
            }
        });
    }
}` : `// Immutable Trading Objects
public final class ImmutableTrade {
    private final String id;
    private final String orderId;
    private final String symbol;
    private final double price;
    private final int quantity;
    private final Instant timestamp;
    private final Map<String, String> metadata;
    
    private ImmutableTrade(Builder builder) {
        this.id = Objects.requireNonNull(builder.id);
        this.orderId = Objects.requireNonNull(builder.orderId);
        this.symbol = Objects.requireNonNull(builder.symbol);
        this.price = builder.price;
        this.quantity = builder.quantity;
        this.timestamp = Objects.requireNonNull(builder.timestamp);
        // Defensive copy for mutable fields
        this.metadata = Map.copyOf(builder.metadata);
    }
    
    // All getters, no setters
    public String getId() { return id; }
    public String getOrderId() { return orderId; }
    public double getPrice() { return price; }
    
    // Return defensive copy of mutable field
    public Map<String, String> getMetadata() {
        return Map.copyOf(metadata);
    }
    
    // Builder pattern for construction
    public static class Builder {
        private String id;
        private String orderId;
        private String symbol;
        private double price;
        private int quantity;
        private Instant timestamp = Instant.now();
        private Map<String, String> metadata = new HashMap<>();
        
        public Builder id(String id) {
            this.id = id;
            return this;
        }
        
        public ImmutableTrade build() {
            return new ImmutableTrade(this);
        }
    }
}`}
                    </SyntaxHighlighter>
                  </div>
                </div>
              </div>
            )}
            
            {detailsTab === 'patterns' && (
              <div>
                <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px', color: '#10b981' }}>Design Patterns</h3>
                
                <div style={{ marginBottom: '24px' }}>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Generic Patterns</h4>
                  <div style={{ display: 'grid', gap: '12px' }}>
                    <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', borderLeft: '3px solid #10b981' }}>
                      <p style={{ color: '#10b981', fontSize: '14px', fontWeight: 'bold' }}>Repository Pattern</p>
                      <p style={{ color: '#e2e8f0', fontSize: '12px' }}>Generic data access with Repository&lt;T&gt; for different entity types</p>
                    </div>
                    <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', borderLeft: '3px solid #3b82f6' }}>
                      <p style={{ color: '#3b82f6', fontSize: '14px', fontWeight: 'bold' }}>Strategy Pattern</p>
                      <p style={{ color: '#e2e8f0', fontSize: '12px' }}>TradingStrategy&lt;T extends Instrument&gt; for type-safe algorithms</p>
                    </div>
                    <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', borderLeft: '3px solid #f59e0b' }}>
                      <p style={{ color: '#f59e0b', fontSize: '14px', fontWeight: 'bold' }}>Builder Pattern</p>
                      <p style={{ color: '#e2e8f0', fontSize: '12px' }}>Generic builders with fluent interfaces and type safety</p>
                    </div>
                  </div>
                </div>
                
                <div style={{ marginBottom: '24px' }}>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Annotation-Driven Patterns</h4>
                  <ul style={{ color: '#e2e8f0', paddingLeft: '20px' }}>
                    <li style={{ marginBottom: '8px' }}>Aspect-Oriented Programming with custom annotations</li>
                    <li style={{ marginBottom: '8px' }}>Configuration-based dependency injection</li>
                    <li style={{ marginBottom: '8px' }}>Validation framework using parameter annotations</li>
                    <li style={{ marginBottom: '8px' }}>Audit trail generation with method annotations</li>
                  </ul>
                </div>
                
                <div>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Enum Patterns</h4>
                  <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px' }}>
                    <div style={{ marginBottom: '8px' }}>
                      <span style={{ color: '#10b981', fontSize: '14px' }}>State Machine:</span>
                      <p style={{ color: '#e2e8f0', fontSize: '12px', marginTop: '4px' }}>
                        Order states with transition validation and behavior
                      </p>
                    </div>
                    <div style={{ marginBottom: '8px' }}>
                      <span style={{ color: '#10b981', fontSize: '14px' }}>Strategy Enum:</span>
                      <p style={{ color: '#e2e8f0', fontSize: '12px', marginTop: '4px' }}>
                        Trading strategies implemented as enum constants with behavior
                      </p>
                    </div>
                    <div>
                      <span style={{ color: '#10b981', fontSize: '14px' }}>Command Pattern:</span>
                      <p style={{ color: '#e2e8f0', fontSize: '12px', marginTop: '4px' }}>
                        Enum-based commands with execute() method implementations
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            {detailsTab === 'examples' && (
              <div>
                <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px', color: '#10b981' }}>Real-World Examples</h3>
                
                <div style={{ marginBottom: '24px' }}>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Generic Order Book</h4>
                  <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px' }}>
                    <SyntaxHighlighter language="java" style={oneDark} customStyle={{ backgroundColor: 'transparent', padding: 0, margin: 0, fontSize: '12px' }}>
{`public class OrderBook<T extends Order> {
    private final TreeSet<T> bids = new TreeSet<>(ORDER_COMPARATOR);
    private final TreeSet<T> asks = new TreeSet<>(ORDER_COMPARATOR.reversed());
    
    public void addOrder(T order) {
        if (order.getSide() == Side.BUY) {
            bids.add(order);
        } else {
            asks.add(order);
        }
    }
    
    public Optional<Match<T>> findMatch(T order) {
        TreeSet<T> oppositeBook = order.getSide() == Side.BUY ? asks : bids;
        return oppositeBook.stream()
            .filter(o -> canMatch(order, o))
            .findFirst()
            .map(o -> new Match<>(order, o));
    }
}`}
                    </SyntaxHighlighter>
                  </div>
                </div>
                
                <div style={{ marginBottom: '24px' }}>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Reflection-Based Configuration</h4>
                  <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px' }}>
                    <SyntaxHighlighter language="java" style={oneDark} customStyle={{ backgroundColor: 'transparent', padding: 0, margin: 0, fontSize: '12px' }}>
{`@ConfigurableStrategy(name = "MovingAverage")
public class MAStrategy implements TradingStrategy {
    @Parameter(name = "shortPeriod", min = 1, max = 50)
    private int shortPeriod = 10;
    
    @Parameter(name = "longPeriod", min = 10, max = 200)
    private int longPeriod = 50;
    
    @Override
    public Decision evaluate(MarketData data) {
        double shortMA = data.getMovingAverage(shortPeriod);
        double longMA = data.getMovingAverage(longPeriod);
        return shortMA > longMA ? Decision.BUY : Decision.SELL;
    }
}`}
                    </SyntaxHighlighter>
                  </div>
                </div>
                
                <div>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Immutable Position</h4>
                  <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px' }}>
                    <SyntaxHighlighter language="java" style={oneDark} customStyle={{ backgroundColor: 'transparent', padding: 0, margin: 0, fontSize: '12px' }}>
{`public final class Position {
    private final String symbol;
    private final int quantity;
    private final double averagePrice;
    private final Instant lastUpdated;
    
    // Constructor, getters...
    
    public Position updateQuantity(int newQuantity) {
        return new Position(symbol, newQuantity, averagePrice, Instant.now());
    }
    
    public Position addTrade(Trade trade) {
        int newQty = quantity + trade.getQuantity();
        double newAvgPrice = calculateNewAverage(trade);
        return new Position(symbol, newQty, newAvgPrice, Instant.now());
    }
}`}
                    </SyntaxHighlighter>
                  </div>
                </div>
              </div>
            )}
            
            {detailsTab === 'pitfalls' && (
              <div>
                <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px', color: '#10b981' }}>Common Pitfalls</h3>
                
                <div style={{ marginBottom: '24px' }}>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#ef4444' }}>Generics Pitfalls</h4>
                  <div style={{ display: 'grid', gap: '12px' }}>
                    <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', borderLeft: '3px solid #ef4444' }}>
                      <p style={{ color: '#ef4444', fontSize: '14px', fontWeight: 'bold' }}>Type Erasure Issues</p>
                      <p style={{ color: '#e2e8f0', fontSize: '12px' }}>Cannot instantiate generic arrays or check instanceof with parameterized types</p>
                    </div>
                    <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', borderLeft: '3px solid #f59e0b' }}>
                      <p style={{ color: '#f59e0b', fontSize: '14px', fontWeight: 'bold' }}>Wildcard Confusion</p>
                      <p style={{ color: '#e2e8f0', fontSize: '12px' }}>Misusing ? extends vs ? super wildcards leading to compilation errors</p>
                    </div>
                    <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', borderLeft: '3px solid #8b5cf6' }}>
                      <p style={{ color: '#8b5cf6', fontSize: '14px', fontWeight: 'bold' }}>Raw Type Usage</p>
                      <p style={{ color: '#e2e8f0', fontSize: '12px' }}>Using raw types defeats the purpose of generics and causes warnings</p>
                    </div>
                  </div>
                </div>
                
                <div style={{ marginBottom: '24px' }}>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#ef4444' }}>Reflection Pitfalls</h4>
                  <ul style={{ color: '#e2e8f0', paddingLeft: '20px' }}>
                    <li style={{ marginBottom: '8px' }}>Performance overhead in critical trading paths</li>
                    <li style={{ marginBottom: '8px' }}>Security restrictions in production environments</li>
                    <li style={{ marginBottom: '8px' }}>Breaking encapsulation and type safety</li>
                    <li style={{ marginBottom: '8px' }}>Difficult to debug and maintain reflective code</li>
                  </ul>
                </div>
                
                <div>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#ef4444' }}>Immutability Pitfalls</h4>
                  <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px' }}>
                    <div style={{ marginBottom: '8px' }}>
                      <span style={{ color: '#ef4444', fontSize: '14px' }}>Shallow vs Deep Immutability:</span>
                      <p style={{ color: '#e2e8f0', fontSize: '12px', marginTop: '4px' }}>
                        Forgetting to make defensive copies of mutable fields
                      </p>
                    </div>
                    <div style={{ marginBottom: '8px' }}>
                      <span style={{ color: '#ef4444', fontSize: '14px' }}>Performance Impact:</span>
                      <p style={{ color: '#e2e8f0', fontSize: '12px', marginTop: '4px' }}>
                        Excessive object creation without considering memory pressure
                      </p>
                    </div>
                    <div>
                      <span style={{ color: '#ef4444', fontSize: '14px' }}>Builder Complexity:</span>
                      <p style={{ color: '#e2e8f0', fontSize: '12px', marginTop: '4px' }}>
                        Overengineering builders for simple immutable objects
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
    </div>
  );
};

export default JavaAdvancedOOP;
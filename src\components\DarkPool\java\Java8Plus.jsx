import React, { useState } from 'react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Code, GitBranch, Zap, Package, Box, Layers, Shield, Server } from 'lucide-react';

const Java8Plus = () => {
  const [selectedFeature, setSelectedFeature] = useState(null);
  const [detailsTab, setDetailsTab] = useState('overview');

  // Reset tab when feature changes
  React.useEffect(() => {
    if (selectedFeature) {
      setDetailsTab('overview');
    }
  }, [selectedFeature]);
  
  const features = [
    { id: 'lambdas', name: 'Lambda Expressions', IconComponent: Code, description: 'Functional programming support', color: '#ef4444', version: 'Java 8' },
    { id: 'streams', name: 'Stream API', IconComponent: GitBranch, description: 'Functional data processing', color: '#3b82f6', version: 'Java 8' },
    { id: 'datetime', name: 'Date/Time API', IconComponent: Layers, description: 'Modern date and time handling', color: '#10b981', version: 'Java 8' },
    { id: 'completablefuture', name: 'CompletableFuture', IconComponent: Box, description: 'Asynchronous programming', color: '#f59e0b', version: 'Java 8' },
    { id: 'optional', name: 'Optional', IconComponent: Shield, description: 'Null-safe value handling', color: '#8b5cf6', version: 'Java 8' },
    { id: 'method-references', name: 'Method References', IconComponent: Code, description: 'Cleaner functional code', color: '#14b8a6', version: 'Java 8' },
    { id: 'parallel-streams', name: 'Parallel Streams', IconComponent: Zap, description: 'Performance for large datasets', color: '#a855f7', version: 'Java 8' },
    { id: 'records', name: 'Records', IconComponent: Package, description: 'Immutable data carriers', color: '#ec4899', version: 'Java 14' },
    { id: 'switch-expressions', name: 'Switch Expressions', IconComponent: GitBranch, description: 'Enhanced control flow', color: '#06b6d4', version: 'Java 14' },
    { id: 'text-blocks', name: 'Text Blocks', IconComponent: Box, description: 'Multi-line string literals', color: '#84cc16', version: 'Java 15' },
    { id: 'pattern-matching', name: 'Pattern Matching', IconComponent: Layers, description: 'Type-safe data extraction', color: '#f97316', version: 'Java 17' },
    { id: 'sealed-classes', name: 'Sealed Classes', IconComponent: Shield, description: 'Controlled inheritance', color: '#0ea5e9', version: 'Java 17' },
    { id: 'vector-api', name: 'Vector API', IconComponent: Zap, description: 'SIMD operations', color: '#d946ef', version: 'Java 16+' },
    { id: 'foreign-memory', name: 'Foreign Memory API', IconComponent: Server, description: 'Native memory access', color: '#22c55e', version: 'Java 19+' },
    { id: 'virtual-threads', name: 'Virtual Threads', IconComponent: Server, description: 'Lightweight concurrency', color: '#eab308', version: 'Java 21' }
  ];

  // Define tabs for each feature
  const getTabsForFeature = (featureId) => {
    const baseTab = ['overview'];
    
    switch (featureId) {
      case 'lambdas':
      case 'method-references':
        return ['overview', 'syntax', 'examples', 'functional-interfaces'];
      case 'streams':
      case 'parallel-streams':
        return ['overview', 'syntax', 'examples', 'operations', 'collectors'];
      case 'datetime':
        return ['overview', 'syntax', 'examples', 'zones', 'formatting'];
      case 'completablefuture':
        return ['overview', 'syntax', 'examples', 'chaining', 'error-handling'];
      case 'optional':
        return ['overview', 'syntax', 'examples', 'chaining'];
      case 'records':
        return ['overview', 'syntax', 'examples', 'features'];
      case 'switch-expressions':
      case 'pattern-matching':
        return ['overview', 'syntax', 'examples', 'patterns'];
      case 'text-blocks':
        return ['overview', 'syntax', 'examples', 'use-cases'];
      case 'sealed-classes':
        return ['overview', 'syntax', 'examples', 'hierarchy'];
      case 'vector-api':
      case 'foreign-memory':
        return ['overview', 'syntax', 'examples', 'performance'];
      case 'virtual-threads':
        return ['overview', 'syntax', 'examples', 'comparison'];
      default:
        return [...baseTab, 'syntax', 'examples'];
    }
  };

  // Feature definitions and overviews
  const featureOverviews = {
    lambdas: {
      definition: "Lambda expressions provide a concise way to represent anonymous functions in Java. They enable functional programming paradigms and make code more readable and maintainable.",
      keyPoints: [
        "Anonymous functions that can be passed as arguments",
        "Enable functional programming in Java",
        "Reduce boilerplate code compared to anonymous classes",
        "Support method references for even cleaner syntax"
      ],
      benefits: "Cleaner code, better performance, and functional programming support"
    },
    streams: {
      definition: "The Stream API provides a functional approach to processing collections of data. It supports operations like filtering, mapping, and reducing in a declarative style.",
      keyPoints: [
        "Functional-style operations on collections",
        "Lazy evaluation for better performance",
        "Built-in support for parallel processing",
        "Chainable operations with intermediate and terminal operations"
      ],
      benefits: "More readable code, better performance with parallel streams, and powerful data processing capabilities"
    },
    datetime: {
      definition: "The java.time API provides a comprehensive, thread-safe, and immutable date-time API that replaces the problematic Date and Calendar classes.",
      keyPoints: [
        "Immutable and thread-safe date-time objects",
        "Clear separation of human time vs machine time",
        "Built-in support for time zones and daylight saving",
        "ISO 8601 standard compliance"
      ],
      benefits: "Thread safety, immutability, better API design, and proper timezone handling"
    },
    completablefuture: {
      definition: "CompletableFuture enables asynchronous programming in Java with a fluent API for composing, combining, and handling asynchronous computations.",
      keyPoints: [
        "Asynchronous computation with callbacks",
        "Composable and chainable operations",
        "Exception handling in async contexts",
        "Integration with thread pools and executors"
      ],
      benefits: "Non-blocking operations, better resource utilization, and scalable async programming"
    },
    optional: {
      definition: "Optional is a container that may or may not contain a value, designed to handle null values safely and eliminate NullPointerExceptions.",
      keyPoints: [
        "Explicit handling of potentially absent values",
        "Eliminates null pointer exceptions",
        "Functional-style operations on optional values",
        "Clear API for presence/absence checks"
      ],
      benefits: "Null safety, clearer APIs, and functional programming support for nullable values"
    },
    'method-references': {
      definition: "Method references provide a shorthand syntax for lambda expressions that call existing methods, making code even more concise and readable.",
      keyPoints: [
        "Shorthand for simple lambda expressions",
        "Four types: static, instance, constructor, and arbitrary object",
        "Improved readability over lambda expressions",
        "Seamless integration with functional interfaces"
      ],
      benefits: "Cleaner syntax, better readability, and reduced code duplication"
    },
    'parallel-streams': {
      definition: "Parallel streams automatically distribute work across multiple CPU cores, providing easy parallelization of stream operations for performance gains.",
      keyPoints: [
        "Automatic parallelization of stream operations",
        "Fork-Join framework under the hood",
        "Simple .parallel() method to enable",
        "Best for CPU-intensive operations on large datasets"
      ],
      benefits: "Improved performance on multi-core systems and easy parallelization"
    },
    records: {
      definition: "Records are a special kind of class designed to hold immutable data. They automatically generate constructors, accessors, equals, hashCode, and toString methods.",
      keyPoints: [
        "Immutable data carriers with minimal boilerplate",
        "Automatic generation of common methods",
        "Compact constructor for validation",
        "Perfect for DTOs and value objects"
      ],
      benefits: "Less boilerplate, immutability by default, and clear intent for data classes"
    },
    'switch-expressions': {
      definition: "Switch expressions extend traditional switch statements to return values and support pattern matching, making control flow more functional and expressive.",
      keyPoints: [
        "Switch as an expression that returns values",
        "Arrow syntax for cleaner code",
        "Exhaustiveness checking by compiler",
        "Support for multiple values per case"
      ],
      benefits: "More functional approach, better compiler checking, and cleaner syntax"
    },
    'text-blocks': {
      definition: "Text blocks provide a way to write multi-line strings with proper formatting, eliminating the need for string concatenation and escape sequences.",
      keyPoints: [
        "Multi-line string literals with proper formatting",
        "Automatic indentation handling",
        "No need for escape sequences in most cases",
        "Perfect for SQL, JSON, HTML, and other structured text"
      ],
      benefits: "Better readability, reduced errors, and easier maintenance of multi-line strings"
    },
    'pattern-matching': {
      definition: "Pattern matching allows you to test and extract values from objects in a single operation, making code more concise and type-safe.",
      keyPoints: [
        "Type testing and casting in one operation",
        "Support for pattern variables",
        "Enhanced switch expressions with patterns",
        "Guarded patterns for additional conditions"
      ],
      benefits: "Safer type checking, reduced boilerplate, and more expressive code"
    },
    'sealed-classes': {
      definition: "Sealed classes restrict which classes can extend them, providing controlled inheritance hierarchies and enabling exhaustive pattern matching.",
      keyPoints: [
        "Controlled inheritance with permits clause",
        "Exhaustive switch expressions without default",
        "Better modeling of restricted type hierarchies",
        "Compile-time guarantees about subclasses"
      ],
      benefits: "Better domain modeling, exhaustive checking, and controlled API evolution"
    },
    'vector-api': {
      definition: "The Vector API provides SIMD (Single Instruction, Multiple Data) operations for high-performance computations on arrays of numeric data.",
      keyPoints: [
        "SIMD operations for parallel data processing",
        "Hardware-specific optimizations",
        "Type-safe vector operations",
        "Incubator feature requiring special JVM flags"
      ],
      benefits: "Significant performance improvements for numerical computations"
    },
    'foreign-memory': {
      definition: "The Foreign Function & Memory API provides safe and efficient access to native memory and functions outside the JVM heap.",
      keyPoints: [
        "Direct memory access outside JVM heap",
        "Safe alternative to sun.misc.Unsafe",
        "Integration with native libraries",
        "Memory sessions for automatic cleanup"
      ],
      benefits: "High performance, safe native memory access, and better native integration"
    },
    'virtual-threads': {
      definition: "Virtual threads are lightweight threads managed by the JVM rather than the OS, enabling massive concurrency with minimal overhead.",
      keyPoints: [
        "Lightweight threads with minimal memory footprint",
        "Managed by JVM, not operating system",
        "Millions of virtual threads possible",
        "Blocking operations don't block carrier threads"
      ],
      benefits: "Massive scalability, simplified concurrent programming, and efficient resource usage"
    }
  };

  const codeExamples = {
    lambdas: `// Lambda expressions for trading strategies
Comparator<Trade> byPrice = (t1, t2) -> 
    Double.compare(t1.getPrice(), t2.getPrice());

// Method reference
trades.sort(Comparator.comparing(Trade::getTimestamp));

// Functional interface implementation
TradingStrategy momentum = (data, portfolio) -> {
    double change = data.getPriceChange();
    if (change > 0.02) return Decision.BUY;
    if (change < -0.02) return Decision.SELL;
    return Decision.HOLD;
};`,
    streams: `// Stream API for trade processing
List<Trade> profitableTrades = trades.stream()
    .filter(trade -> trade.getPnL() > 0)
    .filter(trade -> trade.getVolume() > 1000)
    .sorted(Comparator.comparing(Trade::getPnL).reversed())
    .limit(10)
    .collect(Collectors.toList());

// Calculate VWAP
double vwap = trades.stream()
    .mapToDouble(t -> t.getPrice() * t.getVolume())
    .sum() / trades.stream()
    .mapToDouble(Trade::getVolume)
    .sum();`,
    datetime: `// Date/Time API for trading systems
import java.time.*;
import java.time.zone.*;

// Market hours handling
ZonedDateTime nyOpen = ZonedDateTime.of(
    LocalDate.now(),
    LocalTime.of(9, 30),
    ZoneId.of("America/New_York")
);

// Convert to different markets
ZonedDateTime londonTime = nyOpen.withZoneSameInstant(
    ZoneId.of("Europe/London")
);
ZonedDateTime tokyoTime = nyOpen.withZoneSameInstant(
    ZoneId.of("Asia/Tokyo")
);

// Trading session checks
public boolean isMarketOpen(ZonedDateTime now) {
    LocalTime time = now.toLocalTime();
    return time.isAfter(LocalTime.of(9, 30)) &&
           time.isBefore(LocalTime.of(16, 0));
}

// Calculate settlement date (T+2)
LocalDate settlementDate = LocalDate.now()
    .plus(2, ChronoUnit.DAYS);
    
// Timestamp for audit
Instant executionTime = Instant.now();
long epochMilli = executionTime.toEpochMilli();`,
    completablefuture: `// CompletableFuture for async operations
CompletableFuture<MarketData> marketDataFuture = 
    CompletableFuture.supplyAsync(() -> 
        marketDataService.fetch(symbol)
    );

CompletableFuture<RiskMetrics> riskFuture = 
    CompletableFuture.supplyAsync(() -> 
        riskEngine.calculate(portfolio)
    );

// Combine multiple async operations
CompletableFuture<OrderDecision> decision = 
    marketDataFuture.thenCombine(riskFuture, 
        (marketData, risk) -> {
            if (risk.getValue() < threshold && 
                marketData.getSpread() < maxSpread) {
                return new OrderDecision(Action.EXECUTE);
            }
            return new OrderDecision(Action.REJECT);
        });

// Handle async completion
decision.thenAccept(result -> {
    if (result.getAction() == Action.EXECUTE) {
        orderGateway.submit(order);
    }
}).exceptionally(ex -> {
    log.error("Order processing failed", ex);
    return null;
});`,
    'method-references': `// Method references for cleaner code
// Static method reference
trades.forEach(TradingSystem::processOrder);

// Instance method reference
trades.stream()
    .map(Trade::getSymbol)
    .distinct()
    .forEach(portfolioManager::rebalance);

// Constructor reference
List<Order> orders = trades.stream()
    .map(Order::new)
    .collect(Collectors.toList());

// Reference to instance method of arbitrary object
trades.sort(Comparator.comparing(Trade::getTimestamp));

// Complex example with method references
Map<String, DoubleSummaryStatistics> stats = 
    trades.stream()
        .collect(Collectors.groupingBy(
            Trade::getSymbol,
            Collectors.summarizingDouble(Trade::getPrice)
        ));`,
    'parallel-streams': `// Parallel streams for performance
// Risk calculation across large portfolio
double totalRisk = positions.parallelStream()
    .mapToDouble(this::calculatePositionRisk)
    .sum();

// Parallel processing with custom thread pool
ForkJoinPool customPool = new ForkJoinPool(8);
try {
    List<PricedPosition> priced = customPool.submit(() ->
        positions.parallelStream()
            .map(pos -> enrichWithPrice(pos))
            .filter(pos -> pos.getValue() > minValue)
            .collect(Collectors.toList())
    ).get();
} finally {
    customPool.shutdown();
}

// Performance comparison
long start = System.currentTimeMillis();
// Sequential: ~5000ms for 1M trades
double sequentialVwap = calculateVWAP(trades.stream());

// Parallel: ~1200ms for 1M trades  
double parallelVwap = calculateVWAP(trades.parallelStream());`,
    optional: `// Optional for null-safe operations
Optional<Order> findOrder(String orderId) {
    return Optional.ofNullable(orderCache.get(orderId))
        .filter(order -> order.isActive());
}

// Chaining operations
findOrder(orderId)
    .map(Order::getPosition)
    .filter(pos -> pos.getQuantity() > 0)
    .ifPresentOrElse(
        pos -> executeHedge(pos),
        () -> log.warn("No position to hedge")
    );`,
    records: `// Records for immutable data
public record Trade(
    String id,
    String symbol,
    double price,
    int quantity,
    Instant timestamp
) {
    // Compact constructor for validation
    public Trade {
        if (quantity <= 0) {
            throw new IllegalArgumentException("Invalid quantity");
        }
    }
    
    // Derived property
    public double notional() {
        return price * quantity;
    }
}`,
    'switch-expressions': `// Switch expressions for order routing
String route = switch (order.getType()) {
    case MARKET -> "SMART";
    case LIMIT -> {
        if (order.getPrice() > midPrice * 1.01) {
            yield "PASSIVE";
        } else {
            yield "AGGRESSIVE";  
        }
    }
    case STOP -> "CONDITIONAL";
    case ICEBERG -> "ALGO";
    default -> throw new IllegalArgumentException(
        "Unknown order type: " + order.getType()
    );
};

// Pattern matching with switch (Java 17+)
Object event = getNextEvent();
String response = switch (event) {
    case Trade t when t.getVolume() > 10000 -> 
        processLargeTrade(t);
    case Trade t -> 
        processNormalTrade(t);
    case Quote q when q.isNBBO() -> 
        updateBestQuote(q);
    case Quote q -> 
        processQuote(q);
    case null -> 
        "No event";
    default -> 
        "Unknown event type";
};`,
    'text-blocks': `// Text blocks for SQL and configurations
String query = """
    SELECT t.symbol, 
           t.price,
           t.volume,
           AVG(t.price) OVER (
               PARTITION BY t.symbol 
               ORDER BY t.timestamp 
               ROWS BETWEEN 20 PRECEDING AND CURRENT ROW
           ) as moving_avg_20
    FROM trades t
    WHERE t.timestamp >= ?
      AND t.symbol IN (?, ?, ?)
    ORDER BY t.timestamp DESC
    """;

String config = """
    {
        "connection": {
            "host": "trading-server.example.com",
            "port": 8080,
            "ssl": true
        },
        "limits": {
            "maxOrderSize": 100000,
            "maxDailyVolume": 10000000
        }
    }
    """;`,
    'pattern-matching': `// Pattern matching for cleaner code
// instanceof with pattern variable (Java 16+)
if (event instanceof Trade trade) {
    processTrade(trade.getSymbol(), trade.getPrice());
} else if (event instanceof Quote quote && quote.isValid()) {
    updateQuote(quote);
}

// Pattern matching for switch (Java 17+)
String processMessage(Message msg) {
    return switch (msg) {
        case OrderMessage(var id, var symbol, var qty) -> 
            "Order: " + id + " " + symbol + " x" + qty;
        case CancelMessage(var id, var reason) -> 
            "Cancel: " + id + " - " + reason;
        case StatusMessage(var id, Status.FILLED) ->
            "Filled: " + id;
        case StatusMessage(var id, var status) ->
            "Status: " + id + " = " + status;
        default -> "Unknown message";
    };
}

// Guarded patterns
switch (order) {
    case BuyOrder b when b.getPrice() > askPrice ->
        executeBuy(b);
    case SellOrder s when s.getPrice() < bidPrice ->
        executeSell(s);
    default -> 
        queueOrder(order);
}`,
    'sealed-classes': `// Sealed classes for type-safe hierarchies
public sealed interface OrderType 
    permits MarketOrder, LimitOrder, StopOrder {
    
    String getSymbol();
    int getQuantity();
}

public final class MarketOrder implements OrderType {
    private final String symbol;
    private final int quantity;
    // Implementation...
}

public final class LimitOrder implements OrderType {
    private final String symbol;
    private final int quantity;
    private final double limitPrice;
    // Implementation...
}

public final class StopOrder implements OrderType {
    private final String symbol;
    private final int quantity; 
    private final double stopPrice;
    // Implementation...
}

// Exhaustive switch - compiler ensures all cases covered
double calculateFees(OrderType order) {
    return switch (order) {
        case MarketOrder m -> m.getQuantity() * 0.001;
        case LimitOrder l -> l.getQuantity() * 0.0008;
        case StopOrder s -> s.getQuantity() * 0.0012;
        // No default needed - all cases covered!
    };
}`,
    'vector-api': `// Vector API for SIMD operations (Incubator)
import jdk.incubator.vector.*;

// High-performance VWAP calculation
public double calculateVWAPSimd(float[] prices, float[] volumes) {
    VectorSpecies<Float> SPECIES = FloatVector.SPECIES_256;
    
    float sumPriceVolume = 0;
    float sumVolume = 0;
    int i = 0;
    
    // Process in SIMD chunks
    for (; i < SPECIES.loopBound(prices.length); i += SPECIES.length()) {
        FloatVector vPrices = FloatVector.fromArray(SPECIES, prices, i);
        FloatVector vVolumes = FloatVector.fromArray(SPECIES, volumes, i);
        
        // SIMD multiplication
        FloatVector vPriceVolume = vPrices.mul(vVolumes);
        
        sumPriceVolume += vPriceVolume.reduceLanes(VectorOperators.ADD);
        sumVolume += vVolumes.reduceLanes(VectorOperators.ADD);
    }
    
    // Handle remaining elements
    for (; i < prices.length; i++) {
        sumPriceVolume += prices[i] * volumes[i];
        sumVolume += volumes[i];
    }
    
    return sumPriceVolume / sumVolume;
}`,
    'foreign-memory': `// Foreign Memory API for low-latency ops
import java.lang.foreign.*;

// Direct memory access for market data
public class MarketDataBuffer {
    private final MemorySegment segment;
    private final MemorySession session;
    
    public MarketDataBuffer(long size) {
        this.session = MemorySession.openConfined();
        this.segment = MemorySegment.allocateNative(size, session);
    }
    
    // Zero-copy write
    public void writeTick(long offset, double price, long volume) {
        segment.set(ValueLayout.JAVA_DOUBLE, offset, price);
        segment.set(ValueLayout.JAVA_LONG, offset + 8, volume);
    }
    
    // Zero-copy read
    public double readPrice(long offset) {
        return segment.get(ValueLayout.JAVA_DOUBLE, offset);
    }
    
    // Native function binding
    public static double callNativeRiskCalc(double[] positions) {
        Linker linker = Linker.nativeLinker();
        SymbolLookup lookup = SymbolLookup.loaderLookup();
        
        MethodHandle riskCalc = linker.downcallHandle(
            lookup.find("calculate_risk").orElseThrow(),
            FunctionDescriptor.of(
                ValueLayout.JAVA_DOUBLE,
                ValueLayout.ADDRESS,
                ValueLayout.JAVA_INT
            )
        );
        
        try (var session = MemorySession.openConfined()) {
            MemorySegment array = session.allocateArray(
                ValueLayout.JAVA_DOUBLE, positions
            );
            return (double) riskCalc.invoke(array, positions.length);
        }
    }
}`,
    'virtual-threads': `// Virtual threads for high concurrency
// Handle thousands of concurrent connections
try (var executor = Executors.newVirtualThreadPerTaskExecutor()) {
    for (Order order : orders) {
        executor.submit(() -> {
            validateOrder(order);
            checkRiskLimits(order);
            submitToExchange(order);
        });
    }
}

// Each connection in its own virtual thread
serverSocket.accept(client -> 
    Thread.startVirtualThread(() -> 
        handleTradingClient(client)
    )
);`
  };

  return (
    <div style={{ 
      padding: '40px', 
      paddingTop: '100px', 
      minHeight: '100vh', 
      backgroundColor: '#0f172a', 
      color: 'white' 
    }}>
      <div style={{ maxWidth: '1800px', margin: '0 auto' }}>
        <h1 style={{ 
          fontSize: '32px', 
          fontWeight: 'bold', 
          marginBottom: '32px', 
          color: '#10b981',
          textAlign: 'center' 
        }}>
          Java 8+ Modern Features for Trading Systems
        </h1>
        
        {/* Feature Grid */}
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
          gap: '16px',
          marginBottom: selectedFeature ? '16px' : '32px'
        }}>
          {features.map(feature => {
            const Icon = feature.IconComponent;
            return (
              <div
                key={feature.id}
                onClick={() => setSelectedFeature(feature.id)}
                style={{
                  padding: '20px',
                  backgroundColor: selectedFeature === feature.id ? 'rgba(16, 185, 129, 0.1)' : 'rgba(31, 41, 55, 0.5)',
                  borderRadius: '12px',
                  cursor: 'pointer',
                  border: selectedFeature === feature.id ? '2px solid #10b981' : '1px solid #374151',
                  transition: 'all 0.3s ease'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '8px' }}>
                  <div style={{ color: feature.color }}>
                    <Icon size={20} />
                  </div>
                  <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#e2e8f0' }}>
                    {feature.name}
                  </div>
                </div>
                <div style={{ fontSize: '14px', color: '#94a3b8', marginBottom: '4px' }}>
                  {feature.description}
                </div>
                <div style={{ fontSize: '12px', color: '#10b981' }}>
                  {feature.version}
                </div>
              </div>
            );
          })}
        </div>

        {/* Details Panel - Now at the bottom */}
        {selectedFeature && (
          <div style={{ 
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            border: '1px solid #374151',
            padding: '24px',
            marginTop: '32px'
          }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              marginBottom: '20px'
            }}>
              <h2 style={{ 
                fontSize: '24px', 
                fontWeight: 'bold', 
                color: '#10b981' 
              }}>
                {features.find(f => f.id === selectedFeature)?.name}
              </h2>
              <button 
                onClick={() => setSelectedFeature(null)}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#374151',
                  color: '#e2e8f0',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer'
                }}
              >
                Close
              </button>
            </div>
            
            {/* Tab Navigation */}
            <div style={{ 
              display: 'flex', 
              gap: '8px', 
              marginBottom: '20px',
              borderBottom: '1px solid #374151',
              paddingBottom: '8px'
            }}>
              {getTabsForFeature(selectedFeature).map(tab => (
                <button
                  key={tab}
                  onClick={() => setDetailsTab(tab)}
                  style={{
                    padding: '8px 16px',
                    backgroundColor: detailsTab === tab ? '#10b981' : 'transparent',
                    color: detailsTab === tab ? 'white' : '#94a3b8',
                    border: 'none',
                    borderRadius: '6px',
                    cursor: 'pointer',
                    textTransform: 'capitalize'
                  }}
                >
                  {tab.replace('-', ' ')}
                </button>
              ))}
            </div>
            
            {/* Tab Content */}
            <div style={{ minHeight: '300px' }}>
              {detailsTab === 'overview' && (
                <div>
                  <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px', color: '#f59e0b' }}>
                    Overview & Definition
                  </h3>
                  {featureOverviews[selectedFeature] && (
                    <div>
                      <div style={{ backgroundColor: '#1e293b', padding: '20px', borderRadius: '8px', marginBottom: '20px' }}>
                        <p style={{ color: '#e2e8f0', fontSize: '15px', lineHeight: '1.6', marginBottom: '0' }}>
                          {featureOverviews[selectedFeature].definition}
                        </p>
                      </div>
                      
                      <div style={{ marginBottom: '20px' }}>
                        <h4 style={{ fontSize: '16px', fontWeight: 'bold', color: '#10b981', marginBottom: '12px' }}>
                          Key Points
                        </h4>
                        <div style={{ display: 'grid', gap: '8px' }}>
                          {featureOverviews[selectedFeature].keyPoints.map((point, index) => (
                            <div key={index} style={{ 
                              display: 'flex', 
                              alignItems: 'flex-start', 
                              gap: '12px',
                              padding: '12px',
                              backgroundColor: '#1e293b',
                              borderRadius: '6px',
                              border: '1px solid #334155'
                            }}>
                              <div style={{ 
                                color: '#10b981', 
                                fontSize: '14px',
                                minWidth: '6px'
                              }}>
                                •
                              </div>
                              <span style={{ color: '#cbd5e1', fontSize: '14px' }}>
                                {point}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                      
                      <div style={{ 
                        backgroundColor: '#065f46', 
                        padding: '16px', 
                        borderRadius: '8px',
                        border: '1px solid #10b981'
                      }}>
                        <h4 style={{ fontSize: '14px', fontWeight: 'bold', color: '#10b981', marginBottom: '8px' }}>
                          💡 Primary Benefits
                        </h4>
                        <p style={{ color: '#d1fae5', fontSize: '14px', margin: '0' }}>
                          {featureOverviews[selectedFeature].benefits}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {detailsTab === 'syntax' && (
                <div>
                  <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px', color: '#f59e0b' }}>
                    Syntax & Basic Usage
                  </h3>
                  <div style={{ backgroundColor: '#1e293b', padding: '16px', borderRadius: '8px' }}>
                    <SyntaxHighlighter 
                      language="java" 
                      style={oneDark} 
                      customStyle={{ 
                        backgroundColor: 'transparent', 
                        padding: 0, 
                        margin: 0, 
                        fontSize: '14px' 
                      }}
                    >
                      {codeExamples[selectedFeature] || '// No example available'}
                    </SyntaxHighlighter>
                  </div>
                </div>
              )}
              
              {detailsTab === 'examples' && (
                <div>
                  <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px', color: '#f59e0b' }}>
                    Practical Examples
                  </h3>
                  <p style={{ color: '#94a3b8', marginBottom: '16px' }}>
                    Real-world trading system examples for {features.find(f => f.id === selectedFeature)?.name}
                  </p>
                  {selectedFeature === 'lambdas' && (
                    <div style={{ backgroundColor: '#1e293b', padding: '16px', borderRadius: '8px' }}>
                      <SyntaxHighlighter language="java" style={oneDark} customStyle={{ backgroundColor: 'transparent', padding: 0, margin: 0, fontSize: '13px' }}>
{`// Event handlers with lambdas
marketDataFeed.onPriceUpdate((symbol, price) -> {
    portfolio.updatePrice(symbol, price);
    if (price > stopLoss) {
        executeStopLoss(symbol);
    }
});

// Strategy pattern with lambdas
Map<String, TradingStrategy> strategies = Map.of(
    "MOMENTUM", (data, pos) -> data.getRSI() > 70 ? SELL : HOLD,
    "MEAN_REVERT", (data, pos) -> data.getPrice() < data.getMA20() ? BUY : HOLD
);`}
                      </SyntaxHighlighter>
                    </div>
                  )}
                </div>
              )}
              
              {detailsTab === 'implementations' && (
                <div>
                  <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px', color: '#f59e0b' }}>
                    Implementation Patterns
                  </h3>
                  {selectedFeature === 'streams' && (
                    <div>
                      <h4 style={{ fontSize: '16px', color: '#10b981', marginBottom: '12px' }}>Stream Operations</h4>
                      <div style={{ display: 'grid', gap: '12px' }}>
                        <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px' }}>
                          <p style={{ color: '#f59e0b', fontSize: '14px', marginBottom: '8px' }}>filter()</p>
                          <code style={{ color: '#94a3b8', fontSize: '12px' }}>
                            trades.stream().filter(t {'>'} t.getVolume() {'>'} 1000)
                          </code>
                        </div>
                        <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px' }}>
                          <p style={{ color: '#f59e0b', fontSize: '14px', marginBottom: '8px' }}>map()</p>
                          <code style={{ color: '#94a3b8', fontSize: '12px' }}>
                            trades.stream().map(Trade::getSymbol)
                          </code>
                        </div>
                        <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px' }}>
                          <p style={{ color: '#f59e0b', fontSize: '14px', marginBottom: '8px' }}>flatMap()</p>
                          <code style={{ color: '#94a3b8', fontSize: '12px' }}>
                            portfolios.stream().flatMap(p {'>'} p.getTrades().stream())
                          </code>
                        </div>
                      </div>
                    </div>
                  )}
                  {selectedFeature !== 'streams' && (
                    <p style={{ color: '#94a3b8' }}>
                      Implementation patterns for {features.find(f => f.id === selectedFeature)?.name}
                    </p>
                  )}
                </div>
              )}
              
              {detailsTab === 'best-practices' && (
                <div>
                  <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px', color: '#f59e0b' }}>
                    Best Practices
                  </h3>
                  {selectedFeature === 'lambdas' && (
                    <ul style={{ color: '#e2e8f0', paddingLeft: '20px' }}>
                      <li style={{ marginBottom: '8px' }}>Keep lambdas short and focused (3-5 lines max)</li>
                      <li style={{ marginBottom: '8px' }}>Avoid side effects in lambda expressions</li>
                      <li style={{ marginBottom: '8px' }}>Use method references when possible (Class::method)</li>
                      <li style={{ marginBottom: '8px' }}>Be careful with variable capture and closure scope</li>
                      <li style={{ marginBottom: '8px' }}>Prefer functional interfaces from java.util.function</li>
                    </ul>
                  )}
                  {selectedFeature === 'streams' && (
                    <ul style={{ color: '#e2e8f0', paddingLeft: '20px' }}>
                      <li style={{ marginBottom: '8px' }}>Use parallel() only for CPU-intensive operations with large datasets</li>
                      <li style={{ marginBottom: '8px' }}>Prefer specialized streams (IntStream, LongStream, DoubleStream)</li>
                      <li style={{ marginBottom: '8px' }}>Avoid boxing/unboxing in stream operations</li>
                      <li style={{ marginBottom: '8px' }}>Consider collecting to specialized collections</li>
                      <li style={{ marginBottom: '8px' }}>Be aware of stream operation ordering and short-circuiting</li>
                    </ul>
                  )}
                  {selectedFeature === 'optional' && (
                    <ul style={{ color: '#e2e8f0', paddingLeft: '20px' }}>
                      <li style={{ marginBottom: '8px' }}>Return Optional from methods that may not have a result</li>
                      <li style={{ marginBottom: '8px' }}>Never return null from Optional-returning methods</li>
                      <li style={{ marginBottom: '8px' }}>Avoid Optional fields in classes or as method parameters</li>
                      <li style={{ marginBottom: '8px' }}>Use map(), flatMap(), filter() for Optional chaining</li>
                      <li style={{ marginBottom: '8px' }}>Prefer orElse() or orElseGet() over get()</li>
                    </ul>
                  )}
                  {selectedFeature === 'virtual-threads' && (
                    <ul style={{ color: '#e2e8f0', paddingLeft: '20px' }}>
                      <li style={{ marginBottom: '8px' }}>Perfect for I/O-bound operations (network, database)</li>
                      <li style={{ marginBottom: '8px' }}>Avoid CPU-intensive tasks on virtual threads</li>
                      <li style={{ marginBottom: '8px' }}>Don't use thread pools with virtual threads</li>
                      <li style={{ marginBottom: '8px' }}>Be aware of pinning with synchronized blocks</li>
                      <li style={{ marginBottom: '8px' }}>Use structured concurrency for better control</li>
                    </ul>
                  )}
                  {!['lambdas', 'streams', 'optional', 'virtual-threads'].includes(selectedFeature) && (
                    <ul style={{ color: '#e2e8f0', paddingLeft: '20px' }}>
                      <li style={{ marginBottom: '8px' }}>Follow Java coding standards</li>
                      <li style={{ marginBottom: '8px' }}>Write clean, maintainable code</li>
                      <li style={{ marginBottom: '8px' }}>Use appropriate design patterns</li>
                      <li style={{ marginBottom: '8px' }}>Consider performance implications</li>
                    </ul>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Java8Plus;
import React, { useState } from 'react';
import { Prism as <PERSON>ynta<PERSON><PERSON><PERSON>lighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { GitBranch, Users, Link, Database, ArrowRight, ArrowLeft } from 'lucide-react';

const SQLJoins = () => {
  const [selectedJoin, setSelectedJoin] = useState('inner');
  const [activeTab, setActiveTab] = useState('overview');

  const joinTypes = [
    {
      id: 'inner',
      name: 'INNER JOIN',
      icon: <Link size={20} />,
      description: 'Returns matching records from both tables'
    },
    {
      id: 'left',
      name: 'LEFT JOIN',
      icon: <ArrowLeft size={20} />,
      description: 'Returns all records from left table'
    },
    {
      id: 'right',
      name: 'RIGHT JOIN',
      icon: <ArrowRight size={20} />,
      description: 'Returns all records from right table'
    },
    {
      id: 'full',
      name: 'FULL OUTER JOIN',
      icon: <GitBranch size={20} />,
      description: 'Returns all records from both tables'
    },
    {
      id: 'cross',
      name: 'CROSS JOIN',
      icon: <Database size={20} />,
      description: 'Cartesian product of both tables'
    },
    {
      id: 'self',
      name: 'SELF JOIN',
      icon: <Users size={20} />,
      description: 'Joins a table with itself'
    }
  ];

  const definitions = {
    'inner': `INNER JOIN returns only the records that have matching values in both tables. It's the most common join type in trading systems, used to combine order data with trader information, or trades with instrument details where both records must exist.`,
    'left': `LEFT JOIN returns all records from the left table and matched records from the right table. Unmatched records from the right table appear as NULL. In trading, this is useful for showing all orders with their optional fill details, or all traders with their recent activity.`,
    'right': `RIGHT JOIN returns all records from the right table and matched records from the left table. It's less commonly used than LEFT JOIN but useful when you want to ensure all records from the second table are included in trading reports.`,
    'full': `FULL OUTER JOIN returns all records when there's a match in either table. It combines LEFT and RIGHT joins, showing all data with NULLs where matches don't exist. Useful for comprehensive reporting that needs to show all orders and all trades.`,
    'cross': `CROSS JOIN returns the Cartesian product of both tables - every row from the first table combined with every row from the second table. In trading, it's used for creating all possible combinations, like matching all instruments with all trading strategies.`,
    'self': `SELF JOIN joins a table with itself using table aliases. Common in trading systems for hierarchical data like finding pairs of trades, comparing current prices with previous prices, or analyzing order sequences for the same instrument.`
  };

  const codeExamples = {
    'inner': `-- INNER JOIN examples for trading systems
-- Join orders with trader information
SELECT 
    o.order_id,
    o.symbol,
    o.quantity,
    o.price,
    t.trader_name,
    t.desk,
    t.risk_limit
FROM orders o
INNER JOIN traders t ON o.trader_id = t.trader_id
WHERE o.status = 'NEW';

-- Join trades with instrument details
SELECT 
    tr.trade_id,
    tr.symbol,
    tr.quantity,
    tr.price,
    i.company_name,
    i.sector,
    i.market_cap,
    tr.quantity * tr.price as trade_value
FROM trades tr
INNER JOIN instruments i ON tr.symbol = i.symbol
WHERE tr.trade_date = CURRENT_DATE;

-- Multiple table join: orders, traders, and desks
SELECT 
    o.order_id,
    o.symbol,
    o.side,
    o.quantity,
    t.trader_name,
    d.desk_name,
    d.daily_limit,
    o.quantity * o.price as order_value
FROM orders o
INNER JOIN traders t ON o.trader_id = t.trader_id
INNER JOIN desks d ON t.desk_id = d.desk_id
WHERE d.status = 'ACTIVE'
  AND o.created_at >= CURRENT_DATE;

-- Join with aggregation
SELECT 
    t.trader_name,
    t.desk,
    COUNT(o.order_id) as total_orders,
    SUM(o.quantity * o.price) as total_value
FROM traders t
INNER JOIN orders o ON t.trader_id = o.trader_id
WHERE o.created_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY t.trader_id, t.trader_name, t.desk
HAVING total_value > 100000;`,

    'left': `-- LEFT JOIN examples for trading systems
-- Show all traders with their recent orders (including traders with no orders)
SELECT 
    t.trader_id,
    t.trader_name,
    t.desk,
    COUNT(o.order_id) as order_count,
    COALESCE(SUM(o.quantity * o.price), 0) as total_value
FROM traders t
LEFT JOIN orders o ON t.trader_id = o.trader_id 
  AND o.created_at >= CURRENT_DATE
WHERE t.status = 'ACTIVE'
GROUP BY t.trader_id, t.trader_name, t.desk;

-- All orders with optional execution details
SELECT 
    o.order_id,
    o.symbol,
    o.quantity,
    o.price as order_price,
    o.status,
    e.execution_id,
    e.fill_price,
    e.fill_quantity,
    e.execution_time
FROM orders o
LEFT JOIN executions e ON o.order_id = e.order_id
WHERE o.created_at >= CURRENT_DATE - INTERVAL '1 day'
ORDER BY o.created_at, e.execution_time;

-- Portfolio positions with current market prices
SELECT 
    p.trader_id,
    p.symbol,
    p.quantity,
    p.avg_cost,
    m.current_price,
    p.quantity * COALESCE(m.current_price, p.avg_cost) as market_value,
    (m.current_price - p.avg_cost) / p.avg_cost * 100 as unrealized_pnl_pct
FROM portfolio_positions p
LEFT JOIN market_data m ON p.symbol = m.symbol 
  AND m.timestamp = (
    SELECT MAX(timestamp) 
    FROM market_data 
    WHERE symbol = p.symbol
  )
WHERE p.quantity != 0;

-- Risk limits with current exposure
SELECT 
    r.trader_id,
    r.instrument_type,
    r.max_position,
    r.max_daily_loss,
    COALESCE(SUM(p.market_value), 0) as current_exposure,
    r.max_position - COALESCE(SUM(ABS(p.market_value)), 0) as remaining_limit
FROM risk_limits r
LEFT JOIN portfolio_positions p ON r.trader_id = p.trader_id
GROUP BY r.trader_id, r.instrument_type, r.max_position, r.max_daily_loss;`,

    'right': `-- RIGHT JOIN examples for trading systems
-- Show all market data with any matching orders
SELECT 
    m.symbol,
    m.price,
    m.volume,
    m.timestamp,
    COUNT(o.order_id) as pending_orders,
    AVG(o.price) as avg_order_price
FROM orders o
RIGHT JOIN market_data m ON o.symbol = m.symbol 
  AND o.status IN ('NEW', 'PARTIALLY_FILLED')
WHERE m.timestamp >= CURRENT_DATE
GROUP BY m.symbol, m.price, m.volume, m.timestamp;

-- All instruments with recent trading activity
SELECT 
    i.symbol,
    i.company_name,
    i.sector,
    i.market_cap,
    COUNT(t.trade_id) as trade_count,
    COALESCE(SUM(t.quantity), 0) as total_volume
FROM trades t
RIGHT JOIN instruments i ON t.symbol = i.symbol 
  AND t.trade_date >= CURRENT_DATE - INTERVAL '7 days'
WHERE i.status = 'ACTIVE'
GROUP BY i.symbol, i.company_name, i.sector, i.market_cap;

-- All risk parameters with current violations
SELECT 
    rp.parameter_name,
    rp.max_value,
    rp.warning_threshold,
    rv.current_value,
    rv.violation_time,
    CASE 
      WHEN rv.current_value > rp.max_value THEN 'VIOLATED'
      WHEN rv.current_value > rp.warning_threshold THEN 'WARNING'
      ELSE 'OK'
    END as status
FROM risk_violations rv
RIGHT JOIN risk_parameters rp ON rv.parameter_id = rp.parameter_id
WHERE rp.is_active = TRUE;`,

    'full': `-- FULL OUTER JOIN examples for trading systems
-- Complete view of orders and executions
SELECT 
    COALESCE(o.order_id, e.order_id) as order_id,
    o.symbol as order_symbol,
    o.quantity as order_quantity,
    o.price as order_price,
    o.status,
    e.execution_id,
    e.fill_quantity,
    e.fill_price,
    e.execution_time,
    CASE 
      WHEN o.order_id IS NULL THEN 'EXECUTION_WITHOUT_ORDER'
      WHEN e.execution_id IS NULL THEN 'ORDER_WITHOUT_EXECUTION'
      ELSE 'MATCHED'
    END as match_status
FROM orders o
FULL OUTER JOIN executions e ON o.order_id = e.order_id
WHERE o.created_at >= CURRENT_DATE - INTERVAL '1 day'
   OR e.execution_time >= CURRENT_DATE - INTERVAL '1 day';

-- Complete reconciliation of expected vs actual trades
SELECT 
    COALESCE(et.symbol, at.symbol) as symbol,
    COALESCE(et.trade_date, at.trade_date) as trade_date,
    et.expected_quantity,
    et.expected_price,
    at.actual_quantity,
    at.actual_price,
    ABS(COALESCE(et.expected_quantity, 0) - COALESCE(at.actual_quantity, 0)) as quantity_diff,
    ABS(COALESCE(et.expected_price, 0) - COALESCE(at.actual_price, 0)) as price_diff,
    CASE 
      WHEN et.symbol IS NULL THEN 'UNEXPECTED_TRADE'
      WHEN at.symbol IS NULL THEN 'MISSING_TRADE'
      ELSE 'MATCHED'
    END as reconciliation_status
FROM expected_trades et
FULL OUTER JOIN actual_trades at ON et.symbol = at.symbol 
  AND et.trade_date = at.trade_date;`,

    'cross': `-- CROSS JOIN examples for trading systems
-- Generate all possible instrument-strategy combinations
SELECT 
    i.symbol,
    i.company_name,
    i.sector,
    s.strategy_name,
    s.risk_level,
    s.max_position_pct,
    CASE 
      WHEN i.volatility < 0.2 AND s.risk_level = 'LOW' THEN 'SUITABLE'
      WHEN i.volatility < 0.5 AND s.risk_level = 'MEDIUM' THEN 'SUITABLE'
      WHEN s.risk_level = 'HIGH' THEN 'SUITABLE'
      ELSE 'NOT_SUITABLE'
    END as suitability
FROM instruments i
CROSS JOIN trading_strategies s
WHERE i.status = 'ACTIVE'
  AND s.is_enabled = TRUE;

-- Create all possible trading pairs for arbitrage
SELECT 
    i1.symbol as symbol_1,
    i2.symbol as symbol_2,
    i1.sector as sector_1,
    i2.sector as sector_2,
    CONCAT(i1.symbol, '/', i2.symbol) as pair_name
FROM instruments i1
CROSS JOIN instruments i2
WHERE i1.symbol < i2.symbol  -- Avoid duplicates
  AND i1.sector = i2.sector  -- Same sector pairs
  AND i1.status = 'ACTIVE'
  AND i2.status = 'ACTIVE';

-- Generate time-based analysis grid
SELECT 
    d.date_value,
    h.hour_value,
    CONCAT(d.date_value, ' ', LPAD(h.hour_value, 2, '0'), ':00:00') as datetime_slot
FROM (
    SELECT DATE_ADD(CURRENT_DATE - INTERVAL 7 DAY, INTERVAL n DAY) as date_value
    FROM (SELECT 0 n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6) days
) d
CROSS JOIN (
    SELECT hour_value FROM (
        SELECT 9 hour_value UNION SELECT 10 UNION SELECT 11 UNION SELECT 12 
        UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION SELECT 16
    ) market_hours
) h;`,

    'self': `-- SELF JOIN examples for trading systems
-- Find price movements by comparing current with previous
SELECT 
    current.symbol,
    current.price as current_price,
    previous.price as previous_price,
    (current.price - previous.price) / previous.price * 100 as price_change_pct,
    current.timestamp as current_time,
    previous.timestamp as previous_time
FROM market_data current
JOIN market_data previous ON current.symbol = previous.symbol
WHERE current.timestamp = (
    SELECT MAX(timestamp) FROM market_data WHERE symbol = current.symbol
)
AND previous.timestamp = (
    SELECT MAX(timestamp) 
    FROM market_data 
    WHERE symbol = current.symbol 
      AND timestamp < current.timestamp
);

-- Find related orders from the same trader
SELECT 
    o1.order_id as main_order,
    o1.symbol as main_symbol,
    o1.side as main_side,
    o1.quantity as main_quantity,
    o2.order_id as related_order,
    o2.symbol as related_symbol,
    o2.side as related_side,
    o2.quantity as related_quantity,
    ABS(TIMESTAMPDIFF(SECOND, o1.created_at, o2.created_at)) as time_diff_seconds
FROM orders o1
JOIN orders o2 ON o1.trader_id = o2.trader_id
WHERE o1.order_id != o2.order_id
  AND o1.side != o2.side  -- Opposite sides (hedging)
  AND ABS(TIMESTAMPDIFF(SECOND, o1.created_at, o2.created_at)) <= 60
  AND o1.created_at >= CURRENT_DATE;

-- Hierarchical trader relationships (manager-subordinate)
SELECT 
    manager.trader_id as manager_id,
    manager.trader_name as manager_name,
    subordinate.trader_id as subordinate_id,
    subordinate.trader_name as subordinate_name,
    subordinate.desk,
    subordinate.risk_limit
FROM traders manager
JOIN traders subordinate ON manager.trader_id = subordinate.reports_to
WHERE manager.is_manager = TRUE
  AND subordinate.status = 'ACTIVE';

-- Compare trader performance with peers in same desk
SELECT 
    t1.trader_id,
    t1.trader_name,
    t1.desk,
    t1.ytd_pnl,
    AVG(t2.ytd_pnl) as desk_avg_pnl,
    t1.ytd_pnl - AVG(t2.ytd_pnl) as vs_desk_avg,
    COUNT(t2.trader_id) as desk_size
FROM traders t1
JOIN traders t2 ON t1.desk = t2.desk AND t1.trader_id != t2.trader_id
WHERE t1.status = 'ACTIVE' AND t2.status = 'ACTIVE'
GROUP BY t1.trader_id, t1.trader_name, t1.desk, t1.ytd_pnl;`
  };

  return (
    <div style={{
      padding: '40px',
      fontFamily: 'Inter, system-ui, sans-serif',
      backgroundColor: '#0f172a',
      color: 'white',
      minHeight: '100vh'
    }}>
      <div style={{ marginBottom: '40px' }}>
        <h1 style={{ 
          fontSize: '36px', 
          fontWeight: '700', 
          marginBottom: '16px',
          background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent'
        }}>
          SQL Joins & Relationships
        </h1>
        <p style={{ fontSize: '18px', color: '#94a3b8', lineHeight: '1.6' }}>
          Master table relationships and JOIN operations for complex trading system queries
        </p>
      </div>

      <div style={{ display: 'flex', gap: '40px' }}>
        <div style={{ flex: '1', maxWidth: '300px' }}>
          <h2 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '24px', color: '#10b981' }}>
            Join Types
          </h2>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {joinTypes.map((join) => (
              <button
                key={join.id}
                onClick={() => setSelectedJoin(join.id)}
                style={{
                  padding: '16px',
                  backgroundColor: selectedJoin === join.id ? 'rgba(16, 185, 129, 0.2)' : 'rgba(30, 41, 59, 0.5)',
                  border: `2px solid ${selectedJoin === join.id ? '#10b981' : 'transparent'}`,
                  borderRadius: '12px',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  textAlign: 'left',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px'
                }}
              >
                <div style={{ color: selectedJoin === join.id ? '#10b981' : '#64748b' }}>
                  {join.icon}
                </div>
                <div>
                  <div style={{ 
                    fontWeight: '600', 
                    color: selectedJoin === join.id ? '#10b981' : 'white',
                    marginBottom: '4px'
                  }}>
                    {join.name}
                  </div>
                  <div style={{ fontSize: '14px', color: '#94a3b8' }}>
                    {join.description}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        <div style={{ flex: '2' }}>
          <div style={{ marginBottom: '24px' }}>
            <div style={{ display: 'flex', gap: '8px', marginBottom: '24px' }}>
              {['overview', 'definition', 'code'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  style={{
                    padding: '12px 24px',
                    backgroundColor: activeTab === tab ? '#10b981' : 'transparent',
                    border: `2px solid ${activeTab === tab ? '#10b981' : '#374151'}`,
                    borderRadius: '8px',
                    color: activeTab === tab ? 'white' : '#94a3b8',
                    cursor: 'pointer',
                    fontWeight: '600',
                    textTransform: 'capitalize',
                    transition: 'all 0.2s'
                  }}
                >
                  {tab}
                </button>
              ))}
            </div>
          </div>

          {activeTab === 'overview' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <h2 style={{ fontSize: '28px', fontWeight: '600', marginBottom: '24px', color: '#10b981' }}>
                SQL Joins Overview
              </h2>
              <div style={{ fontSize: '16px', lineHeight: '1.8', color: '#e2e8f0' }}>
                <p style={{ marginBottom: '20px' }}>
                  SQL JOIN operations are essential for combining data from multiple tables in trading systems. 
                  They enable complex queries that relate orders to traders, trades to instruments, and 
                  positions to market data.
                </p>
                <div style={{ marginBottom: '24px' }}>
                  <h3 style={{ color: '#10b981', fontSize: '20px', marginBottom: '16px' }}>Common Trading Scenarios:</h3>
                  <ul style={{ marginLeft: '24px', lineHeight: '1.8' }}>
                    <li><strong>Order Management:</strong> Join orders with trader and instrument data</li>
                    <li><strong>Trade Reporting:</strong> Combine execution data with order details</li>
                    <li><strong>Risk Analysis:</strong> Link positions with current market prices</li>
                    <li><strong>Performance Analytics:</strong> Connect trader data with P&L calculations</li>
                  </ul>
                </div>
                <div style={{ 
                  backgroundColor: 'rgba(16, 185, 129, 0.1)', 
                  padding: '20px', 
                  borderRadius: '8px',
                  border: '1px solid #10b981'
                }}>
                  <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Join Selection Guidelines:</h4>
                  <p>
                    Choose INNER JOIN for required relationships, LEFT JOIN for optional data, 
                    and SELF JOIN for hierarchical or comparative analysis within the same table.
                  </p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'definition' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <h2 style={{ fontSize: '24px', fontWeight: '600', marginBottom: '20px', color: '#10b981' }}>
                {joinTypes.find(j => j.id === selectedJoin)?.name} Definition
              </h2>
              <p style={{ fontSize: '16px', lineHeight: '1.8', color: '#e2e8f0' }}>
                {definitions[selectedJoin]}
              </p>
            </div>
          )}

          {activeTab === 'code' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <div style={{ marginBottom: '20px' }}>
                <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px' }}>
                  {joinTypes.find(j => j.id === selectedJoin)?.name} Examples
                </h3>
              </div>
              <SyntaxHighlighter
                language="sql"
                style={oneDark}
                customStyle={{
                  backgroundColor: '#1e293b',
                  padding: '20px',
                  borderRadius: '8px',
                  fontSize: '14px',
                  lineHeight: '1.6'
                }}
              >
                {codeExamples[selectedJoin]}
              </SyntaxHighlighter>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SQLJoins;
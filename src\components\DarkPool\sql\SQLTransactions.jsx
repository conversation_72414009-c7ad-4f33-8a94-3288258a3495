import React, { useState } from 'react';
import { Prism as <PERSON>ynta<PERSON><PERSON><PERSON>lighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Lock, Shield, Database, AlertTriangle, Zap, Settings } from 'lucide-react';

const SQLTransactions = () => {
  const [selectedTopic, setSelectedTopic] = useState('acid-properties');
  const [activeTab, setActiveTab] = useState('overview');

  const topics = [
    {
      id: 'acid-properties',
      name: 'ACID Properties',
      icon: <Shield size={20} />,
      description: 'Atomicity, Consistency, Isolation, Durability'
    },
    {
      id: 'isolation-levels',
      name: 'Isolation Levels',
      icon: <Lock size={20} />,
      description: 'Transaction isolation and concurrency'
    },
    {
      id: 'locking',
      name: 'Locking Mechanisms',
      icon: <Database size={20} />,
      description: 'Row, table, and deadlock handling'
    },
    {
      id: 'deadlocks',
      name: 'Deadlock Prevention',
      icon: <AlertTriangle size={20} />,
      description: 'Avoiding and resolving deadlocks'
    },
    {
      id: 'savepoints',
      name: 'Savepoints & Rollback',
      icon: <Zap size={20} />,
      description: 'Partial transaction rollbacks'
    },
    {
      id: 'distributed',
      name: 'Distributed Transactions',
      icon: <Settings size={20} />,
      description: 'Two-phase commit and XA transactions'
    }
  ];

  const definitions = {
    'acid-properties': `ACID properties ensure database reliability through Atomicity (all-or-nothing transactions), Consistency (valid state transitions), Isolation (concurrent transaction separation), and Durability (permanent committed changes). In trading systems, ACID compliance is crucial for maintaining financial data integrity.`,
    'isolation-levels': `Isolation levels control how transactions interact with each other, balancing consistency with performance. Trading systems must carefully choose isolation levels to prevent phantom reads in position calculations while maintaining high throughput for order processing.`,
    'locking': `Database locking prevents concurrent transactions from corrupting data. Trading systems use various locking strategies to ensure order integrity while minimizing contention that could delay time-sensitive trading operations.`,
    'deadlocks': `Deadlocks occur when transactions wait for each other indefinitely. In trading systems, deadlock prevention is critical as blocked transactions can cause order delays, missed trading opportunities, and system instability during high-volume periods.`,
    'savepoints': `Savepoints allow partial rollbacks within transactions, enabling complex operations to recover from errors without losing all work. Trading systems use savepoints for batch order processing and multi-step settlement operations.`,
    'distributed': `Distributed transactions coordinate changes across multiple databases or systems. Trading systems often require distributed transactions for cross-exchange operations, settlement processes, and maintaining consistency across trading venues.`
  };

  const codeExamples = {
    'acid-properties': `-- ACID Properties in Trading Systems
-- ATOMICITY: All-or-nothing trade execution
START TRANSACTION;
    -- Either all steps succeed or all fail
    INSERT INTO trades (trade_id, order_id, symbol, quantity, price, trade_time)
    VALUES ('TRD-001', 'ORD-001', 'AAPL', 100, 150.50, NOW());
    
    UPDATE orders 
    SET status = 'FILLED', filled_quantity = 100, avg_fill_price = 150.50
    WHERE order_id = 'ORD-001';
    
    UPDATE portfolio_positions 
    SET quantity = quantity + 100, 
        avg_cost = ((quantity * avg_cost) + (100 * 150.50)) / (quantity + 100)
    WHERE trader_id = 'TRD-123' AND symbol = 'AAPL';
    
    INSERT INTO cash_movements (trader_id, amount, movement_type, reference_id)
    VALUES ('TRD-123', -15050.00, 'TRADE_SETTLEMENT', 'TRD-001');
    
COMMIT;  -- All changes are permanent
-- If any step fails, ROLLBACK undoes all changes

-- CONSISTENCY: Maintaining business rules
START TRANSACTION;
    -- Check available cash before trade
    SELECT cash_balance INTO @available_cash
    FROM trader_accounts 
    WHERE trader_id = 'TRD-123' FOR UPDATE;
    
    -- Verify sufficient funds
    IF @available_cash >= 15050.00 THEN
        -- Proceed with trade execution
        INSERT INTO trades (trade_id, order_id, symbol, quantity, price, trade_time)
        VALUES ('TRD-002', 'ORD-002', 'GOOGL', 5, 3010.00, NOW());
        
        UPDATE trader_accounts 
        SET cash_balance = cash_balance - 15050.00
        WHERE trader_id = 'TRD-123';
        
        COMMIT;
    ELSE
        -- Insufficient funds - maintain consistency
        UPDATE orders SET status = 'REJECTED', reject_reason = 'INSUFFICIENT_FUNDS'
        WHERE order_id = 'ORD-002';
        
        COMMIT;
    END IF;

-- ISOLATION: Preventing interference between concurrent transactions
-- Transaction 1: Calculate portfolio value
SET TRANSACTION ISOLATION LEVEL REPEATABLE READ;
START TRANSACTION;
    -- Get consistent snapshot of positions
    SELECT SUM(quantity * current_price) INTO @portfolio_value
    FROM portfolio_positions p
    JOIN market_data m ON p.symbol = m.symbol
    WHERE trader_id = 'TRD-123';
    
    -- Use the calculated value for risk check
    INSERT INTO risk_calculations (trader_id, portfolio_value, calculation_time)
    VALUES ('TRD-123', @portfolio_value, NOW());
COMMIT;

-- Transaction 2: Update position (isolated from Transaction 1)
SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
START TRANSACTION;
    UPDATE portfolio_positions 
    SET quantity = quantity + 50
    WHERE trader_id = 'TRD-123' AND symbol = 'AAPL';
COMMIT;

-- DURABILITY: Ensuring committed changes survive system failures
-- Configure for maximum durability in trading systems
SET GLOBAL innodb_flush_log_at_trx_commit = 1;  -- Sync log on each commit
SET GLOBAL sync_binlog = 1;                      -- Sync binary log
SET GLOBAL innodb_doublewrite = ON;              -- Enable doublewrite buffer

-- Transaction log monitoring for durability assurance
SELECT 
    transaction_id,
    start_time,
    state,
    isolation_level,
    autocommit,
    thread_id,
    processlist_info as query_text
FROM performance_schema.events_transactions_current
WHERE state = 'COMMITTED'
  AND end_event_id IS NOT NULL;

-- Recovery verification after system restart
SELECT 
    'ACID Verification' as check_type,
    COUNT(*) as trade_count,
    SUM(quantity * price) as total_value,
    MIN(trade_time) as first_trade,
    MAX(trade_time) as last_trade
FROM trades 
WHERE trade_time >= (
    SELECT MAX(startup_time) 
    FROM performance_schema.log_status
);`,

    'isolation-levels': `-- Transaction Isolation Levels for Trading Systems
-- READ UNCOMMITTED: Lowest isolation, highest performance (risky for trading)
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
START TRANSACTION;
    -- May read uncommitted changes from other transactions
    -- Only use for non-critical reporting queries
    SELECT symbol, AVG(price) as avg_price
    FROM trades 
    WHERE trade_date = CURRENT_DATE;
COMMIT;

-- READ COMMITTED: Prevents dirty reads (default for many systems)
SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
START TRANSACTION;
    -- Reads only committed data, but may see different results on re-read
    SELECT order_id, status, quantity 
    FROM orders 
    WHERE trader_id = 'TRD-123' AND status = 'NEW';
    
    -- Later in same transaction, may see different results
    -- if another transaction commits changes
    SELECT order_id, status, quantity 
    FROM orders 
    WHERE trader_id = 'TRD-123' AND status = 'NEW';
COMMIT;

-- REPEATABLE READ: Prevents dirty and non-repeatable reads
SET TRANSACTION ISOLATION LEVEL REPEATABLE READ;
START TRANSACTION;
    -- First read
    SELECT SUM(quantity * price) as portfolio_value
    FROM portfolio_positions 
    WHERE trader_id = 'TRD-123';
    
    -- Risk calculation based on portfolio value
    -- This read will return the same result as above
    SELECT SUM(quantity * price) as portfolio_value_check
    FROM portfolio_positions 
    WHERE trader_id = 'TRD-123';
    
    -- Calculate risk based on consistent data
    INSERT INTO risk_snapshots (trader_id, portfolio_value, risk_level, snapshot_time)
    VALUES ('TRD-123', 
            (SELECT SUM(quantity * price) FROM portfolio_positions WHERE trader_id = 'TRD-123'),
            'MEDIUM', NOW());
COMMIT;

-- SERIALIZABLE: Highest isolation level (lowest concurrency)
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
START TRANSACTION;
    -- Complete isolation - transactions appear to run sequentially
    -- Use for critical financial calculations
    
    -- Calculate end-of-day position reconciliation
    SELECT 
        p.symbol,
        p.quantity as book_quantity,
        COALESCE(SUM(CASE WHEN t.side = 'BUY' THEN t.quantity ELSE -t.quantity END), 0) as trade_quantity
    FROM portfolio_positions p
    LEFT JOIN trades t ON p.symbol = t.symbol AND p.trader_id = t.trader_id
    WHERE p.trader_id = 'TRD-123'
      AND (t.trade_date = CURRENT_DATE OR t.trade_date IS NULL)
    GROUP BY p.symbol, p.quantity;
COMMIT;

-- Isolation level selection based on use case
DELIMITER //
CREATE PROCEDURE execute_with_isolation(
    IN isolation_level VARCHAR(20),
    IN operation_type VARCHAR(50)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    CASE isolation_level
        WHEN 'READ_uncommitted' THEN
            SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
        WHEN 'read_committed' THEN
            SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
        WHEN 'repeatable_read' THEN
            SET TRANSACTION ISOLATION LEVEL REPEATABLE READ;
        WHEN 'serializable' THEN
            SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
    END CASE;
    
    START TRANSACTION;
    
    CASE operation_type
        WHEN 'order_processing' THEN
            -- Use READ COMMITTED for order processing (balance speed/consistency)
            CALL process_new_orders();
        WHEN 'risk_calculation' THEN
            -- Use REPEATABLE READ for risk calculations
            CALL calculate_trader_risk();
        WHEN 'eod_reconciliation' THEN
            -- Use SERIALIZABLE for critical reconciliation
            CALL reconcile_positions();
    END CASE;
    
    COMMIT;
END//
DELIMITER ;

-- Monitor isolation level conflicts
SELECT 
    trx_id,
    trx_state,
    trx_isolation_level,
    trx_mysql_thread_id,
    trx_query,
    trx_lock_structs,
    trx_lock_memory_bytes,
    trx_rows_locked,
    trx_rows_modified
FROM information_schema.innodb_trx
WHERE trx_state != 'RUNNING'
ORDER BY trx_started;

-- Deadlock information from isolation conflicts
SHOW ENGINE INNODB STATUS;  -- Check for deadlock section

-- Optimistic locking pattern for high-concurrency scenarios
UPDATE orders 
SET status = 'FILLED', 
    filled_quantity = 100, 
    version = version + 1,
    updated_at = NOW()
WHERE order_id = 'ORD-001' 
  AND version = @expected_version;  -- Optimistic lock check

-- If @@ROW_COUNT = 0, another transaction modified the record`,

    'locking': `-- Locking Mechanisms in Trading Systems
-- Explicit row-level locking
START TRANSACTION;
    -- Lock specific order for update to prevent concurrent modifications
    SELECT order_id, status, quantity, price
    FROM orders 
    WHERE order_id = 'ORD-001' FOR UPDATE;
    
    -- Now safe to modify - other transactions will wait
    UPDATE orders 
    SET status = 'PARTIALLY_FILLED', 
        filled_quantity = 50,
        updated_at = NOW()
    WHERE order_id = 'ORD-001';
COMMIT;

-- Shared locks for reading critical data
START TRANSACTION;
    -- Multiple transactions can hold shared locks simultaneously
    SELECT cash_balance 
    FROM trader_accounts 
    WHERE trader_id = 'TRD-123' LOCK IN SHARE MODE;
    
    -- Check if balance is sufficient before attempting trade
    -- Other readers allowed, but no updates until we commit
COMMIT;

-- Table-level locking for maintenance operations
LOCK TABLES orders READ, trades WRITE;
    -- Maintenance operation requiring consistent view
    INSERT INTO daily_summary 
    SELECT 
        trader_id,
        COUNT(*) as order_count,
        SUM(quantity * price) as total_value
    FROM orders 
    WHERE created_at >= CURRENT_DATE
    GROUP BY trader_id;
UNLOCK TABLES;

-- Lock timeout configuration for trading systems
SET SESSION innodb_lock_wait_timeout = 5;  -- 5 second timeout
SET SESSION lock_wait_timeout = 10;        -- Table lock timeout

-- Handling lock timeouts in application code
DELIMITER //
CREATE PROCEDURE safe_order_update(
    IN p_order_id VARCHAR(50),
    IN p_new_status VARCHAR(20),
    IN p_retry_count INT
)
BEGIN
    DECLARE v_retry INT DEFAULT 0;
    DECLARE v_success BOOLEAN DEFAULT FALSE;
    
    retry_loop: WHILE v_retry < p_retry_count AND NOT v_success DO
        BEGIN
            DECLARE lock_timeout_exception CONDITION FOR SQLSTATE '50200';
            DECLARE CONTINUE HANDLER FOR lock_timeout_exception
            BEGIN
                SET v_retry = v_retry + 1;
                SELECT CONCAT('Lock timeout, retry ', v_retry, ' of ', p_retry_count) as message;
                -- Wait before retry
                DO SLEEP(0.1 * v_retry);  -- Exponential backoff
            END;
            
            START TRANSACTION;
                SELECT order_id INTO @check_id
                FROM orders 
                WHERE order_id = p_order_id FOR UPDATE;
                
                UPDATE orders 
                SET status = p_new_status, updated_at = NOW()
                WHERE order_id = p_order_id;
                
                SET v_success = TRUE;
            COMMIT;
        END;
    END WHILE;
    
    IF NOT v_success THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Unable to acquire lock after retries';
    END IF;
END//
DELIMITER ;

-- Lock monitoring and diagnostics
SELECT 
    r.trx_id as requesting_trx,
    r.trx_mysql_thread_id as requesting_thread,
    l.lock_table,
    l.lock_index,
    l.lock_mode,
    l.lock_type,
    b.trx_id as blocking_trx,
    b.trx_mysql_thread_id as blocking_thread,
    b.trx_time as blocking_duration,
    b.trx_query as blocking_query
FROM information_schema.innodb_lock_waits w
JOIN information_schema.innodb_locks l ON w.requested_lock_id = l.lock_id
JOIN information_schema.innodb_trx r ON w.requesting_trx_id = r.trx_id
JOIN information_schema.innodb_trx b ON w.blocking_trx_id = b.trx_id
ORDER BY b.trx_time DESC;

-- Lock escalation prevention
-- Keep transactions short and specific
START TRANSACTION;
    -- Process small batches to avoid lock escalation
    UPDATE orders 
    SET status = 'EXPIRED'
    WHERE status = 'NEW' 
      AND created_at < NOW() - INTERVAL 1 HOUR
    LIMIT 1000;  -- Process in batches
COMMIT;

-- Row versioning to reduce locking (if supported)
ALTER TABLE orders ADD COLUMN version INT DEFAULT 1;
ALTER TABLE orders ADD COLUMN last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- Multi-Version Concurrency Control (MVCC) friendly queries
-- Read without locking when possible
SELECT order_id, status, quantity, price
FROM orders 
WHERE trader_id = 'TRD-123' 
  AND status IN ('NEW', 'PARTIALLY_FILLED')
-- No FOR UPDATE needed for read-only operations

-- Intent locks for hierarchical locking
-- Database handles intent locks automatically, but understanding helps
-- IX (Intent Exclusive) on table when updating rows
-- IS (Intent Shared) on table when reading rows with shared locks`,

    'deadlocks': `-- Deadlock Prevention and Resolution in Trading Systems
-- Common deadlock scenario: Two transactions accessing resources in different orders
-- Transaction 1:
START TRANSACTION;
    -- Locks orders table first
    UPDATE orders SET status = 'PROCESSING' WHERE order_id = 'ORD-001';
    -- Then tries to lock trades table
    INSERT INTO trades (trade_id, order_id, symbol, quantity, price)
    VALUES ('TRD-001', 'ORD-001', 'AAPL', 100, 150.50);
COMMIT;

-- Transaction 2 (running concurrently):
START TRANSACTION;
    -- Locks trades table first
    SELECT MAX(trade_id) FROM trades FOR UPDATE;
    -- Then tries to lock orders table - DEADLOCK!
    UPDATE orders SET filled_quantity = 100 WHERE order_id = 'ORD-001';
COMMIT;

-- SOLUTION 1: Consistent lock ordering
-- Always access tables/rows in the same order across all transactions
-- Standard order: accounts -> orders -> trades -> positions

DELIMITER //
CREATE PROCEDURE process_trade_with_consistent_locking(
    IN p_order_id VARCHAR(50),
    IN p_quantity DECIMAL(15,2),
    IN p_price DECIMAL(15,4)
)
BEGIN
    DECLARE v_trader_id VARCHAR(20);
    DECLARE v_symbol VARCHAR(10);
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
        -- 1. Lock accounts first (consistent order)
        SELECT trader_id INTO v_trader_id
        FROM orders 
        WHERE order_id = p_order_id;
        
        SELECT cash_balance 
        FROM trader_accounts 
        WHERE trader_id = v_trader_id FOR UPDATE;
        
        -- 2. Lock orders second
        SELECT symbol INTO v_symbol
        FROM orders 
        WHERE order_id = p_order_id FOR UPDATE;
        
        -- 3. Lock trades third
        INSERT INTO trades (trade_id, order_id, symbol, quantity, price, trade_time)
        VALUES (CONCAT('TRD-', UUID_SHORT()), p_order_id, v_symbol, p_quantity, p_price, NOW());
        
        -- 4. Lock positions last
        UPDATE portfolio_positions 
        SET quantity = quantity + p_quantity
        WHERE trader_id = v_trader_id AND symbol = v_symbol;
        
    COMMIT;
END//
DELIMITER ;

-- SOLUTION 2: Lock timeout with retry logic
SET SESSION innodb_lock_wait_timeout = 1;  -- Short timeout to detect deadlocks quickly

DELIMITER //
CREATE PROCEDURE trade_with_deadlock_retry(
    IN p_order_id VARCHAR(50),
    IN p_max_retries INT
)
BEGIN
    DECLARE v_retry_count INT DEFAULT 0;
    DECLARE v_completed BOOLEAN DEFAULT FALSE;
    
    retry_loop: WHILE v_retry_count < p_max_retries AND NOT v_completed DO
        BEGIN
            DECLARE deadlock_detected CONDITION FOR SQLSTATE '40001';
            DECLARE lock_timeout CONDITION FOR SQLSTATE 'HY000';
            
            DECLARE CONTINUE HANDLER FOR deadlock_detected
            BEGIN
                ROLLBACK;
                SET v_retry_count = v_retry_count + 1;
                -- Exponential backoff with jitter
                DO SLEEP(0.01 * POWER(2, v_retry_count) + (RAND() * 0.01));
                SELECT CONCAT('Deadlock detected, retry ', v_retry_count) as message;
            END;
            
            START TRANSACTION;
                -- Execute trade logic here
                CALL execute_trade_logic(p_order_id);
                SET v_completed = TRUE;
            COMMIT;
        END;
    END WHILE;
    
    IF NOT v_completed THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Transaction failed after maximum retries';
    END IF;
END//
DELIMITER ;

-- SOLUTION 3: Reduce lock duration
-- Keep transactions as short as possible
START TRANSACTION;
    -- Prepare all data first (outside transaction if possible)
    SELECT trader_id, symbol, quantity, price 
    INTO @trader_id, @symbol, @quantity, @price
    FROM orders WHERE order_id = 'ORD-001';
    
    -- Quick transaction with minimal lock time
    UPDATE orders SET status = 'FILLED' WHERE order_id = 'ORD-001';
    INSERT INTO trades VALUES (UUID(), 'ORD-001', @symbol, @quantity, @price, NOW());
COMMIT;

-- Deadlock monitoring and analysis
SELECT 
    ENGINE_TRANSACTION_ID,
    OBJECT_SCHEMA,
    OBJECT_NAME,
    LOCK_TYPE,
    LOCK_MODE,
    LOCK_STATUS,
    LOCK_DATA
FROM performance_schema.data_locks
WHERE OBJECT_SCHEMA = 'trading_system'
ORDER BY ENGINE_TRANSACTION_ID;

-- Check recent deadlocks
SHOW ENGINE INNODB STATUS;  -- Look for LATEST DETECTED DEADLOCK section

-- Deadlock victim selection optimization
-- Smaller transactions are typically chosen as victims
-- Keep transactions small and uniform in size

-- Application-level deadlock detection
CREATE TABLE deadlock_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    thread_id BIGINT,
    transaction_info TEXT,
    deadlock_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    retry_count INT,
    eventually_succeeded BOOLEAN DEFAULT FALSE
);

-- Log deadlocks for analysis
DELIMITER //
CREATE PROCEDURE log_deadlock_incident(
    IN p_thread_id BIGINT,
    IN p_transaction_info TEXT,
    IN p_retry_count INT
)
BEGIN
    INSERT INTO deadlock_log (thread_id, transaction_info, retry_count)
    VALUES (p_thread_id, p_transaction_info, p_retry_count);
END//
DELIMITER ;

-- Prevention strategies summary:
-- 1. Always access resources in consistent order
-- 2. Keep transactions short
-- 3. Use appropriate isolation levels
-- 4. Implement retry logic with backoff
-- 5. Consider using SELECT ... FOR UPDATE NOWAIT when appropriate
-- 6. Monitor and analyze deadlock patterns
-- 7. Use connection pooling to reduce connection overhead`,

    'savepoints': `-- Savepoints and Rollback in Trading Systems
-- Complex order processing with multiple savepoints
START TRANSACTION;
    -- Savepoint after initial order validation
    INSERT INTO order_audit (order_id, action, timestamp)
    VALUES ('ORD-001', 'VALIDATION_START', NOW());
    
    SAVEPOINT sp_validation_complete;
    
    -- Risk check phase
    INSERT INTO risk_checks (order_id, check_type, result)
    VALUES ('ORD-001', 'POSITION_LIMIT', 'PASS');
    
    INSERT INTO risk_checks (order_id, check_type, result)
    VALUES ('ORD-001', 'CAPITAL_REQUIREMENT', 'PASS');
    
    SAVEPOINT sp_risk_checks_complete;
    
    -- Market data validation
    SELECT price INTO @current_price 
    FROM market_data 
    WHERE symbol = 'AAPL' 
    ORDER BY timestamp DESC LIMIT 1;
    
    -- Check if order price is reasonable (within 5% of market)
    SELECT price INTO @order_price FROM orders WHERE order_id = 'ORD-001';
    
    IF ABS(@order_price - @current_price) / @current_price > 0.05 THEN
        -- Price is unreasonable, rollback to after risk checks
        ROLLBACK TO sp_risk_checks_complete;
        
        INSERT INTO risk_checks (order_id, check_type, result)
        VALUES ('ORD-001', 'PRICE_VALIDATION', 'FAIL');
        
        UPDATE orders 
        SET status = 'REJECTED', reject_reason = 'PRICE_OUT_OF_RANGE'
        WHERE order_id = 'ORD-001';
        
        COMMIT;
    ELSE
        SAVEPOINT sp_market_validation_complete;
        
        -- Execute the trade
        INSERT INTO trades (trade_id, order_id, symbol, quantity, price, trade_time)
        VALUES ('TRD-001', 'ORD-001', 'AAPL', 100, @order_price, NOW());
        
        UPDATE orders 
        SET status = 'FILLED', filled_quantity = 100, avg_fill_price = @order_price
        WHERE order_id = 'ORD-001';
        
        -- If execution fails, can rollback to market validation
        IF @@ERROR != 0 THEN
            ROLLBACK TO sp_market_validation_complete;
            UPDATE orders SET status = 'REJECTED', reject_reason = 'EXECUTION_FAILED'
            WHERE order_id = 'ORD-001';
        END IF;
        
        COMMIT;
    END IF;

-- Batch processing with savepoints
DELIMITER //
CREATE PROCEDURE process_orders_batch(IN batch_size INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_order_id VARCHAR(50);
    DECLARE v_processed_count INT DEFAULT 0;
    DECLARE v_error_count INT DEFAULT 0;
    
    DECLARE order_cursor CURSOR FOR
        SELECT order_id FROM orders WHERE status = 'NEW' LIMIT batch_size;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        SET v_error_count = v_error_count + 1;
        ROLLBACK TO sp_order_start;
        GET DIAGNOSTICS CONDITION 1 @error_message = MESSAGE_TEXT;
        INSERT INTO error_log (order_id, error_message, error_time)
        VALUES (v_order_id, @error_message, NOW());
    END;
    
    START TRANSACTION;
        SAVEPOINT sp_batch_start;
        
        OPEN order_cursor;
        
        order_loop: LOOP
            FETCH order_cursor INTO v_order_id;
            IF done THEN
                LEAVE order_loop;
            END IF;
            
            -- Savepoint for individual order
            SAVEPOINT sp_order_start;
            
            -- Process individual order
            CALL process_single_order(v_order_id);
            
            SET v_processed_count = v_processed_count + 1;
            
            -- Release savepoint after successful processing
            RELEASE SAVEPOINT sp_order_start;
            
        END LOOP;
        
        CLOSE order_cursor;
        
        -- Log batch results
        INSERT INTO batch_log (batch_time, processed_count, error_count)
        VALUES (NOW(), v_processed_count, v_error_count);
        
    COMMIT;
END//
DELIMITER ;

-- Nested savepoints for complex workflows
START TRANSACTION;
    -- Main transaction savepoint
    INSERT INTO workflow_log (workflow_id, status, start_time)
    VALUES ('WF-001', 'STARTED', NOW());
    
    SAVEPOINT sp_main_workflow;
    
    -- Phase 1: Data validation
    SAVEPOINT sp_validation;
    
    INSERT INTO validation_results (workflow_id, phase, result)
    VALUES ('WF-001', 'PHASE1', 'IN_PROGRESS');
    
    -- If validation fails
    IF validation_failed() THEN
        ROLLBACK TO sp_validation;
        INSERT INTO validation_results (workflow_id, phase, result)
        VALUES ('WF-001', 'PHASE1', 'FAILED');
        ROLLBACK TO sp_main_workflow;
    ELSE
        RELEASE SAVEPOINT sp_validation;
    END IF;
    
    -- Phase 2: Processing
    SAVEPOINT sp_processing;
    
    -- Complex processing logic here
    CALL complex_processing_step('WF-001');
    
    -- Phase 3: Finalization
    SAVEPOINT sp_finalization;
    
    UPDATE workflow_log 
    SET status = 'COMPLETED', end_time = NOW()
    WHERE workflow_id = 'WF-001';
    
COMMIT;

-- Monitoring savepoint usage
SELECT 
    trx_id,
    trx_mysql_thread_id,
    trx_query,
    trx_operation_state,
    trx_tables_in_use,
    trx_tables_locked
FROM information_schema.innodb_trx
WHERE trx_operation_state IS NOT NULL;

-- Best practices for savepoints in trading:
-- 1. Use meaningful savepoint names
-- 2. Release savepoints after successful completion
-- 3. Handle exceptions properly with rollback to appropriate savepoint
-- 4. Don't create too many nested savepoints (performance impact)
-- 5. Document the rollback strategy in complex workflows

-- Example: Settlement processing with savepoints
DELIMITER //
CREATE PROCEDURE settle_trade_with_savepoints(IN p_trade_id VARCHAR(50))
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
        SAVEPOINT sp_settlement_start;
        
        -- Update trade status
        UPDATE trades SET settlement_status = 'SETTLING' WHERE trade_id = p_trade_id;
        
        SAVEPOINT sp_trade_updated;
        
        -- Update positions
        CALL update_positions_for_trade(p_trade_id);
        
        SAVEPOINT sp_positions_updated;
        
        -- Update cash balances
        CALL update_cash_balances_for_trade(p_trade_id);
        
        SAVEPOINT sp_cash_updated;
        
        -- Final settlement
        UPDATE trades SET settlement_status = 'SETTLED', settlement_time = NOW()
        WHERE trade_id = p_trade_id;
        
        -- All successful - commit everything
        COMMIT;
        
        -- Log successful settlement
        INSERT INTO settlement_log (trade_id, settlement_time, status)
        VALUES (p_trade_id, NOW(), 'SUCCESS');
        
END//
DELIMITER ;`,

    'distributed': `-- Distributed Transactions in Trading Systems
-- XA Transaction example for cross-database consistency
-- Database 1: Orders database
-- Database 2: Positions database  
-- Database 3: Risk management database

-- Prepare phase of two-phase commit
-- Connection 1 (Orders DB):
XA START 'trade_txn_001', 'orders_branch', 1;
    INSERT INTO orders (order_id, trader_id, symbol, quantity, price, status)
    VALUES ('ORD-001', 'TRD-123', 'AAPL', 100, 150.50, 'FILLED');
    
    INSERT INTO order_executions (order_id, execution_price, execution_quantity, execution_time)
    VALUES ('ORD-001', 150.50, 100, NOW());
XA END 'trade_txn_001', 'orders_branch', 1;
XA PREPARE 'trade_txn_001', 'orders_branch', 1;

-- Connection 2 (Positions DB):
XA START 'trade_txn_001', 'positions_branch', 1;
    UPDATE portfolio_positions 
    SET quantity = quantity + 100,
        avg_cost = ((quantity * avg_cost) + (100 * 150.50)) / (quantity + 100),
        last_updated = NOW()
    WHERE trader_id = 'TRD-123' AND symbol = 'AAPL';
    
    INSERT INTO position_history (trader_id, symbol, quantity_change, price, change_time)
    VALUES ('TRD-123', 'AAPL', 100, 150.50, NOW());
XA END 'trade_txn_001', 'positions_branch', 1;
XA PREPARE 'trade_txn_001', 'positions_branch', 1;

-- Connection 3 (Risk DB):
XA START 'trade_txn_001', 'risk_branch', 1;
    UPDATE trader_risk_metrics 
    SET position_value = position_value + (100 * 150.50),
        last_calculation = NOW()
    WHERE trader_id = 'TRD-123';
    
    INSERT INTO risk_events (trader_id, event_type, amount, event_time)
    VALUES ('TRD-123', 'POSITION_INCREASE', 15050.00, NOW());
XA END 'trade_txn_001', 'risk_branch', 1;
XA PREPARE 'trade_txn_001', 'risk_branch', 1;

-- Commit phase (if all prepare phases successful)
XA COMMIT 'trade_txn_001', 'orders_branch', 1;
XA COMMIT 'trade_txn_001', 'positions_branch', 1;
XA COMMIT 'trade_txn_001', 'risk_branch', 1;

-- Recovery procedures for distributed transactions
-- Check for prepared transactions that may need recovery
XA RECOVER;

-- Rollback if any branch failed
-- XA ROLLBACK 'trade_txn_001', 'orders_branch', 1;
-- XA ROLLBACK 'trade_txn_001', 'positions_branch', 1;
-- XA ROLLBACK 'trade_txn_001', 'risk_branch', 1;

-- Application-managed distributed transaction
DELIMITER //
CREATE PROCEDURE execute_distributed_trade(
    IN p_order_id VARCHAR(50),
    IN p_trader_id VARCHAR(20),
    IN p_symbol VARCHAR(10),
    IN p_quantity DECIMAL(15,2),
    IN p_price DECIMAL(15,4)
)
BEGIN
    DECLARE v_xa_id VARCHAR(64);
    DECLARE v_success BOOLEAN DEFAULT TRUE;
    DECLARE v_error_message TEXT DEFAULT '';
    
    -- Generate unique XA transaction ID
    SET v_xa_id = CONCAT('trade_', p_order_id, '_', UNIX_TIMESTAMP());
    
    -- Phase 1: Prepare all branches
    BEGIN
        DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
        BEGIN
            SET v_success = FALSE;
            GET DIAGNOSTICS CONDITION 1 v_error_message = MESSAGE_TEXT;
        END;
        
        -- Prepare orders branch
        SET @sql = CONCAT('XA START ''', v_xa_id, ''', ''orders'', 1');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        
        INSERT INTO orders (order_id, trader_id, symbol, quantity, price, status)
        VALUES (p_order_id, p_trader_id, p_symbol, p_quantity, p_price, 'FILLED');
        
        SET @sql = CONCAT('XA END ''', v_xa_id, ''', ''orders'', 1');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        
        SET @sql = CONCAT('XA PREPARE ''', v_xa_id, ''', ''orders'', 1');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    END;
    
    IF v_success THEN
        -- Prepare positions branch (would be on different connection/server)
        -- Similar XA START/END/PREPARE pattern for positions updates
        
        -- Prepare risk branch (would be on different connection/server)
        -- Similar XA START/END/PREPARE pattern for risk updates
        NULL;  -- Placeholder for additional branch logic
    END IF;
    
    -- Phase 2: Commit or rollback based on success
    IF v_success THEN
        -- Commit all branches
        SET @sql = CONCAT('XA COMMIT ''', v_xa_id, ''', ''orders'', 1');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        
        -- Commit other branches...
        
        INSERT INTO transaction_log (xa_id, status, completion_time)
        VALUES (v_xa_id, 'COMMITTED', NOW());
    ELSE
        -- Rollback all branches
        SET @sql = CONCAT('XA ROLLBACK ''', v_xa_id, ''', ''orders'', 1');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        
        -- Rollback other branches...
        
        INSERT INTO transaction_log (xa_id, status, error_message, completion_time)
        VALUES (v_xa_id, 'ROLLED_BACK', v_error_message, NOW());
        
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = v_error_message;
    END IF;
    
END//
DELIMITER ;

-- Saga pattern for long-running distributed transactions
-- Alternative to two-phase commit with compensation actions
CREATE TABLE saga_transactions (
    saga_id VARCHAR(64) PRIMARY KEY,
    transaction_type VARCHAR(50),
    status ENUM('STARTED', 'COMPLETED', 'COMPENSATING', 'COMPENSATED', 'FAILED'),
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completion_time TIMESTAMP NULL
);

CREATE TABLE saga_steps (
    saga_id VARCHAR(64),
    step_number INT,
    service_name VARCHAR(50),
    action_type ENUM('FORWARD', 'COMPENSATE'),
    status ENUM('PENDING', 'COMPLETED', 'FAILED'),
    request_data JSON,
    response_data JSON,
    execution_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (saga_id, step_number, action_type)
);

-- Distributed transaction monitoring
SELECT 
    s.saga_id,
    s.transaction_type,
    s.status as saga_status,
    COUNT(st.step_number) as total_steps,
    SUM(CASE WHEN st.status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_steps,
    SUM(CASE WHEN st.status = 'FAILED' THEN 1 ELSE 0 END) as failed_steps
FROM saga_transactions s
LEFT JOIN saga_steps st ON s.saga_id = st.saga_id
WHERE s.start_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
GROUP BY s.saga_id, s.transaction_type, s.status;

-- Compensation workflow for failed distributed transactions
DELIMITER //
CREATE PROCEDURE compensate_failed_transaction(IN p_saga_id VARCHAR(64))
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_step_number INT;
    DECLARE v_service_name VARCHAR(50);
    DECLARE v_request_data JSON;
    
    DECLARE step_cursor CURSOR FOR
        SELECT step_number, service_name, request_data
        FROM saga_steps
        WHERE saga_id = p_saga_id 
          AND action_type = 'FORWARD'
          AND status = 'COMPLETED'
        ORDER BY step_number DESC;  -- Reverse order for compensation
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    UPDATE saga_transactions 
    SET status = 'COMPENSATING' 
    WHERE saga_id = p_saga_id;
    
    OPEN step_cursor;
    
    compensation_loop: LOOP
        FETCH step_cursor INTO v_step_number, v_service_name, v_request_data;
        IF done THEN
            LEAVE compensation_loop;
        END IF;
        
        -- Execute compensation action for each completed step
        INSERT INTO saga_steps (saga_id, step_number, service_name, action_type, status, request_data)
        VALUES (p_saga_id, v_step_number, v_service_name, 'COMPENSATE', 'PENDING', v_request_data);
        
        -- Call compensation service (would be external call in real implementation)
        CALL execute_compensation_action(p_saga_id, v_step_number, v_service_name, v_request_data);
        
    END LOOP;
    
    CLOSE step_cursor;
    
    UPDATE saga_transactions 
    SET status = 'COMPENSATED', completion_time = NOW()
    WHERE saga_id = p_saga_id;
    
END//
DELIMITER ;`
  };

  return (
    <div style={{
      padding: '40px',
      fontFamily: 'Inter, system-ui, sans-serif',
      backgroundColor: '#0f172a',
      color: 'white',
      minHeight: '100vh'
    }}>
      <div style={{ marginBottom: '40px' }}>
        <h1 style={{ 
          fontSize: '36px', 
          fontWeight: '700', 
          marginBottom: '16px',
          background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent'
        }}>
          SQL Transactions & ACID
        </h1>
        <p style={{ fontSize: '18px', color: '#94a3b8', lineHeight: '1.6' }}>
          Master transaction management and data consistency for reliable trading systems
        </p>
      </div>

      <div style={{ display: 'flex', gap: '40px' }}>
        <div style={{ flex: '1', maxWidth: '300px' }}>
          <h2 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '24px', color: '#10b981' }}>
            Transaction Topics
          </h2>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {topics.map((topic) => (
              <button
                key={topic.id}
                onClick={() => setSelectedTopic(topic.id)}
                style={{
                  padding: '16px',
                  backgroundColor: selectedTopic === topic.id ? 'rgba(16, 185, 129, 0.2)' : 'rgba(30, 41, 59, 0.5)',
                  border: `2px solid ${selectedTopic === topic.id ? '#10b981' : 'transparent'}`,
                  borderRadius: '12px',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  textAlign: 'left',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px'
                }}
              >
                <div style={{ color: selectedTopic === topic.id ? '#10b981' : '#64748b' }}>
                  {topic.icon}
                </div>
                <div>
                  <div style={{ 
                    fontWeight: '600', 
                    color: selectedTopic === topic.id ? '#10b981' : 'white',
                    marginBottom: '4px'
                  }}>
                    {topic.name}
                  </div>
                  <div style={{ fontSize: '14px', color: '#94a3b8' }}>
                    {topic.description}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        <div style={{ flex: '2' }}>
          <div style={{ marginBottom: '24px' }}>
            <div style={{ display: 'flex', gap: '8px', marginBottom: '24px' }}>
              {['overview', 'definition', 'code'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  style={{
                    padding: '12px 24px',
                    backgroundColor: activeTab === tab ? '#10b981' : 'transparent',
                    border: `2px solid ${activeTab === tab ? '#10b981' : '#374151'}`,
                    borderRadius: '8px',
                    color: activeTab === tab ? 'white' : '#94a3b8',
                    cursor: 'pointer',
                    fontWeight: '600',
                    textTransform: 'capitalize',
                    transition: 'all 0.2s'
                  }}
                >
                  {tab}
                </button>
              ))}
            </div>
          </div>

          {activeTab === 'overview' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <h2 style={{ fontSize: '28px', fontWeight: '600', marginBottom: '24px', color: '#10b981' }}>
                Transactions & ACID Overview
              </h2>
              <div style={{ fontSize: '16px', lineHeight: '1.8', color: '#e2e8f0' }}>
                <p style={{ marginBottom: '20px' }}>
                  Database transactions ensure data integrity and consistency in trading systems where financial 
                  accuracy is paramount. ACID properties, proper isolation levels, and deadlock prevention are 
                  essential for maintaining reliable operations under high concurrency.
                </p>
                <div style={{ marginBottom: '24px' }}>
                  <h3 style={{ color: '#10b981', fontSize: '20px', marginBottom: '16px' }}>Core Concepts:</h3>
                  <ul style={{ marginLeft: '24px', lineHeight: '1.8' }}>
                    <li><strong>ACID Compliance:</strong> Ensuring atomicity, consistency, isolation, and durability</li>
                    <li><strong>Isolation Levels:</strong> Balancing consistency with performance requirements</li>
                    <li><strong>Locking Strategies:</strong> Managing concurrent access to critical trading data</li>
                    <li><strong>Deadlock Prevention:</strong> Avoiding transaction conflicts in high-frequency scenarios</li>
                  </ul>
                </div>
                <div style={{ 
                  backgroundColor: 'rgba(16, 185, 129, 0.1)', 
                  padding: '20px', 
                  borderRadius: '8px',
                  border: '1px solid #10b981'
                }}>
                  <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Trading System Requirements:</h4>
                  <p>
                    Financial transactions must be atomic and consistent. A partially executed trade could result 
                    in data inconsistencies, regulatory violations, and financial losses. Proper transaction 
                    management ensures all-or-nothing execution for critical trading operations.
                  </p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'definition' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <h2 style={{ fontSize: '24px', fontWeight: '600', marginBottom: '20px', color: '#10b981' }}>
                {topics.find(t => t.id === selectedTopic)?.name} Definition
              </h2>
              <p style={{ fontSize: '16px', lineHeight: '1.8', color: '#e2e8f0' }}>
                {definitions[selectedTopic]}
              </p>
            </div>
          )}

          {activeTab === 'code' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <div style={{ marginBottom: '20px' }}>
                <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px' }}>
                  {topics.find(t => t.id === selectedTopic)?.name} Examples
                </h3>
              </div>
              <SyntaxHighlighter
                language="sql"
                style={oneDark}
                customStyle={{
                  backgroundColor: '#1e293b',
                  padding: '20px',
                  borderRadius: '8px',
                  fontSize: '14px',
                  lineHeight: '1.6'
                }}
              >
                {codeExamples[selectedTopic]}
              </SyntaxHighlighter>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SQLTransactions;
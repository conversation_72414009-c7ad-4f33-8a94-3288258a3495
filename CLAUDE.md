# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
This is a React application bootstrapped with Create React App, using TailwindCSS for styling. The project name is "darkpool-architecture" and appears to be in early development stages.

## Development Commands
- `npm start` - Runs the app in development mode (http://localhost:3000)
- `npm test` - Launches the test runner in interactive watch mode  
- `npm run build` - Builds the app for production to the `build` folder
- `npm run eject` - Ejects from Create React App (one-way operation)

## Architecture
- **Frontend Framework**: React 19.1.1 with React DOM
- **Styling**: TailwindCSS 4.1.12 with PostCSS and Autoprefixer
- **Testing**: Jest with React Testing Library (@testing-library/react, @testing-library/jest-dom)
- **Build Tool**: Create React App (react-scripts 5.0.1)
- **Additional Libraries**: 
  - lucide-react for icons
  - html2canvas for screenshot/canvas functionality
  - web-vitals for performance monitoring

## Project Structure
- `/src/` - Main application source code
  - `App.js` - Main application component
  - `index.js` - Application entry point with ReactDOM.createRoot
  - `App.css`, `index.css` - Styling files
  - `setupTests.js` - Jest testing configuration
  - `reportWebVitals.js` - Performance monitoring setup
- `/public/` - Static assets including index.html, favicon, and PWA manifest

## Key Dependencies
The project uses modern React patterns with React 19's new createRoot API and includes comprehensive testing utilities. TailwindCSS is configured for utility-first styling.
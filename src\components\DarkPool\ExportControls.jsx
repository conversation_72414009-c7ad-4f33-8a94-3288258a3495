import React, { useState } from 'react';
import html2canvas from 'html2canvas';
import { Download } from 'lucide-react';

const ExportControls = ({ containerRef }) => {
  const [isExporting, setIsExporting] = useState(false);

  const exportToImage = async () => {
    setIsExporting(true);
    try {
      const canvas = await html2canvas(containerRef.current, {
        backgroundColor: '#0f172a',
        scale: 2
      });
      
      canvas.toBlob((blob) => {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'darkpool-architecture.png';
        a.click();
        URL.revokeObjectURL(url);
      });
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <button
      onClick={exportToImage}
      disabled={isExporting}
      className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
    >
      <Download size={20} />
      {isExporting ? 'Exporting...' : 'Export'}
    </button>
  );
};

export default ExportControls;
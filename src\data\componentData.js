export const components = [
  {
    id: 'order-gateway',
    name: 'Order Gateway',
    tech: ['Spring Boot + Netty', 'FIX Protocol', 'Rate Limiting'],
    position: { top: 50, left: 50 },
    latency: '< 50μs',
    throughput: '1M+ ops/s',
    category: 'processing',
    keywords: ['gateway', 'order', 'fix', 'authentication', 'rate limit']
  },
  {
    id: 'matching-engine',
    name: 'Matching Engine',
    tech: ['Java + Chronicle Map', 'Disruptor Pattern', 'Lock-free Data Structures'],
    position: { top: 50, left: 300 },
    latency: '< 100μs',
    throughput: '500K+ matches/s',
    category: 'processing',
    keywords: ['matching', 'engine', 'order book', 'trade', 'execution']
  },
  {
    id: 'settlement-hub',
    name: 'Settlement Hub',
    tech: ['Spring Boot', 'Apache Kafka', 'Database Integration'],
    position: { top: 50, left: 550 },
    latency: '< 500μs',
    throughput: '100K+ settlements/s',
    category: 'processing',
    keywords: ['settlement', 'clearing', 'trade confirmation', 'netting']
  },
  {
    id: 'market-data',
    name: 'Market Data Feed',
    tech: ['C++ + UDP', 'Binary Protocol', 'Multicast'],
    position: { top: 200, left: 50 },
    latency: '< 10μs',
    throughput: '10M+ updates/s',
    category: 'data',
    keywords: ['market data', 'feed', 'real-time', 'pricing', 'quotes']
  },
  {
    id: 'risk-engine',
    name: 'Risk Engine',
    tech: ['Go + gRPC', 'Real-time Calculations', 'Circuit Breakers'],
    position: { top: 200, left: 300 },
    latency: '< 200μs',
    throughput: '250K+ checks/s',
    category: 'processing',
    keywords: ['risk', 'limits', 'validation', 'exposure', 'compliance']
  },
  {
    id: 'trade-reporting',
    name: 'Trade Reporting',
    tech: ['Node.js + Express', 'REST APIs', 'Regulatory Compliance'],
    position: { top: 200, left: 550 },
    latency: '< 1ms',
    throughput: '50K+ reports/s',
    category: 'processing',
    keywords: ['reporting', 'compliance', 'audit', 'regulatory', 'transaction']
  },
  {
    id: 'order-book',
    name: 'Order Book',
    tech: ['Redis Cluster', 'In-Memory Storage', 'Pub/Sub'],
    position: { top: 350, left: 175 },
    category: 'storage',
    keywords: ['order book', 'redis', 'memory', 'cache', 'book state']
  },
  {
    id: 'kafka-cluster',
    name: 'Kafka Cluster',
    tech: ['Apache Kafka', 'Event Streaming', 'Partitioning'],
    position: { top: 350, left: 425 },
    category: 'infrastructure',
    keywords: ['kafka', 'messaging', 'stream', 'event', 'queue']
  },
  {
    id: 'redis-cluster',
    name: 'Redis Cluster',
    tech: ['Redis', 'Sharding', 'High Availability'],
    position: { top: 500, left: 50 },
    category: 'storage',
    keywords: ['redis', 'cache', 'memory', 'session', 'fast storage']
  },
  {
    id: 'postgresql',
    name: 'PostgreSQL',
    tech: ['ACID Compliance', 'Read Replicas', 'Connection Pooling'],
    position: { top: 500, left: 300 },
    category: 'storage',
    keywords: ['postgresql', 'database', 'sql', 'persistent', 'acid']
  },
  {
    id: 'influxdb',
    name: 'InfluxDB',
    tech: ['Time Series', 'Metrics Storage', 'Grafana Integration'],
    position: { top: 500, left: 550 },
    category: 'storage',
    keywords: ['influx', 'time series', 'metrics', 'monitoring', 'grafana']
  },
  {
    id: 'monitoring',
    name: 'Monitoring Stack',
    tech: ['Prometheus', 'Grafana', 'AlertManager'],
    position: { top: 650, left: 175 },
    category: 'infrastructure',
    keywords: ['monitoring', 'prometheus', 'grafana', 'alerts', 'observability']
  },
  {
    id: 'kubernetes',
    name: 'Kubernetes',
    tech: ['Container Orchestration', 'Auto-scaling', 'Service Mesh'],
    position: { top: 650, left: 425 },
    category: 'infrastructure',
    keywords: ['kubernetes', 'k8s', 'containers', 'orchestration', 'scaling']
  }
];

export const connections = [
  { from: 'order-gateway', to: 'matching-engine', id: 'c1', type: 'primary' },
  { from: 'matching-engine', to: 'settlement-hub', id: 'c2', type: 'primary' },
  { from: 'market-data', to: 'matching-engine', id: 'c3', type: 'data' },
  { from: 'risk-engine', to: 'matching-engine', id: 'c4', type: 'validation' },
  { from: 'matching-engine', to: 'trade-reporting', id: 'c5', type: 'reporting' },
  { from: 'matching-engine', to: 'order-book', id: 'c6', type: 'storage' },
  { from: 'settlement-hub', to: 'kafka-cluster', id: 'c7', type: 'messaging' },
  { from: 'order-gateway', to: 'redis-cluster', id: 'c8', type: 'session' },
  { from: 'trade-reporting', to: 'postgresql', id: 'c9', type: 'persistence' },
  { from: 'risk-engine', to: 'influxdb', id: 'c10', type: 'metrics' },
  { from: 'monitoring', to: 'influxdb', id: 'c11', type: 'metrics' },
  { from: 'kubernetes', to: 'monitoring', id: 'c12', type: 'infrastructure' }
];

export const componentDetails = {
  'order-gateway': {
    details: {
      title: 'Order Gateway',
      functions: [
        'Order validation and normalization',
        'Client authentication and authorization',
        'Rate limiting and throttling',
        'FIX protocol handling',
        'Session management',
        'Order routing and enrichment'
      ],
      stack: [
        { name: 'Spring Boot', desc: 'Application framework' },
        { name: 'Netty', desc: 'High-performance networking' },
        { name: 'FIX Protocol', desc: 'Financial messaging standard' }
      ]
    },
    workflow: `1. Client connects via FIX session
2. Authentication and session establishment
3. Order received and validated
4. Rate limiting checks applied
5. Order enriched with metadata
6. Forwarded to matching engine`,
    code: `@Component
public class OrderGateway {
    @Autowired
    private OrderValidator validator;
    
    @EventListener
    public void handleOrder(Order order) {
        if (validator.validate(order)) {
            matchingEngine.submit(order);
        }
    }
}`
  },
  'matching-engine': {
    details: {
      title: 'Matching Engine',
      functions: [
        'Order book management',
        'Price-time priority matching',
        'Trade execution and confirmation',
        'Market depth calculation',
        'Cross trade detection',
        'Fill notifications'
      ],
      stack: [
        { name: 'Java', desc: 'Core matching logic' },
        { name: 'Chronicle Map', desc: 'Off-heap storage' },
        { name: 'Disruptor', desc: 'Lock-free concurrency' }
      ]
    },
    workflow: `1. Receive order from gateway
2. Update order book state
3. Check for matching opportunities
4. Execute trades at best price
5. Generate fill confirmations
6. Update market data feeds`,
    code: `public class MatchingEngine {
    private final OrderBook orderBook;
    private final Disruptor<OrderEvent> disruptor;
    
    public void processOrder(Order order) {
        orderBook.addOrder(order);
        executeMatches();
    }
}`
  }
};
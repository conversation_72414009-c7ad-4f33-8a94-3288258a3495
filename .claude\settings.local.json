{"permissions": {"allow": ["Bash(npm install)", "Bash(npm install:*)", "Bash(npm start)", "Bash(npm uninstall:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(pkill:*)", "Bash(lsof:*)", "Bash(npx react-scripts build:*)", "Bash(node:*)", "Bash(grep:*)", "Bash(npx eslint:*)", "Bash(npx @babel/parser:*)", "Bash(git remote add:*)", "Bash(git add:*)", "Bash(git config:*)", "Bash(git push:*)", "Bash(gh auth:*)", "Bash(git commit:*)", "Bash(git remote set-url:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(timeout 10s npm start)", "<PERSON><PERSON>(timeout 15s npm start)", "<PERSON><PERSON>(timeout 30s npm start)", "Bash(npx babel:*)", "Bash(npm start:*)", "Bash(PORT=3001 npm start)", "Bash(PORT=3002 npm start)", "Bash(/bashes)", "Bash(tree:*)"], "deny": [], "ask": []}}
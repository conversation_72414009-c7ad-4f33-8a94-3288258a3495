import React, { useState } from 'react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Car, Truck, Bike, Shield, CreditCard, Timer, MapPin, Users, DollarSign, BarChart3, Lock, AlertCircle, Layers, GitBranch, Package, Code } from 'lucide-react';

const ParkingLotOOP = () => {
  const [selectedConcept, setSelectedConcept] = useState('abstraction');
  const [selectedClass, setSelectedClass] = useState(null);
  const [activeTab, setActiveTab] = useState('concepts');
  const [classPositions, setClassPositions] = useState({});
  const [isDragging, setIsDragging] = useState(null);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  // OOP concepts with colors and highlighted classes
  const oopConcepts = [
    {
      id: 'abstraction',
      title: 'Abstraction',
      icon: <Shield size={20} />,
      color: '#8b5cf6',
      highlightClasses: ['parkable', 'chargeable', 'vehicle', 'parking-spot'],
      description: 'Abstract classes and interfaces define contracts',
      details: 'Abstract Vehicle class and interfaces like Parkable and Chargeable define contracts without implementation details.'
    },
    {
      id: 'encapsulation',
      title: 'Encapsulation',
      icon: <Lock size={20} />,
      color: '#ef4444',
      highlightClasses: ['parking-lot', 'payment-system'],
      description: 'Data hiding with controlled access',
      details: 'Private fields with public methods ensure controlled access to internal state and maintain data integrity.'
    },
    {
      id: 'inheritance',
      title: 'Inheritance',
      icon: <GitBranch size={20} />,
      color: '#10b981',
      highlightClasses: ['vehicle', 'car', 'motorcycle', 'truck', 'electric-car', 'parking-spot', 'compact-spot', 'regular-spot', 'large-spot', 'electric-spot'],
      description: 'Class hierarchy and code reuse',
      details: 'Vehicle hierarchy and ParkingSpot hierarchy demonstrate inheritance with shared behaviors and specialized implementations.'
    },
    {
      id: 'polymorphism',
      title: 'Polymorphism',
      icon: <Layers size={20} />,
      color: '#f59e0b',
      highlightClasses: ['car', 'motorcycle', 'truck', 'electric-car'],
      description: 'Multiple forms of behavior',
      details: 'Different vehicle types implement calculateParkingFee() differently, allowing uniform treatment of diverse objects.'
    }
  ];

  // UML Classes with positions for the diagram - spaced out for better readability
  const umlClasses = [
    {
      id: 'parkable',
      name: 'Parkable',
      type: 'interface',
      position: { x: 80, y: 30 },
      methods: ['+park()', '+unpark()', '+isParked()'],
      attributes: [],
      color: '#8b5cf6'
    },
    {
      id: 'chargeable',
      name: 'Chargeable',
      type: 'interface',
      position: { x: 350, y: 30 },
      methods: ['+startCharging()', '+getBatteryLevel()'],
      attributes: [],
      color: '#10b981'
    },
    {
      id: 'vehicle',
      name: 'Vehicle',
      type: 'abstract',
      position: { x: 200, y: 200 },
      methods: ['+calculateParkingFee()', '+canFitInSpot()'],
      attributes: ['#licensePlate: String', '#entryTime: DateTime'],
      color: '#ef4444'
    },
    {
      id: 'car',
      name: 'Car',
      type: 'class',
      position: { x: 30, y: 400 },
      methods: ['+park()', '+calculateFee()'],
      attributes: ['-doors: int'],
      color: '#3b82f6'
    },
    {
      id: 'motorcycle',
      name: 'Motorcycle',
      type: 'class',
      position: { x: 200, y: 400 },
      methods: ['+park()', '+calculateFee()'],
      attributes: ['-sidecar: boolean'],
      color: '#f59e0b'
    },
    {
      id: 'truck',
      name: 'Truck',
      type: 'class',
      position: { x: 370, y: 400 },
      methods: ['+park()', '+calculateFee()'],
      attributes: ['-capacity: double'],
      color: '#ec4899'
    },
    {
      id: 'electric-car',
      name: 'ElectricCar',
      type: 'class',
      position: { x: 540, y: 400 },
      methods: ['+startCharging()', '+calculateFee()'],
      attributes: ['-batteryLevel: double'],
      color: '#06b6d4'
    },
    {
      id: 'parking-lot',
      name: 'ParkingLot',
      type: 'class',
      position: { x: 700, y: 200 },
      methods: ['+parkVehicle()', '+exitVehicle()'],
      attributes: ['-name: String', '-floors: Map'],
      color: '#84cc16'
    },
    {
      id: 'parking-spot',
      name: 'ParkingSpot',
      type: 'abstract',
      position: { x: 700, y: 400 },
      methods: ['+assignVehicle()', '+removeVehicle()'],
      attributes: ['#spotId: String', '#isAvailable: boolean'],
      color: '#6b7280'
    },
    {
      id: 'compact-spot',
      name: 'CompactSpot',
      type: 'class',
      position: { x: 550, y: 580 },
      methods: ['+canFitVehicle()'],
      attributes: [],
      color: '#94a3b8'
    },
    {
      id: 'regular-spot',
      name: 'RegularSpot',
      type: 'class',
      position: { x: 700, y: 580 },
      methods: ['+canFitVehicle()'],
      attributes: [],
      color: '#94a3b8'
    },
    {
      id: 'large-spot',
      name: 'LargeSpot',
      type: 'class',
      position: { x: 850, y: 580 },
      methods: ['+canFitVehicle()'],
      attributes: [],
      color: '#94a3b8'
    },
    {
      id: 'electric-spot',
      name: 'ElectricSpot',
      type: 'class',
      position: { x: 1000, y: 580 },
      methods: ['+canFitVehicle()', '+charge()'],
      attributes: ['-chargingRate: double'],
      color: '#10b981'
    },
    {
      id: 'payment-system',
      name: 'PaymentSystem',
      type: 'class',
      position: { x: 950, y: 200 },
      methods: ['+processPayment()', '+calculateFee()'],
      attributes: ['-transactions: List'],
      color: '#fbbf24'
    }
  ];

  // Function to check if a class should be highlighted
  const isClassHighlighted = (classId) => {
    const concept = oopConcepts.find(c => c.id === selectedConcept);
    return concept && concept.highlightClasses.includes(classId);
  };

  // Get position for a class (either from state or default)
  const getClassPosition = (classId) => {
    if (classPositions[classId]) {
      return classPositions[classId];
    }
    const cls = umlClasses.find(c => c.id === classId);
    return cls ? cls.position : { x: 0, y: 0 };
  };

  // Handle mouse down on class
  const handleMouseDown = (e, classId) => {
    e.stopPropagation();
    const svg = e.currentTarget.closest('svg');
    const pt = svg.createSVGPoint();
    pt.x = e.clientX;
    pt.y = e.clientY;
    const svgP = pt.matrixTransform(svg.getScreenCTM().inverse());
    
    const currentPos = getClassPosition(classId);
    setIsDragging(classId);
    setDragStart({
      x: svgP.x - currentPos.x,
      y: svgP.y - currentPos.y
    });
    e.preventDefault();
  };

  // Handle mouse move for dragging
  const handleMouseMove = (e) => {
    if (!isDragging) return;
    
    const svg = e.currentTarget;
    const pt = svg.createSVGPoint();
    pt.x = e.clientX;
    pt.y = e.clientY;
    const svgP = pt.matrixTransform(svg.getScreenCTM().inverse());
    
    const newX = svgP.x - dragStart.x;
    const newY = svgP.y - dragStart.y;
    
    setClassPositions(prev => ({
      ...prev,
      [isDragging]: { x: Math.max(0, Math.min(1050, newX)), y: Math.max(0, Math.min(600, newY)) }
    }));
  };

  // Handle mouse up to stop dragging
  const handleMouseUp = () => {
    setIsDragging(null);
  };

  // Generate complete Java application with main method
  const generateCompleteJavaCode = () => {
    return `// ParkingLotSystem.java - Complete OOP Implementation
// Compile: javac ParkingLotSystem.java
// Run: java ParkingLotSystem

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

// ==== INTERFACES (ABSTRACTION) ====

interface Parkable {
    boolean park(ParkingSpot spot);
    boolean unpark();
    boolean isParked();
}

interface Chargeable {
    void startCharging();
    void stopCharging();
    double getBatteryLevel();
    boolean isCharging();
}

// ==== ABSTRACT CLASSES (ABSTRACTION) ====

abstract class Vehicle implements Parkable {
    protected String licensePlate;
    protected LocalDateTime entryTime;
    protected ParkingSpot currentSpot;
    protected boolean parked;
    
    public Vehicle(String licensePlate) {
        this.licensePlate = licensePlate;
        this.parked = false;
    }
    
    // Abstract methods - must be implemented by subclasses
    public abstract double calculateParkingFee();
    public abstract boolean canFitInSpot(ParkingSpot spot);
    public abstract String getVehicleType();
    
    // Concrete methods
    @Override
    public boolean park(ParkingSpot spot) {
        if (canFitInSpot(spot) && spot.isAvailable()) {
            this.currentSpot = spot;
            this.entryTime = LocalDateTime.now();
            this.parked = true;
            spot.occupySpot(this);
            return true;
        }
        return false;
    }
    
    @Override
    public boolean unpark() {
        if (parked && currentSpot != null) {
            currentSpot.freeSpot();
            this.parked = false;
            this.currentSpot = null;
            return true;
        }
        return false;
    }
    
    @Override
    public boolean isParked() {
        return parked;
    }
    
    public String getLicensePlate() { return licensePlate; }
    public LocalDateTime getEntryTime() { return entryTime; }
    public ParkingSpot getCurrentSpot() { return currentSpot; }
    
    protected long getParkedHours() {
        if (entryTime != null) {
            return ChronoUnit.HOURS.between(entryTime, LocalDateTime.now());
        }
        return 0;
    }
}

abstract class ParkingSpot {
    protected int spotNumber;
    protected boolean available;
    protected Vehicle parkedVehicle;
    
    public ParkingSpot(int spotNumber) {
        this.spotNumber = spotNumber;
        this.available = true;
    }
    
    public abstract boolean canFitVehicle(Vehicle vehicle);
    public abstract String getSpotType();
    
    public boolean isAvailable() { return available; }
    public int getSpotNumber() { return spotNumber; }
    public Vehicle getParkedVehicle() { return parkedVehicle; }
    
    public void occupySpot(Vehicle vehicle) {
        this.parkedVehicle = vehicle;
        this.available = false;
    }
    
    public void freeSpot() {
        this.parkedVehicle = null;
        this.available = true;
    }
}

// ==== CONCRETE VEHICLE CLASSES (INHERITANCE & POLYMORPHISM) ====

class Car extends Vehicle {
    private int doors;
    
    public Car(String licensePlate, int doors) {
        super(licensePlate);
        this.doors = doors;
    }
    
    @Override
    public double calculateParkingFee() {
        long hours = getParkedHours();
        return Math.max(1, hours) * 5.0; // $5 per hour
    }
    
    @Override
    public boolean canFitInSpot(ParkingSpot spot) {
        return spot instanceof CompactSpot || spot instanceof RegularSpot;
    }
    
    @Override
    public String getVehicleType() {
        return "Car";
    }
    
    public int getDoors() { return doors; }
}

class Motorcycle extends Vehicle {
    private String engineType;
    
    public Motorcycle(String licensePlate, String engineType) {
        super(licensePlate);
        this.engineType = engineType;
    }
    
    @Override
    public double calculateParkingFee() {
        long hours = getParkedHours();
        return Math.max(1, hours) * 2.0; // $2 per hour (cheaper)
    }
    
    @Override
    public boolean canFitInSpot(ParkingSpot spot) {
        return true; // Motorcycles can fit in any spot
    }
    
    @Override
    public String getVehicleType() {
        return "Motorcycle";
    }
}

class Truck extends Vehicle {
    private double payload;
    
    public Truck(String licensePlate, double payload) {
        super(licensePlate);
        this.payload = payload;
    }
    
    @Override
    public double calculateParkingFee() {
        long hours = getParkedHours();
        return Math.max(1, hours) * 10.0; // $10 per hour (more expensive)
    }
    
    @Override
    public boolean canFitInSpot(ParkingSpot spot) {
        return spot instanceof LargeSpot;
    }
    
    @Override
    public String getVehicleType() {
        return "Truck";
    }
}

class ElectricCar extends Car implements Chargeable {
    private double batteryLevel;
    private boolean charging;
    
    public ElectricCar(String licensePlate, int doors, double batteryLevel) {
        super(licensePlate, doors);
        this.batteryLevel = batteryLevel;
        this.charging = false;
    }
    
    @Override
    public double calculateParkingFee() {
        // Electric cars get 20% discount
        return super.calculateParkingFee() * 0.8;
    }
    
    @Override
    public boolean canFitInSpot(ParkingSpot spot) {
        return spot instanceof ElectricSpot || super.canFitInSpot(spot);
    }
    
    @Override
    public String getVehicleType() {
        return "Electric Car";
    }
    
    @Override
    public void startCharging() {
        if (currentSpot instanceof ElectricSpot) {
            this.charging = true;
            System.out.println("Started charging " + licensePlate);
        }
    }
    
    @Override
    public void stopCharging() {
        this.charging = false;
        System.out.println("Stopped charging " + licensePlate);
    }
    
    @Override
    public double getBatteryLevel() {
        return batteryLevel;
    }
    
    @Override
    public boolean isCharging() {
        return charging;
    }
}

// ==== CONCRETE PARKING SPOT CLASSES (INHERITANCE) ====

class CompactSpot extends ParkingSpot {
    public CompactSpot(int spotNumber) {
        super(spotNumber);
    }
    
    @Override
    public boolean canFitVehicle(Vehicle vehicle) {
        return vehicle instanceof Car || vehicle instanceof Motorcycle;
    }
    
    @Override
    public String getSpotType() {
        return "Compact";
    }
}

class RegularSpot extends ParkingSpot {
    public RegularSpot(int spotNumber) {
        super(spotNumber);
    }
    
    @Override
    public boolean canFitVehicle(Vehicle vehicle) {
        return vehicle instanceof Car || vehicle instanceof Motorcycle;
    }
    
    @Override
    public String getSpotType() {
        return "Regular";
    }
}

class LargeSpot extends ParkingSpot {
    public LargeSpot(int spotNumber) {
        super(spotNumber);
    }
    
    @Override
    public boolean canFitVehicle(Vehicle vehicle) {
        return true; // Large spots can fit any vehicle
    }
    
    @Override
    public String getSpotType() {
        return "Large";
    }
}

class ElectricSpot extends RegularSpot {
    private boolean chargingStationActive;
    
    public ElectricSpot(int spotNumber) {
        super(spotNumber);
        this.chargingStationActive = true;
    }
    
    @Override
    public boolean canFitVehicle(Vehicle vehicle) {
        return super.canFitVehicle(vehicle);
    }
    
    @Override
    public String getSpotType() {
        return "Electric";
    }
    
    public boolean isChargingStationActive() {
        return chargingStationActive;
    }
}

// ==== MAIN SYSTEM CLASSES (ENCAPSULATION) ====

class PaymentSystem {
    private List<Transaction> transactions;
    private AtomicInteger transactionId;
    
    public PaymentSystem() {
        this.transactions = new ArrayList<>();
        this.transactionId = new AtomicInteger(1);
    }
    
    public boolean processPayment(Vehicle vehicle, double amount) {
        if (amount <= 0) return false;
        
        Transaction transaction = new Transaction(
            transactionId.getAndIncrement(),
            vehicle.getLicensePlate(),
            amount,
            LocalDateTime.now()
        );
        
        transactions.add(transaction);
        System.out.println("Payment processed: $" + amount + " for " + vehicle.getLicensePlate());
        return true;
    }
    
    public List<Transaction> getTransactions() {
        return new ArrayList<>(transactions); // Return copy for encapsulation
    }
    
    private static class Transaction {
        private final int id;
        private final String vehicleLicense;
        private final double amount;
        private final LocalDateTime timestamp;
        
        public Transaction(int id, String vehicleLicense, double amount, LocalDateTime timestamp) {
            this.id = id;
            this.vehicleLicense = vehicleLicense;
            this.amount = amount;
            this.timestamp = timestamp;
        }
        
        @Override
        public String toString() {
            return String.format("Transaction{id=%d, vehicle='%s', amount=$%.2f, time=%s}", 
                id, vehicleLicense, amount, timestamp);
        }
    }
}

class ParkingLot {
    private String name;
    private Map<Integer, ParkingSpot> spots;
    private PaymentSystem paymentSystem;
    private int totalSpots;
    private int availableSpots;
    
    public ParkingLot(String name) {
        this.name = name;
        this.spots = new HashMap<>();
        this.paymentSystem = new PaymentSystem();
        this.totalSpots = 0;
        this.availableSpots = 0;
        initializeParkingSpots();
    }
    
    private void initializeParkingSpots() {
        // Create different types of spots
        for (int i = 1; i <= 20; i++) {
            ParkingSpot spot;
            if (i <= 5) {
                spot = new CompactSpot(i);
            } else if (i <= 15) {
                spot = new RegularSpot(i);
            } else if (i <= 18) {
                spot = new LargeSpot(i);
            } else {
                spot = new ElectricSpot(i);
            }
            spots.put(i, spot);
            totalSpots++;
            availableSpots++;
        }
    }
    
    public boolean parkVehicle(Vehicle vehicle) {
        ParkingSpot availableSpot = findAvailableSpot(vehicle);
        if (availableSpot != null) {
            boolean parked = vehicle.park(availableSpot);
            if (parked) {
                availableSpots--;
                System.out.println(vehicle.getVehicleType() + " " + vehicle.getLicensePlate() + 
                    " parked in " + availableSpot.getSpotType() + " spot #" + availableSpot.getSpotNumber());
                return true;
            }
        }
        System.out.println("No suitable spot available for " + vehicle.getLicensePlate());
        return false;
    }
    
    public boolean unparkVehicle(Vehicle vehicle) {
        if (vehicle.isParked()) {
            double fee = vehicle.calculateParkingFee();
            boolean paymentSuccess = paymentSystem.processPayment(vehicle, fee);
            
            if (paymentSuccess) {
                ParkingSpot spot = vehicle.getCurrentSpot();
                vehicle.unpark();
                availableSpots++;
                System.out.println(vehicle.getVehicleType() + " " + vehicle.getLicensePlate() + 
                    " unparked from spot #" + spot.getSpotNumber());
                return true;
            }
        }
        return false;
    }
    
    private ParkingSpot findAvailableSpot(Vehicle vehicle) {
        // Find the best suitable spot for the vehicle
        for (ParkingSpot spot : spots.values()) {
            if (spot.isAvailable() && spot.canFitVehicle(vehicle)) {
                return spot;
            }
        }
        return null;
    }
    
    public void displayStatus() {
        System.out.println("\\n=== " + name + " Status ===");
        System.out.println("Total Spots: " + totalSpots);
        System.out.println("Available Spots: " + availableSpots);
        System.out.println("Occupied Spots: " + (totalSpots - availableSpots));
        
        System.out.println("\\nOccupied Spots:");
        for (ParkingSpot spot : spots.values()) {
            if (!spot.isAvailable()) {
                Vehicle vehicle = spot.getParkedVehicle();
                System.out.println("  Spot #" + spot.getSpotNumber() + " (" + spot.getSpotType() + "): " + 
                    vehicle.getVehicleType() + " " + vehicle.getLicensePlate());
            }
        }
    }
    
    public String getName() { return name; }
    public int getAvailableSpots() { return availableSpots; }
    public int getTotalSpots() { return totalSpots; }
}

// ==== MAIN APPLICATION ====

public class ParkingLotSystem {
    public static void main(String[] args) {
        System.out.println("=== OOP Parking Lot System Demo ===\\n");
        
        // Create parking lot
        ParkingLot parkingLot = new ParkingLot("Downtown Parking Garage");
        
        // Create different types of vehicles (POLYMORPHISM)
        List<Vehicle> vehicles = Arrays.asList(
            new Car("CAR001", 4),
            new Motorcycle("BIKE001", "Sport"),
            new Truck("TRUCK001", 5000.0),
            new ElectricCar("EV001", 4, 85.0),
            new Car("CAR002", 2),
            new ElectricCar("EV002", 4, 92.0)
        );
        
        System.out.println("Created " + vehicles.size() + " vehicles\\n");
        
        // Demonstrate parking (ABSTRACTION & ENCAPSULATION)
        System.out.println("=== PARKING VEHICLES ===");
        for (Vehicle vehicle : vehicles) {
            parkingLot.parkVehicle(vehicle);
        }
        
        parkingLot.displayStatus();
        
        // Demonstrate electric car charging (INTERFACE IMPLEMENTATION)
        System.out.println("\\n=== ELECTRIC CAR CHARGING ===");
        for (Vehicle vehicle : vehicles) {
            if (vehicle instanceof ElectricCar) {
                ElectricCar ev = (ElectricCar) vehicle;
                ev.startCharging();
            }
        }
        
        // Simulate some time passing and unpark some vehicles
        System.out.println("\\n=== UNPARKING VEHICLES ===");
        for (int i = 0; i < 3; i++) {
            if (i < vehicles.size()) {
                parkingLot.unparkVehicle(vehicles.get(i));
            }
        }
        
        parkingLot.displayStatus();
        
        // Demonstrate polymorphism - different fee calculations
        System.out.println("\\n=== PARKING FEE DEMONSTRATION (POLYMORPHISM) ===");
        for (Vehicle vehicle : vehicles) {
            if (vehicle.isParked()) {
                System.out.println(vehicle.getVehicleType() + " " + vehicle.getLicensePlate() + 
                    " - Fee: $" + String.format("%.2f", vehicle.calculateParkingFee()));
            }
        }
        
        System.out.println("\\n=== OOP PRINCIPLES DEMONSTRATED ===");
        System.out.println("1. ABSTRACTION: Vehicle and ParkingSpot abstract classes");
        System.out.println("2. ENCAPSULATION: Private fields with public methods in ParkingLot");
        System.out.println("3. INHERITANCE: Car, Motorcycle, Truck extend Vehicle");
        System.out.println("4. POLYMORPHISM: Different calculateParkingFee() implementations");
        System.out.println("5. INTERFACES: Parkable and Chargeable interfaces");
        
        System.out.println("\\nDemo completed successfully!");
    }
}`;
  };

  // Download function
  const downloadJavaCode = () => {
    const code = generateCompleteJavaCode();
    const blob = new Blob([code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'ParkingLotSystem.java';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Function to draw inheritance arrows
  const drawInheritanceArrow = (x1, y1, x2, y2) => (
    <g>
      <line
        x1={x1}
        y1={y1}
        x2={x2}
        y2={y2}
        stroke="#6b7280"
        strokeWidth="2"
        markerEnd="url(#inheritance-arrow)"
      />
    </g>
  );

  // Function to draw implementation arrows (dashed)
  const drawImplementationArrow = (x1, y1, x2, y2) => (
    <g>
      <line
        x1={x1}
        y1={y1}
        x2={x2}
        y2={y2}
        stroke="#6b7280"
        strokeWidth="2"
        strokeDasharray="5,5"
        markerEnd="url(#implementation-arrow)"
      />
    </g>
  );

  return (
    <div style={{ 
      padding: '20px',
      backgroundColor: '#0f172a',
      minHeight: '100vh',
      color: 'white'
    }}>
      {/* Header */}
      <div style={{ 
        textAlign: 'center', 
        marginBottom: '30px',
        borderBottom: '1px solid #374151',
        paddingBottom: '20px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '12px', marginBottom: '12px' }}>
          <Car size={28} color="#8b5cf6" />
          <h1 style={{ fontSize: '28px', fontWeight: 'bold', color: '#e2e8f0', margin: 0 }}>
            Parking Lot OOP Design System
          </h1>
        </div>
        <p style={{ color: '#94a3b8', fontSize: '14px', maxWidth: '600px', margin: '0 auto' }}>
          Interactive demonstration of Object-Oriented Programming principles
        </p>
      </div>

      {/* Horizontal OOP Concepts Bar */}
      <div style={{ 
        display: 'flex',
        gap: '16px',
        justifyContent: 'center',
        marginBottom: '30px',
        padding: '16px',
        backgroundColor: 'rgba(31, 41, 55, 0.5)',
        borderRadius: '12px',
        border: '1px solid #374151'
      }}>
        {oopConcepts.map((concept) => (
          <div
            key={concept.id}
            onClick={() => setSelectedConcept(concept.id)}
            style={{
              padding: '12px 20px',
              backgroundColor: selectedConcept === concept.id 
                ? `${concept.color}33` 
                : 'rgba(31, 41, 55, 0.3)',
              border: `2px solid ${selectedConcept === concept.id ? concept.color : '#374151'}`,
              borderRadius: '8px',
              cursor: 'pointer',
              transition: 'all 0.3s',
              minWidth: '140px',
              textAlign: 'center',
              boxShadow: selectedConcept === concept.id 
                ? `0 0 20px ${concept.color}66`
                : 'none'
            }}
          >
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              gap: '8px',
              marginBottom: '4px'
            }}>
              <div style={{ color: concept.color }}>{concept.icon}</div>
              <span style={{ 
                fontSize: '14px', 
                fontWeight: '600',
                color: selectedConcept === concept.id ? concept.color : '#e2e8f0'
              }}>
                {concept.title}
              </span>
            </div>
            <p style={{ 
              fontSize: '11px', 
              color: '#94a3b8',
              margin: 0
            }}>
              {concept.description}
            </p>
          </div>
        ))}
      </div>

      {/* Main Content Area */}
      <div style={{ display: 'flex', gap: '20px' }}>
        {/* Left: UML Diagram */}
        <div style={{ 
          flex: '1 1 60%',
          backgroundColor: '#1e293b',
          borderRadius: '12px',
          padding: '20px',
          border: '1px solid #374151'
        }}>
          <h3 style={{ 
            color: '#a78bfa', 
            fontSize: '16px',
            marginBottom: '20px',
            textAlign: 'center'
          }}>
            Interactive UML Class Diagram
          </h3>
          
          <svg 
            width="900" 
            height="600" 
            style={{ 
              backgroundColor: '#0f172a', 
              borderRadius: '8px',
              border: '1px solid #374151',
              width: '100%',
              cursor: isDragging ? 'grabbing' : 'default'
            }}
            viewBox="0 0 1200 700"
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
          >
            {/* Define arrow markers */}
            <defs>
              <marker
                id="inheritance-arrow"
                markerWidth="10"
                markerHeight="10"
                refX="9"
                refY="3"
                orient="auto"
                markerUnits="strokeWidth"
              >
                <path d="M0,0 L0,6 L9,3 z" fill="none" stroke="#6b7280" strokeWidth="1"/>
              </marker>
              <marker
                id="implementation-arrow"
                markerWidth="10"
                markerHeight="10"
                refX="9"
                refY="3"
                orient="auto"
                markerUnits="strokeWidth"
              >
                <path d="M0,0 L0,6 L9,3 z" fill="none" stroke="#6b7280" strokeWidth="1"/>
              </marker>
            </defs>

            {/* Draw relationships with dynamic positions */}
            {/* Parkable to Vehicle */}
            {(() => {
              const parkablePos = getClassPosition('parkable');
              const vehiclePos = getClassPosition('vehicle');
              return drawImplementationArrow(
                parkablePos.x + 70, parkablePos.y + 80,
                vehiclePos.x + 70, vehiclePos.y
              );
            })()}
            
            {/* Chargeable to ElectricCar */}
            {(() => {
              const chargeablePos = getClassPosition('chargeable');
              const electricPos = getClassPosition('electric-car');
              return drawImplementationArrow(
                chargeablePos.x + 70, chargeablePos.y + 80,
                electricPos.x + 70, electricPos.y
              );
            })()}
            
            {/* Vehicle to Car */}
            {(() => {
              const vehiclePos = getClassPosition('vehicle');
              const carPos = getClassPosition('car');
              return drawInheritanceArrow(
                vehiclePos.x + 30, vehiclePos.y + 100,
                carPos.x + 70, carPos.y
              );
            })()}
            
            {/* Vehicle to Motorcycle */}
            {(() => {
              const vehiclePos = getClassPosition('vehicle');
              const motoPos = getClassPosition('motorcycle');
              return drawInheritanceArrow(
                vehiclePos.x + 60, vehiclePos.y + 100,
                motoPos.x + 70, motoPos.y
              );
            })()}
            
            {/* Vehicle to Truck */}
            {(() => {
              const vehiclePos = getClassPosition('vehicle');
              const truckPos = getClassPosition('truck');
              return drawInheritanceArrow(
                vehiclePos.x + 90, vehiclePos.y + 100,
                truckPos.x + 70, truckPos.y
              );
            })()}
            
            {/* Vehicle to ElectricCar */}
            {(() => {
              const vehiclePos = getClassPosition('vehicle');
              const electricPos = getClassPosition('electric-car');
              return drawInheritanceArrow(
                vehiclePos.x + 120, vehiclePos.y + 100,
                electricPos.x + 30, electricPos.y
              );
            })()}
            
            {/* ParkingSpot to CompactSpot */}
            {(() => {
              const spotPos = getClassPosition('parking-spot');
              const compactPos = getClassPosition('compact-spot');
              return drawInheritanceArrow(
                spotPos.x + 30, spotPos.y + 100,
                compactPos.x + 70, compactPos.y
              );
            })()}
            
            {/* ParkingSpot to RegularSpot */}
            {(() => {
              const spotPos = getClassPosition('parking-spot');
              const regularPos = getClassPosition('regular-spot');
              return drawInheritanceArrow(
                spotPos.x + 60, spotPos.y + 100,
                regularPos.x + 70, regularPos.y
              );
            })()}
            
            {/* ParkingSpot to LargeSpot */}
            {(() => {
              const spotPos = getClassPosition('parking-spot');
              const largePos = getClassPosition('large-spot');
              return drawInheritanceArrow(
                spotPos.x + 90, spotPos.y + 100,
                largePos.x + 70, largePos.y
              );
            })()}
            
            {/* ParkingSpot to ElectricSpot */}
            {(() => {
              const spotPos = getClassPosition('parking-spot');
              const elecSpotPos = getClassPosition('electric-spot');
              return drawInheritanceArrow(
                spotPos.x + 120, spotPos.y + 100,
                elecSpotPos.x + 40, elecSpotPos.y
              );
            })()}

            {/* Draw UML Classes */}
            {umlClasses.map((cls) => {
              const isHighlighted = isClassHighlighted(cls.id);
              const strokeColor = isHighlighted ? 
                oopConcepts.find(c => c.id === selectedConcept)?.color : 
                '#374151';
              const fillColor = isHighlighted ? 
                `${oopConcepts.find(c => c.id === selectedConcept)?.color}22` : 
                '#1e293b';
              const position = getClassPosition(cls.id);

              return (
                <g 
                  key={cls.id}
                  onMouseDown={(e) => handleMouseDown(e, cls.id)}
                  onClick={() => !isDragging && setSelectedClass(cls)}
                  style={{ cursor: isDragging === cls.id ? 'grabbing' : 'grab' }}
                >
                  {/* Class box */}
                  <rect
                    x={position.x}
                    y={position.y}
                    width={140}
                    height={cls.attributes.length * 15 + cls.methods.length * 15 + 40}
                    fill={fillColor}
                    stroke={strokeColor}
                    strokeWidth={isHighlighted ? 3 : 1}
                    rx="4"
                    style={{
                      filter: isHighlighted ? `drop-shadow(0 0 10px ${strokeColor}66)` : 'none',
                      transition: 'all 0.3s'
                    }}
                  />
                  
                  {/* Class name header */}
                  <rect
                    x={position.x}
                    y={position.y}
                    width={140}
                    height={30}
                    fill={`${cls.color}33`}
                    stroke={strokeColor}
                    strokeWidth={isHighlighted ? 3 : 1}
                    rx="4"
                  />
                  
                  {/* Class type and name */}
                  <text
                    x={position.x + 70}
                    y={position.y + 20}
                    textAnchor="middle"
                    fill={cls.color}
                    fontSize="12"
                    fontWeight="bold"
                  >
                    {cls.type === 'interface' && '«interface»'}
                    {cls.type === 'abstract' && '«abstract»'}
                  </text>
                  <text
                    x={position.x + 70}
                    y={position.y + (cls.type !== 'class' ? 35 : 20)}
                    textAnchor="middle"
                    fill="#e2e8f0"
                    fontSize="13"
                    fontWeight="bold"
                  >
                    {cls.name}
                  </text>
                  
                  {/* Attributes */}
                  {cls.attributes.map((attr, idx) => (
                    <text
                      key={idx}
                      x={position.x + 5}
                      y={position.y + 50 + idx * 15}
                      fill="#94a3b8"
                      fontSize="10"
                    >
                      {attr}
                    </text>
                  ))}
                  
                  {/* Methods */}
                  {cls.methods.map((method, idx) => (
                    <text
                      key={idx}
                      x={position.x + 5}
                      y={position.y + 50 + cls.attributes.length * 15 + idx * 15}
                      fill="#60a5fa"
                      fontSize="10"
                    >
                      {method}
                    </text>
                  ))}
                </g>
              );
            })}
          </svg>
        </div>

        {/* Right: Tabbed Content */}
        <div style={{ 
          flex: '1 1 40%',
          backgroundColor: 'rgba(31, 41, 55, 0.5)',
          borderRadius: '12px',
          border: '1px solid #374151',
          display: 'flex',
          flexDirection: 'column'
        }}>
          {/* Tab Navigation */}
          <div style={{ 
            display: 'flex',
            borderBottom: '1px solid #374151'
          }}>
            {[
              { id: 'concepts', label: 'Concepts', icon: <Layers size={16} /> },
              { id: 'code', label: 'Complete Code', icon: <Code size={16} /> }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                style={{
                  flex: 1,
                  padding: '16px',
                  backgroundColor: activeTab === tab.id ? 'rgba(139, 92, 246, 0.2)' : 'transparent',
                  border: 'none',
                  color: activeTab === tab.id ? '#a78bfa' : '#9ca3af',
                  fontSize: '14px',
                  fontWeight: '500',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px',
                  transition: 'all 0.2s',
                  borderBottom: activeTab === tab.id ? '2px solid #a78bfa' : '2px solid transparent'
                }}
              >
                {tab.icon}
                {tab.label}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div style={{ flex: 1, padding: '24px', overflowY: 'auto' }}>
            {activeTab === 'concepts' && (
              <>
                <div style={{ marginBottom: '20px' }}>
                  <h3 style={{ 
                    color: oopConcepts.find(c => c.id === selectedConcept)?.color,
                    fontSize: '20px',
                    marginBottom: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}>
                    {oopConcepts.find(c => c.id === selectedConcept)?.icon}
                    {oopConcepts.find(c => c.id === selectedConcept)?.title}
                  </h3>
                  <p style={{ color: '#e2e8f0', fontSize: '14px', lineHeight: '1.6' }}>
                    {oopConcepts.find(c => c.id === selectedConcept)?.details}
                  </p>
                </div>

                {/* Code Example */}
                <div>
                  <h4 style={{ color: '#94a3b8', fontSize: '14px', marginBottom: '12px' }}>
                    Example Implementation
                  </h4>
                  <SyntaxHighlighter
                    language="java"
                    style={oneDark}
                    customStyle={{
                      borderRadius: '8px',
                      fontSize: '12px',
                      maxHeight: '400px'
                    }}
                  >
                    {selectedConcept === 'abstraction' && `// Abstract Vehicle class
public abstract class Vehicle {
    protected String licensePlate;
    protected LocalDateTime entryTime;
    
    // Abstract methods
    public abstract double calculateParkingFee();
    public abstract boolean canFitInSpot(ParkingSpot spot);
}

// Interface for parkable entities
public interface Parkable {
    boolean park(ParkingSpot spot);
    boolean unpark();
    boolean isParked();
}`}
                    {selectedConcept === 'encapsulation' && `// Encapsulation example
public class ParkingLot {
    // Private fields
    private String name;
    private Map<Integer, ParkingFloor> floors;
    private int availableSpots;
    
    // Public methods for controlled access
    public boolean parkVehicle(Vehicle vehicle) {
        // Implementation hidden
        return findAvailableSpot(vehicle);
    }
    
    // Private helper methods
    private boolean findAvailableSpot(Vehicle v) {
        // Internal logic hidden from outside
    }
}`}
                    {selectedConcept === 'inheritance' && `// Inheritance hierarchy
public class Car extends Vehicle {
    private int doors;
    
    @Override
    public double calculateParkingFee() {
        return hours * 5.0; // $5 per hour
    }
}

public class ElectricCar extends Car 
    implements Chargeable {
    private double batteryLevel;
    
    @Override
    public double calculateParkingFee() {
        return super.calculateParkingFee() * 0.8;
    }
    
    public void startCharging() {
        // Electric-specific behavior
    }
}`}
                    {selectedConcept === 'polymorphism' && `// Polymorphic behavior
List<Vehicle> vehicles = Arrays.asList(
    new Car("ABC123"),
    new Motorcycle("XYZ789"),
    new Truck("TRK456"),
    new ElectricCar("EV001")
);

// Same method, different behavior
for (Vehicle vehicle : vehicles) {
    double fee = vehicle.calculateParkingFee();
    boolean canPark = vehicle.canFitInSpot(spot);
    // Each vehicle type calculates differently
}`}
                  </SyntaxHighlighter>
                </div>

                {/* Selected Class Details */}
                {selectedClass && (
                  <div style={{ 
                    marginTop: '20px',
                    padding: '16px',
                    backgroundColor: 'rgba(139, 92, 246, 0.1)',
                    borderRadius: '8px',
                    border: '1px solid rgba(139, 92, 246, 0.3)'
                  }}>
                    <h4 style={{ color: '#a78bfa', fontSize: '14px', marginBottom: '8px' }}>
                      Selected: {selectedClass.name}
                    </h4>
                    <p style={{ color: '#e2e8f0', fontSize: '12px' }}>
                      Type: {selectedClass.type}
                    </p>
                    <p style={{ color: '#94a3b8', fontSize: '11px', marginTop: '8px' }}>
                      This class demonstrates {selectedConcept} through its structure and relationships.
                    </p>
                  </div>
                )}
              </>
            )}

            {activeTab === 'code' && (
              <>
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  alignItems: 'center',
                  marginBottom: '20px'
                }}>
                  <h3 style={{ color: '#8b5cf6', fontSize: '18px', margin: 0 }}>
                    Complete Parking Lot System
                  </h3>
                  <button
                    onClick={downloadJavaCode}
                    style={{
                      padding: '8px 16px',
                      backgroundColor: '#8b5cf6',
                      color: 'white',
                      border: 'none',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      fontSize: '12px',
                      fontWeight: '500',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '6px'
                    }}
                  >
                    <Package size={14} />
                    Download .java
                  </button>
                </div>

                <div style={{ 
                  padding: '16px',
                  backgroundColor: 'rgba(139, 92, 246, 0.1)',
                  borderRadius: '8px',
                  border: '1px solid rgba(139, 92, 246, 0.3)',
                  marginBottom: '16px'
                }}>
                  <h4 style={{ color: '#a78bfa', fontSize: '14px', marginBottom: '8px' }}>
                    📋 Instructions
                  </h4>
                  <p style={{ color: '#e2e8f0', fontSize: '12px', margin: 0 }}>
                    1. Click "Download .java" to get the complete source code<br/>
                    2. Compile: <code style={{ background: '#1e293b', padding: '2px 4px', borderRadius: '3px' }}>javac ParkingLotSystem.java</code><br/>
                    3. Run: <code style={{ background: '#1e293b', padding: '2px 4px', borderRadius: '3px' }}>java ParkingLotSystem</code>
                  </p>
                </div>

                <SyntaxHighlighter
                  language="java"
                  style={oneDark}
                  customStyle={{
                    borderRadius: '8px',
                    fontSize: '11px',
                    maxHeight: '600px'
                  }}
                >
                  {generateCompleteJavaCode()}
                </SyntaxHighlighter>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ParkingLotOOP;
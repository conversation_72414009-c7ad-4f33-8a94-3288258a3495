import React, { useState } from 'react';
import { Prism as <PERSON>ynta<PERSON><PERSON><PERSON>lighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { MessageSquare, Zap, Database, Settings, BarChart3, Shield } from 'lucide-react';

const Kafka = () => {
  const [selectedTopic, setSelectedTopic] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [detailsTab, setDetailsTab] = useState('definition');

  const topics = [
    {
      id: 'producers',
      name: 'Producers',
      icon: <Zap size={20} />,
      description: 'Publishing messages to Kafka topics'
    },
    {
      id: 'consumers',
      name: 'Consumers',
      icon: <Database size={20} />,
      description: 'Consuming messages from Kafka topics'
    },
    {
      id: 'streams',
      name: 'Kafka Streams',
      icon: <BarChart3 size={20} />,
      description: 'Stream processing applications'
    },
    {
      id: 'connect',
      name: 'Kafka Connect',
      icon: <Settings size={20} />,
      description: 'Data integration framework'
    },
    {
      id: 'configuration',
      name: 'Configuration',
      icon: <Settings size={20} />,
      description: 'Cluster and topic configuration'
    },
    {
      id: 'security',
      name: 'Security & Monitoring',
      icon: <Shield size={20} />,
      description: 'Security and operational monitoring'
    }
  ];

  const definitions = {
    'producers': `Kafka producers are client applications that publish messages to Kafka topics. In trading systems, producers send market data updates, order events, trade confirmations, and risk alerts. They support batching, compression, and exactly-once semantics for reliable message delivery.`,
    'consumers': `Kafka consumers read messages from topics and process them. Trading systems use consumers for real-time order processing, risk calculations, settlement workflows, and regulatory reporting. Consumer groups enable horizontal scaling and fault tolerance.`,
    'streams': `Kafka Streams is a library for building real-time stream processing applications. It enables complex event processing, windowing operations, and stateful computations on trading data streams, perfect for algorithmic trading and real-time analytics.`,
    'connect': `Kafka Connect is a framework for streaming data between Kafka and external systems. In trading environments, it integrates with databases, market data feeds, order management systems, and regulatory reporting platforms.`,
    'configuration': `Proper Kafka configuration is crucial for trading systems requiring low latency and high throughput. This includes broker settings, topic partitioning strategies, replication factors, and performance tuning for financial workloads.`,
    'security': `Kafka security includes authentication, authorization, and encryption. Trading systems require strict access controls, audit logging, and compliance monitoring to meet financial regulations and protect sensitive trading data.`
  };

  const codeExamples = {
    'producers': `// Kafka Producer Configuration for Trading Systems
import org.apache.kafka.clients.producer.*;
import org.apache.kafka.common.serialization.StringSerializer;
import java.util.Properties;
import java.time.Instant;
import java.math.BigDecimal;

public class TradingDataProducer {
    private final Producer<String, String> producer;
    private static final String MARKET_DATA_TOPIC = "market-data";
    private static final String ORDER_EVENTS_TOPIC = "order-events";
    private static final String TRADE_CONFIRMATIONS_TOPIC = "trade-confirmations";
    
    public TradingDataProducer() {
        Properties props = new Properties();
        
        // Basic configuration
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "kafka1:9092,kafka2:9092,kafka3:9092");
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        
        // Performance and reliability settings for trading
        props.put(ProducerConfig.ACKS_CONFIG, "all"); // Wait for all replicas
        props.put(ProducerConfig.RETRIES_CONFIG, Integer.MAX_VALUE);
        props.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, true); // Exactly-once semantics
        props.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, 1); // Ordering guarantee
        
        // Latency optimization
        props.put(ProducerConfig.LINGER_MS_CONFIG, 1); // Low latency
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384); // Batch size in bytes
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 33554432); // 32MB buffer
        
        // Compression for throughput
        props.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "lz4");
        
        this.producer = new KafkaProducer<>(props);
    }
    
    public void publishMarketData(String symbol, BigDecimal price, long volume, Instant timestamp) {
        try {
            // Create market data message
            String marketDataJson = String.format(
                "{\\"symbol\\": \\"%s\\", \\"price\\": %s, \\"volume\\": %d, \\"timestamp\\": \\"%s\\", \\"type\\": \\"TICK\\"}",
                symbol, price.toString(), volume, timestamp.toString()
            );
            
            // Use symbol as partition key for ordering
            ProducerRecord<String, String> record = new ProducerRecord<>(
                MARKET_DATA_TOPIC, 
                symbol, // Key for partitioning
                marketDataJson
            );
            
            // Add headers for metadata
            record.headers().add("source", "market-data-feed".getBytes());
            record.headers().add("version", "1.0".getBytes());
            
            // Send with callback for monitoring
            producer.send(record, new Callback() {
                @Override
                public void onCompletion(RecordMetadata metadata, Exception exception) {
                    if (exception != null) {
                        // Log error and potentially retry
                        System.err.printf("Failed to send market data for %s: %s%n", 
                                        symbol, exception.getMessage());
                        // In production: send to error topic or dead letter queue
                    } else {
                        // Success metrics
                        System.out.printf("Market data sent: topic=%s, partition=%d, offset=%d%n",
                                        metadata.topic(), metadata.partition(), metadata.offset());
                    }
                }
            });
            
        } catch (Exception e) {
            System.err.println("Error publishing market data: " + e.getMessage());
        }
    }
    
    public void publishOrderEvent(String orderId, String traderId, String symbol, 
                                String side, BigDecimal quantity, BigDecimal price, String status) {
        try {
            String orderEventJson = String.format(
                "{\\"orderId\\": \\"%s\\", \\"traderId\\": \\"%s\\", \\"symbol\\": \\"%s\\", " +
                "\\"side\\": \\"%s\\", \\"quantity\\": %s, \\"price\\": %s, \\"status\\": \\"%s\\", " +
                "\\"timestamp\\": \\"%s\\"}",
                orderId, traderId, symbol, side, quantity.toString(), price.toString(), 
                status, Instant.now().toString()
            );
            
            ProducerRecord<String, String> record = new ProducerRecord<>(
                ORDER_EVENTS_TOPIC,
                orderId, // Use orderId as key for ordering
                orderEventJson
            );
            
            // Priority header for urgent orders
            if ("MARKET".equals(side)) {
                record.headers().add("priority", "HIGH".getBytes());
            }
            
            producer.send(record);
            
        } catch (Exception e) {
            System.err.println("Error publishing order event: " + e.getMessage());
        }
    }
    
    public void publishTradeConfirmation(String tradeId, String buyOrderId, String sellOrderId,
                                       String symbol, BigDecimal executedPrice, BigDecimal executedQuantity) {
        try {
            String tradeJson = String.format(
                "{\\"tradeId\\": \\"%s\\", \\"buyOrderId\\": \\"%s\\", \\"sellOrderId\\": \\"%s\\", " +
                "\\"symbol\\": \\"%s\\", \\"executedPrice\\": %s, \\"executedQuantity\\": %s, " +
                "\\"executionTime\\": \\"%s\\", \\"status\\": \\"CONFIRMED\\"}",
                tradeId, buyOrderId, sellOrderId, symbol, executedPrice.toString(), 
                executedQuantity.toString(), Instant.now().toString()
            );
            
            ProducerRecord<String, String> record = new ProducerRecord<>(
                TRADE_CONFIRMATIONS_TOPIC,
                tradeId,
                tradeJson
            );
            
            // Send synchronously for trade confirmations (critical)
            RecordMetadata metadata = producer.send(record).get();
            System.out.printf("Trade confirmation sent: %s at offset %d%n", 
                            tradeId, metadata.offset());
                            
        } catch (Exception e) {
            System.err.println("Critical error publishing trade confirmation: " + e.getMessage());
            // In production: immediately alert operations team
        }
    }
    
    public void close() {
        producer.flush(); // Ensure all messages are sent
        producer.close();
    }
}

// Transaction-aware producer for exactly-once semantics
public class TransactionalTradingProducer {
    private final Producer<String, String> producer;
    
    public TransactionalTradingProducer() {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "kafka1:9092,kafka2:9092,kafka3:9092");
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        
        // Transactional settings
        props.put(ProducerConfig.TRANSACTIONAL_ID_CONFIG, "trading-producer-" + System.currentTimeMillis());
        props.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, true);
        
        this.producer = new KafkaProducer<>(props);
        producer.initTransactions();
    }
    
    public void executeTradeWorkflow(String orderId, String symbol, BigDecimal price, BigDecimal quantity) {
        try {
            producer.beginTransaction();
            
            // 1. Update order status
            publishOrderEvent(orderId, "FILLED", symbol, price, quantity);
            
            // 2. Create trade record
            String tradeId = generateTradeId();
            publishTradeConfirmation(tradeId, orderId, symbol, price, quantity);
            
            // 3. Update position
            publishPositionUpdate(symbol, quantity);
            
            producer.commitTransaction();
            System.out.println("Trade workflow completed successfully");
            
        } catch (Exception e) {
            producer.abortTransaction();
            System.err.println("Trade workflow failed, transaction aborted: " + e.getMessage());
        }
    }
}`,

    'consumers': `// Kafka Consumer Configuration for Trading Systems
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.TopicPartition;
import java.util.*;
import java.time.Duration;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class TradingDataConsumer {
    private final Consumer<String, String> consumer;
    private final ExecutorService executorService;
    private volatile boolean running = true;
    
    public TradingDataConsumer(String groupId, List<String> topics) {
        Properties props = new Properties();
        
        // Basic configuration
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, "kafka1:9092,kafka2:9092,kafka3:9092");
        props.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        
        // Performance settings for trading
        props.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, 1); // Low latency
        props.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, 10); // Max wait 10ms
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 1000); // Batch size
        
        // Offset management
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false); // Manual commit for reliability
        
        // Session and heartbeat settings
        props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 30000);
        props.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, 10000);
        
        this.consumer = new KafkaConsumer<>(props);
        this.executorService = Executors.newSingleThreadExecutor();
        
        consumer.subscribe(topics);
    }
    
    public void startConsuming() {
        executorService.submit(() -> {
            try {
                while (running) {
                    ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(100));
                    
                    if (records.isEmpty()) {
                        continue;
                    }
                    
                    // Process records by topic
                    for (TopicPartition partition : records.partitions()) {
                        List<ConsumerRecord<String, String>> partitionRecords = records.records(partition);
                        
                        switch (partition.topic()) {
                            case "market-data":
                                processMarketData(partitionRecords);
                                break;
                            case "order-events":
                                processOrderEvents(partitionRecords);
                                break;
                            case "trade-confirmations":
                                processTradeConfirmations(partitionRecords);
                                break;
                            default:
                                System.err.println("Unknown topic: " + partition.topic());
                        }
                    }
                    
                    // Manual commit after successful processing
                    try {
                        consumer.commitSync();
                    } catch (Exception e) {
                        System.err.println("Commit failed: " + e.getMessage());
                        // In production: implement retry logic
                    }
                }
            } catch (Exception e) {
                System.err.println("Consumer error: " + e.getMessage());
            } finally {
                consumer.close();
            }
        });
    }
    
    private void processMarketData(List<ConsumerRecord<String, String>> records) {
        for (ConsumerRecord<String, String> record : records) {
            try {
                String symbol = record.key();
                String marketDataJson = record.value();
                
                // Parse JSON (using Jackson or similar in production)
                System.out.printf("Processing market data for %s: %s%n", symbol, marketDataJson);
                
                // Update in-memory price cache
                updatePriceCache(symbol, marketDataJson);
                
                // Trigger risk calculations
                triggerRiskCalculation(symbol);
                
                // Check for algorithmic trading signals
                checkTradingSignals(symbol, marketDataJson);
                
            } catch (Exception e) {
                System.err.printf("Error processing market data record: %s%n", e.getMessage());
                // In production: send to dead letter topic
            }
        }
    }
    
    private void processOrderEvents(List<ConsumerRecord<String, String>> records) {
        for (ConsumerRecord<String, String> record : records) {
            try {
                String orderId = record.key();
                String orderEventJson = record.value();
                
                System.out.printf("Processing order event for %s: %s%n", orderId, orderEventJson);
                
                // Update order status in database
                updateOrderStatus(orderId, orderEventJson);
                
                // Notify risk management
                notifyRiskManagement(orderEventJson);
                
                // Update position calculations
                updatePositions(orderEventJson);
                
            } catch (Exception e) {
                System.err.printf("Error processing order event: %s%n", e.getMessage());
            }
        }
    }
    
    private void processTradeConfirmations(List<ConsumerRecord<String, String>> records) {
        for (ConsumerRecord<String, String> record : records) {
            try {
                String tradeId = record.key();
                String tradeJson = record.value();
                
                System.out.printf("Processing trade confirmation %s: %s%n", tradeId, tradeJson);
                
                // Store trade in database
                storeTrade(tradeId, tradeJson);
                
                // Update settlement queue
                addToSettlementQueue(tradeId, tradeJson);
                
                // Generate regulatory reports
                generateRegulatoryReport(tradeJson);
                
            } catch (Exception e) {
                System.err.printf("Critical error processing trade confirmation: %s%n", e.getMessage());
                // In production: immediately alert operations
            }
        }
    }
    
    // Utility methods (implementation depends on your system)
    private void updatePriceCache(String symbol, String marketDataJson) {
        // Update real-time price cache
    }
    
    private void triggerRiskCalculation(String symbol) {
        // Trigger VaR/CVaR calculations
    }
    
    private void checkTradingSignals(String symbol, String marketDataJson) {
        // Check algorithmic trading signals
    }
    
    private void updateOrderStatus(String orderId, String orderEventJson) {
        // Update order in database
    }
    
    private void notifyRiskManagement(String orderEventJson) {
        // Send risk notifications
    }
    
    private void updatePositions(String orderEventJson) {
        // Update position calculations
    }
    
    private void storeTrade(String tradeId, String tradeJson) {
        // Store trade in database
    }
    
    private void addToSettlementQueue(String tradeId, String tradeJson) {
        // Add to settlement processing
    }
    
    private void generateRegulatoryReport(String tradeJson) {
        // Generate required regulatory reports
    }
    
    public void shutdown() {
        running = false;
        executorService.shutdown();
    }
}

// High-performance consumer with manual partition assignment
public class HighPerformanceMarketDataConsumer {
    private final Consumer<String, String> consumer;
    
    public HighPerformanceMarketDataConsumer() {
        Properties props = new Properties();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, "kafka1:9092,kafka2:9092,kafka3:9092");
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        
        // No group ID for manual assignment
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        props.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, 1);
        props.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, 1); // Ultra-low latency
        
        this.consumer = new KafkaConsumer<>(props);
        
        // Manual partition assignment for specific symbols
        List<TopicPartition> partitions = Arrays.asList(
            new TopicPartition("market-data", 0), // AAPL partition
            new TopicPartition("market-data", 1), // GOOGL partition
            new TopicPartition("market-data", 2)  // MSFT partition
        );
        consumer.assign(partitions);
    }
    
    public void consumeWithLowLatency() {
        while (true) {
            ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(1));
            
            for (ConsumerRecord<String, String> record : records) {
                // Ultra-fast processing
                processMarketDataFast(record);
            }
            
            // Commit every 100 messages or 10ms, whichever comes first
            consumer.commitAsync();
        }
    }
    
    private void processMarketDataFast(ConsumerRecord<String, String> record) {
        // Minimal processing for ultra-low latency
        long timestamp = System.nanoTime();
        // Process market data...
        long processingTime = System.nanoTime() - timestamp;
        
        if (processingTime > 1_000_000) { // Alert if processing takes > 1ms
            System.err.printf("Slow processing detected: %d ns%n", processingTime);
        }
    }
}`,

    'streams': `// Kafka Streams for Trading System Analytics
import org.apache.kafka.common.serialization.Serdes;
import org.apache.kafka.streams.KafkaStreams;
import org.apache.kafka.streams.StreamsBuilder;
import org.apache.kafka.streams.StreamsConfig;
import org.apache.kafka.streams.kstream.*;
import org.apache.kafka.streams.state.Stores;
import java.time.Duration;
import java.util.Properties;

public class TradingStreamsProcessor {
    
    public static void main(String[] args) {
        Properties props = new Properties();
        props.put(StreamsConfig.APPLICATION_ID_CONFIG, "trading-streams-processor");
        props.put(StreamsConfig.BOOTSTRAP_SERVERS_CONFIG, "kafka1:9092,kafka2:9092,kafka3:9092");
        props.put(StreamsConfig.DEFAULT_KEY_SERDE_CLASS_CONFIG, Serdes.String().getClass());
        props.put(StreamsConfig.DEFAULT_VALUE_SERDE_CLASS_CONFIG, Serdes.String().getClass());
        
        // Performance settings for trading
        props.put(StreamsConfig.COMMIT_INTERVAL_MS_CONFIG, 100); // Fast commits
        props.put(StreamsConfig.CACHE_MAX_BYTES_BUFFERING_CONFIG, 0); // Disable caching for real-time
        props.put(StreamsConfig.NUM_STREAM_THREADS_CONFIG, 4); // Parallel processing
        
        StreamsBuilder builder = new StreamsBuilder();
        
        // Build trading analytics topology
        buildMarketDataAnalytics(builder);
        buildOrderFlowAnalytics(builder);
        buildRiskAnalytics(builder);
        buildTradingSignals(builder);
        
        KafkaStreams streams = new KafkaStreams(builder.build(), props);
        
        // Start the streams application
        streams.start();
        
        // Shutdown hook
        Runtime.getRuntime().addShutdownHook(new Thread(streams::close));
    }
    
    private static void buildMarketDataAnalytics(StreamsBuilder builder) {
        // Stream market data and calculate VWAP
        KStream<String, String> marketData = builder.stream("market-data");
        
        // Parse market data and calculate Volume Weighted Average Price
        KTable<Windowed<String>, String> vwapTable = marketData
            .selectKey((key, value) -> extractSymbol(value)) // Re-key by symbol
            .filter((key, value) -> isValidMarketData(value))
            .groupByKey()
            .windowedBy(TimeWindows.of(Duration.ofMinutes(1)).advanceBy(Duration.ofSeconds(1)))
            .aggregate(
                () -> new VWAPAccumulator(0.0, 0L).toJson(), // Initial value
                (key, value, aggregate) -> {
                    VWAPAccumulator acc = VWAPAccumulator.fromJson(aggregate);
                    MarketData md = MarketData.fromJson(value);
                    
                    double newTotalValue = acc.totalValue + (md.price * md.volume);
                    long newTotalVolume = acc.totalVolume + md.volume;
                    double newVWAP = newTotalVolume > 0 ? newTotalValue / newTotalVolume : 0.0;
                    
                    return new VWAPAccumulator(newTotalValue, newTotalVolume, newVWAP).toJson();
                },
                Materialized.<String, String>as(Stores.inMemoryWindowStore("vwap-store", 
                    Duration.ofHours(1), Duration.ofMinutes(1), false))
                    .withKeySerde(Serdes.String())
                    .withValueSerde(Serdes.String())
            );
        
        // Output VWAP to topic
        vwapTable.toStream()
            .map((windowedKey, value) -> KeyValue.pair(
                windowedKey.key(), 
                String.format("{\\"symbol\\": \\"%s\\", \\"vwap\\": %s, \\"window\\": \\"%s\\"}",
                    windowedKey.key(), extractVWAP(value), windowedKey.window().toString())
            ))
            .to("market-analytics-vwap");
        
        // Calculate price changes and volatility
        marketData
            .selectKey((key, value) -> extractSymbol(value))
            .groupByKey()
            .windowedBy(TimeWindows.of(Duration.ofMinutes(5)))
            .aggregate(
                () -> "{}",
                (key, value, aggregate) -> calculatePriceStats(aggregate, value),
                Materialized.with(Serdes.String(), Serdes.String())
            )
            .toStream()
            .to("price-volatility");
    }
    
    private static void buildOrderFlowAnalytics(StreamsBuilder builder) {
        KStream<String, String> orderEvents = builder.stream("order-events");
        
        // Calculate order flow imbalance
        KTable<Windowed<String>, String> orderFlowImbalance = orderEvents
            .selectKey((key, value) -> extractSymbolFromOrder(value))
            .filter((key, value) -> isNewOrder(value))
            .groupByKey()
            .windowedBy(TimeWindows.of(Duration.ofSeconds(30)))
            .aggregate(
                () -> "{\\"buyVolume\\": 0, \\"sellVolume\\": 0}",
                (key, value, aggregate) -> {
                    OrderFlowAccumulator acc = OrderFlowAccumulator.fromJson(aggregate);
                    OrderEvent order = OrderEvent.fromJson(value);
                    
                    if ("BUY".equals(order.side)) {
                        acc.buyVolume += order.quantity;
                    } else {
                        acc.sellVolume += order.quantity;
                    }
                    
                    double imbalance = (acc.buyVolume - acc.sellVolume) / (acc.buyVolume + acc.sellVolume);
                    acc.imbalance = imbalance;
                    
                    return acc.toJson();
                },
                Materialized.with(Serdes.String(), Serdes.String())
            );
        
        orderFlowImbalance.toStream().to("order-flow-analytics");
    }
    
    private static void buildRiskAnalytics(StreamsBuilder builder) {
        KStream<String, String> trades = builder.stream("trade-confirmations");
        KStream<String, String> positions = builder.stream("position-updates");
        
        // Join trades with current positions for P&L calculation
        KStream<String, String> pnlStream = trades
            .selectKey((key, value) -> extractTraderIdFromTrade(value))
            .join(
                positions.selectKey((key, value) -> extractTraderIdFromPosition(value)),
                (trade, position) -> calculatePnL(trade, position),
                JoinWindows.of(Duration.ofSeconds(5))
            );
        
        // Calculate running P&L by trader
        KTable<String, String> runningPnL = pnlStream
            .groupByKey()
            .aggregate(
                () -> "{\\"totalPnL\\": 0.0, \\"dayPnL\\": 0.0}",
                (key, value, aggregate) -> updateRunningPnL(aggregate, value),
                Materialized.with(Serdes.String(), Serdes.String())
            );
        
        // Risk alerts for P&L limits
        runningPnL.toStream()
            .filter((trader, pnl) -> exceedsRiskLimit(trader, pnl))
            .mapValues(pnl -> createRiskAlert(pnl))
            .to("risk-alerts");
    }
    
    private static void buildTradingSignals(StreamsBuilder builder) {
        KStream<String, String> marketData = builder.stream("market-data");
        
        // Moving average crossover signals
        KTable<String, String> shortMA = marketData
            .selectKey((key, value) -> extractSymbol(value))
            .groupByKey()
            .windowedBy(TimeWindows.of(Duration.ofMinutes(5)))
            .aggregate(
                () -> new MovingAverage(20).toJson(),
                (key, value, aggregate) -> {
                    MovingAverage ma = MovingAverage.fromJson(aggregate);
                    double price = extractPrice(value);
                    ma.addPrice(price);
                    return ma.toJson();
                },
                Materialized.with(Serdes.String(), Serdes.String())
            );
        
        KTable<String, String> longMA = marketData
            .selectKey((key, value) -> extractSymbol(value))
            .groupByKey()
            .windowedBy(TimeWindows.of(Duration.ofMinutes(20)))
            .aggregate(
                () -> new MovingAverage(100).toJson(),
                (key, value, aggregate) -> {
                    MovingAverage ma = MovingAverage.fromJson(aggregate);
                    double price = extractPrice(value);
                    ma.addPrice(price);
                    return ma.toJson();
                },
                Materialized.with(Serdes.String(), Serdes.String())
            );
        
        // Join short and long MA to generate crossover signals
        shortMA.toStream()
            .selectKey((windowedKey, value) -> windowedKey.key())
            .join(
                longMA.toStream().selectKey((windowedKey, value) -> windowedKey.key()),
                (shortMAValue, longMAValue) -> {
                    double shortAvg = MovingAverage.fromJson(shortMAValue).getAverage();
                    double longAvg = MovingAverage.fromJson(longMAValue).getAverage();
                    
                    if (shortAvg > longAvg) {
                        return "{\\"signal\\": \\"BUY\\", \\"shortMA\\": " + shortAvg + ", \\"longMA\\": " + longAvg + "}";
                    } else if (shortAvg < longAvg) {
                        return "{\\"signal\\": \\"SELL\\", \\"shortMA\\": " + shortAvg + ", \\"longMA\\": " + longAvg + "}";
                    }
                    return "{\\"signal\\": \\"HOLD\\", \\"shortMA\\": " + shortAvg + ", \\"longMA\\": " + longAvg + "}";
                },
                JoinWindows.of(Duration.ofSeconds(1))
            )
            .filter((key, value) -> !value.contains("HOLD"))
            .to("trading-signals");
    }
    
    // Helper methods (implementations would be in separate classes)
    private static String extractSymbol(String marketDataJson) {
        // Parse JSON and extract symbol
        return "AAPL"; // Placeholder
    }
    
    private static boolean isValidMarketData(String value) {
        // Validate market data
        return true; // Placeholder
    }
    
    private static String calculatePriceStats(String aggregate, String newValue) {
        // Calculate price statistics
        return "{}"; // Placeholder
    }
    
    private static String extractSymbolFromOrder(String orderJson) {
        return "AAPL"; // Placeholder
    }
    
    private static boolean isNewOrder(String orderJson) {
        return true; // Placeholder
    }
    
    private static String extractTraderIdFromTrade(String tradeJson) {
        return "trader1"; // Placeholder
    }
    
    private static String extractTraderIdFromPosition(String positionJson) {
        return "trader1"; // Placeholder
    }
    
    private static String calculatePnL(String trade, String position) {
        return "{}"; // Placeholder
    }
    
    private static String updateRunningPnL(String aggregate, String pnlUpdate) {
        return "{}"; // Placeholder
    }
    
    private static boolean exceedsRiskLimit(String trader, String pnl) {
        return false; // Placeholder
    }
    
    private static String createRiskAlert(String pnl) {
        return "{}"; // Placeholder
    }
    
    private static double extractPrice(String marketDataJson) {
        return 100.0; // Placeholder
    }
    
    private static String extractVWAP(String vwapJson) {
        return "100.0"; // Placeholder
    }
}

// Supporting classes
class VWAPAccumulator {
    public double totalValue;
    public long totalVolume;
    public double vwap;
    
    public VWAPAccumulator(double totalValue, long totalVolume) {
        this.totalValue = totalValue;
        this.totalVolume = totalVolume;
        this.vwap = totalVolume > 0 ? totalValue / totalVolume : 0.0;
    }
    
    public VWAPAccumulator(double totalValue, long totalVolume, double vwap) {
        this.totalValue = totalValue;
        this.totalVolume = totalVolume;
        this.vwap = vwap;
    }
    
    public String toJson() {
        return String.format("{\\"totalValue\\": %f, \\"totalVolume\\": %d, \\"vwap\\": %f}", 
                           totalValue, totalVolume, vwap);
    }
    
    public static VWAPAccumulator fromJson(String json) {
        // Parse JSON and create VWAPAccumulator
        return new VWAPAccumulator(0.0, 0L); // Placeholder
    }
}`,

    'connect': `// Kafka Connect Configuration for Trading Systems
// Database Source Connector Configuration (JSON)
{
  "name": "trading-database-source",
  "config": {
    "connector.class": "io.confluent.connect.jdbc.JdbcSourceConnector",
    "connection.url": "************************************************",
    "connection.user": "kafka_connect_user",
    "connection.password": "secure_password",
    "topic.prefix": "db-",
    "poll.interval.ms": 1000,
    "batch.max.rows": 1000,
    
    // Tables to monitor
    "table.whitelist": "orders,trades,positions,market_data",
    
    // Incremental loading
    "mode": "incrementing",
    "incrementing.column.name": "updated_at",
    
    // Transform configurations
    "transforms": "addTimestamp,addSource",
    "transforms.addTimestamp.type": "org.apache.kafka.connect.transforms.InsertField$Value",
    "transforms.addTimestamp.timestamp.field": "kafka_timestamp",
    "transforms.addSource.type": "org.apache.kafka.connect.transforms.InsertField$Value",
    "transforms.addSource.static.field": "source_system",
    "transforms.addSource.static.value": "trading_database"
  }
}

// Market Data Feed Connector (Custom)
import org.apache.kafka.connect.source.SourceConnector;
import org.apache.kafka.connect.source.SourceTask;
import org.apache.kafka.connect.connector.Task;
import org.apache.kafka.common.config.ConfigDef;
import java.util.List;
import java.util.Map;

public class MarketDataFeedConnector extends SourceConnector {
    private Map<String, String> configProps;
    
    @Override
    public void start(Map<String, String> props) {
        this.configProps = props;
    }
    
    @Override
    public Class<? extends Task> taskClass() {
        return MarketDataFeedTask.class;
    }
    
    @Override
    public List<Map<String, String>> taskConfigs(int maxTasks) {
        // Configure tasks for parallel processing
        List<Map<String, String>> configs = new ArrayList<>();
        
        // Split symbols across tasks for parallel processing
        String[] symbols = configProps.get("symbols").split(",");
        int symbolsPerTask = symbols.length / maxTasks;
        
        for (int i = 0; i < maxTasks; i++) {
            Map<String, String> taskConfig = new HashMap<>(configProps);
            
            int start = i * symbolsPerTask;
            int end = (i == maxTasks - 1) ? symbols.length : (i + 1) * symbolsPerTask;
            
            String taskSymbols = String.join(",", 
                Arrays.copyOfRange(symbols, start, end));
            taskConfig.put("task.symbols", taskSymbols);
            taskConfig.put("task.id", String.valueOf(i));
            
            configs.add(taskConfig);
        }
        
        return configs;
    }
    
    @Override
    public void stop() {
        // Cleanup resources
    }
    
    @Override
    public ConfigDef config() {
        return new ConfigDef()
            .define("feed.url", ConfigDef.Type.STRING, ConfigDef.Importance.HIGH,
                   "Market data feed URL")
            .define("symbols", ConfigDef.Type.STRING, ConfigDef.Importance.HIGH,
                   "Comma-separated list of symbols to subscribe to")
            .define("api.key", ConfigDef.Type.PASSWORD, ConfigDef.Importance.HIGH,
                   "API key for market data feed")
            .define("topic.prefix", ConfigDef.Type.STRING, "market-data",
                   ConfigDef.Importance.MEDIUM, "Topic prefix for market data")
            .define("poll.interval.ms", ConfigDef.Type.INT, 100,
                   ConfigDef.Importance.MEDIUM, "Polling interval in milliseconds");
    }
    
    @Override
    public String version() {
        return "1.0.0";
    }
}

// Market Data Feed Task
import org.apache.kafka.connect.source.SourceRecord;
import org.apache.kafka.connect.source.SourceTask;
import org.apache.kafka.connect.data.Schema;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

public class MarketDataFeedTask extends SourceTask {
    private MarketDataClient marketDataClient;
    private BlockingQueue<MarketDataTick> tickQueue;
    private String[] symbols;
    private String topicPrefix;
    private volatile boolean running = false;
    
    @Override
    public String version() {
        return "1.0.0";
    }
    
    @Override
    public void start(Map<String, String> props) {
        String feedUrl = props.get("feed.url");
        String apiKey = props.get("api.key");
        this.symbols = props.get("task.symbols").split(",");
        this.topicPrefix = props.get("topic.prefix");
        
        this.tickQueue = new LinkedBlockingQueue<>(10000);
        this.marketDataClient = new MarketDataClient(feedUrl, apiKey);
        
        // Start market data client
        marketDataClient.subscribe(Arrays.asList(symbols), tick -> {
            try {
                tickQueue.offer(tick, 1, TimeUnit.MILLISECONDS);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
        
        this.running = true;
    }
    
    @Override
    public List<SourceRecord> poll() throws InterruptedException {
        List<SourceRecord> records = new ArrayList<>();
        List<MarketDataTick> ticks = new ArrayList<>();
        
        // Drain available ticks (up to 1000)
        tickQueue.drainTo(ticks, 1000);
        
        for (MarketDataTick tick : ticks) {
            // Create source record
            Map<String, Object> sourcePartition = Collections.singletonMap("symbol", tick.symbol);
            Map<String, Object> sourceOffset = Collections.singletonMap("timestamp", tick.timestamp);
            
            String topic = topicPrefix + "-" + tick.symbol.toLowerCase();
            
            // Create JSON value
            String value = String.format(
                "{\\"symbol\\": \\"%s\\", \\"price\\": %f, \\"volume\\": %d, " +
                "\\"bid\\": %f, \\"ask\\": %f, \\"timestamp\\": \\"%s\\"}",
                tick.symbol, tick.price, tick.volume, tick.bid, tick.ask, 
                Instant.ofEpochMilli(tick.timestamp).toString()
            );
            
            SourceRecord record = new SourceRecord(
                sourcePartition,
                sourceOffset,
                topic,
                null, // partition will be determined by Kafka
                Schema.STRING_SCHEMA,
                tick.symbol,
                Schema.STRING_SCHEMA,
                value,
                tick.timestamp
            );
            
            records.add(record);
        }
        
        if (records.isEmpty() && running) {
            // Sleep briefly if no data available
            Thread.sleep(10);
        }
        
        return records;
    }
    
    @Override
    public void stop() {
        running = false;
        if (marketDataClient != null) {
            marketDataClient.close();
        }
    }
}

// Sink Connector for Trade Reporting
{
  "name": "trade-reporting-sink",
  "config": {
    "connector.class": "io.confluent.connect.jdbc.JdbcSinkConnector",
    "connection.url": "***************************************************",
    "connection.user": "reporting_user",
    "connection.password": "reporting_password",
    
    "topics": "trade-confirmations,order-events,position-updates",
    
    // Table creation and management
    "auto.create": true,
    "auto.evolve": true,
    "insert.mode": "insert",
    
    // Batch settings for performance
    "batch.size": 1000,
    "max.retries": 3,
    "retry.backoff.ms": 5000,
    
    // Transform for flattening JSON
    "transforms": "flatten,addReportingTimestamp",
    "transforms.flatten.type": "org.apache.kafka.connect.transforms.Flatten$Value",
    "transforms.addReportingTimestamp.type": "org.apache.kafka.connect.transforms.InsertField$Value",
    "transforms.addReportingTimestamp.timestamp.field": "reported_at"
  }
}

// Custom SMT (Single Message Transform) for Trading Data
import org.apache.kafka.connect.transforms.Transformation;
import org.apache.kafka.connect.connector.ConnectRecord;
import org.apache.kafka.common.config.ConfigDef;

public class TradingDataEnricher<R extends ConnectRecord<R>> implements Transformation<R> {
    
    @Override
    public R apply(R record) {
        if (record.value() == null) {
            return record;
        }
        
        // Parse the trading message
        String originalValue = record.value().toString();
        
        // Enrich with additional metadata
        String enrichedValue = enrichTradingData(originalValue);
        
        return record.newRecord(
            record.topic(),
            record.kafkaPartition(),
            record.keySchema(),
            record.key(),
            record.valueSchema(),
            enrichedValue,
            record.timestamp()
        );
    }
    
    private String enrichTradingData(String originalValue) {
        try {
            // Parse JSON and add enrichments
            ObjectMapper mapper = new ObjectMapper();
            JsonNode originalJson = mapper.readTree(originalValue);
            
            ObjectNode enrichedJson = (ObjectNode) originalJson;
            
            // Add trading session information
            enrichedJson.put("trading_session", getCurrentTradingSession());
            
            // Add market status
            enrichedJson.put("market_status", getMarketStatus());
            
            // Add regulatory flags
            if (originalJson.has("symbol")) {
                String symbol = originalJson.get("symbol").asText();
                enrichedJson.put("requires_reporting", requiresRegularReporting(symbol));
            }
            
            return mapper.writeValueAsString(enrichedJson);
            
        } catch (Exception e) {
            // Log error and return original value
            System.err.println("Error enriching trading data: " + e.getMessage());
            return originalValue;
        }
    }
    
    private String getCurrentTradingSession() {
        // Determine current trading session (PRE_MARKET, MARKET, POST_MARKET)
        return "MARKET"; // Placeholder
    }
    
    private String getMarketStatus() {
        // Get current market status (OPEN, CLOSED, HALTED)
        return "OPEN"; // Placeholder
    }
    
    private boolean requiresRegularReporting(String symbol) {
        // Check if symbol requires regulatory reporting
        return true; // Placeholder
    }
    
    @Override
    public ConfigDef config() {
        return new ConfigDef();
    }
    
    @Override
    public void close() {
        // Cleanup resources
    }
    
    @Override
    public void configure(Map<String, ?> configs) {
        // Configure transformation
    }
}`,

    'configuration': `# Kafka Configuration for Trading Systems

# Server Configuration (server.properties)
############################# Server Basics #############################
broker.id=1
listeners=PLAINTEXT://kafka1:9092,SSL://kafka1:9093
advertised.listeners=PLAINTEXT://kafka1:9092,SSL://kafka1:9093
num.network.threads=8
num.io.threads=16

############################# Socket Server Settings #############################
socket.send.buffer.bytes=102400
socket.receive.buffer.bytes=102400
socket.request.max.bytes=104857600

############################# Log Basics #############################
log.dirs=/var/kafka-logs
num.partitions=12
default.replication.factor=3
min.insync.replicas=2

# Segment and retention settings for trading data
log.segment.bytes=1073741824
log.retention.hours=168
log.retention.bytes=107374182400
log.segment.delete.delay.ms=60000

############################# Internal Topic Settings #############################
offsets.topic.replication.factor=3
transaction.state.log.replication.factor=3
transaction.state.log.min.isr=2

############################# Group Coordinator Settings #############################
group.initial.rebalance.delay.ms=3000

############################# Performance Settings for Trading #############################
# Reduce latency
replica.fetch.max.bytes=1048576
message.max.bytes=1048576

# Increase throughput
num.replica.fetchers=4
replica.fetch.min.bytes=1
replica.fetch.wait.max.ms=500

# Memory settings
replica.high.watermark.checkpoint.interval.ms=5000
replica.lag.time.max.ms=30000

# Compression
compression.type=lz4

############################# Zookeeper #############################
zookeeper.connect=zk1:2181,zk2:2181,zk3:2181
zookeeper.connection.timeout.ms=18000

# Topic Configuration for Trading
# Market Data Topic
kafka-topics --create \\
  --bootstrap-server kafka1:9092,kafka2:9092,kafka3:9092 \\
  --topic market-data \\
  --partitions 24 \\
  --replication-factor 3 \\
  --config min.insync.replicas=2 \\
  --config compression.type=lz4 \\
  --config cleanup.policy=delete \\
  --config retention.ms=604800000 \\
  --config segment.ms=3600000 \\
  --config max.message.bytes=1048576

# Order Events Topic
kafka-topics --create \\
  --bootstrap-server kafka1:9092,kafka2:9092,kafka3:9092 \\
  --topic order-events \\
  --partitions 12 \\
  --replication-factor 3 \\
  --config min.insync.replicas=2 \\
  --config cleanup.policy=compact,delete \\
  --config retention.ms=2592000000 \\
  --config segment.ms=3600000 \\
  --config compression.type=lz4

# Trade Confirmations Topic (Critical - higher replication)
kafka-topics --create \\
  --bootstrap-server kafka1:9092,kafka2:9092,kafka3:9092 \\
  --topic trade-confirmations \\
  --partitions 6 \\
  --replication-factor 5 \\
  --config min.insync.replicas=3 \\
  --config cleanup.policy=compact \\
  --config retention.ms=-1 \\
  --config compression.type=lz4 \\
  --config flush.messages=1 \\
  --config flush.ms=1000

# Risk Alerts Topic (Low latency)
kafka-topics --create \\
  --bootstrap-server kafka1:9092,kafka2:9092,kafka3:9092 \\
  --topic risk-alerts \\
  --partitions 3 \\
  --replication-factor 3 \\
  --config min.insync.replicas=2 \\
  --config cleanup.policy=delete \\
  --config retention.ms=86400000 \\
  --config segment.ms=300000 \\
  --config max.message.bytes=262144

# JVM Settings for Kafka Brokers (kafka-server-start.sh)
export KAFKA_HEAP_OPTS="-Xmx8G -Xms8G"
export KAFKA_JVM_PERFORMANCE_OPTS="-server -XX:+UseG1GC -XX:MaxGCPauseMillis=20 -XX:InitiatingHeapOccupancyPercent=35 -XX:+ExplicitGCInvokesConcurrent -XX:MaxInlineLevel=15 -Djava.awt.headless=true"
export KAFKA_GC_LOG_OPTS="-Xlog:gc*:$KAFKA_LOG_DIR/kafka-gc.log:time,tags:filecount=10,filesize=100M"
export KAFKA_LOG4J_OPTS="-Dlog4j.configuration=file:$KAFKA_HOME/config/log4j.properties"

# Producer Configuration (Java Properties)
bootstrap.servers=kafka1:9092,kafka2:9092,kafka3:9092
key.serializer=org.apache.kafka.common.serialization.StringSerializer
value.serializer=org.apache.kafka.common.serialization.StringSerializer

# Reliability settings for trading
acks=all
retries=2147483647
enable.idempotence=true
max.in.flight.requests.per.connection=1

# Performance settings
batch.size=16384
linger.ms=1
buffer.memory=33554432
compression.type=lz4

# Timeout settings
request.timeout.ms=30000
delivery.timeout.ms=120000

# Consumer Configuration (Java Properties)
bootstrap.servers=kafka1:9092,kafka2:9092,kafka3:9092
key.deserializer=org.apache.kafka.common.serialization.StringDeserializer
value.deserializer=org.apache.kafka.common.serialization.StringDeserializer

# Consumer group settings
group.id=trading-consumer-group
auto.offset.reset=earliest
enable.auto.commit=false

# Performance settings for real-time processing
fetch.min.bytes=1
fetch.max.wait.ms=10
max.poll.records=1000
max.poll.interval.ms=300000

# Session management
session.timeout.ms=30000
heartbeat.interval.ms=10000

# Streams Configuration (Java Properties)
application.id=trading-streams-app
bootstrap.servers=kafka1:9092,kafka2:9092,kafka3:9092
default.key.serde=org.apache.kafka.common.serialization.Serdes$StringSerde
default.value.serde=org.apache.kafka.common.serialization.Serdes$StringSerde

# Performance settings for streams
commit.interval.ms=100
cache.max.bytes.buffering=0
num.stream.threads=4

# State store settings
state.dir=/tmp/kafka-streams
replication.factor=3
num.standby.replicas=1

# Connect Configuration (connect-distributed.properties)
bootstrap.servers=kafka1:9092,kafka2:9092,kafka3:9092
group.id=trading-connect-cluster

# Storage topics
config.storage.topic=trading-connect-configs
config.storage.replication.factor=3
offset.storage.topic=trading-connect-offsets
offset.storage.replication.factor=3
offset.storage.partitions=25
status.storage.topic=trading-connect-status
status.storage.replication.factor=3

# Connector settings
plugin.path=/usr/share/kafka/plugins
connector.client.config.override.policy=Principal

# REST API
rest.port=8083
rest.advertised.host.name=connect1

# Security Configuration
# SSL Configuration
ssl.keystore.location=/etc/kafka/ssl/kafka.server.keystore.jks
ssl.keystore.password=password
ssl.key.password=password
ssl.truststore.location=/etc/kafka/ssl/kafka.server.truststore.jks
ssl.truststore.password=password
ssl.client.auth=required
ssl.enabled.protocols=TLSv1.2,TLSv1.3
ssl.keystore.type=JKS
ssl.truststore.type=JKS

# SASL Configuration
sasl.enabled.mechanisms=PLAIN,SCRAM-SHA-256,SCRAM-SHA-512
sasl.mechanism.inter.broker.protocol=SCRAM-SHA-256

# ACL Configuration
authorizer.class.name=kafka.security.authorizer.AclAuthorizer
super.users=User:admin;User:kafka

# Monitoring Configuration (JMX)
export JMX_PORT=9999
export KAFKA_JMX_OPTS="-Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Djava.rmi.server.hostname=kafka1 -Djava.net.preferIPv4Stack=true"

# Schema Registry Configuration (if using Confluent)
kafkastore.bootstrap.servers=kafka1:9092,kafka2:9092,kafka3:9092
kafkastore.topic=_schemas
kafkastore.topic.replication.factor=3
debug=false

# Trading-specific topic configurations script
#!/bin/bash
# Trading Topics Setup Script

BROKERS="kafka1:9092,kafka2:9092,kafka3:9092"

# Function to create topic with specific configuration
create_trading_topic() {
    local topic_name=$1
    local partitions=$2
    local replication=$3
    local min_isr=$4
    local retention_ms=$5
    local segment_ms=$6
    local cleanup_policy=$7
    
    kafka-topics --create \\
        --bootstrap-server $BROKERS \\
        --topic $topic_name \\
        --partitions $partitions \\
        --replication-factor $replication \\
        --config min.insync.replicas=$min_isr \\
        --config retention.ms=$retention_ms \\
        --config segment.ms=$segment_ms \\
        --config cleanup.policy=$cleanup_policy \\
        --config compression.type=lz4
}

# Create all trading topics
create_trading_topic "market-data" 24 3 2 604800000 3600000 "delete"
create_trading_topic "order-events" 12 3 2 2592000000 3600000 "compact,delete"
create_trading_topic "trade-confirmations" 6 5 3 -1 3600000 "compact"
create_trading_topic "position-updates" 8 3 2 86400000 1800000 "compact"
create_trading_topic "risk-alerts" 3 3 2 86400000 300000 "delete"
create_trading_topic "settlement-instructions" 4 5 3 -1 3600000 "compact"
create_trading_topic "regulatory-reports" 2 5 3 -1 86400000 "compact"
create_trading_topic "audit-trail" 6 5 3 -1 86400000 "compact"

echo "All trading topics created successfully"`,

    'security': `// Kafka Security and Monitoring for Trading Systems
import org.apache.kafka.clients.admin.*;
import org.apache.kafka.common.acl.*;
import org.apache.kafka.common.resource.*;
import javax.management.MBeanServerConnection;
import javax.management.ObjectName;
import javax.management.remote.JMXConnector;
import javax.management.remote.JMXConnectorFactory;
import javax.management.remote.JMXServiceURL;
import java.util.*;

public class KafkaSecurityManager {
    private final AdminClient adminClient;
    
    public KafkaSecurityManager(Properties adminProps) {
        this.adminClient = AdminClient.create(adminProps);
    }
    
    // Set up ACLs for trading system users
    public void setupTradingACLs() {
        List<AclBinding> aclBindings = new ArrayList<>();
        
        // Market data producer ACLs
        aclBindings.add(createAclBinding(
            ResourceType.TOPIC, "market-data", 
            "market-data-producer", AclOperation.WRITE
        ));
        
        // Trading application consumer ACLs
        aclBindings.add(createAclBinding(
            ResourceType.TOPIC, "market-data", 
            "trading-app", AclOperation.READ
        ));
        aclBindings.add(createAclBinding(
            ResourceType.GROUP, "trading-consumer-group", 
            "trading-app", AclOperation.READ
        ));
        
        // Order processing service ACLs
        aclBindings.add(createAclBinding(
            ResourceType.TOPIC, "order-events", 
            "order-service", AclOperation.ALL
        ));
        
        // Risk management ACLs
        aclBindings.add(createAclBinding(
            ResourceType.TOPIC, "risk-alerts", 
            "risk-manager", AclOperation.ALL
        ));
        
        // Settlement system ACLs
        aclBindings.add(createAclBinding(
            ResourceType.TOPIC, "trade-confirmations", 
            "settlement-service", AclOperation.READ
        ));
        aclBindings.add(createAclBinding(
            ResourceType.TOPIC, "settlement-instructions", 
            "settlement-service", AclOperation.WRITE
        ));
        
        // Regulatory reporting ACLs
        aclBindings.add(createAclBinding(
            ResourceType.TOPIC, "regulatory-reports", 
            "compliance-service", AclOperation.WRITE
        ));
        
        // Audit system ACLs (read-only access to all trading topics)
        String[] auditTopics = {"market-data", "order-events", "trade-confirmations", 
                               "position-updates", "risk-alerts", "settlement-instructions"};
        for (String topic : auditTopics) {
            aclBindings.add(createAclBinding(
                ResourceType.TOPIC, topic, 
                "audit-service", AclOperation.READ
            ));
        }
        
        // Apply ACLs
        try {
            CreateAclsResult result = adminClient.createAcls(aclBindings);
            result.all().get(); // Wait for completion
            System.out.println("Trading ACLs created successfully");
        } catch (Exception e) {
            System.err.println("Failed to create ACLs: " + e.getMessage());
        }
    }
    
    private AclBinding createAclBinding(ResourceType resourceType, String resourceName, 
                                      String principal, AclOperation operation) {
        ResourcePattern resource = new ResourcePattern(resourceType, resourceName, PatternType.LITERAL);
        AccessControlEntry ace = new AccessControlEntry(
            "User:" + principal, "*", operation, AclPermissionType.ALLOW
        );
        return new AclBinding(resource, ace);
    }
    
    // Monitor broker health and performance
    public class KafkaMonitor {
        private final Map<String, JMXConnector> jmxConnectors;
        
        public KafkaMonitor(List<String> brokerJmxUrls) {
            this.jmxConnectors = new HashMap<>();
            
            for (String url : brokerJmxUrls) {
                try {
                    JMXServiceURL jmxUrl = new JMXServiceURL(url);
                    JMXConnector connector = JMXConnectorFactory.connect(jmxUrl);
                    jmxConnectors.put(url, connector);
                } catch (Exception e) {
                    System.err.println("Failed to connect to JMX: " + url + " - " + e.getMessage());
                }
            }
        }
        
        // Monitor critical metrics for trading systems
        public void monitorTradingMetrics() {
            for (Map.Entry<String, JMXConnector> entry : jmxConnectors.entrySet()) {
                try {
                    MBeanServerConnection mbsc = entry.getValue().getMBeanServerConnection();
                    
                    // Broker metrics
                    monitorBrokerMetrics(mbsc, entry.getKey());
                    
                    // Topic metrics for trading topics
                    monitorTopicMetrics(mbsc, "market-data");
                    monitorTopicMetrics(mbsc, "order-events");
                    monitorTopicMetrics(mbsc, "trade-confirmations");
                    
                    // Producer/Consumer metrics
                    monitorProducerMetrics(mbsc);
                    monitorConsumerMetrics(mbsc);
                    
                } catch (Exception e) {
                    System.err.println("Monitoring error for " + entry.getKey() + ": " + e.getMessage());
                }
            }
        }
        
        private void monitorBrokerMetrics(MBeanServerConnection mbsc, String brokerUrl) throws Exception {
            // Messages in per second
            ObjectName messagesInPerSec = new ObjectName("kafka.server:type=BrokerTopicMetrics,name=MessagesInPerSec");
            Double messagesInRate = (Double) mbsc.getAttribute(messagesInPerSec, "OneMinuteRate");
            
            // Bytes in per second
            ObjectName bytesInPerSec = new ObjectName("kafka.server:type=BrokerTopicMetrics,name=BytesInPerSec");
            Double bytesInRate = (Double) mbsc.getAttribute(bytesInPerSec, "OneMinuteRate");
            
            // Under replicated partitions (critical for trading)
            ObjectName underReplicatedPartitions = new ObjectName("kafka.server:type=ReplicaManager,name=UnderReplicatedPartitions");
            Integer underReplicated = (Integer) mbsc.getAttribute(underReplicatedPartitions, "Value");
            
            // Request handler idle ratio
            ObjectName requestHandlerAvgIdlePercent = new ObjectName("kafka.server:type=KafkaRequestHandlerPool,name=RequestHandlerAvgIdlePercent");
            Double idlePercent = (Double) mbsc.getAttribute(requestHandlerAvgIdlePercent, "OneMinuteRate");
            
            // Network processor idle ratio
            ObjectName networkProcessorAvgIdlePercent = new ObjectName("kafka.network:type=SocketServer,name=NetworkProcessorAvgIdlePercent");
            Double networkIdlePercent = (Double) mbsc.getAttribute(networkProcessorAvgIdlePercent, "Value");
            
            System.out.printf("Broker %s Metrics:%n", brokerUrl);
            System.out.printf("  Messages/sec: %.2f%n", messagesInRate);
            System.out.printf("  Bytes/sec: %.2f%n", bytesInRate);
            System.out.printf("  Under-replicated partitions: %d%n", underReplicated);
            System.out.printf("  Request handler idle: %.2f%%%n", idlePercent * 100);
            System.out.printf("  Network processor idle: %.2f%%%n", networkIdlePercent * 100);
            
            // Alert conditions for trading systems
            if (underReplicated > 0) {
                System.err.printf("ALERT: Under-replicated partitions detected on %s%n", brokerUrl);
            }
            if (idlePercent < 0.1) {
                System.err.printf("ALERT: High request handler utilization on %s%n", brokerUrl);
            }
            if (networkIdlePercent < 0.1) {
                System.err.printf("ALERT: High network processor utilization on %s%n", brokerUrl);
            }
        }
        
        private void monitorTopicMetrics(MBeanServerConnection mbsc, String topicName) throws Exception {
            // Topic-specific metrics
            String topicMetricsPattern = "kafka.server:type=BrokerTopicMetrics,name=%s,topic=" + topicName;
            
            ObjectName topicBytesIn = new ObjectName(String.format(topicMetricsPattern, "BytesInPerSec"));
            Double topicBytesInRate = (Double) mbsc.getAttribute(topicBytesIn, "OneMinuteRate");
            
            ObjectName topicMessagesIn = new ObjectName(String.format(topicMetricsPattern, "MessagesInPerSec"));
            Double topicMessagesInRate = (Double) mbsc.getAttribute(topicMessagesIn, "OneMinuteRate");
            
            System.out.printf("Topic %s Metrics:%n", topicName);
            System.out.printf("  Messages/sec: %.2f%n", topicMessagesInRate);
            System.out.printf("  Bytes/sec: %.2f%n", topicBytesInRate);
            
            // Trading-specific alerts
            if ("market-data".equals(topicName) && topicMessagesInRate < 100) {
                System.err.printf("ALERT: Low market data rate: %.2f messages/sec%n", topicMessagesInRate);
            }
            if ("trade-confirmations".equals(topicName) && topicMessagesInRate > 1000) {
                System.out.printf("INFO: High trading volume: %.2f trades/sec%n", topicMessagesInRate);
            }
        }
        
        private void monitorProducerMetrics(MBeanServerConnection mbsc) throws Exception {
            // Producer metrics that matter for trading
            ObjectName producerRecordSendRate = new ObjectName("kafka.producer:type=producer-metrics,client-id=*,attribute=record-send-rate");
            Set<ObjectName> producerMetrics = mbsc.queryNames(producerRecordSendRate, null);
            
            for (ObjectName on : producerMetrics) {
                Double sendRate = (Double) mbsc.getAttribute(on, "Value");
                String clientId = on.getKeyProperty("client-id");
                
                // Request latency (critical for trading)
                ObjectName requestLatencyAvg = new ObjectName("kafka.producer:type=producer-metrics,client-id=" + clientId);
                try {
                    Double latencyAvg = (Double) mbsc.getAttribute(requestLatencyAvg, "request-latency-avg");
                    
                    System.out.printf("Producer %s: %.2f records/sec, %.2fms avg latency%n", 
                                    clientId, sendRate, latencyAvg);
                    
                    if (latencyAvg > 10.0) { // Alert if latency > 10ms
                        System.err.printf("ALERT: High producer latency for %s: %.2fms%n", clientId, latencyAvg);
                    }
                } catch (Exception e) {
                    // Some metrics might not be available
                }
            }
        }
        
        private void monitorConsumerMetrics(MBeanServerConnection mbsc) throws Exception {
            // Consumer lag (critical for trading systems)
            ObjectName consumerLag = new ObjectName("kafka.consumer:type=consumer-fetch-manager-metrics,client-id=*,attribute=records-lag-max");
            Set<ObjectName> consumerMetrics = mbsc.queryNames(consumerLag, null);
            
            for (ObjectName on : consumerMetrics) {
                try {
                    Double lagMax = (Double) mbsc.getAttribute(on, "Value");
                    String clientId = on.getKeyProperty("client-id");
                    
                    System.out.printf("Consumer %s: %.0f max lag%n", clientId, lagMax);
                    
                    if (lagMax > 1000) { // Alert if lag > 1000 messages
                        System.err.printf("ALERT: High consumer lag for %s: %.0f messages%n", clientId, lagMax);
                    }
                } catch (Exception e) {
                    // Metric might not be available
                }
            }
        }
        
        public void close() {
            for (JMXConnector connector : jmxConnectors.values()) {
                try {
                    connector.close();
                } catch (Exception e) {
                    System.err.println("Error closing JMX connector: " + e.getMessage());
                }
            }
        }
    }
    
    // Security audit and compliance monitoring
    public class SecurityAuditor {
        
        public void auditTopicAccess() {
            try {
                // List all ACLs
                DescribeAclsResult aclsResult = adminClient.describeAcls(AclBindingFilter.ANY);
                Collection<AclBinding> acls = aclsResult.values().get();
                
                System.out.println("=== Topic Access Audit ===");
                Map<String, List<String>> topicAccess = new HashMap<>();
                
                for (AclBinding acl : acls) {
                    if (acl.pattern().resourceType() == ResourceType.TOPIC) {
                        String topic = acl.pattern().name();
                        String principal = acl.entry().principal();
                        String operation = acl.entry().operation().toString();
                        
                        String accessInfo = String.format("%s: %s", principal, operation);
                        topicAccess.computeIfAbsent(topic, k -> new ArrayList<>()).add(accessInfo);
                    }
                }
                
                // Report access by topic
                for (Map.Entry<String, List<String>> entry : topicAccess.entrySet()) {
                    System.out.printf("Topic: %s%n", entry.getKey());
                    for (String access : entry.getValue()) {
                        System.out.printf("  %s%n", access);
                    }
                }
                
                // Check for compliance violations
                checkComplianceViolations(topicAccess);
                
            } catch (Exception e) {
                System.err.println("Failed to audit topic access: " + e.getMessage());
            }
        }
        
        private void checkComplianceViolations(Map<String, List<String>> topicAccess) {
            // Check for sensitive topics with overly broad access
            String[] sensitiveTopics = {"trade-confirmations", "settlement-instructions", 
                                      "regulatory-reports", "audit-trail"};
            
            for (String topic : sensitiveTopics) {
                List<String> accessList = topicAccess.get(topic);
                if (accessList != null) {
                    for (String access : accessList) {
                        if (access.contains("User:*") || access.contains("ALL")) {
                            System.err.printf("COMPLIANCE VIOLATION: Overly broad access to sensitive topic %s: %s%n", 
                                            topic, access);
                        }
                    }
                }
            }
            
            // Check for missing audit access
            if (!topicAccess.containsKey("audit-trail")) {
                System.err.println("COMPLIANCE VIOLATION: No audit trail topic configured");
            }
        }
        
        public void generateComplianceReport() {
            System.out.println("=== Kafka Compliance Report ===");
            System.out.println("Generated: " + new Date());
            
            try {
                // Topic configuration compliance
                ListTopicsResult topicsResult = adminClient.listTopics();
                Set<String> topics = topicsResult.names().get();
                
                DescribeTopicsResult topicsDescResult = adminClient.describeTopics(topics);
                Map<String, TopicDescription> topicDescriptions = topicsDescResult.all().get();
                
                System.out.println("\\n--- Topic Configuration Compliance ---");
                for (Map.Entry<String, TopicDescription> entry : topicDescriptions.entrySet()) {
                    String topicName = entry.getKey();
                    TopicDescription desc = entry.getValue();
                    
                    int replicationFactor = desc.partitions().get(0).replicas().size();
                    
                    // Check replication requirements for trading topics
                    if (isImportantTradingTopic(topicName)) {
                        if (replicationFactor < 3) {
                            System.err.printf("COMPLIANCE ISSUE: Topic %s has insufficient replication factor: %d%n", 
                                            topicName, replicationFactor);
                        } else {
                            System.out.printf("COMPLIANT: Topic %s replication factor: %d%n", topicName, replicationFactor);
                        }
                    }
                }
                
            } catch (Exception e) {
                System.err.println("Failed to generate compliance report: " + e.getMessage());
            }
        }
        
        private boolean isImportantTradingTopic(String topicName) {
            String[] importantTopics = {"market-data", "order-events", "trade-confirmations", 
                                      "settlement-instructions", "regulatory-reports"};
            return Arrays.asList(importantTopics).contains(topicName);
        }
    }
    
    public void close() {
        adminClient.close();
    }
}

// Usage example
public class TradingKafkaSecurityExample {
    public static void main(String[] args) {
        Properties adminProps = new Properties();
        adminProps.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, "kafka1:9092,kafka2:9092,kafka3:9092");
        adminProps.put(AdminClientConfig.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
        adminProps.put("sasl.mechanism", "SCRAM-SHA-256");
        adminProps.put("sasl.jaas.config", 
            "org.apache.kafka.common.security.scram.ScramLoginModule required " +
            "username=\\"admin\\" password=\\"admin-password\\";");
        
        KafkaSecurityManager securityManager = new KafkaSecurityManager(adminProps);
        
        // Set up ACLs
        securityManager.setupTradingACLs();
        
        // Start monitoring
        List<String> jmxUrls = Arrays.asList(
            "service:jmx:rmi:///jndi/rmi://kafka1:9999/jmxrmi",
            "service:jmx:rmi:///jndi/rmi://kafka2:9999/jmxrmi",
            "service:jmx:rmi:///jndi/rmi://kafka3:9999/jmxrmi"
        );
        
        KafkaSecurityManager.KafkaMonitor monitor = securityManager.new KafkaMonitor(jmxUrls);
        KafkaSecurityManager.SecurityAuditor auditor = securityManager.new SecurityAuditor();
        
        // Run monitoring loop
        Timer timer = new Timer(true);
        timer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                monitor.monitorTradingMetrics();
                auditor.auditTopicAccess();
            }
        }, 0, 60000); // Monitor every minute
        
        // Generate daily compliance report
        Timer complianceTimer = new Timer(true);
        complianceTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                auditor.generateComplianceReport();
            }
        }, 0, 24 * 60 * 60 * 1000); // Daily report
        
        // Keep running
        try {
            Thread.sleep(Long.MAX_VALUE);
        } catch (InterruptedException e) {
            monitor.close();
            securityManager.close();
        }
    }
}`
  };

  return (
    <div style={{
      padding: '40px',
      fontFamily: 'Inter, system-ui, sans-serif',
      backgroundColor: '#0f172a',
      color: 'white',
      minHeight: '100vh'
    }}>
      <div style={{ marginBottom: '40px' }}>
        <h1 style={{ 
          fontSize: '36px', 
          fontWeight: '700', 
          marginBottom: '16px',
          background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent'
        }}>
          Apache Kafka
        </h1>
        <p style={{ fontSize: '18px', color: '#94a3b8', lineHeight: '1.6' }}>
          Distributed event streaming platform for high-throughput trading systems
        </p>
      </div>

      <div style={{ display: 'flex', gap: '40px' }}>
        <div style={{ flex: '1', maxWidth: '300px' }}>
          <h2 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '24px', color: '#10b981' }}>
            Kafka Components
          </h2>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {topics.map((topic) => (
              <button
                key={topic.id}
                onClick={() => setSelectedTopic(topic)}
                style={{
                  padding: '16px',
                  backgroundColor: selectedTopic?.id === topic.id ? 'rgba(16, 185, 129, 0.2)' : 'rgba(30, 41, 59, 0.5)',
                  border: `2px solid ${selectedTopic?.id === topic.id ? '#10b981' : 'transparent'}`,
                  borderRadius: '12px',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  textAlign: 'left',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px'
                }}
              >
                <div style={{ color: selectedTopic === topic.id ? '#10b981' : '#64748b' }}>
                  {topic.icon}
                </div>
                <div>
                  <div style={{ 
                    fontWeight: '600', 
                    color: selectedTopic === topic.id ? '#10b981' : 'white',
                    marginBottom: '4px'
                  }}>
                    {topic.name}
                  </div>
                  <div style={{ fontSize: '14px', color: '#94a3b8' }}>
                    {topic.description}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        <div style={{ flex: '2' }}>
          <div style={{ marginBottom: '24px' }}>
            <div style={{ display: 'flex', gap: '8px', marginBottom: '24px' }}>
              {['overview', 'definition', 'code'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  style={{
                    padding: '12px 24px',
                    backgroundColor: activeTab === tab ? '#10b981' : 'transparent',
                    border: `2px solid ${activeTab === tab ? '#10b981' : '#374151'}`,
                    borderRadius: '8px',
                    color: activeTab === tab ? 'white' : '#94a3b8',
                    cursor: 'pointer',
                    fontWeight: '600',
                    textTransform: 'capitalize',
                    transition: 'all 0.2s'
                  }}
                >
                  {tab}
                </button>
              ))}
            </div>
          </div>

          {activeTab === 'overview' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <h2 style={{ fontSize: '28px', fontWeight: '600', marginBottom: '24px', color: '#10b981' }}>
                Apache Kafka Overview
              </h2>
              <div style={{ fontSize: '16px', lineHeight: '1.8', color: '#e2e8f0' }}>
                <p style={{ marginBottom: '20px' }}>
                  Apache Kafka is a distributed event streaming platform designed for high-throughput, 
                  low-latency data processing. In trading systems, Kafka handles market data distribution, 
                  order processing, trade confirmations, and real-time analytics with sub-millisecond latency.
                </p>
                <div style={{ marginBottom: '24px' }}>
                  <h3 style={{ color: '#10b981', fontSize: '20px', marginBottom: '16px' }}>Core Components:</h3>
                  <ul style={{ marginLeft: '24px', lineHeight: '1.8' }}>
                    <li><strong>Producers:</strong> Publish trading events, market data, and order updates</li>
                    <li><strong>Consumers:</strong> Process real-time streams for trading decisions and analytics</li>
                    <li><strong>Kafka Streams:</strong> Stream processing for algorithmic trading and risk calculations</li>
                    <li><strong>Kafka Connect:</strong> Integration with trading platforms and market data feeds</li>
                    <li><strong>Security:</strong> Authentication, authorization, and compliance monitoring</li>
                  </ul>
                </div>
                <div style={{ 
                  backgroundColor: 'rgba(16, 185, 129, 0.1)', 
                  padding: '20px', 
                  borderRadius: '8px',
                  border: '1px solid #10b981'
                }}>
                  <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Trading System Benefits:</h4>
                  <p>
                    Kafka provides exactly-once semantics, horizontal scalability, and fault tolerance 
                    essential for financial markets. It enables real-time risk management, regulatory 
                    compliance, and high-frequency trading with guaranteed message ordering.
                  </p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'definition' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <h2 style={{ fontSize: '24px', fontWeight: '600', marginBottom: '20px', color: '#10b981' }}>
                {topics.find(t => t.id === selectedTopic)?.name} Definition
              </h2>
              <p style={{ fontSize: '16px', lineHeight: '1.8', color: '#e2e8f0' }}>
                {definitions[selectedTopic]}
              </p>
            </div>
          )}

          {activeTab === 'code' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <div style={{ marginBottom: '20px' }}>
                <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px' }}>
                  {topics.find(t => t.id === selectedTopic)?.name} Examples
                </h3>
              </div>
              <SyntaxHighlighter
                language="java"
                style={oneDark}
                customStyle={{
                  backgroundColor: '#1e293b',
                  padding: '20px',
                  borderRadius: '8px',
                  fontSize: '14px',
                  lineHeight: '1.6'
                }}
              >
                {codeExamples[selectedTopic]}
              </SyntaxHighlighter>
            </div>
          )}
        </div>
      </div>
      
      {/* Details Panel - Matching Dark Pool Style */}
      {selectedTopic && (
        <div style={{
          position: 'fixed',
          right: 0,
          top: 0,
          width: '500px',
          height: '100vh',
          backgroundColor: 'rgba(17, 24, 39, 0.95)',
          backdropFilter: 'blur(12px)',
          borderLeft: '1px solid #374151',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          zIndex: 1000
        }}>
          {/* Panel Header */}
          <div style={{
            padding: '20px',
            borderBottom: '1px solid #374151',
            background: 'linear-gradient(135deg, #1f2937 0%, #**********%)'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start' }}>
              <div>
                <h2 style={{ fontSize: '18px', fontWeight: 'bold', color: '#fbbf24', margin: '0 0 8px 0' }}>
                  {selectedTopic.name}
                </h2>
                <div style={{ fontSize: '12px', color: '#9ca3af' }}>
                  {selectedTopic.description}
                </div>
              </div>
              <button
                onClick={() => setSelectedTopic(null)}
                style={{
                  background: 'transparent',
                  border: 'none',
                  color: '#9ca3af',
                  fontSize: '24px',
                  cursor: 'pointer',
                  padding: 0,
                  lineHeight: 1
                }}
              >
                ×
              </button>
            </div>
          </div>

          {/* Tabs */}
          <div style={{ display: 'flex', borderBottom: '1px solid #374151' }}>
            {['definition', 'code', 'architecture', 'best-practices'].map(tab => (
              <button
                key={tab}
                onClick={() => setDetailsTab(tab)}
                style={{
                  flex: 1,
                  padding: '12px',
                  background: detailsTab === tab ? '#1f2937' : 'transparent',
                  border: 'none',
                  color: detailsTab === tab ? '#fbbf24' : '#9ca3af',
                  fontSize: '12px',
                  fontWeight: '600',
                  textTransform: 'capitalize',
                  cursor: 'pointer'
                }}
              >
                {tab.replace('-', ' ')}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div style={{ flex: 1, padding: '20px', overflowY: 'auto' }}>
            {detailsTab === 'definition' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  What are Kafka {selectedTopic.name}?
                </h3>
                <div style={{ color: '#d1d5db', fontSize: '13px', lineHeight: '1.8' }}>
                  <p style={{ marginBottom: '16px' }}>
                    {definitions[selectedTopic.id]}
                  </p>
                  
                  <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Key Benefits in Trading Systems:</h4>
                  <ul style={{ paddingLeft: '20px' }}>
                    {selectedTopic.id === 'producers' && (
                      <>
                        <li>High-throughput market data publishing</li>
                        <li>Reliable order event streaming</li>
                        <li>Exactly-once trade confirmation delivery</li>
                        <li>Low-latency message publishing</li>
                      </>
                    )}
                    {selectedTopic.id === 'consumers' && (
                      <>
                        <li>Parallel order processing with consumer groups</li>
                        <li>Fault-tolerant message consumption</li>
                        <li>Offset management for replay capabilities</li>
                        <li>Real-time trade settlement processing</li>
                      </>
                    )}
                    {selectedTopic.id === 'streams' && (
                      <>
                        <li>Real-time algorithmic trading logic</li>
                        <li>Complex event processing for risk management</li>
                        <li>Windowed aggregations for analytics</li>
                        <li>Stateful stream processing applications</li>
                      </>
                    )}
                  </ul>
                </div>
              </div>
            )}

            {detailsTab === 'code' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  Implementation Example
                </h3>
                <div style={{
                  background: '#0f172a',
                  padding: '16px',
                  borderRadius: '8px',
                  maxHeight: '600px',
                  overflow: 'auto'
                }}>
                  <SyntaxHighlighter
                    language="java"
                    style={oneDark}
                    customStyle={{
                      background: 'transparent',
                      padding: 0,
                      margin: 0,
                      fontSize: '12px',
                      lineHeight: '1.5'
                    }}
                  >
                    {codeExamples[selectedTopic.id] || '// Code example not available'}
                  </SyntaxHighlighter>
                </div>
              </div>
            )}

            {detailsTab === 'architecture' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  Trading System Architecture
                </h3>
                <div style={{ display: 'grid', gap: '12px' }}>
                  {[
                    'Market data feeds → Kafka → Real-time analytics',
                    'Order management → Kafka → Risk validation',
                    'Trade execution → Kafka → Settlement systems',
                    'Position updates → Kafka → Portfolio management',
                    'Risk alerts → Kafka → Monitoring dashboards',
                    'Regulatory data → Kafka → Compliance reporting'
                  ].map((flow, idx) => (
                    <div key={idx} style={{
                      padding: '12px',
                      background: '#1f2937',
                      borderRadius: '6px',
                      borderLeft: '3px solid #10b981',
                      color: '#d1d5db',
                      fontSize: '13px'
                    }}>
                      {flow}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {detailsTab === 'best-practices' && (
              <div>
                <h3 style={{ color: '#10b981', fontSize: '14px', marginBottom: '16px' }}>
                  Best Practices
                </h3>
                <div style={{ display: 'grid', gap: '12px' }}>
                  {[
                    'Use idempotent producers for exactly-once semantics',
                    'Configure appropriate replication factor (3+ for production)',
                    'Monitor consumer lag and partition balance',
                    'Implement proper error handling and retry logic',
                    'Use schema registry for message format evolution',
                    'Set up monitoring and alerting for cluster health',
                    'Tune batch size and linger settings for latency/throughput'
                  ].map((practice, idx) => (
                    <div key={idx} style={{
                      padding: '12px',
                      background: '#1f2937',
                      borderRadius: '6px',
                      borderLeft: '3px solid #10b981',
                      color: '#d1d5db',
                      fontSize: '13px'
                    }}>
                      {practice}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Kafka;
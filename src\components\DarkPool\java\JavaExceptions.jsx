import React, { useState } from 'react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { AlertCircle, Shield, AlertTriangle, Info, XCircle, CheckCircle } from 'lucide-react';

const JavaExceptions = () => {
  const [selectedTopic, setSelectedTopic] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [detailsTab, setDetailsTab] = useState('overview');

  const topics = [
    {
      id: 'hierarchy',
      name: 'Exception Hierarchy',
      icon: <AlertCircle size={20} />,
      description: 'Checked vs Unchecked exceptions',
      color: '#ef4444'
    },
    {
      id: 'handling',
      name: 'Exception Handling',
      icon: <Shield size={20} />,
      description: 'Try-catch-finally patterns',
      color: '#3b82f6'
    },
    {
      id: 'custom',
      name: 'Custom Exceptions',
      icon: <AlertTriangle size={20} />,
      description: 'Domain-specific exceptions',
      color: '#10b981'
    },
    {
      id: 'best-practices',
      name: 'Best Practices',
      icon: <CheckCircle size={20} />,
      description: 'Error handling strategies',
      color: '#f59e0b'
    }
  ];

  const codeExamples = {
    hierarchy: `// Trading System Exception Hierarchy
// Base exception for all trading-related errors
public abstract class TradingException extends Exception {
    private final String errorCode;
    private final Instant timestamp;
    
    protected TradingException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
        this.timestamp = Instant.now();
    }
    
    protected TradingException(String message, String errorCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.timestamp = Instant.now();
    }
}

// Checked exceptions for recoverable errors
public class InsufficientFundsException extends TradingException {
    private final double required;
    private final double available;
    
    public InsufficientFundsException(double required, double available) {
        super(
            String.format("Insufficient funds: required %.2f, available %.2f", 
                required, available),
            "INSUFFICIENT_FUNDS"
        );
        this.required = required;
        this.available = available;
    }
}

// Runtime exceptions for programming errors
public class InvalidOrderException extends RuntimeException {
    public InvalidOrderException(String message) {
        super(message);
    }
}

// Market-specific exceptions
public class MarketClosedException extends TradingException {
    private final String market;
    
    public MarketClosedException(String market) {
        super("Market is closed: " + market, "MARKET_CLOSED");
        this.market = market;
    }
}`,
    handling: `// Comprehensive Exception Handling Patterns
public class OrderProcessor {
    private static final Logger log = LoggerFactory.getLogger(OrderProcessor.class);
    
    // Multi-catch and resource management
    public OrderResult processOrder(Order order) {
        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(INSERT_ORDER)) {
            
            // Validate order
            validateOrder(order);
            
            // Execute trade
            Trade trade = executeTrade(order);
            
            // Persist to database
            stmt.setString(1, trade.getId());
            stmt.setDouble(2, trade.getPrice());
            stmt.executeUpdate();
            
            return OrderResult.success(trade);
            
        } catch (InsufficientFundsException | MarketClosedException e) {
            // Handle business exceptions
            log.warn("Order rejected: {}", e.getMessage());
            return OrderResult.rejected(e.getMessage());
            
        } catch (SQLException | DataAccessException e) {
            // Handle data exceptions
            log.error("Database error processing order", e);
            // Retry logic
            return retryOrder(order);
            
        } catch (Exception e) {
            // Catch-all for unexpected errors
            log.error("Unexpected error processing order {}", order.getId(), e);
            alertOperations(order, e);
            return OrderResult.failed("System error");
            
        } finally {
            // Always execute cleanup
            auditLog.record(order);
            metrics.recordOrderProcessing(order);
        }
    }
    
    // Exception chaining and wrapping
    public void validateOrder(Order order) throws ValidationException {
        try {
            checkOrderFields(order);
            checkRiskLimits(order);
            checkMarketHours(order);
        } catch (IllegalArgumentException e) {
            throw new ValidationException("Invalid order data", e);
        } catch (RiskLimitExceededException e) {
            throw new ValidationException("Risk check failed", e);
        }
    }
    
    // Suppressed exceptions handling
    public void closeResources(AutoCloseable... resources) {
        Exception suppressed = null;
        
        for (AutoCloseable resource : resources) {
            try {
                if (resource != null) {
                    resource.close();
                }
            } catch (Exception e) {
                if (suppressed == null) {
                    suppressed = e;
                } else {
                    suppressed.addSuppressed(e);
                }
            }
        }
        
        if (suppressed != null) {
            throw new ResourceCleanupException("Failed to close resources", suppressed);
        }
    }
}`,
    custom: `// Custom Trading Exceptions with Context
public class TradingExceptions {
    
    // Rich exception with context
    public class OrderExecutionException extends TradingException {
        private final Order order;
        private final ExecutionStage failedStage;
        private final Map<String, Object> context;
        
        public OrderExecutionException(
                Order order, 
                ExecutionStage stage, 
                String message, 
                Throwable cause) {
            super(message, "ORDER_EXECUTION_FAILED", cause);
            this.order = order;
            this.failedStage = stage;
            this.context = new HashMap<>();
            captureContext();
        }
        
        private void captureContext() {
            context.put("orderId", order.getId());
            context.put("symbol", order.getSymbol());
            context.put("stage", failedStage);
            context.put("timestamp", Instant.now());
            context.put("marketState", MarketState.current());
        }
        
        public String getDetailedMessage() {
            return String.format(
                "Order %s failed at stage %s: %s\nContext: %s",
                order.getId(), failedStage, getMessage(), context
            );
        }
    }
    
    // Exception factory pattern
    public class TradingExceptionFactory {
        
        public TradingException createException(ErrorCode code, Object... args) {
            switch (code) {
                case INSUFFICIENT_FUNDS:
                    return new InsufficientFundsException(
                        (Double) args[0], (Double) args[1]
                    );
                    
                case MARKET_CLOSED:
                    return new MarketClosedException((String) args[0]);
                    
                case RISK_LIMIT_EXCEEDED:
                    return new RiskLimitExceededException(
                        (String) args[0], (Double) args[1]
                    );
                    
                default:
                    return new GenericTradingException(code.getMessage());
            }
        }
    }
    
    // Retry-able exception
    public class RetryableException extends TradingException {
        private final int maxRetries;
        private final long retryDelayMs;
        
        public RetryableException(String message, int maxRetries, long retryDelayMs) {
            super(message, "RETRYABLE_ERROR");
            this.maxRetries = maxRetries;
            this.retryDelayMs = retryDelayMs;
        }
        
        public boolean canRetry(int attemptNumber) {
            return attemptNumber < maxRetries;
        }
    }
}`,
    'best-practices': `// Exception Handling Best Practices
public class ExceptionBestPractices {
    
    // 1. Use specific exceptions
    public void specificExceptions(Order order) throws OrderValidationException {
        // BAD: Generic exception
        // if (!isValid(order)) throw new Exception("Invalid order");
        
        // GOOD: Specific exception
        if (!isValid(order)) {
            throw new OrderValidationException(order, ValidationError.INVALID_PRICE);
        }
    }
    
    // 2. Fail fast with validation
    public Trade executeTrade(Order order) {
        // Validate early
        Objects.requireNonNull(order, "Order cannot be null");
        Preconditions.checkArgument(order.getQuantity() > 0, 
            "Quantity must be positive");
        
        return processOrder(order);
    }
    
    // 3. Use Optional instead of null/exceptions
    public Optional<Position> findPosition(String symbol) {
        // Instead of throwing NotFoundException
        return Optional.ofNullable(positions.get(symbol));
    }
    
    // 4. Circuit breaker pattern
    public class TradingServiceCircuitBreaker {
        private final AtomicInteger failureCount = new AtomicInteger(0);
        private final int threshold = 5;
        private volatile boolean open = false;
        
        public OrderResult executeWithCircuitBreaker(Order order) {
            if (open) {
                throw new ServiceUnavailableException("Circuit breaker is open");
            }
            
            try {
                OrderResult result = executeOrder(order);
                reset();
                return result;
            } catch (Exception e) {
                recordFailure();
                throw e;
            }
        }
        
        private void recordFailure() {
            if (failureCount.incrementAndGet() >= threshold) {
                open = true;
                scheduleReset();
            }
        }
        
        private void reset() {
            failureCount.set(0);
            open = false;
        }
    }
    
    // 5. Structured error responses
    @RestControllerAdvice
    public class GlobalExceptionHandler {
        
        @ExceptionHandler(TradingException.class)
        public ResponseEntity<ErrorResponse> handleTradingException(
                TradingException e) {
            ErrorResponse error = ErrorResponse.builder()
                .code(e.getErrorCode())
                .message(e.getMessage())
                .timestamp(e.getTimestamp())
                .traceId(MDC.get("traceId"))
                .build();
                
            return ResponseEntity
                .status(HttpStatus.BAD_REQUEST)
                .body(error);
        }
        
        @ExceptionHandler(Exception.class)
        public ResponseEntity<ErrorResponse> handleGenericException(
                Exception e) {
            log.error("Unexpected error", e);
            
            ErrorResponse error = ErrorResponse.builder()
                .code("INTERNAL_ERROR")
                .message("An error occurred processing your request")
                .timestamp(Instant.now())
                .build();
                
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(error);
        }
    }
}`
  };

  return (
    <div style={{
      padding: '40px',
      paddingTop: '100px',
      minHeight: '100vh',
      backgroundColor: '#0f172a',
      color: 'white'
    }}>
      <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
        <div style={{ marginBottom: '40px' }}>
          <h1 style={{ fontSize: '36px', fontWeight: 'bold', marginBottom: '16px', color: '#10b981' }}>
            Java Exception Handling
          </h1>
          <p style={{ fontSize: '18px', color: '#94a3b8' }}>
            Robust error handling strategies for financial systems
          </p>
        </div>

        <div style={{ display: 'flex', gap: '20px', marginBottom: '40px' }}>
          {['overview', 'definition', 'code', 'patterns', 'guidelines'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              style={{
                padding: '12px 24px',
                backgroundColor: activeTab === tab ? '#10b981' : 'transparent',
                border: `2px solid ${activeTab === tab ? '#10b981' : '#374151'}`,
                borderRadius: '8px',
                color: activeTab === tab ? 'white' : '#9ca3af',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                textTransform: 'capitalize',
                transition: 'all 0.2s'
              }}
            >
              {tab}
            </button>
          ))}
        </div>

        {activeTab === 'definition' && (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <div style={{ marginBottom: '20px' }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px' }}>
                {selectedTopic === 'hierarchy' ? 'Exception Hierarchy' :
                 selectedTopic === 'handling' ? 'Exception Handling' :
                 selectedTopic === 'custom' ? 'Custom Exceptions' :
                 'Best Practices'} Definition
              </h3>
            </div>
            
            {selectedTopic === 'hierarchy' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What is Exception Hierarchy?</h4>
                <p style={{ marginBottom: '16px' }}>
                  Java's exception hierarchy is a class structure with Throwable at the root, branching into Error and Exception. Exceptions further divide into checked (compile-time) and unchecked (runtime) exceptions.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why is it crucial for Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>Distinguish between recoverable business errors and system failures</li>
                  <li>Ensure proper error handling at different system layers</li>
                  <li>Enable graceful degradation during trading disruptions</li>
                  <li>Provide clear error communication to operators</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to design it effectively?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Use checked exceptions for recoverable business errors</li>
                  <li>Use unchecked exceptions for programming errors</li>
                  <li>Create domain-specific exception hierarchies</li>
                  <li>Include relevant context in exception messages</li>
                </ul>
              </div>
            )}
            
            {selectedTopic === 'handling' && (
              <div style={{ lineHeight: '1.8', color: '#e2e8f0' }}>
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>What is Exception Handling?</h4>
                <p style={{ marginBottom: '16px' }}>
                  Exception handling is the process of catching, processing, and responding to exceptional circumstances during program execution using try-catch-finally blocks and related constructs.
                </p>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Why is it vital for Trading Systems?</h4>
                <ul style={{ marginBottom: '16px', paddingLeft: '20px' }}>
                  <li>Ensure system stability during market volatility</li>
                  <li>Prevent cascading failures that could halt trading</li>
                  <li>Maintain audit trails of errors for regulatory compliance</li>
                  <li>Enable quick recovery from transient network issues</li>
                </ul>
                
                <h4 style={{ color: '#10b981', marginBottom: '12px' }}>How to implement it robustly?</h4>
                <ul style={{ paddingLeft: '20px' }}>
                  <li>Use try-with-resources for automatic resource management</li>
                  <li>Implement retry logic for transient failures</li>
                  <li>Log exceptions with appropriate severity levels</li>
                  <li>Use circuit breakers for external service failures</li>
                </ul>
              </div>
            )}
          </div>
        )}

        {activeTab === 'overview' && (
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>
            {topics.map((topic) => (
              <div
                key={topic.id}
                onClick={() => setSelectedTopic(topic.id)}
                style={{
                  padding: '24px',
                  backgroundColor: selectedTopic === topic.id ? 'rgba(16, 185, 129, 0.1)' : 'rgba(31, 41, 55, 0.5)',
                  border: `2px solid ${selectedTopic === topic.id ? '#10b981' : 'transparent'}`,
                  borderRadius: '12px',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '12px' }}>
                  <div style={{ color: topic.color }}>{topic.icon}</div>
                  <h3 style={{ fontSize: '20px', fontWeight: '600' }}>{topic.name}</h3>
                </div>
                <p style={{ color: '#94a3b8', fontSize: '14px' }}>{topic.description}</p>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'code' && (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            padding: '24px',
            border: '1px solid #374151'
          }}>
            <div style={{ marginBottom: '20px' }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px', textTransform: 'capitalize' }}>
                {selectedTopic ? selectedTopic.replace('-', ' ') : 'Exception Handling'} Example
              </h3>
            </div>
            <SyntaxHighlighter
              language="java"
              style={oneDark}
              customStyle={{
                backgroundColor: '#1e293b',
                padding: '20px',
                borderRadius: '8px',
                fontSize: '14px',
                lineHeight: '1.6'
              }}
            >
              {selectedTopic ? codeExamples[selectedTopic] : '// Select a topic to view code examples'}
            </SyntaxHighlighter>
          </div>
        )}

        {activeTab === 'patterns' && (
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
            <div style={{
              padding: '24px',
              backgroundColor: 'rgba(31, 41, 55, 0.5)',
              borderRadius: '12px',
              border: '1px solid #374151'
            }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px', color: '#10b981' }}>
                Exception Patterns
              </h3>
              <ul style={{ listStyle: 'none', padding: 0 }}>
                {[
                  'Try-with-resources for auto-closing',
                  'Multi-catch for similar handling',
                  'Exception chaining for context',
                  'Circuit breaker for fault tolerance',
                  'Retry logic with exponential backoff'
                ].map((pattern, index) => (
                  <li key={index} style={{ padding: '8px 0', color: '#94a3b8', fontSize: '14px' }}>
                    <span style={{ color: '#10b981', marginRight: '8px' }}>•</span>
                    {pattern}
                  </li>
                ))}
              </ul>
            </div>
            <div style={{
              padding: '24px',
              backgroundColor: 'rgba(31, 41, 55, 0.5)',
              borderRadius: '12px',
              border: '1px solid #374151'
            }}>
              <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px', color: '#ef4444' }}>
                Anti-Patterns to Avoid
              </h3>
              <ul style={{ listStyle: 'none', padding: 0 }}>
                {[
                  'Catching Exception or Throwable',
                  'Empty catch blocks',
                  'Throwing from finally',
                  'Using exceptions for flow control',
                  'Losing stack traces'
                ].map((antipattern, index) => (
                  <li key={index} style={{ padding: '8px 0', color: '#94a3b8', fontSize: '14px' }}>
                    <span style={{ color: '#ef4444', marginRight: '8px' }}>•</span>
                    {antipattern}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {activeTab === 'guidelines' && (
          <div style={{
            padding: '24px',
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            border: '1px solid #374151'
          }}>
            <h3 style={{ fontSize: '24px', fontWeight: '600', marginBottom: '20px', color: '#10b981' }}>
              Exception Handling Guidelines
            </h3>
            <div style={{ display: 'grid', gap: '16px' }}>
              {[
                {
                  title: 'Use Checked Exceptions for Recoverable Errors',
                  description: 'Business logic errors that the caller can handle'
                },
                {
                  title: 'Use Unchecked Exceptions for Programming Errors',
                  description: 'Bugs, null pointers, illegal arguments'
                },
                {
                  title: 'Provide Context in Exceptions',
                  description: 'Include relevant data to help debugging'
                },
                {
                  title: 'Document Exceptions in JavaDoc',
                  description: 'Use @throws to document what can be thrown'
                },
                {
                  title: 'Log at Appropriate Levels',
                  description: 'ERROR for unexpected, WARN for business errors'
                }
              ].map((guideline, index) => (
                <div key={index} style={{
                  padding: '16px',
                  backgroundColor: '#1e293b',
                  borderRadius: '8px',
                  borderLeft: '4px solid #10b981'
                }}>
                  <h4 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '8px' }}>
                    {guideline.title}
                  </h4>
                  <p style={{ color: '#94a3b8', fontSize: '14px' }}>
                    {guideline.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      
        {/* Details Panel - Moved to bottom */}
        {selectedTopic && (
          <div style={{
            backgroundColor: 'rgba(31, 41, 55, 0.5)',
            borderRadius: '12px',
            border: '1px solid #374151',
            padding: '24px',
            marginTop: '32px'
          }}>
            <div style={{ marginBottom: '20px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                <h2 style={{ fontSize: '24px', fontWeight: 'bold', color: '#10b981' }}>
                  {topics.find(t => t.id === selectedTopic)?.name}
                </h2>
                <button
                  onClick={() => setSelectedTopic(null)}
                  style={{
                    background: 'none',
                    border: 'none',
                    color: '#9ca3af',
                    fontSize: '24px',
                    cursor: 'pointer'
                  }}
                >
                  ×
                </button>
              </div>
              
              {/* Exception Handling Tabs */}
              <div style={{ display: 'flex', borderBottom: '1px solid #374151' }}>
                {['overview', 'strategies', 'recovery', 'monitoring'].map(tab => (
                  <button
                    key={tab}
                    onClick={() => setDetailsTab(tab)}
                    style={{
                      padding: '8px 16px',
                      backgroundColor: detailsTab === tab ? '#10b981' : 'transparent',
                      color: detailsTab === tab ? 'white' : '#9ca3af',
                      border: 'none',
                      borderBottom: detailsTab === tab ? '2px solid #10b981' : '2px solid transparent',
                      cursor: 'pointer',
                      fontSize: '14px',
                      textTransform: 'capitalize',
                      transition: 'all 0.2s'
                    }}
                  >
                    {tab}
                  </button>
                ))}
              </div>
            </div>
            
            {detailsTab === 'overview' && (
              <div>
                <div style={{ marginBottom: '24px' }}>
                  <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '12px', color: '#10b981' }}>
                    Exception Handling in Trading Systems
                  </h3>
                  <p style={{ color: '#e2e8f0', lineHeight: '1.6' }}>
                    {selectedTopic === 'hierarchy' ? (
                      "Exception hierarchy design is critical for trading systems where different types of errors require different handling strategies. Market data failures, order validation errors, and system failures each need specific recovery approaches."
                    ) : selectedTopic === 'handling' ? (
                      "Exception handling patterns ensure trading systems remain resilient during market volatility. Proper error handling prevents cascading failures and maintains system availability during critical trading periods."
                    ) : selectedTopic === 'custom' ? (
                      "Custom exceptions provide domain-specific error information essential for trading operations. They capture business context like order details, market conditions, and regulatory requirements for proper error resolution."
                    ) : (
                      "Best practices in exception handling ensure trading systems meet regulatory requirements for error tracking, maintain audit trails, and provide clear operational guidance during system failures."
                    )}
                  </p>
                </div>
                
                <div style={{ marginBottom: '24px' }}>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Trading System Impact</h4>
                  <ul style={{ color: '#e2e8f0', paddingLeft: '20px' }}>
                    {selectedTopic === 'hierarchy' && [
                      'Distinguish business errors from system failures',
                      'Enable appropriate recovery strategies per error type',
                      'Support regulatory reporting requirements',
                      'Provide clear escalation paths for operators'
                    ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                    
                    {selectedTopic === 'handling' && [
                      'Prevent trading halts due to unhandled exceptions',
                      'Maintain order processing during network issues',
                      'Ensure proper resource cleanup for connections',
                      'Enable graceful degradation during overload'
                    ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                    
                    {selectedTopic === 'custom' && [
                      'Capture business context for error analysis',
                      'Enable specific recovery actions per error type',
                      'Support automated error classification',
                      'Provide detailed audit information'
                    ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                    
                    {selectedTopic === 'best-practices' && [
                      'Ensure regulatory compliance for error tracking',
                      'Minimize system downtime through proper handling',
                      'Enable rapid problem identification and resolution',
                      'Support high-availability trading requirements'
                    ].map((benefit, idx) => <li key={idx} style={{ marginBottom: '8px' }}>{benefit}</li>)}
                  </ul>
                </div>
                
                <div>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#ef4444' }}>Implementation Example</h4>
                  <div style={{
                    backgroundColor: '#1e293b',
                    padding: '16px',
                    borderRadius: '8px',
                    border: '1px solid #374151'
                  }}>
                    <SyntaxHighlighter
                      language="java"
                      style={oneDark}
                      customStyle={{
                        backgroundColor: 'transparent',
                        padding: 0,
                        margin: 0,
                        fontSize: '12px'
                      }}
                    >
                      {selectedTopic === 'hierarchy' ? `// Dark Pool Exception Hierarchy
public abstract class DarkPoolException extends Exception {
    private final ErrorCode code;
    private final Instant timestamp;
    private final Map<String, Object> context;
    
    protected DarkPoolException(ErrorCode code, String message) {
        super(message);
        this.code = code;
        this.timestamp = Instant.now();
        this.context = new HashMap<>();
    }
}

// Business exceptions (checked)
public class OrderMatchingException extends DarkPoolException {
    public OrderMatchingException(String orderId, String reason) {
        super(ErrorCode.ORDER_MATCHING_FAILED, 
              "Order " + orderId + " matching failed: " + reason);
        context.put("orderId", orderId);
        context.put("reason", reason);
    }
}

// System exceptions (unchecked)
public class DarkPoolSystemException extends RuntimeException {
    public DarkPoolSystemException(String message, Throwable cause) {
        super(message, cause);
    }
}` : selectedTopic === 'handling' ? `// Robust Error Handling for Dark Pool
public class DarkPoolOrderProcessor {
    private static final int MAX_RETRIES = 3;
    private final RetryPolicy retryPolicy = RetryPolicy.exponentialBackoff();
    
    public OrderResult processOrder(Order order) {
        try {
            return attemptOrderProcessing(order);
        } catch (TransientException e) {
            return handleTransientError(order, e);
        } catch (BusinessException e) {
            return handleBusinessError(order, e);
        } catch (Exception e) {
            return handleSystemError(order, e);
        } finally {
            auditOrderAttempt(order);
        }
    }
    
    private OrderResult handleTransientError(Order order, TransientException e) {
        if (e.getAttemptNumber() < MAX_RETRIES) {
            log.warn("Retrying order {} after transient error: {}", 
                    order.getId(), e.getMessage());
            return retryPolicy.execute(() -> processOrder(order));
        }
        return OrderResult.failed("Max retries exceeded");
    }
}` : selectedTopic === 'custom' ? `// Custom Dark Pool Exceptions with Context
public class HiddenOrderException extends DarkPoolException {
    private final Order order;
    private final ExecutionContext context;
    
    public HiddenOrderException(Order order, String reason) {
        super(ErrorCode.HIDDEN_ORDER_ERROR, 
              "Hidden order processing failed: " + reason);
        this.order = order;
        this.context = ExecutionContext.current();
        
        // Capture trading context
        addContext("symbol", order.getSymbol());
        addContext("orderType", order.getType());
        addContext("marketState", context.getMarketState());
        addContext("liquidityLevel", context.getLiquidityLevel());
    }
    
    public boolean isRecoverable() {
        return !context.isMarketClosed() && 
               context.getLiquidityLevel() > MINIMUM_LIQUIDITY;
    }
    
    public RecoveryAction suggestRecoveryAction() {
        if (context.isHighVolatility()) {
            return RecoveryAction.DEFER_EXECUTION;
        }
        return RecoveryAction.RETRY_WITH_BACKOFF;
    }
}` : `// Exception Handling Best Practices
public class TradingExceptionHandler {
    private final AlertService alertService;
    private final AuditLogger auditLogger;
    
    @ExceptionHandler(TradingException.class)
    public ResponseEntity<?> handleTradingException(TradingException e) {
        // Log with appropriate level
        if (e.isBusinessError()) {
            log.warn("Business error: {}", e.getMessage());
        } else {
            log.error("System error", e);
            alertService.sendAlert(AlertLevel.HIGH, e);
        }
        
        // Audit all trading errors
        auditLogger.logError(AuditEvent.builder()
            .errorCode(e.getCode())
            .message(e.getMessage())
            .context(e.getContext())
            .timestamp(e.getTimestamp())
            .build());
        
        // Return structured error response
        return ResponseEntity.badRequest().body(
            ErrorResponse.from(e)
        );
    }
    
    @ExceptionHandler(Exception.class)
    public ResponseEntity<?> handleUnexpectedError(Exception e) {
        // Never expose internal details
        log.error("Unexpected error", e);
        alertService.sendAlert(AlertLevel.CRITICAL, e);
        
        return ResponseEntity.status(500).body(
            ErrorResponse.generic("Internal system error")
        );
    }
}`}
                    </SyntaxHighlighter>
                  </div>
                </div>
              </div>
            )}
            
            {detailsTab === 'strategies' && (
              <div>
                <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px', color: '#10b981' }}>Error Handling Strategies</h3>
                
                <div style={{ marginBottom: '24px' }}>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Fail-Fast Strategy</h4>
                  <p style={{ color: '#e2e8f0', marginBottom: '12px', lineHeight: '1.6' }}>
                    Detect errors as early as possible and halt processing to prevent corrupted state.
                  </p>
                  <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px' }}>
                    <SyntaxHighlighter language="java" style={oneDark} customStyle={{ backgroundColor: 'transparent', padding: 0, margin: 0, fontSize: '12px' }}>
{`public void validateOrder(Order order) {
    Objects.requireNonNull(order, "Order cannot be null");
    
    if (order.getQuantity() <= 0) {
        throw new IllegalArgumentException("Quantity must be positive");
    }
    
    if (!isValidSymbol(order.getSymbol())) {
        throw new InvalidSymbolException(order.getSymbol());
    }
    
    // Fail fast on validation errors
    if (!isMarketOpen(order.getSymbol())) {
        throw new MarketClosedException(order.getSymbol());
    }
}`}
                    </SyntaxHighlighter>
                  </div>
                </div>
                
                <div style={{ marginBottom: '24px' }}>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Circuit Breaker Pattern</h4>
                  <p style={{ color: '#e2e8f0', marginBottom: '12px', lineHeight: '1.6' }}>
                    Prevent cascading failures by temporarily stopping requests to failing services.
                  </p>
                  <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px' }}>
                    <SyntaxHighlighter language="java" style={oneDark} customStyle={{ backgroundColor: 'transparent', padding: 0, margin: 0, fontSize: '12px' }}>
{`public class MarketDataCircuitBreaker {
    private final AtomicInteger failures = new AtomicInteger(0);
    private volatile boolean open = false;
    private volatile long lastFailureTime = 0;
    
    public Optional<MarketData> getMarketData(String symbol) {
        if (isCircuitOpen()) {
            throw new ServiceUnavailableException("Market data circuit breaker is open");
        }
        
        try {
            MarketData data = marketDataService.fetch(symbol);
            onSuccess();
            return Optional.of(data);
        } catch (Exception e) {
            onFailure();
            return Optional.empty();
        }
    }
    
    private boolean isCircuitOpen() {
        return open && (System.currentTimeMillis() - lastFailureTime < TIMEOUT);
    }
}`}
                    </SyntaxHighlighter>
                  </div>
                </div>
                
                <div>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Graceful Degradation</h4>
                  <p style={{ color: '#e2e8f0', marginBottom: '12px', lineHeight: '1.6' }}>
                    Continue operating with reduced functionality when components fail.
                  </p>
                  <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px' }}>
                    <SyntaxHighlighter language="java" style={oneDark} customStyle={{ backgroundColor: 'transparent', padding: 0, margin: 0, fontSize: '12px' }}>
{`public class TradingService {
    public OrderResult executeOrder(Order order) {
        try {
            // Try optimal execution path
            return executeWithAdvancedRouting(order);
        } catch (AdvancedRoutingException e) {
            log.warn("Advanced routing failed, falling back to basic execution");
            try {
                return executeWithBasicRouting(order);
            } catch (BasicRoutingException e2) {
                log.error("All routing failed, using manual intervention");
                return OrderResult.pendingManualReview(order);
            }
        }
    }
}`}
                    </SyntaxHighlighter>
                  </div>
                </div>
              </div>
            )}
            
            {detailsTab === 'recovery' && (
              <div>
                <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px', color: '#10b981' }}>Recovery Mechanisms</h3>
                
                <div style={{ marginBottom: '24px' }}>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Automatic Retry Logic</h4>
                  <ul style={{ color: '#e2e8f0', paddingLeft: '20px' }}>
                    <li style={{ marginBottom: '8px' }}>Exponential backoff for transient failures</li>
                    <li style={{ marginBottom: '8px' }}>Maximum retry limits to prevent infinite loops</li>
                    <li style={{ marginBottom: '8px' }}>Jitter to prevent thundering herd</li>
                    <li style={{ marginBottom: '8px' }}>Different strategies for different error types</li>
                  </ul>
                </div>
                
                <div style={{ marginBottom: '24px' }}>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>State Recovery</h4>
                  <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px' }}>
                    <p style={{ color: '#e2e8f0', fontSize: '14px', marginBottom: '8px' }}>Transaction Recovery:</p>
                    <p style={{ color: '#9ca3af', fontSize: '12px', marginBottom: '8px' }}>→ Roll back partial transactions on failure</p>
                    
                    <p style={{ color: '#e2e8f0', fontSize: '14px', marginBottom: '8px', marginTop: '12px' }}>State Snapshots:</p>
                    <p style={{ color: '#9ca3af', fontSize: '12px', marginBottom: '8px' }}>→ Restore to last known good state</p>
                    
                    <p style={{ color: '#e2e8f0', fontSize: '14px', marginBottom: '8px', marginTop: '12px' }}>Event Sourcing:</p>
                    <p style={{ color: '#9ca3af', fontSize: '12px' }}>→ Replay events to rebuild state</p>
                  </div>
                </div>
                
                <div>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Failover Strategies</h4>
                  <div style={{ display: 'grid', gap: '12px' }}>
                    <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', borderLeft: '3px solid #3b82f6' }}>
                      <p style={{ color: '#3b82f6', fontSize: '14px', fontWeight: 'bold' }}>Hot Standby</p>
                      <p style={{ color: '#e2e8f0', fontSize: '12px' }}>Immediate failover to secondary system with real-time replication</p>
                    </div>
                    <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', borderLeft: '3px solid #f59e0b' }}>
                      <p style={{ color: '#f59e0b', fontSize: '14px', fontWeight: 'bold' }}>Cold Standby</p>
                      <p style={{ color: '#e2e8f0', fontSize: '12px' }}>Manual failover to backup system with data restoration</p>
                    </div>
                    <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', borderLeft: '3px solid #10b981' }}>
                      <p style={{ color: '#10b981', fontSize: '14px', fontWeight: 'bold' }}>Load Balancing</p>
                      <p style={{ color: '#e2e8f0', fontSize: '12px' }}>Distribute load across multiple instances with health checks</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            {detailsTab === 'monitoring' && (
              <div>
                <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px', color: '#10b981' }}>Error Monitoring & Alerting</h3>
                
                <div style={{ marginBottom: '24px' }}>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Logging Strategy</h4>
                  <ul style={{ color: '#e2e8f0', paddingLeft: '20px' }}>
                    <li style={{ marginBottom: '8px' }}>ERROR level for system failures requiring immediate attention</li>
                    <li style={{ marginBottom: '8px' }}>WARN level for business errors and recoverable issues</li>
                    <li style={{ marginBottom: '8px' }}>Include correlation IDs for request tracing</li>
                    <li style={{ marginBottom: '8px' }}>Structure logs with searchable fields</li>
                  </ul>
                </div>
                
                <div style={{ marginBottom: '24px' }}>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#f59e0b' }}>Metrics Collection</h4>
                  <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px' }}>
                    <div style={{ display: 'grid', gridTemplateColumns: 'auto 1fr', gap: '12px', alignItems: 'center', fontSize: '12px' }}>
                      <span style={{ color: '#3b82f6', fontWeight: 'bold' }}>Error Rate</span>
                      <span style={{ color: '#e2e8f0' }}>Percentage of failed requests per time window</span>
                      
                      <span style={{ color: '#3b82f6', fontWeight: 'bold' }}>Error Types</span>
                      <span style={{ color: '#e2e8f0' }}>Distribution of different exception types</span>
                      
                      <span style={{ color: '#3b82f6', fontWeight: 'bold' }}>Recovery Time</span>
                      <span style={{ color: '#e2e8f0' }}>Time to recover from failures</span>
                      
                      <span style={{ color: '#3b82f6', fontWeight: 'bold' }}>Circuit Breaker</span>
                      <span style={{ color: '#e2e8f0' }}>Open/closed state and trigger frequency</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px', color: '#ef4444' }}>Alert Escalation</h4>
                  <div style={{ display: 'grid', gap: '12px' }}>
                    <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', borderLeft: '3px solid #ef4444' }}>
                      <p style={{ color: '#ef4444', fontSize: '14px', fontWeight: 'bold' }}>Critical: Trading System Down</p>
                      <p style={{ color: '#e2e8f0', fontSize: '12px' }}>Immediate escalation to on-call engineer and management</p>
                    </div>
                    <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', borderLeft: '3px solid #f59e0b' }}>
                      <p style={{ color: '#f59e0b', fontSize: '14px', fontWeight: 'bold' }}>High: Order Processing Errors</p>
                      <p style={{ color: '#e2e8f0', fontSize: '12px' }}>Alert operations team within 5 minutes</p>
                    </div>
                    <div style={{ backgroundColor: '#1e293b', padding: '12px', borderRadius: '6px', borderLeft: '3px solid #8b5cf6' }}>
                      <p style={{ color: '#8b5cf6', fontSize: '14px', fontWeight: 'bold' }}>Medium: Business Rule Violations</p>
                      <p style={{ color: '#e2e8f0', fontSize: '12px' }}>Daily summary report to compliance team</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
    </div>
  );
};

export default JavaExceptions;
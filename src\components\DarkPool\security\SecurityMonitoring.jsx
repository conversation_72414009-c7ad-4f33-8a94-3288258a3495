import React, { useState } from 'react';
import { 
  Activity, Eye, AlertTriangle, Shield, Monitor, BarChart3,
  Clock, TrendingUp, Bell, Settings, RefreshCw, Download,
  Search, Filter, Zap, Database, Users, Globe, Lock,
  CheckCircle, Info, Book, Target, Code, FileText, Cpu
} from 'lucide-react';

const SecurityMonitoring = () => {
  const [activeTab, setActiveTab] = useState('questions');
  const [selectedTopic, setSelectedTopic] = useState('siem-fundamentals');

  // Security Monitoring Interview Questions
  const interviewQuestions = {
    'siem-fundamentals': {
      title: 'SIEM Fundamentals',
      description: 'Core concepts and architecture of Security Information and Event Management systems',
      questions: [
        {
          question: 'What is SIEM and what are its core components?',
          answer: 'SIEM (Security Information and Event Management) combines Security Information Management (SIM) and Security Event Management (SEM). Core components include: Data Collection (log aggregation), Data Storage (centralized repository), Correlation Engine (rule-based analysis), Dashboards/Reporting, Alerting System, and Case Management.',
          difficulty: 'Fundamental',
          tags: ['Architecture', 'Basics']
        },
        {
          question: 'Explain the difference between correlation rules and analytics in SIEM.',
          answer: 'Correlation rules are predefined logic statements that trigger when specific conditions are met (e.g., "if 5+ failed logins from same IP in 5 minutes"). Analytics use statistical methods, machine learning, and behavioral analysis to identify anomalies and patterns that might not trigger traditional rules.',
          difficulty: 'Intermediate',
          tags: ['Analytics', 'Rules']
        },
        {
          question: 'How do you handle false positives in SIEM systems?',
          answer: 'Strategies include: Rule tuning (adjusting thresholds), Baseline establishment, Whitelisting known good behavior, Context enrichment, Risk scoring, Analyst feedback loops, and Regular review of detection logic. Track false positive rates as KPIs.',
          difficulty: 'Advanced',
          tags: ['Tuning', 'Operations']
        },
        {
          question: 'What is the EPS (Events Per Second) capacity and why is it important?',
          answer: 'EPS measures how many security events a SIEM can process per second. It\'s crucial for: Sizing hardware requirements, Ensuring real-time processing, Planning for peak loads, Determining storage needs, and calculating licensing costs. Typical enterprise SIEMs handle 50k-500k+ EPS.',
          difficulty: 'Intermediate',
          tags: ['Performance', 'Capacity']
        }
      ]
    },
    'log-analysis': {
      title: 'Log Analysis & Parsing',
      description: 'Techniques for parsing, normalizing, and analyzing security logs',
      questions: [
        {
          question: 'How do you parse and normalize logs from different sources?',
          answer: 'Use parsing techniques like: Regex patterns for structured extraction, Grok patterns (Logstash), Field extraction rules, Timestamp normalization, IP/hostname standardization, and Custom parsers for proprietary formats. Create common taxonomy (CIM/OSSEM).',
          difficulty: 'Advanced',
          tags: ['Parsing', 'Normalization']
        },
        {
          question: 'What Windows Event IDs are most critical for security monitoring?',
          answer: 'Key Event IDs: 4624 (Successful logon), 4625 (Failed logon), 4648 (Logon with explicit credentials), 4672 (Admin rights assigned), 4720 (User account created), 4728 (User added to global group), 4732 (User added to local group), 4756 (User added to universal group).',
          difficulty: 'Intermediate',
          tags: ['Windows', 'Event IDs']
        },
        {
          question: 'How would you detect lateral movement using log analysis?',
          answer: 'Look for patterns: Unusual authentication patterns across multiple systems, Process creation on multiple hosts, Network connections between internal systems, Service account usage outside normal scope, PowerShell/WMI activity, and Time-based correlation of activities across the network.',
          difficulty: 'Advanced',
          tags: ['Lateral Movement', 'Detection']
        }
      ]
    },
    'incident-response': {
      title: 'Incident Response',
      description: 'SIEM integration with incident response processes and workflows',
      questions: [
        {
          question: 'How do you integrate SIEM with incident response workflows?',
          answer: 'Integration includes: Automated ticket creation, Escalation workflows, Playbook triggers, Evidence preservation, Chain of custody tracking, Communication templates, Status updates, and Post-incident analysis. Use APIs for SOAR/ITSM integration.',
          difficulty: 'Advanced',
          tags: ['Integration', 'Workflow']
        },
        {
          question: 'What metrics do you use to measure SOC/SIEM effectiveness?',
          answer: 'Key metrics: Mean Time to Detection (MTTD), Mean Time to Response (MTTR), False Positive Rate, Alert Volume, Coverage percentage, Analyst productivity, Escalation rates, SLA compliance, and Detection accuracy. Track trends over time.',
          difficulty: 'Intermediate',
          tags: ['Metrics', 'KPIs']
        },
        {
          question: 'Describe the triage process for security alerts.',
          answer: 'Triage steps: 1) Alert validation and enrichment, 2) Severity/priority assessment, 3) Initial containment if needed, 4) Assignment to appropriate analyst, 5) Investigation and evidence gathering, 6) Response actions, 7) Documentation and closure, 8) Lessons learned integration.',
          difficulty: 'Intermediate',
          tags: ['Triage', 'Process']
        }
      ]
    }
  };

  // Hands-on Lab Scenarios
  const labScenarios = [
    {
      title: 'Suspicious Login Activity Analysis',
      difficulty: 'Intermediate',
      scenario: 'Multiple failed login attempts followed by successful login from different geographic locations',
      tasks: [
        'Analyze authentication logs for patterns',
        'Create correlation rule for geographic anomalies',
        'Implement risk scoring based on multiple factors',
        'Design automated response workflow'
      ],
      sampleQuery: `index=windows EventCode=4625 OR EventCode=4624
| eval src_ip=coalesce(IpAddress, ClientIP, NetworkAddress)
| lookup geoip src_ip
| stats count by Account_Name, src_ip, Country, EventCode
| where count > 5
| sort -count`,
      expectedFindings: [
        'Multiple countries within short timeframe',
        'Impossible travel scenarios',
        'Failed attempts preceding success',
        'Unusual authentication methods'
      ]
    },
    {
      title: 'Data Exfiltration Detection',
      difficulty: 'Advanced',
      scenario: 'Unusual network traffic patterns suggesting large-scale data movement',
      tasks: [
        'Correlate network flows with file access logs',
        'Identify baseline vs anomalous data transfer volumes',
        'Create detection rules for suspicious patterns',
        'Implement automated alerting and containment'
      ],
      sampleQuery: `index=proxy OR index=firewall
| eval bytes_mb=bytes/1024/1024
| stats sum(bytes_mb) as total_mb, count as connections, dc(dest_ip) as unique_destinations by src_ip, user
| where total_mb > 1000 OR unique_destinations > 50
| sort -total_mb`,
      expectedFindings: [
        'Unusual volume of outbound traffic',
        'Connections to suspicious destinations',
        'Off-hours data transfer activity',
        'Correlation with file system access'
      ]
    },
    {
      title: 'APT Campaign Investigation',
      difficulty: 'Expert',
      scenario: 'Multi-stage attack campaign with persistence mechanisms and lateral movement',
      tasks: [
        'Timeline reconstruction across multiple systems',
        'Identify persistence mechanisms',
        'Map lateral movement paths',
        'Attribute tactics to known threat groups'
      ],
      sampleQuery: `(index=windows EventCode=4688) OR (index=sysmon EventID=1) OR (index=proxy)
| eval process_name=coalesce(NewProcessName, Image, ProcessName)
| search process_name="*powershell*" OR process_name="*cmd*" OR process_name="*wmic*"
| transaction Computer maxspan=1h
| where eventcount > 10`,
      expectedFindings: [
        'Multi-stage payload deployment',
        'Living-off-the-land techniques',
        'Command and control communications',
        'Data staging and exfiltration preparation'
      ]
    }
  ];

  // SIEM Tools & Technologies with Configuration Examples
  const siemTools = {
    'splunk': {
      name: 'Splunk Enterprise Security',
      description: 'Leading SIEM platform with advanced analytics and machine learning capabilities',
      configuration: `# indexes.conf - Index Configuration
[security]
homePath = $SPLUNK_DB/security/db
coldPath = $SPLUNK_DB/security/colddb
thawedPath = $SPLUNK_DB/security/thaweddb
maxDataSize = auto_high_volume
maxHotBuckets = 10
maxWarmDBCount = 300

# Correlation Search Example
| tstats count FROM datamodel=Authentication WHERE Authentication.action=failure 
  BY Authentication.src, Authentication.user span=5m
| where count > 5
| eval risk_score = case(
    count > 20, 100,
    count > 10, 75,
    count > 5, 50,
    1=1, 25)
| outputlookup brute_force_attempts`,
      features: [
        'SPL (Search Processing Language)',
        'Machine Learning Toolkit',
        'ES Content Packs',
        'SOAR Integration',
        'Risk-Based Alerting',
        'Threat Intelligence Framework'
      ],
      useCases: [
        'Advanced Persistent Threats',
        'Insider Threat Detection',
        'Compliance Reporting',
        'Threat Hunting'
      ]
    },
    'elk-stack': {
      name: 'ELK Stack (Elasticsearch, Logstash, Kibana)',
      description: 'Open-source SIEM solution with powerful search and visualization capabilities',
      configuration: `# Logstash Configuration - Windows Security Logs
input {
  beats {
    port => 5044
  }
}

filter {
  if [winlog][event_id] == 4625 {
    grok {
      match => { 
        "[winlog][event_data][IpAddress]" => "%{IP:source_ip}" 
      }
    }
    
    geoip {
      source => "source_ip"
      target => "geoip"
    }
    
    mutate {
      add_field => { "alert_type" => "failed_login" }
      add_field => { "severity" => "medium" }
    }
  }
}

output {
  elasticsearch {
    hosts => ["localhost:9200"]
    index => "security-logs-%{+YYYY.MM.dd}"
  }
}`,
      features: [
        'Elasticsearch Query DSL',
        'Kibana Visualizations',
        'Watcher Alerting',
        'X-Pack Security',
        'Machine Learning Jobs',
        'Canvas Dashboards'
      ],
      useCases: [
        'Log Aggregation',
        'Real-time Analytics',
        'Custom Dashboards',
        'Cost-effective SIEM'
      ]
    },
    'sentinel': {
      name: 'Microsoft Sentinel',
      description: 'Cloud-native SIEM with AI-powered security analytics and automated response',
      configuration: `// KQL Query - Detect Suspicious PowerShell Activity
SecurityEvent
| where TimeGenerated > ago(24h)
| where EventID == 4688
| where Process contains "powershell"
| extend CommandLine = tostring(CommandLine)
| where CommandLine contains "-enc" or 
        CommandLine contains "-e " or
        CommandLine contains "iex" or
        CommandLine contains "invoke-expression"
| summarize count() by Computer, Account, CommandLine, TimeGenerated
| where count_ > 1
| project TimeGenerated, Computer, Account, CommandLine, count_
| sort by TimeGenerated desc

// Analytics Rule - Brute Force Detection
let threshold = 10;
let timeframe = 5m;
SigninLogs
| where TimeGenerated > ago(timeframe)
| where ResultType != "0"
| summarize FailureCount = count() by IPAddress, UserPrincipalName, bin(TimeGenerated, timeframe)
| where FailureCount > threshold
| project IPAddress, UserPrincipalName, FailureCount, TimeGenerated`,
      features: [
        'KQL (Kusto Query Language)',
        'Built-in Analytics Rules',
        'Logic Apps Integration',
        'Threat Intelligence Platform',
        'Investigation Workbooks',
        'Automated Playbooks'
      ],
      useCases: [
        'Cloud Security Monitoring',
        'Hybrid Environment Protection',
        'Automated Response',
        'Microsoft 365 Security'
      ]
    }
  };

  // Case Studies
  const caseStudies = [
    {
      title: 'SolarWinds Supply Chain Attack',
      category: 'APT Campaign',
      overview: 'Nation-state actors compromised software supply chain affecting thousands of organizations',
      timeline: 'March 2020 - December 2020',
      detectionChallenges: [
        'Signed malicious code bypassing security controls',
        'Living-off-the-land techniques avoiding detection',
        'Long dwell time before discovery',
        'Legitimate network traffic masking C2 communications'
      ],
      siemDetectionStrategies: [
        'Baseline normal SolarWinds Orion behavior patterns',
        'Monitor for unusual network connections from management servers',
        'Correlate DNS requests with known IOCs',
        'Detect suspicious PowerShell and file system activities',
        'Implement certificate transparency monitoring'
      ],
      lessonsLearned: [
        'Supply chain security monitoring is critical',
        'Behavioral analysis more effective than signature-based detection',
        'Need for continuous monitoring of management infrastructure',
        'Importance of threat intelligence integration'
      ]
    },
    {
      title: 'Insider Threat - Edward Snowden Case',
      category: 'Insider Threat',
      overview: 'Privileged user exfiltrated classified documents over extended period',
      timeline: '2012 - 2013',
      detectionChallenges: [
        'Legitimate access to sensitive systems',
        'Authorized data downloads',
        'Lack of behavioral baseline',
        'Insufficient monitoring of privileged users'
      ],
      siemDetectionStrategies: [
        'User and Entity Behavior Analytics (UEBA)',
        'Data Loss Prevention (DLP) integration',
        'Privileged access monitoring',
        'Unusual data access pattern detection',
        'Off-hours activity monitoring',
        'Volume-based anomaly detection'
      ],
      lessonsLearned: [
        'Critical need for insider threat programs',
        'Behavioral analytics over rule-based detection',
        'Importance of data classification and monitoring',
        'Need for continuous privileged access review'
      ]
    }
  ];

  // Performance Metrics & SLAs
  const performanceMetrics = [
    {
      category: 'Detection Metrics',
      metrics: [
        {
          name: 'Mean Time to Detection (MTTD)',
          target: '< 15 minutes',
          description: 'Average time from event occurrence to alert generation',
          calculation: 'Sum of (Alert Time - Event Time) / Number of Alerts'
        },
        {
          name: 'False Positive Rate',
          target: '< 5%',
          description: 'Percentage of alerts that are determined to be false positives',
          calculation: '(False Positives / Total Alerts) × 100'
        },
        {
          name: 'Coverage Percentage',
          target: '> 95%',
          description: 'Percentage of critical assets and attack vectors monitored',
          calculation: '(Monitored Assets / Total Critical Assets) × 100'
        }
      ]
    },
    {
      category: 'Response Metrics',
      metrics: [
        {
          name: 'Mean Time to Response (MTTR)',
          target: '< 30 minutes',
          description: 'Average time from alert generation to response initiation',
          calculation: 'Sum of (Response Time - Alert Time) / Number of Incidents'
        },
        {
          name: 'Alert Escalation Rate',
          target: '< 10%',
          description: 'Percentage of alerts that require escalation to senior analysts',
          calculation: '(Escalated Alerts / Total Alerts) × 100'
        },
        {
          name: 'SLA Compliance',
          target: '> 98%',
          description: 'Percentage of incidents handled within defined SLA timeframes',
          calculation: '(Incidents Meeting SLA / Total Incidents) × 100'
        }
      ]
    },
    {
      category: 'Operational Metrics',
      metrics: [
        {
          name: 'System Availability',
          target: '> 99.9%',
          description: 'Percentage of time SIEM system is operational',
          calculation: '(Uptime Hours / Total Hours) × 100'
        },
        {
          name: 'Data Ingestion Rate',
          target: 'Variable',
          description: 'Events per second processed by the SIEM',
          calculation: 'Total Events / Time Period (seconds)'
        },
        {
          name: 'Storage Utilization',
          target: '< 80%',
          description: 'Percentage of allocated storage space used',
          calculation: '(Used Storage / Total Storage) × 100'
        }
      ]
    }
  ];

  const getSeverityColor = (severity) => {
    switch(severity) {
      case 'Expert': return '#ef4444';
      case 'Advanced': return '#f59e0b';
      case 'Intermediate': return '#3b82f6';
      case 'Fundamental': return '#10b981';
      default: return '#6b7280';
    }
  };

  const getDifficultyColor = (difficulty) => {
    switch(difficulty) {
      case 'Expert': return '#ef4444';
      case 'Advanced': return '#f59e0b';
      case 'Intermediate': return '#3b82f6';
      case 'Fundamental': return '#10b981';
      default: return '#6b7280';
    }
  };

  return (
    <div style={{
      width: '100%',
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%)',
      color: '#ffffff',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif',
      padding: '20px'
    }}>
      {/* Header */}
      <div style={{
        padding: '24px',
        background: 'rgba(15, 23, 42, 0.8)',
        borderRadius: '16px',
        border: '1px solid rgba(51, 65, 85, 0.5)',
        marginBottom: '24px'
      }}>
        <h1 style={{
          fontSize: '36px',
          margin: '0 0 8px 0',
          background: 'linear-gradient(135deg, #10b981 0%, #3b82f6 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          fontWeight: '700'
        }}>
          Security Monitoring Interview Prep
        </h1>
        <p style={{
          fontSize: '16px',
          color: '#94a3b8',
          margin: 0
        }}>
          SIEM fundamentals, log analysis, incident response scenarios, and monitoring best practices
        </p>
      </div>

      {/* Navigation Tabs */}
      <div style={{
        display: 'flex',
        gap: '4px',
        marginBottom: '24px',
        backgroundColor: 'rgba(15, 23, 42, 0.6)',
        borderRadius: '12px',
        padding: '8px',
        border: '1px solid rgba(51, 65, 85, 0.5)'
      }}>
        {[
          { id: 'questions', label: 'Interview Questions', icon: <Book size={16} /> },
          { id: 'scenarios', label: 'Lab Scenarios', icon: <Target size={16} /> },
          { id: 'tools', label: 'SIEM Tools', icon: <Settings size={16} /> },
          { id: 'cases', label: 'Case Studies', icon: <FileText size={16} /> },
          { id: 'metrics', label: 'Performance Metrics', icon: <BarChart3 size={16} /> }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '12px 16px',
              backgroundColor: activeTab === tab.id ? '#3b82f6' : 'transparent',
              color: activeTab === tab.id ? 'white' : '#94a3b8',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500',
              transition: 'all 0.2s',
              flex: 1,
              justifyContent: 'center'
            }}
            onMouseEnter={(e) => {
              if (activeTab !== tab.id) {
                e.currentTarget.style.backgroundColor = 'rgba(59, 130, 246, 0.1)';
                e.currentTarget.style.color = '#3b82f6';
              }
            }}
            onMouseLeave={(e) => {
              if (activeTab !== tab.id) {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = '#94a3b8';
              }
            }}
          >
            {tab.icon}
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'questions' && (
        <div style={{ display: 'flex', gap: '24px' }}>
          {/* Topic Selection Sidebar */}
          <div style={{
            width: '300px',
            background: 'rgba(15, 23, 42, 0.8)',
            borderRadius: '16px',
            border: '1px solid rgba(51, 65, 85, 0.5)',
            padding: '20px'
          }}>
            <h3 style={{ fontSize: '18px', color: 'white', marginBottom: '16px' }}>
              Interview Topics
            </h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              {Object.entries(interviewQuestions).map(([key, topic]) => (
                <button
                  key={key}
                  onClick={() => setSelectedTopic(key)}
                  style={{
                    padding: '12px 16px',
                    backgroundColor: selectedTopic === key ? 'rgba(59, 130, 246, 0.2)' : 'rgba(31, 41, 55, 0.3)',
                    border: selectedTopic === key ? '1px solid #3b82f6' : '1px solid rgba(51, 65, 85, 0.3)',
                    borderRadius: '8px',
                    color: selectedTopic === key ? '#60a5fa' : '#94a3b8',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: '500',
                    transition: 'all 0.2s',
                    textAlign: 'left'
                  }}
                >
                  <div style={{ fontWeight: '600', marginBottom: '4px' }}>
                    {topic.title}
                  </div>
                  <div style={{ fontSize: '11px', opacity: 0.8 }}>
                    {topic.questions.length} questions
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Questions Content */}
          <div style={{ flex: 1 }}>
            {interviewQuestions[selectedTopic] && (
              <div>
                <div style={{
                  background: 'rgba(15, 23, 42, 0.8)',
                  borderRadius: '16px',
                  border: '1px solid rgba(51, 65, 85, 0.5)',
                  padding: '24px',
                  marginBottom: '24px'
                }}>
                  <h2 style={{ fontSize: '24px', color: 'white', marginBottom: '8px' }}>
                    {interviewQuestions[selectedTopic].title}
                  </h2>
                  <p style={{ fontSize: '14px', color: '#94a3b8', margin: 0 }}>
                    {interviewQuestions[selectedTopic].description}
                  </p>
                </div>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
                  {interviewQuestions[selectedTopic].questions.map((q, idx) => (
                    <div key={idx} style={{
                      background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
                      borderRadius: '16px',
                      border: '1px solid rgba(51, 65, 85, 0.5)',
                      padding: '24px'
                    }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start', marginBottom: '16px' }}>
                        <h3 style={{ fontSize: '18px', color: 'white', margin: 0, flex: 1, marginRight: '16px' }}>
                          {q.question}
                        </h3>
                        <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                          <span style={{
                            fontSize: '11px',
                            padding: '4px 8px',
                            backgroundColor: `${getDifficultyColor(q.difficulty)}20`,
                            color: getDifficultyColor(q.difficulty),
                            borderRadius: '6px',
                            whiteSpace: 'nowrap'
                          }}>
                            {q.difficulty}
                          </span>
                        </div>
                      </div>

                      <p style={{ fontSize: '14px', color: '#d1d5db', lineHeight: '1.6', marginBottom: '16px' }}>
                        {q.answer}
                      </p>

                      <div style={{ display: 'flex', gap: '6px', flexWrap: 'wrap' }}>
                        {q.tags.map((tag, tagIdx) => (
                          <span key={tagIdx} style={{
                            fontSize: '10px',
                            padding: '3px 6px',
                            backgroundColor: 'rgba(139, 92, 246, 0.1)',
                            color: '#8b5cf6',
                            borderRadius: '4px'
                          }}>
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === 'scenarios' && (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
          {labScenarios.map((scenario, idx) => (
            <div key={idx} style={{
              background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
              borderRadius: '16px',
              border: '1px solid rgba(51, 65, 85, 0.5)',
              padding: '24px'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start', marginBottom: '16px' }}>
                <h3 style={{ fontSize: '22px', color: 'white', margin: 0 }}>
                  {scenario.title}
                </h3>
                <span style={{
                  fontSize: '11px',
                  padding: '4px 8px',
                  backgroundColor: `${getDifficultyColor(scenario.difficulty)}20`,
                  color: getDifficultyColor(scenario.difficulty),
                  borderRadius: '6px'
                }}>
                  {scenario.difficulty}
                </span>
              </div>

              <p style={{ fontSize: '14px', color: '#94a3b8', marginBottom: '20px' }}>
                <strong>Scenario:</strong> {scenario.scenario}
              </p>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px', marginBottom: '20px' }}>
                <div>
                  <h4 style={{ fontSize: '16px', color: '#10b981', marginBottom: '12px' }}>
                    Tasks to Complete:
                  </h4>
                  <ul style={{ margin: 0, paddingLeft: '20px', color: '#d1d5db' }}>
                    {scenario.tasks.map((task, taskIdx) => (
                      <li key={taskIdx} style={{ marginBottom: '6px', fontSize: '14px' }}>
                        {task}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 style={{ fontSize: '16px', color: '#f59e0b', marginBottom: '12px' }}>
                    Expected Findings:
                  </h4>
                  <ul style={{ margin: 0, paddingLeft: '20px', color: '#d1d5db' }}>
                    {scenario.expectedFindings.map((finding, findingIdx) => (
                      <li key={findingIdx} style={{ marginBottom: '6px', fontSize: '14px' }}>
                        {finding}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <div>
                <h4 style={{ fontSize: '16px', color: '#3b82f6', marginBottom: '12px', display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <Code size={16} />
                  Sample Query/Analysis
                </h4>
                <pre style={{
                  fontSize: '12px',
                  backgroundColor: '#0f172a',
                  color: '#d1d5db',
                  padding: '16px',
                  borderRadius: '8px',
                  overflow: 'auto',
                  lineHeight: '1.4',
                  fontFamily: 'Consolas, Monaco, monospace',
                  margin: 0
                }}>
                  {scenario.sampleQuery}
                </pre>
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'tools' && (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
          {Object.values(siemTools).map((tool, idx) => (
            <div key={idx} style={{
              background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
              borderRadius: '16px',
              border: '1px solid rgba(51, 65, 85, 0.5)',
              padding: '24px'
            }}>
              <h3 style={{ fontSize: '24px', color: 'white', marginBottom: '8px' }}>
                {tool.name}
              </h3>
              <p style={{ fontSize: '14px', color: '#94a3b8', marginBottom: '20px' }}>
                {tool.description}
              </p>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px', marginBottom: '20px' }}>
                <div>
                  <h4 style={{ fontSize: '16px', color: '#10b981', marginBottom: '12px' }}>
                    Key Features:
                  </h4>
                  <div style={{ display: 'flex', flexWrap: 'wrap', gap: '6px' }}>
                    {tool.features.map((feature, featureIdx) => (
                      <span key={featureIdx} style={{
                        fontSize: '11px',
                        padding: '4px 8px',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        color: '#10b981',
                        borderRadius: '4px'
                      }}>
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 style={{ fontSize: '16px', color: '#8b5cf6', marginBottom: '12px' }}>
                    Common Use Cases:
                  </h4>
                  <ul style={{ margin: 0, paddingLeft: '20px', color: '#d1d5db' }}>
                    {tool.useCases.map((useCase, useCaseIdx) => (
                      <li key={useCaseIdx} style={{ marginBottom: '4px', fontSize: '13px' }}>
                        {useCase}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <div>
                <h4 style={{ fontSize: '16px', color: '#f59e0b', marginBottom: '12px', display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <Settings size={16} />
                  Configuration Example
                </h4>
                <pre style={{
                  fontSize: '11px',
                  backgroundColor: '#0f172a',
                  color: '#d1d5db',
                  padding: '16px',
                  borderRadius: '8px',
                  overflow: 'auto',
                  maxHeight: '400px',
                  lineHeight: '1.5',
                  fontFamily: 'Consolas, Monaco, monospace',
                  margin: 0
                }}>
                  {tool.configuration}
                </pre>
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'cases' && (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
          {caseStudies.map((caseStudy, idx) => (
            <div key={idx} style={{
              background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
              borderRadius: '16px',
              border: '1px solid rgba(51, 65, 85, 0.5)',
              padding: '24px'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start', marginBottom: '16px' }}>
                <h3 style={{ fontSize: '22px', color: 'white', margin: 0 }}>
                  {caseStudy.title}
                </h3>
                <div style={{ display: 'flex', gap: '8px' }}>
                  <span style={{
                    fontSize: '11px',
                    padding: '4px 8px',
                    backgroundColor: 'rgba(59, 130, 246, 0.2)',
                    color: '#60a5fa',
                    borderRadius: '6px'
                  }}>
                    {caseStudy.category}
                  </span>
                  <span style={{
                    fontSize: '11px',
                    padding: '4px 8px',
                    backgroundColor: 'rgba(107, 114, 128, 0.2)',
                    color: '#9ca3af',
                    borderRadius: '6px'
                  }}>
                    {caseStudy.timeline}
                  </span>
                </div>
              </div>

              <p style={{ fontSize: '14px', color: '#94a3b8', marginBottom: '20px' }}>
                {caseStudy.overview}
              </p>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px', marginBottom: '20px' }}>
                <div>
                  <h4 style={{ fontSize: '16px', color: '#ef4444', marginBottom: '12px' }}>
                    Detection Challenges:
                  </h4>
                  <ul style={{ margin: 0, paddingLeft: '20px', color: '#d1d5db' }}>
                    {caseStudy.detectionChallenges.map((challenge, challengeIdx) => (
                      <li key={challengeIdx} style={{ marginBottom: '6px', fontSize: '13px' }}>
                        {challenge}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 style={{ fontSize: '16px', color: '#10b981', marginBottom: '12px' }}>
                    SIEM Detection Strategies:
                  </h4>
                  <ul style={{ margin: 0, paddingLeft: '20px', color: '#d1d5db' }}>
                    {caseStudy.siemDetectionStrategies.map((strategy, strategyIdx) => (
                      <li key={strategyIdx} style={{ marginBottom: '6px', fontSize: '13px' }}>
                        {strategy}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <div>
                <h4 style={{ fontSize: '16px', color: '#8b5cf6', marginBottom: '12px' }}>
                  Lessons Learned:
                </h4>
                <ul style={{ margin: 0, paddingLeft: '20px', color: '#d1d5db' }}>
                  {caseStudy.lessonsLearned.map((lesson, lessonIdx) => (
                    <li key={lessonIdx} style={{ marginBottom: '6px', fontSize: '14px' }}>
                      {lesson}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'metrics' && (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
          {performanceMetrics.map((category, categoryIdx) => (
            <div key={categoryIdx} style={{
              background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
              borderRadius: '16px',
              border: '1px solid rgba(51, 65, 85, 0.5)',
              padding: '24px'
            }}>
              <h3 style={{ fontSize: '22px', color: 'white', marginBottom: '20px' }}>
                {category.category}
              </h3>

              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))', gap: '16px' }}>
                {category.metrics.map((metric, metricIdx) => (
                  <div key={metricIdx} style={{
                    backgroundColor: 'rgba(31, 41, 55, 0.5)',
                    borderRadius: '12px',
                    border: '1px solid rgba(51, 65, 85, 0.3)',
                    padding: '20px'
                  }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start', marginBottom: '12px' }}>
                      <h4 style={{ fontSize: '16px', fontWeight: '600', color: 'white', margin: 0 }}>
                        {metric.name}
                      </h4>
                      <span style={{
                        fontSize: '12px',
                        padding: '4px 8px',
                        backgroundColor: 'rgba(16, 185, 129, 0.2)',
                        color: '#10b981',
                        borderRadius: '6px',
                        fontWeight: '600'
                      }}>
                        Target: {metric.target}
                      </span>
                    </div>

                    <p style={{ fontSize: '13px', color: '#9ca3af', marginBottom: '12px', lineHeight: '1.4' }}>
                      {metric.description}
                    </p>

                    <div style={{
                      fontSize: '11px',
                      padding: '8px 12px',
                      backgroundColor: 'rgba(59, 130, 246, 0.1)',
                      color: '#60a5fa',
                      borderRadius: '6px',
                      fontFamily: 'Consolas, Monaco, monospace'
                    }}>
                      <strong>Calculation:</strong> {metric.calculation}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}

          {/* Interview Questions about Metrics */}
          <div style={{
            background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
            borderRadius: '16px',
            border: '1px solid rgba(51, 65, 85, 0.5)',
            padding: '24px'
          }}>
            <h3 style={{ fontSize: '22px', color: 'white', marginBottom: '16px' }}>
              Common Interview Questions about Security Monitoring Metrics
            </h3>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              {[
                {
                  question: "How do you balance security monitoring coverage with false positive rates?",
                  answer: "Implement risk-based monitoring focusing on high-value assets, use machine learning for behavioral baselines, implement tiered alerting with different thresholds, and continuously tune rules based on analyst feedback."
                },
                {
                  question: "What factors contribute to high MTTR and how do you reduce it?",
                  answer: "Factors include: poor alert quality, lack of context, manual processes, skill gaps. Reduce by: improving alert enrichment, automating response workflows, providing better tooling, and implementing playbooks."
                },
                {
                  question: "How do you measure the effectiveness of your security monitoring program?",
                  answer: "Use metrics like detection coverage, MTTD/MTTR, false positive rates, analyst productivity, threat actor dwell time, and business impact of security incidents. Regular red team exercises validate detection capabilities."
                }
              ].map((item, idx) => (
                <div key={idx} style={{
                  backgroundColor: 'rgba(31, 41, 55, 0.5)',
                  borderRadius: '8px',
                  padding: '16px',
                  borderLeft: '3px solid #3b82f6'
                }}>
                  <h4 style={{ fontSize: '15px', color: '#60a5fa', marginBottom: '8px', fontWeight: '600' }}>
                    {item.question}
                  </h4>
                  <p style={{ fontSize: '13px', color: '#d1d5db', margin: 0, lineHeight: '1.5' }}>
                    {item.answer}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SecurityMonitoring;
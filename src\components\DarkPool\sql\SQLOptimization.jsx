import React, { useState } from 'react';
import { Prism as <PERSON>ynta<PERSON><PERSON><PERSON>lighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Gauge, Zap, Database, Search, BarChart3, Settings } from 'lucide-react';

const SQLOptimization = () => {
  const [selectedTopic, setSelectedTopic] = useState('indexing');
  const [activeTab, setActiveTab] = useState('overview');

  const topics = [
    {
      id: 'indexing',
      name: 'Indexing Strategies',
      icon: <Search size={20} />,
      description: 'Index design and optimization'
    },
    {
      id: 'execution-plans',
      name: 'Execution Plans',
      icon: <BarChart3 size={20} />,
      description: 'Query plan analysis and tuning'
    },
    {
      id: 'query-optimization',
      name: 'Query Optimization',
      icon: <Zap size={20} />,
      description: 'Writing efficient SQL queries'
    },
    {
      id: 'partitioning',
      name: 'Table Partitioning',
      icon: <Database size={20} />,
      description: 'Data partitioning strategies'
    },
    {
      id: 'statistics',
      name: 'Database Statistics',
      icon: <Gauge size={20} />,
      description: 'Statistics maintenance and analysis'
    },
    {
      id: 'performance-tuning',
      name: 'Performance Tuning',
      icon: <Settings size={20} />,
      description: 'System-level optimization'
    }
  ];

  const definitions = {
    'indexing': `Database indexes are data structures that improve query performance by creating shortcuts to data. In trading systems, proper indexing is critical for fast order lookups, real-time price queries, and rapid trade settlement. The key is balancing query speed with storage overhead and maintenance costs.`,
    'execution-plans': `Execution plans show how the database engine processes a query, revealing the operations performed and their costs. Understanding execution plans is essential for identifying performance bottlenecks in trading systems where milliseconds matter for order processing and risk calculations.`,
    'query-optimization': `Query optimization involves writing SQL that executes efficiently by minimizing resource usage and execution time. In high-frequency trading systems, optimized queries can mean the difference between profitable and unprofitable trades, especially for real-time market data processing.`,
    'partitioning': `Table partitioning divides large tables into smaller, more manageable pieces based on specified criteria. For trading systems with millions of daily transactions, partitioning by date or symbol can dramatically improve query performance and maintenance operations.`,
    'statistics': `Database statistics provide the query optimizer with information about data distribution and cardinality. Accurate statistics are crucial for trading systems to ensure optimal query plans, especially for complex analytical queries on large datasets.`,
    'performance-tuning': `Performance tuning encompasses system-level optimizations including memory allocation, I/O optimization, and configuration tuning. Trading systems require careful tuning to handle high-frequency data ingestion and low-latency query processing.`
  };

  const codeExamples = {
    'indexing': `-- Indexing strategies for trading systems
-- Basic index creation for frequently queried columns
CREATE INDEX idx_orders_trader_symbol ON orders (trader_id, symbol);
CREATE INDEX idx_orders_status_time ON orders (status, created_at);
CREATE INDEX idx_trades_symbol_time ON trades (symbol, trade_time);
CREATE INDEX idx_market_data_symbol_timestamp ON market_data (symbol, timestamp);

-- Composite index for multi-column queries
CREATE INDEX idx_orders_composite ON orders (trader_id, symbol, status, created_at);

-- Covering index to avoid key lookups
CREATE INDEX idx_orders_covering ON orders (symbol, status) 
INCLUDE (order_id, trader_id, quantity, price, created_at);

-- Partial index for specific conditions (PostgreSQL)
CREATE INDEX idx_orders_active ON orders (symbol, created_at) 
WHERE status IN ('NEW', 'PARTIALLY_FILLED');

-- Functional index for calculated columns
CREATE INDEX idx_orders_value ON orders ((quantity * price));
CREATE INDEX idx_market_data_day ON market_data (DATE(timestamp));

-- Index for JSON columns (MySQL 8.0+)
CREATE INDEX idx_order_metadata ON orders ((CAST(metadata->>'$.risk_score' AS DECIMAL(5,2))));

-- Show index usage statistics
SELECT 
    i.name as index_name,
    i.type_desc,
    s.user_seeks,
    s.user_scans,
    s.user_lookups,
    s.user_updates,
    (s.user_seeks + s.user_scans + s.user_lookups) as total_reads,
    s.user_updates,
    CASE 
        WHEN s.user_updates > (s.user_seeks + s.user_scans + s.user_lookups) 
        THEN 'Consider dropping - more writes than reads'
        WHEN s.user_seeks + s.user_scans + s.user_lookups = 0 
        THEN 'Unused index'
        ELSE 'Active index'
    END as recommendation
FROM sys.indexes i
JOIN sys.dm_db_index_usage_stats s ON i.object_id = s.object_id AND i.index_id = s.index_id
JOIN sys.objects o ON i.object_id = o.object_id
WHERE o.name IN ('orders', 'trades', 'market_data')
ORDER BY total_reads DESC;

-- Index fragmentation analysis
SELECT 
    i.name as index_name,
    ips.avg_fragmentation_in_percent,
    ips.page_count,
    CASE 
        WHEN ips.avg_fragmentation_in_percent > 30 THEN 'REBUILD'
        WHEN ips.avg_fragmentation_in_percent > 10 THEN 'REORGANIZE'
        ELSE 'OK'
    END as recommendation
FROM sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, 'DETAILED') ips
JOIN sys.indexes i ON ips.object_id = i.object_id AND ips.index_id = i.index_id
WHERE ips.page_count > 100  -- Only show substantial indexes
ORDER BY ips.avg_fragmentation_in_percent DESC;

-- Missing index suggestions (SQL Server)
SELECT 
    mid.statement as table_name,
    mid.equality_columns,
    mid.inequality_columns,
    mid.included_columns,
    migs.user_seeks * migs.avg_total_user_cost * (migs.avg_user_impact / 100.0) as improvement_measure,
    'CREATE INDEX IX_' + REPLACE(REPLACE(REPLACE(mid.statement, '[', ''), ']', ''), '.', '_') + '_suggested'
    + ' ON ' + mid.statement + ' (' + ISNULL(mid.equality_columns, '') 
    + CASE WHEN mid.inequality_columns IS NOT NULL THEN ', ' + mid.inequality_columns ELSE '' END + ')'
    + CASE WHEN mid.included_columns IS NOT NULL THEN ' INCLUDE (' + mid.included_columns + ')' ELSE '' END as create_statement
FROM sys.dm_db_missing_index_details mid
JOIN sys.dm_db_missing_index_groups mig ON mid.index_handle = mig.index_handle
JOIN sys.dm_db_missing_index_group_stats migs ON mig.index_group_handle = migs.group_handle
WHERE mid.statement LIKE '%orders%' OR mid.statement LIKE '%trades%' OR mid.statement LIKE '%market_data%'
ORDER BY improvement_measure DESC;`,

    'execution-plans': `-- Execution plan analysis for trading queries
-- Enable execution plan display
SET STATISTICS IO ON;
SET STATISTICS TIME ON;
EXPLAIN (ANALYZE, BUFFERS, FORMAT TEXT) 
SELECT 
    o.order_id,
    o.symbol,
    o.quantity,
    o.price,
    t.trader_name
FROM orders o
JOIN traders t ON o.trader_id = t.trader_id
WHERE o.symbol = 'AAPL'
  AND o.status = 'NEW'
  AND o.created_at >= CURRENT_DATE;

-- Analyze expensive queries from query store
SELECT TOP 10
    qst.query_sql_text,
    rs.avg_cpu_time,
    rs.avg_duration,
    rs.avg_logical_io_reads,
    rs.avg_physical_io_reads,
    rs.execution_count,
    rs.avg_cpu_time * rs.execution_count as total_cpu_time
FROM sys.query_store_runtime_stats rs
JOIN sys.query_store_plan p ON rs.plan_id = p.plan_id
JOIN sys.query_store_query q ON p.query_id = q.query_id
JOIN sys.query_store_query_text qst ON q.query_text_id = qst.query_text_id
WHERE qst.query_sql_text LIKE '%orders%'
  AND rs.last_execution_time >= DATEADD(hour, -24, GETDATE())
ORDER BY rs.avg_cpu_time DESC;

-- Query performance comparison
WITH query_performance AS (
    SELECT 
        'Original Query' as version,
        COUNT(*) as execution_count,
        AVG(duration_ms) as avg_duration,
        AVG(cpu_time_ms) as avg_cpu_time,
        AVG(logical_reads) as avg_logical_reads
    FROM (
        -- Original query performance metrics would be captured here
        SELECT 100 as duration_ms, 80 as cpu_time_ms, 1000 as logical_reads
    ) original
    
    UNION ALL
    
    SELECT 
        'Optimized Query' as version,
        COUNT(*) as execution_count,
        AVG(duration_ms) as avg_duration,
        AVG(cpu_time_ms) as avg_cpu_time,
        AVG(logical_reads) as avg_logical_reads
    FROM (
        -- Optimized query performance metrics would be captured here
        SELECT 25 as duration_ms, 20 as cpu_time_ms, 250 as logical_reads
    ) optimized
)
SELECT 
    version,
    avg_duration,
    avg_cpu_time,
    avg_logical_reads,
    -- Performance improvement calculations
    LAG(avg_duration) OVER (ORDER BY version) as baseline_duration,
    (LAG(avg_duration) OVER (ORDER BY version) - avg_duration) / 
    LAG(avg_duration) OVER (ORDER BY version) * 100 as duration_improvement_pct
FROM query_performance;

-- Plan cache analysis
SELECT 
    cp.objtype,
    cp.cacheobjtype,
    cp.size_in_bytes / 1024 as size_kb,
    cp.usecounts,
    st.text as query_text,
    qp.query_plan
FROM sys.dm_exec_cached_plans cp
CROSS APPLY sys.dm_exec_sql_text(cp.plan_handle) st
CROSS APPLY sys.dm_exec_query_plan(cp.plan_handle) qp
WHERE st.text LIKE '%orders%' OR st.text LIKE '%trades%'
ORDER BY cp.usecounts DESC, cp.size_in_bytes DESC;

-- Blocking and wait statistics
SELECT 
    r.session_id,
    r.blocking_session_id,
    r.wait_type,
    r.wait_time,
    r.wait_resource,
    t.text as query_text,
    r.cpu_time,
    r.reads,
    r.writes,
    r.logical_reads
FROM sys.dm_exec_requests r
CROSS APPLY sys.dm_exec_sql_text(r.sql_handle) t
WHERE r.session_id > 50  -- Exclude system processes
  AND (t.text LIKE '%orders%' OR t.text LIKE '%trades%' OR t.text LIKE '%market_data%')
ORDER BY r.wait_time DESC;`,

    'query-optimization': `-- Query optimization techniques for trading systems
-- BEFORE: Inefficient query with function in WHERE clause
-- This forces a table scan
SELECT order_id, symbol, quantity, price
FROM orders 
WHERE YEAR(created_at) = 2024 
  AND MONTH(created_at) = 3;

-- AFTER: Optimized with date range
SELECT order_id, symbol, quantity, price
FROM orders 
WHERE created_at >= '2024-03-01' 
  AND created_at < '2024-04-01';

-- BEFORE: SELECT * with unnecessary columns
SELECT * FROM trades t
JOIN orders o ON t.order_id = o.order_id
WHERE t.symbol = 'AAPL';

-- AFTER: Select only needed columns
SELECT t.trade_id, t.symbol, t.quantity, t.price, o.trader_id
FROM trades t
JOIN orders o ON t.order_id = o.order_id
WHERE t.symbol = 'AAPL';

-- BEFORE: Correlated subquery
SELECT o.order_id, o.symbol, o.quantity,
    (SELECT AVG(price) FROM trades WHERE symbol = o.symbol) as avg_price
FROM orders o
WHERE o.status = 'NEW';

-- AFTER: JOIN with aggregated subquery
SELECT o.order_id, o.symbol, o.quantity, t.avg_price
FROM orders o
JOIN (
    SELECT symbol, AVG(price) as avg_price
    FROM trades
    GROUP BY symbol
) t ON o.symbol = t.symbol
WHERE o.status = 'NEW';

-- Optimized pagination for large result sets
-- BEFORE: OFFSET can be slow for large offsets
SELECT order_id, symbol, quantity, price
FROM orders
ORDER BY created_at DESC
LIMIT 100 OFFSET 10000;

-- AFTER: Cursor-based pagination
SELECT order_id, symbol, quantity, price
FROM orders
WHERE created_at < '2024-03-15 10:30:00'  -- Last timestamp from previous page
ORDER BY created_at DESC
LIMIT 100;

-- Batch operations instead of row-by-row processing
-- BEFORE: Multiple single-row updates
UPDATE orders SET status = 'CANCELLED' WHERE order_id = 'ORD-001';
UPDATE orders SET status = 'CANCELLED' WHERE order_id = 'ORD-002';
-- ... hundreds more

-- AFTER: Single batch update
UPDATE orders 
SET status = 'CANCELLED'
WHERE order_id IN (
    SELECT order_id 
    FROM temp_cancelled_orders
);

-- Use EXISTS instead of IN for better performance
-- BEFORE: IN with large subquery
SELECT * FROM orders o
WHERE o.trader_id IN (
    SELECT trader_id FROM traders WHERE desk = 'EQUITY'
);

-- AFTER: EXISTS clause
SELECT * FROM orders o
WHERE EXISTS (
    SELECT 1 FROM traders t 
    WHERE t.trader_id = o.trader_id AND t.desk = 'EQUITY'
);

-- Optimize GROUP BY with covering indexes
-- Query designed to use covering index
SELECT trader_id, symbol, 
       COUNT(*) as order_count,
       SUM(quantity) as total_quantity,
       AVG(price) as avg_price
FROM orders
WHERE status IN ('NEW', 'PARTIALLY_FILLED')
  AND created_at >= CURRENT_DATE
GROUP BY trader_id, symbol;

-- Index hint for specific optimization (SQL Server)
SELECT o.order_id, o.symbol, o.quantity
FROM orders o WITH (INDEX(idx_orders_symbol_time))
WHERE o.symbol = 'AAPL' 
  AND o.created_at >= DATEADD(hour, -1, GETDATE());

-- Query hints for parallel processing
SELECT /*+ PARALLEL(4) */ 
    symbol,
    COUNT(*) as trade_count,
    SUM(quantity) as total_volume,
    AVG(price) as avg_price
FROM trades
WHERE trade_date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY symbol;`,

    'partitioning': `-- Table partitioning strategies for trading systems
-- Range partitioning by date (most common for trading data)
CREATE TABLE trades_partitioned (
    trade_id VARCHAR(50),
    order_id VARCHAR(50),
    symbol VARCHAR(10),
    quantity DECIMAL(15,2),
    price DECIMAL(15,4),
    trade_time TIMESTAMP,
    trade_date DATE GENERATED ALWAYS AS (DATE(trade_time)) STORED,
    PRIMARY KEY (trade_id, trade_date)
) PARTITION BY RANGE (trade_date) (
    PARTITION p_2024_01 VALUES LESS THAN ('2024-02-01'),
    PARTITION p_2024_02 VALUES LESS THAN ('2024-03-01'),
    PARTITION p_2024_03 VALUES LESS THAN ('2024-04-01'),
    PARTITION p_2024_04 VALUES LESS THAN ('2024-05-01'),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- Hash partitioning by symbol for even distribution
CREATE TABLE market_data_partitioned (
    symbol VARCHAR(10),
    timestamp TIMESTAMP,
    price DECIMAL(15,4),
    volume BIGINT,
    bid DECIMAL(15,4),
    ask DECIMAL(15,4),
    PRIMARY KEY (symbol, timestamp)
) PARTITION BY HASH(symbol) PARTITIONS 16;

-- List partitioning by trading desk
CREATE TABLE orders_by_desk (
    order_id VARCHAR(50) PRIMARY KEY,
    trader_id VARCHAR(20),
    desk VARCHAR(20),
    symbol VARCHAR(10),
    quantity DECIMAL(15,2),
    price DECIMAL(15,4),
    status ENUM('NEW', 'FILLED', 'CANCELLED'),
    created_at TIMESTAMP
) PARTITION BY LIST(desk) (
    PARTITION p_equity VALUES IN ('EQUITY', 'ETF'),
    PARTITION p_fixed_income VALUES IN ('BONDS', 'TREASURIES'),
    PARTITION p_derivatives VALUES IN ('OPTIONS', 'FUTURES'),
    PARTITION p_fx VALUES IN ('FX', 'CURRENCY'),
    PARTITION p_commodities VALUES IN ('COMMODITIES', 'METALS')
);

-- Automatic partition management procedures
DELIMITER //
CREATE PROCEDURE add_monthly_partition(IN table_name VARCHAR(64), IN partition_date DATE)
BEGIN
    DECLARE partition_name VARCHAR(64);
    DECLARE next_month DATE;
    
    SET partition_name = CONCAT('p_', DATE_FORMAT(partition_date, '%Y_%m'));
    SET next_month = DATE_ADD(partition_date, INTERVAL 1 MONTH);
    
    SET @sql = CONCAT('ALTER TABLE ', table_name, 
                     ' ADD PARTITION (PARTITION ', partition_name,
                     ' VALUES LESS THAN (''', next_month, '''))');
    
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END//
DELIMITER ;

-- Drop old partitions procedure
DELIMITER //
CREATE PROCEDURE drop_old_partitions(IN table_name VARCHAR(64), IN months_to_keep INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE partition_name VARCHAR(64);
    DECLARE partition_date DATE;
    DECLARE cutoff_date DATE;
    
    DECLARE partition_cursor CURSOR FOR
        SELECT PARTITION_NAME, 
               STR_TO_DATE(SUBSTRING(PARTITION_NAME, 3), '%Y_%m') as part_date
        FROM INFORMATION_SCHEMA.PARTITIONS
        WHERE TABLE_NAME = table_name
          AND PARTITION_NAME IS NOT NULL
          AND PARTITION_NAME != 'p_future';
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    SET cutoff_date = DATE_SUB(CURRENT_DATE, INTERVAL months_to_keep MONTH);
    
    OPEN partition_cursor;
    
    partition_loop: LOOP
        FETCH partition_cursor INTO partition_name, partition_date;
        IF done THEN
            LEAVE partition_loop;
        END IF;
        
        IF partition_date < cutoff_date THEN
            SET @sql = CONCAT('ALTER TABLE ', table_name, 
                             ' DROP PARTITION ', partition_name);
            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
        END IF;
    END LOOP;
    
    CLOSE partition_cursor;
END//
DELIMITER ;

-- Partition pruning examples
-- Query that benefits from partition elimination
SELECT symbol, SUM(quantity) as total_volume
FROM trades_partitioned
WHERE trade_date = '2024-03-15'  -- Only scans p_2024_03 partition
GROUP BY symbol;

-- Range query across multiple partitions
SELECT COUNT(*) as trade_count
FROM trades_partitioned
WHERE trade_date BETWEEN '2024-02-15' AND '2024-03-15';  -- Scans p_2024_02 and p_2024_03

-- Partition information and statistics
SELECT 
    TABLE_NAME,
    PARTITION_NAME,
    PARTITION_DESCRIPTION,
    TABLE_ROWS,
    DATA_LENGTH / 1024 / 1024 as DATA_SIZE_MB,
    INDEX_LENGTH / 1024 / 1024 as INDEX_SIZE_MB,
    CREATE_TIME
FROM INFORMATION_SCHEMA.PARTITIONS
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME IN ('trades_partitioned', 'orders_by_desk', 'market_data_partitioned')
ORDER BY TABLE_NAME, PARTITION_ORDINAL_POSITION;`,

    'statistics': `-- Database statistics management for trading systems
-- Update table statistics manually
UPDATE STATISTICS orders;
UPDATE STATISTICS trades;
UPDATE STATISTICS market_data;

-- Update statistics with full scan for accuracy
UPDATE STATISTICS orders WITH FULLSCAN;
UPDATE STATISTICS trades WITH FULLSCAN, NORECOMPUTE;

-- Create statistics on specific columns
CREATE STATISTICS stat_orders_trader_symbol ON orders (trader_id, symbol);
CREATE STATISTICS stat_orders_quantity_price ON orders (quantity, price);
CREATE STATISTICS stat_trades_volume ON trades (quantity * price);

-- Auto-update statistics configuration
-- Enable auto-update statistics
ALTER DATABASE trading_system SET AUTO_UPDATE_STATISTICS ON;
ALTER DATABASE trading_system SET AUTO_UPDATE_STATISTICS_ASYNC ON;

-- View statistics information
SELECT 
    s.name as statistics_name,
    c.name as column_name,
    s.auto_created,
    s.user_created,
    STATS_DATE(s.object_id, s.stats_id) as last_updated,
    s.modification_counter,
    sp.rows,
    sp.rows_sampled,
    sp.modification_counter
FROM sys.stats s
JOIN sys.stats_columns sc ON s.object_id = sc.object_id AND s.stats_id = sc.stats_id
JOIN sys.columns c ON sc.object_id = c.object_id AND sc.column_id = c.column_id
OUTER APPLY sys.dm_db_stats_properties(s.object_id, s.stats_id) sp
WHERE s.object_id = OBJECT_ID('orders')
ORDER BY s.name;

-- Histogram analysis for data distribution
DBCC SHOW_STATISTICS('orders', 'idx_orders_symbol');
DBCC SHOW_STATISTICS('trades', 'stat_trades_volume');

-- Statistics maintenance script
SELECT 
    t.name as table_name,
    s.name as statistics_name,
    STATS_DATE(s.object_id, s.stats_id) as last_updated,
    sp.modification_counter,
    sp.rows,
    CASE 
        WHEN STATS_DATE(s.object_id, s.stats_id) < DATEADD(day, -7, GETDATE()) 
        THEN 'UPDATE NEEDED'
        WHEN sp.modification_counter > sp.rows * 0.2 
        THEN 'HIGH MODIFICATIONS'
        ELSE 'OK'
    END as status,
    'UPDATE STATISTICS ' + t.name + ' ' + s.name + ' WITH FULLSCAN;' as update_command
FROM sys.tables t
JOIN sys.stats s ON t.object_id = s.object_id
OUTER APPLY sys.dm_db_stats_properties(s.object_id, s.stats_id) sp
WHERE t.name IN ('orders', 'trades', 'market_data', 'portfolio_positions')
  AND s.auto_created = 0  -- Only user-created and index statistics
ORDER BY last_updated ASC;

-- Query plan stability with statistics
-- Force statistics update before critical queries
UPDATE STATISTICS orders WITH FULLSCAN;
UPDATE STATISTICS trades WITH FULLSCAN;

-- Then run critical trading query
SELECT 
    o.trader_id,
    COUNT(*) as order_count,
    SUM(o.quantity * o.price) as total_value,
    AVG(DATEDIFF(second, o.created_at, t.execution_time)) as avg_fill_time
FROM orders o
JOIN trades t ON o.order_id = t.order_id
WHERE o.created_at >= DATEADD(hour, -1, GETDATE())
  AND o.status = 'FILLED'
GROUP BY o.trader_id;

-- Statistics for partitioned tables
SELECT 
    p.partition_number,
    p.rows as partition_rows,
    s.name as statistics_name,
    STATS_DATE(s.object_id, s.stats_id) as last_updated
FROM sys.partitions p
JOIN sys.stats s ON p.object_id = s.object_id
WHERE p.object_id = OBJECT_ID('trades_partitioned')
  AND p.index_id <= 1  -- Table or clustered index only
ORDER BY p.partition_number;

-- Automated statistics maintenance job
CREATE EVENT automated_stats_update
ON SCHEDULE EVERY 1 DAY
STARTS TIMESTAMP(CURRENT_DATE, '02:00:00')
DO
BEGIN
    -- Update statistics on high-activity trading tables
    UPDATE STATISTICS orders WITH FULLSCAN;
    UPDATE STATISTICS trades WITH FULLSCAN;
    UPDATE STATISTICS market_data WITH SAMPLE 50 PERCENT;
    UPDATE STATISTICS portfolio_positions WITH FULLSCAN;
    
    -- Log maintenance activity
    INSERT INTO maintenance_log (operation, table_name, start_time, status)
    VALUES ('UPDATE_STATISTICS', 'ALL_TRADING_TABLES', NOW(), 'COMPLETED');
END;`,

    'performance-tuning': `-- Performance tuning for trading systems
-- Memory optimization settings
-- Configure buffer pool size (80% of available RAM for dedicated DB server)
SET GLOBAL innodb_buffer_pool_size = '32G';  -- For 40GB RAM server
SET GLOBAL innodb_buffer_pool_instances = 8;
SET GLOBAL innodb_log_file_size = '2G';
SET GLOBAL innodb_log_buffer_size = '64M';

-- I/O optimization
SET GLOBAL innodb_io_capacity = 2000;        -- For SSD storage
SET GLOBAL innodb_io_capacity_max = 4000;
SET GLOBAL innodb_read_io_threads = 8;
SET GLOBAL innodb_write_io_threads = 8;
SET GLOBAL innodb_flush_method = 'O_DIRECT';

-- Connection and query optimization
SET GLOBAL max_connections = 1000;
SET GLOBAL thread_cache_size = 100;
SET GLOBAL query_cache_size = 0;             -- Disable query cache for OLTP
SET GLOBAL tmp_table_size = '256M';
SET GLOBAL max_heap_table_size = '256M';

-- Trading system specific optimizations
-- Disable autocommit for batch operations
SET autocommit = 0;

-- Optimize for low latency
SET GLOBAL innodb_flush_log_at_trx_commit = 2;  -- Flush every second
SET GLOBAL sync_binlog = 0;                      -- Don't sync on every commit

-- Monitor active queries and connections
SELECT 
    ID,
    USER,
    HOST,
    DB,
    COMMAND,
    TIME,
    STATE,
    LEFT(INFO, 100) as QUERY_START
FROM INFORMATION_SCHEMA.PROCESSLIST
WHERE COMMAND != 'Sleep'
  AND DB = 'trading_system'
ORDER BY TIME DESC;

-- Identify slow queries in real-time
SELECT 
    query_id,
    LEFT(sql_text, 100) as query_start,
    exec_count,
    avg_timer_wait / 1000000000 as avg_duration_sec,
    sum_timer_wait / 1000000000 as total_duration_sec,
    sum_rows_examined / exec_count as avg_rows_examined,
    sum_rows_sent / exec_count as avg_rows_sent
FROM performance_schema.events_statements_summary_by_digest
WHERE schema_name = 'trading_system'
  AND avg_timer_wait > 1000000000  -- Queries taking more than 1 second
ORDER BY avg_timer_wait DESC
LIMIT 20;

-- Table optimization maintenance
-- Analyze table fragmentation
SELECT 
    table_name,
    data_length / 1024 / 1024 as data_size_mb,
    index_length / 1024 / 1024 as index_size_mb,
    data_free / 1024 / 1024 as free_space_mb,
    (data_free / (data_length + index_length)) * 100 as fragmentation_pct
FROM information_schema.tables
WHERE table_schema = 'trading_system'
  AND table_type = 'BASE TABLE'
ORDER BY fragmentation_pct DESC;

-- Optimize tables with high fragmentation
OPTIMIZE TABLE orders;
OPTIMIZE TABLE trades;
OPTIMIZE TABLE market_data;

-- Connection pooling optimization
-- Configure connection pool settings
SET GLOBAL thread_pool_size = 16;            -- Match CPU cores
SET GLOBAL thread_pool_max_threads = 2000;
SET GLOBAL thread_pool_stall_limit = 10;

-- Monitor buffer pool efficiency
SELECT 
    (SELECT VARIABLE_VALUE FROM performance_schema.global_status 
     WHERE VARIABLE_NAME = 'Innodb_buffer_pool_read_requests') as read_requests,
    (SELECT VARIABLE_VALUE FROM performance_schema.global_status 
     WHERE VARIABLE_NAME = 'Innodb_buffer_pool_reads') as disk_reads,
    (1 - (
        (SELECT VARIABLE_VALUE FROM performance_schema.global_status WHERE VARIABLE_NAME = 'Innodb_buffer_pool_reads') /
        (SELECT VARIABLE_VALUE FROM performance_schema.global_status WHERE VARIABLE_NAME = 'Innodb_buffer_pool_read_requests')
    )) * 100 as buffer_pool_hit_ratio;

-- Lock monitoring for concurrent trading operations
SELECT 
    r.trx_id,
    r.trx_mysql_thread_id,
    r.trx_query,
    b.blocking_trx_id,
    b.blocking_mysql_thread_id,
    l.lock_table,
    l.lock_mode,
    l.lock_type,
    w.requesting_trx_id,
    w.requested_lock_id
FROM information_schema.innodb_lock_waits w
JOIN information_schema.innodb_locks l ON w.requested_lock_id = l.lock_id
JOIN information_schema.innodb_trx r ON w.requesting_trx_id = r.trx_id
JOIN information_schema.innodb_trx b ON w.blocking_trx_id = b.trx_id;

-- Database health monitoring query
SELECT 
    'Connections' as metric,
    VARIABLE_VALUE as current_value,
    (SELECT VARIABLE_VALUE FROM performance_schema.global_variables 
     WHERE VARIABLE_NAME = 'max_connections') as max_value,
    ROUND((VARIABLE_VALUE / (SELECT VARIABLE_VALUE FROM performance_schema.global_variables 
     WHERE VARIABLE_NAME = 'max_connections')) * 100, 2) as utilization_pct
FROM performance_schema.global_status
WHERE VARIABLE_NAME = 'Threads_connected'

UNION ALL

SELECT 
    'Buffer Pool Usage' as metric,
    ROUND(VARIABLE_VALUE / 1024 / 1024, 2) as current_value_mb,
    (SELECT ROUND(VARIABLE_VALUE / 1024 / 1024, 2) FROM performance_schema.global_variables 
     WHERE VARIABLE_NAME = 'innodb_buffer_pool_size') as max_value_mb,
    ROUND((VARIABLE_VALUE / (SELECT VARIABLE_VALUE FROM performance_schema.global_variables 
     WHERE VARIABLE_NAME = 'innodb_buffer_pool_size')) * 100, 2) as utilization_pct
FROM performance_schema.global_status
WHERE VARIABLE_NAME = 'Innodb_buffer_pool_bytes_data';`
  };

  return (
    <div style={{
      padding: '40px',
      fontFamily: 'Inter, system-ui, sans-serif',
      backgroundColor: '#0f172a',
      color: 'white',
      minHeight: '100vh'
    }}>
      <div style={{ marginBottom: '40px' }}>
        <h1 style={{ 
          fontSize: '36px', 
          fontWeight: '700', 
          marginBottom: '16px',
          background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent'
        }}>
          SQL Query Optimization
        </h1>
        <p style={{ fontSize: '18px', color: '#94a3b8', lineHeight: '1.6' }}>
          Master database performance optimization for high-frequency trading systems
        </p>
      </div>

      <div style={{ display: 'flex', gap: '40px' }}>
        <div style={{ flex: '1', maxWidth: '300px' }}>
          <h2 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '24px', color: '#10b981' }}>
            Optimization Topics
          </h2>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {topics.map((topic) => (
              <button
                key={topic.id}
                onClick={() => setSelectedTopic(topic.id)}
                style={{
                  padding: '16px',
                  backgroundColor: selectedTopic === topic.id ? 'rgba(16, 185, 129, 0.2)' : 'rgba(30, 41, 59, 0.5)',
                  border: `2px solid ${selectedTopic === topic.id ? '#10b981' : 'transparent'}`,
                  borderRadius: '12px',
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  textAlign: 'left',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px'
                }}
              >
                <div style={{ color: selectedTopic === topic.id ? '#10b981' : '#64748b' }}>
                  {topic.icon}
                </div>
                <div>
                  <div style={{ 
                    fontWeight: '600', 
                    color: selectedTopic === topic.id ? '#10b981' : 'white',
                    marginBottom: '4px'
                  }}>
                    {topic.name}
                  </div>
                  <div style={{ fontSize: '14px', color: '#94a3b8' }}>
                    {topic.description}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        <div style={{ flex: '2' }}>
          <div style={{ marginBottom: '24px' }}>
            <div style={{ display: 'flex', gap: '8px', marginBottom: '24px' }}>
              {['overview', 'definition', 'code'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  style={{
                    padding: '12px 24px',
                    backgroundColor: activeTab === tab ? '#10b981' : 'transparent',
                    border: `2px solid ${activeTab === tab ? '#10b981' : '#374151'}`,
                    borderRadius: '8px',
                    color: activeTab === tab ? 'white' : '#94a3b8',
                    cursor: 'pointer',
                    fontWeight: '600',
                    textTransform: 'capitalize',
                    transition: 'all 0.2s'
                  }}
                >
                  {tab}
                </button>
              ))}
            </div>
          </div>

          {activeTab === 'overview' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <h2 style={{ fontSize: '28px', fontWeight: '600', marginBottom: '24px', color: '#10b981' }}>
                Query Optimization Overview
              </h2>
              <div style={{ fontSize: '16px', lineHeight: '1.8', color: '#e2e8f0' }}>
                <p style={{ marginBottom: '20px' }}>
                  Query optimization is critical for trading systems where microseconds matter. Proper indexing, 
                  execution plan analysis, and system tuning ensure that high-frequency trading operations 
                  can process thousands of transactions per second with minimal latency.
                </p>
                <div style={{ marginBottom: '24px' }}>
                  <h3 style={{ color: '#10b981', fontSize: '20px', marginBottom: '16px' }}>Key Optimization Areas:</h3>
                  <ul style={{ marginLeft: '24px', lineHeight: '1.8' }}>
                    <li><strong>Indexing:</strong> Strategic index design for fast data retrieval</li>
                    <li><strong>Execution Plans:</strong> Understanding and optimizing query paths</li>
                    <li><strong>Partitioning:</strong> Dividing large tables for better performance</li>
                    <li><strong>Statistics:</strong> Maintaining accurate data distribution information</li>
                    <li><strong>System Tuning:</strong> Memory, I/O, and connection optimization</li>
                  </ul>
                </div>
                <div style={{ 
                  backgroundColor: 'rgba(16, 185, 129, 0.1)', 
                  padding: '20px', 
                  borderRadius: '8px',
                  border: '1px solid #10b981'
                }}>
                  <h4 style={{ color: '#10b981', marginBottom: '12px' }}>Trading System Performance Goals:</h4>
                  <p>
                    Sub-millisecond order processing, real-time risk calculations, and instant market data queries 
                    require carefully optimized databases with proper indexing strategies and system configuration.
                  </p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'definition' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <h2 style={{ fontSize: '24px', fontWeight: '600', marginBottom: '20px', color: '#10b981' }}>
                {topics.find(t => t.id === selectedTopic)?.name} Definition
              </h2>
              <p style={{ fontSize: '16px', lineHeight: '1.8', color: '#e2e8f0' }}>
                {definitions[selectedTopic]}
              </p>
            </div>
          )}

          {activeTab === 'code' && (
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              padding: '32px',
              borderRadius: '16px',
              border: '1px solid #374151'
            }}>
              <div style={{ marginBottom: '20px' }}>
                <h3 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '12px' }}>
                  {topics.find(t => t.id === selectedTopic)?.name} Examples
                </h3>
              </div>
              <SyntaxHighlighter
                language="sql"
                style={oneDark}
                customStyle={{
                  backgroundColor: '#1e293b',
                  padding: '20px',
                  borderRadius: '8px',
                  fontSize: '14px',
                  lineHeight: '1.6'
                }}
              >
                {codeExamples[selectedTopic]}
              </SyntaxHighlighter>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SQLOptimization;
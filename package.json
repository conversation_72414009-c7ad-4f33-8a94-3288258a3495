{"name": "darkpool-architecture", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "has-flag": "^5.0.1", "html2canvas": "^1.4.1", "lucide-react": "^0.542.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-scripts": "5.0.1", "react-syntax-highlighter": "^15.6.6", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}
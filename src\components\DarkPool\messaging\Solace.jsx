import React, { useState } from 'react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Cloud, MessageSquare, Shield, Zap, Network, BarChart } from 'lucide-react';

const Solace = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedTopic, setSelectedTopic] = useState(0);

  const topics = [
    {
      title: 'Publishers & Subscribers',
      icon: <MessageSquare size={20} />,
      overview: 'Solace PubSub+ provides high-performance publishing and subscribing capabilities with advanced topic matching and filtering for real-time trading data distribution.',
      definition: 'Solace publishers send messages to topics, while subscribers receive messages based on topic subscriptions. The platform supports both guaranteed and direct messaging with ultra-low latency.',
      code: `// Solace Publisher for Trading Data
import com.solacesystems.jcsmp.*;

public class TradingDataPublisher {
    private JCSMPSession session;
    private XMLMessageProducer producer;
    private Topic marketDataTopic;
    
    public void initialize() throws JCSMPException {
        JCSMPProperties properties = new JCSMPProperties();
        properties.setProperty(JCSMPProperties.HOST, "tcp://solace-broker:55555");
        properties.setProperty(JCSMPProperties.USERNAME, "trading-publisher");
        properties.setProperty(JCSMPProperties.VPN_NAME, "trading-vpn");
        properties.setProperty(JCSMPProperties.PASSWORD, "secure-password");
        
        session = JCSMPFactory.onlyInstance().createSession(properties);
        session.connect();
        
        producer = session.getMessageProducer(new JCSMPStreamingPublishEventHandler() {
            @Override
            public void responseReceived(String messageID) {
                System.out.println("Message acknowledged: " + messageID);
            }
            
            @Override
            public void handleError(String messageID, JCSMPException ex, long timestamp) {
                System.err.println("Publishing error for message: " + messageID);
                ex.printStackTrace();
            }
        });
        
        marketDataTopic = JCSMPFactory.onlyInstance().createTopic("trading/market-data/equities");
    }
    
    public void publishMarketData(String symbol, double price, long volume) throws JCSMPException {
        TextMessage message = JCSMPFactory.onlyInstance().createTextMessage();
        
        String jsonData = String.format(
            "{\\"symbol\\": \\"%s\\", \\"price\\": %.4f, \\"volume\\": %d, \\"timestamp\\": %d}",
            symbol, price, volume, System.currentTimeMillis()
        );
        
        message.setText(jsonData);
        message.setCorrelationId(UUID.randomUUID().toString());
        message.setDeliveryMode(DeliveryMode.PERSISTENT);
        message.setTimeToLive(30000); // 30 second TTL
        
        // Add custom headers for routing
        message.setApplicationMessageType("MARKET_DATA");
        message.setApplicationMessageId(symbol + "-" + System.currentTimeMillis());
        
        producer.send(message, marketDataTopic);
    }
}

// Solace Subscriber for Trading Data
public class TradingDataSubscriber {
    private JCSMPSession session;
    private XMLMessageConsumer consumer;
    
    public void initialize() throws JCSMPException {
        JCSMPProperties properties = new JCSMPProperties();
        properties.setProperty(JCSMPProperties.HOST, "tcp://solace-broker:55555");
        properties.setProperty(JCSMPProperties.USERNAME, "trading-subscriber");
        properties.setProperty(JCSMPProperties.VPN_NAME, "trading-vpn");
        properties.setProperty(JCSMPProperties.PASSWORD, "secure-password");
        
        session = JCSMPFactory.onlyInstance().createSession(properties);
        session.connect();
        
        consumer = session.getMessageConsumer(new XMLMessageListener() {
            @Override
            public void onReceive(BytesXMLMessage message) {
                if (message instanceof TextMessage) {
                    TextMessage textMessage = (TextMessage) message;
                    try {
                        String payload = textMessage.getText();
                        String symbol = textMessage.getApplicationMessageId().split("-")[0];
                        
                        // Process market data with low-latency handling
                        processMarketData(symbol, payload);
                        
                        // Acknowledge message
                        message.ackMessage();
                        
                    } catch (JCSMPException e) {
                        System.err.println("Error processing message: " + e.getMessage());
                    }
                }
            }
            
            @Override
            public void onException(JCSMPException exception) {
                System.err.println("Consumer exception: " + exception.getMessage());
            }
        });
        
        // Subscribe to multiple topic patterns
        consumer.addSubscription(JCSMPFactory.onlyInstance().createTopic("trading/market-data/>"));
        consumer.addSubscription(JCSMPFactory.onlyInstance().createTopic("trading/orders/>"));
        consumer.start();
    }
    
    private void processMarketData(String symbol, String data) {
        // Ultra-low latency processing
        System.out.println("Processing market data for " + symbol + ": " + data);
        // Forward to trading algorithms or risk systems
    }
}`
    },
    {
      title: 'Event Mesh & Routing',
      icon: <Network size={20} />,
      overview: 'Solace Event Mesh provides intelligent event routing across hybrid and multi-cloud environments with dynamic topic-based routing and content filtering.',
      definition: 'Event Mesh connects distributed applications through intelligent event routing, enabling seamless communication between trading systems across different environments.',
      code: `// Event Mesh Configuration for Trading Systems
public class TradingEventMeshConfig {
    private JCSMPSession session;
    private Map<String, Topic> topicCache = new ConcurrentHashMap<>();
    
    public void setupEventMesh() throws JCSMPException {
        JCSMPProperties properties = new JCSMPProperties();
        properties.setProperty(JCSMPProperties.HOST, "tcp://event-mesh-broker:55555");
        properties.setProperty(JCSMPProperties.USERNAME, "event-mesh-client");
        properties.setProperty(JCSMPProperties.VPN_NAME, "global-trading-vpn");
        
        // Enable event mesh features
        properties.setProperty(JCSMPProperties.REAPPLY_SUBSCRIPTIONS, true);
        properties.setProperty(JCSMPProperties.GENERATE_SEQUENCE_NUMBERS, true);
        properties.setProperty(JCSMPProperties.CALCULATE_MESSAGE_EXPIRATION, true);
        
        session = JCSMPFactory.onlyInstance().createSession(properties);
        session.connect();
        
        setupTopicRouting();
    }
    
    private void setupTopicRouting() throws JCSMPException {
        // Multi-region trading data routing
        addTopicMapping("trading/americas/market-data", "trading/global/market-data/americas");
        addTopicMapping("trading/emea/market-data", "trading/global/market-data/emea");
        addTopicMapping("trading/apac/market-data", "trading/global/market-data/apac");
        
        // Cross-region order routing with priority
        addTopicMapping("trading/orders/high-priority", "trading/global/orders/priority/high");
        addTopicMapping("trading/orders/normal", "trading/global/orders/priority/normal");
        
        // Risk alerts routing
        addTopicMapping("risk/alerts/critical", "trading/global/risk/critical");
        addTopicMapping("risk/alerts/warning", "trading/global/risk/warning");
    }
    
    private void addTopicMapping(String sourceTopic, String destinationTopic) throws JCSMPException {
        Topic source = JCSMPFactory.onlyInstance().createTopic(sourceTopic);
        Topic destination = JCSMPFactory.onlyInstance().createTopic(destinationTopic);
        
        topicCache.put(sourceTopic, source);
        topicCache.put(destinationTopic, destination);
        
        System.out.println("Mapped topic: " + sourceTopic + " -> " + destinationTopic);
    }
}

// Dynamic Content Filtering for Trading Events
public class TradingEventFilter {
    private XMLMessageConsumer consumer;
    
    public void setupContentBasedRouting() throws JCSMPException {
        consumer = session.getMessageConsumer(new XMLMessageListener() {
            @Override
            public void onReceive(BytesXMLMessage message) {
                // Content-based routing for trading events
                String messageType = message.getApplicationMessageType();
                String priority = message.getProperties().getString("priority");
                Double amount = message.getProperties().getDouble("amount");
                
                if ("ORDER".equals(messageType)) {
                    if (amount != null && amount > 1000000) {
                        // Route large orders to special handling
                        routeToTopic(message, "trading/orders/large");
                    } else if ("HIGH".equals(priority)) {
                        routeToTopic(message, "trading/orders/priority");
                    } else {
                        routeToTopic(message, "trading/orders/standard");
                    }
                } else if ("MARKET_DATA".equals(messageType)) {
                    String symbol = message.getProperties().getString("symbol");
                    if (isHighVolumeSymbol(symbol)) {
                        routeToTopic(message, "trading/market-data/high-volume");
                    } else {
                        routeToTopic(message, "trading/market-data/standard");
                    }
                }
            }
            
            @Override
            public void onException(JCSMPException exception) {
                System.err.println("Filter exception: " + exception.getMessage());
            }
        });
        
        // Subscribe to all trading events for filtering
        consumer.addSubscription(JCSMPFactory.onlyInstance().createTopic("trading/>"));
        consumer.start();
    }
    
    private void routeToTopic(BytesXMLMessage originalMessage, String targetTopic) {
        // Implement intelligent routing logic
        System.out.println("Routing message to: " + targetTopic);
    }
    
    private boolean isHighVolumeSymbol(String symbol) {
        // Logic to determine high-volume symbols
        return Arrays.asList("AAPL", "MSFT", "GOOGL", "TSLA").contains(symbol);
    }
}`
    },
    {
      title: 'Guaranteed Messaging',
      icon: <Shield size={20} />,
      overview: 'Solace Guaranteed Messaging ensures critical trading messages are delivered exactly once with persistent queues, transactions, and acknowledgments.',
      definition: 'Guaranteed messaging provides persistent, transactional message delivery with exactly-once semantics, essential for financial transactions and regulatory compliance.',
      code: `// Guaranteed Message Publishing for Trading Orders
public class GuaranteedOrderPublisher {
    private JCSMPSession session;
    private XMLMessageProducer producer;
    private Queue orderQueue;
    
    public void initialize() throws JCSMPException {
        JCSMPProperties properties = new JCSMPProperties();
        properties.setProperty(JCSMPProperties.HOST, "tcp://solace-broker:55555");
        properties.setProperty(JCSMPProperties.USERNAME, "order-publisher");
        properties.setProperty(JCSMPProperties.VPN_NAME, "trading-vpn");
        properties.setProperty(JCSMPProperties.PASSWORD, "secure-password");
        
        // Enable guaranteed messaging
        properties.setBooleanProperty(JCSMPProperties.GENERATE_SEQUENCE_NUMBERS, true);
        properties.setIntegerProperty(JCSMPProperties.PUB_ACK_WINDOW_SIZE, 100);
        
        session = JCSMPFactory.onlyInstance().createSession(properties);
        session.connect();
        
        producer = session.getMessageProducer(new JCSMPStreamingPublishEventHandler() {
            @Override
            public void responseReceived(String messageID) {
                System.out.println("Order message guaranteed delivered: " + messageID);
                // Update order status in database
                updateOrderStatus(messageID, "DELIVERED");
            }
            
            @Override
            public void handleError(String messageID, JCSMPException exception, long timestamp) {
                System.err.println("Guaranteed delivery failed for order: " + messageID);
                // Implement retry logic or dead letter handling
                handleDeliveryFailure(messageID, exception);
            }
        });
        
        // Create persistent queue for orders
        orderQueue = JCSMPFactory.onlyInstance().createQueue("trading.orders.queue");
    }
    
    public void publishOrder(String orderId, String symbol, String side, 
                           double quantity, double price) throws JCSMPException {
        TextMessage orderMessage = JCSMPFactory.onlyInstance().createTextMessage();
        
        String orderData = String.format(
            "{\\"orderId\\": \\"%s\\", \\"symbol\\": \\"%s\\", \\"side\\": \\"%s\\", " +
            "\\"quantity\\": %.2f, \\"price\\": %.4f, \\"timestamp\\": %d}",
            orderId, symbol, side, quantity, price, System.currentTimeMillis()
        );
        
        orderMessage.setText(orderData);
        orderMessage.setCorrelationId(orderId);
        orderMessage.setDeliveryMode(DeliveryMode.PERSISTENT);
        orderMessage.setApplicationMessageId(orderId);
        orderMessage.setApplicationMessageType("TRADING_ORDER");
        
        // Set message properties for guaranteed delivery
        orderMessage.getProperties().putString("orderId", orderId);
        orderMessage.getProperties().putString("priority", "HIGH");
        orderMessage.getProperties().putLong("expiryTime", 
            System.currentTimeMillis() + 300000); // 5 minutes
        
        producer.send(orderMessage, orderQueue);
        System.out.println("Order published with guaranteed delivery: " + orderId);
    }
    
    private void updateOrderStatus(String messageId, String status) {
        // Update order tracking system
        System.out.println("Updated order " + messageId + " status to: " + status);
    }
    
    private void handleDeliveryFailure(String messageId, JCSMPException exception) {
        // Implement sophisticated error handling
        System.err.println("Handling delivery failure for: " + messageId);
    }
}

// Guaranteed Message Consumer with Transactions
public class GuaranteedOrderConsumer {
    private JCSMPSession session;
    private FlowReceiver flowReceiver;
    private Queue orderQueue;
    
    public void initialize() throws JCSMPException {
        JCSMPProperties properties = new JCSMPProperties();
        properties.setProperty(JCSMPProperties.HOST, "tcp://solace-broker:55555");
        properties.setProperty(JCSMPProperties.USERNAME, "order-consumer");
        properties.setProperty(JCSMPProperties.VPN_NAME, "trading-vpn");
        
        session = JCSMPFactory.onlyInstance().createSession(properties);
        session.connect();
        
        orderQueue = JCSMPFactory.onlyInstance().createQueue("trading.orders.queue");
        
        // Configure flow properties for guaranteed messaging
        ConsumerFlowProperties flowProps = new ConsumerFlowProperties();
        flowProps.setEndpoint(orderQueue);
        flowProps.setAckMode(JCSMPProperties.SUPPORTED_MESSAGE_ACK_CLIENT);
        flowProps.setActiveFlowIndication(true);
        
        flowReceiver = session.createFlow(new XMLMessageListener() {
            @Override
            public void onReceive(BytesXMLMessage message) {
                try {
                    // Start transaction for order processing
                    JCSMPTransactedSession txSession = session.createTransactedSession();
                    
                    if (message instanceof TextMessage) {
                        TextMessage orderMessage = (TextMessage) message;
                        String orderId = orderMessage.getApplicationMessageId();
                        String orderData = orderMessage.getText();
                        
                        // Process order with transaction
                        boolean processed = processOrder(orderId, orderData);
                        
                        if (processed) {
                            // Commit transaction and acknowledge message
                            txSession.commit();
                            message.ackMessage();
                            System.out.println("Order processed successfully: " + orderId);
                        } else {
                            // Rollback transaction
                            txSession.rollback();
                            System.err.println("Order processing failed: " + orderId);
                        }
                    }
                    
                } catch (Exception e) {
                    System.err.println("Error processing guaranteed message: " + e.getMessage());
                    // Message will be redelivered
                }
            }
            
            @Override
            public void onException(JCSMPException exception) {
                System.err.println("Flow receiver exception: " + exception.getMessage());
            }
        }, flowProps);
        
        flowReceiver.start();
    }
    
    private boolean processOrder(String orderId, String orderData) {
        try {
            // Simulate order processing with potential failure
            System.out.println("Processing guaranteed order: " + orderId);
            Thread.sleep(100); // Simulate processing time
            
            // Validate order and execute
            return validateAndExecuteOrder(orderData);
            
        } catch (Exception e) {
            System.err.println("Order processing error: " + e.getMessage());
            return false;
        }
    }
    
    private boolean validateAndExecuteOrder(String orderData) {
        // Implement order validation and execution logic
        return Math.random() > 0.1; // 90% success rate simulation
    }
}`
    },
    {
      title: 'High Availability & Clustering',
      icon: <Zap size={20} />,
      overview: 'Solace PubSub+ provides enterprise-grade high availability through active/standby replication, disaster recovery, and seamless failover for mission-critical trading systems.',
      definition: 'HA clustering ensures continuous operation through redundant broker instances, automatic failover, and geo-distributed replication for global trading operations.',
      code: `// High Availability Configuration for Trading Systems
public class SolaceHAConfiguration {
    private List<JCSMPSession> sessions = new ArrayList<>();
    private String primaryBroker = "tcp://solace-primary:55555";
    private String standbyBroker = "tcp://solace-standby:55555";
    private String drSite = "tcp://solace-dr:55555";
    
    public void setupHighAvailability() throws JCSMPException {
        // Primary session with failover
        JCSMPSession primarySession = createHASession(primaryBroker, standbyBroker);
        sessions.add(primarySession);
        
        // Setup connection monitoring
        setupConnectionMonitoring();
        
        // Configure replication
        setupReplication();
    }
    
    private JCSMPSession createHASession(String primary, String standby) throws JCSMPException {
        JCSMPProperties properties = new JCSMPProperties();
        
        // Configure multiple hosts for failover
        properties.setProperty(JCSMPProperties.HOST, primary + "," + standby);
        properties.setProperty(JCSMPProperties.USERNAME, "ha-trading-client");
        properties.setProperty(JCSMPProperties.VPN_NAME, "trading-vpn");
        properties.setProperty(JCSMPProperties.PASSWORD, "secure-password");
        
        // High Availability settings
        properties.setIntegerProperty(JCSMPProperties.CONNECT_RETRIES, 5);
        properties.setIntegerProperty(JCSMPProperties.RECONNECT_RETRIES, -1); // Infinite retries
        properties.setIntegerProperty(JCSMPProperties.CONNECT_TIMEOUT, 10000); // 10 seconds
        properties.setBooleanProperty(JCSMPProperties.REAPPLY_SUBSCRIPTIONS, true);
        
        // Connection recovery settings
        properties.setIntegerProperty(JCSMPProperties.KEEP_ALIVE_INTERVAL, 3000);
        properties.setIntegerProperty(JCSMPProperties.KEEP_ALIVE_LIMIT, 3);
        properties.setBooleanProperty(JCSMPProperties.AUTO_RECONNECT, true);
        
        JCSMPSession session = JCSMPFactory.onlyInstance().createSession(
            properties, null, new SessionEventHandler() {
                @Override
                public void handleEvent(SessionEventArgs args) {
                    SessionEvent event = args.getEvent();
                    
                    switch (event) {
                        case UP_NOTICE:
                            System.out.println("Trading session connected: " + args.getInfo());
                            onSessionConnected();
                            break;
                        case DOWN_ERROR_NOTICE:
                            System.err.println("Trading session down: " + args.getInfo());
                            onSessionDisconnected(args.getException());
                            break;
                        case RECONNECTING_NOTICE:
                            System.out.println("Trading session reconnecting: " + args.getInfo());
                            onSessionReconnecting();
                            break;
                        case RECONNECTED_NOTICE:
                            System.out.println("Trading session reconnected: " + args.getInfo());
                            onSessionReconnected();
                            break;
                    }
                }
            }
        );
        
        session.connect();
        return session;
    }
    
    private void setupConnectionMonitoring() {
        // Monitor connection health
        ScheduledExecutorService monitor = Executors.newScheduledThreadPool(1);
        monitor.scheduleAtFixedRate(() -> {
            for (JCSMPSession session : sessions) {
                if (session != null && !session.isClosed()) {
                    try {
                        // Send heartbeat message
                        sendHeartbeat(session);
                        logConnectionHealth(session, "HEALTHY");
                    } catch (Exception e) {
                        logConnectionHealth(session, "UNHEALTHY: " + e.getMessage());
                        // Trigger failover if needed
                        triggerFailoverIfNeeded(session);
                    }
                }
            }
        }, 0, 30, TimeUnit.SECONDS);
    }
    
    private void sendHeartbeat(JCSMPSession session) throws JCSMPException {
        XMLMessageProducer producer = session.getMessageProducer((JCSMPStreamingPublishEventHandler) null);
        TextMessage heartbeat = JCSMPFactory.onlyInstance().createTextMessage();
        heartbeat.setText("HEARTBEAT-" + System.currentTimeMillis());
        
        Topic heartbeatTopic = JCSMPFactory.onlyInstance().createTopic("system/heartbeat");
        producer.send(heartbeat, heartbeatTopic);
    }
    
    private void onSessionConnected() {
        System.out.println("Trading system connected - resuming operations");
        // Resume trading operations
        resumeTradingOperations();
    }
    
    private void onSessionDisconnected(JCSMPException exception) {
        System.err.println("Trading system disconnected: " + exception.getMessage());
        // Pause trading operations and activate failover
        pauseTradingOperations();
        activateFailoverProcedures();
    }
    
    private void onSessionReconnecting() {
        System.out.println("Trading system attempting reconnection");
        // Maintain order state during reconnection
        preserveOrderState();
    }
    
    private void onSessionReconnected() {
        System.out.println("Trading system reconnected - checking state consistency");
        // Verify state consistency and resume operations
        verifyStateConsistency();
        resumeTradingOperations();
    }
}

// Disaster Recovery Configuration
public class SolaceDisasterRecovery {
    private String primarySite = "tcp://solace-ny:55555";
    private String drSite = "tcp://solace-london:55555";
    private JCSMPSession replicationSession;
    
    public void setupDisasterRecovery() throws JCSMPException {
        // Setup cross-site replication
        JCSMPProperties replicationProps = new JCSMPProperties();
        replicationProps.setProperty(JCSMPProperties.HOST, drSite);
        replicationProps.setProperty(JCSMPProperties.USERNAME, "dr-replication");
        replicationProps.setProperty(JCSMPProperties.VPN_NAME, "global-trading-vpn");
        
        replicationSession = JCSMPFactory.onlyInstance().createSession(replicationProps);
        replicationSession.connect();
        
        // Setup bidirectional replication
        setupBidirectionalReplication();
        
        // Monitor site health
        monitorSiteHealth();
    }
    
    private void setupBidirectionalReplication() throws JCSMPException {
        // Consumer for replication from primary to DR
        XMLMessageConsumer replicationConsumer = replicationSession.getMessageConsumer(
            new XMLMessageListener() {
                @Override
                public void onReceive(BytesXMLMessage message) {
                    // Replicate message to DR site
                    replicateMessage(message);
                }
                
                @Override
                public void onException(JCSMPException exception) {
                    System.err.println("Replication error: " + exception.getMessage());
                }
            }
        );
        
        // Subscribe to all trading data for replication
        replicationConsumer.addSubscription(
            JCSMPFactory.onlyInstance().createTopic("trading/>")
        );
        replicationConsumer.start();
    }
    
    private void replicateMessage(BytesXMLMessage originalMessage) {
        try {
            // Create replica with DR site routing
            System.out.println("Replicating message to DR site: " + 
                originalMessage.getApplicationMessageId());
            // Implementation for message replication
        } catch (Exception e) {
            System.err.println("Message replication failed: " + e.getMessage());
        }
    }
}`
    },
    {
      title: 'Security & Compliance',
      icon: <Shield size={20} />,
      overview: 'Solace provides enterprise-grade security with OAuth, LDAP integration, message-level encryption, and comprehensive audit trails for regulatory compliance.',
      definition: 'Security features include authentication, authorization, encryption in transit and at rest, and detailed audit logging to meet financial industry compliance requirements.',
      code: `// Security Configuration for Trading Systems
public class SolaceSecurityConfig {
    private JCSMPSession secureSession;
    private String oauthToken;
    private String clientCertPath = "/certs/trading-client.p12";
    
    public void setupSecureConnection() throws JCSMPException {
        JCSMPProperties properties = new JCSMPProperties();
        
        // TLS/SSL Configuration
        properties.setProperty(JCSMPProperties.HOST, "tcps://secure-solace:55443");
        properties.setBooleanProperty(JCSMPProperties.SSL_CONNECTION_DOWNGRADE_TO, false);
        properties.setProperty(JCSMPProperties.SSL_TRUST_STORE, "/certs/truststore.jks");
        properties.setProperty(JCSMPProperties.SSL_TRUST_STORE_PASSWORD, "truststore-password");
        properties.setProperty(JCSMPProperties.SSL_KEY_STORE, clientCertPath);
        properties.setProperty(JCSMPProperties.SSL_KEY_STORE_PASSWORD, "client-cert-password");
        
        // Client Certificate Authentication
        properties.setProperty(JCSMPProperties.SSL_VALIDATE_CERTIFICATE, true);
        properties.setProperty(JCSMPProperties.SSL_VALIDATE_CERTIFICATE_DATE, true);
        
        // OAuth 2.0 Authentication
        setupOAuthAuthentication(properties);
        
        // VPN and User Configuration
        properties.setProperty(JCSMPProperties.VPN_NAME, "secure-trading-vpn");
        properties.setProperty(JCSMPProperties.USERNAME, "trading-service-account");
        
        secureSession = JCSMPFactory.onlyInstance().createSession(properties);
        secureSession.connect();
        
        // Setup message-level security
        setupMessageSecurity();
        
        // Enable audit logging
        enableAuditLogging();
    }
    
    private void setupOAuthAuthentication(JCSMPProperties properties) {
        try {
            // OAuth token acquisition
            oauthToken = acquireOAuthToken();
            properties.setProperty(JCSMPProperties.OAUTH2_ACCESS_TOKEN, oauthToken);
            properties.setProperty(JCSMPProperties.AUTHENTICATION_SCHEME, 
                JCSMPProperties.AUTHENTICATION_SCHEME_OAUTH2);
            
            // Token refresh configuration
            properties.setIntegerProperty(JCSMPProperties.OAUTH2_ACCESS_TOKEN_EXPIRY, 3600);
            
            System.out.println("OAuth authentication configured");
        } catch (Exception e) {
            System.err.println("OAuth setup failed: " + e.getMessage());
        }
    }
    
    private String acquireOAuthToken() {
        // Implementation for OAuth token acquisition
        // This would typically involve calling an OAuth server
        return "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."; // Mock token
    }
    
    private void setupMessageSecurity() throws JCSMPException {
        XMLMessageProducer secureProducer = secureSession.getMessageProducer(
            new JCSMPStreamingPublishEventHandler() {
                @Override
                public void responseReceived(String messageID) {
                    auditLog("MESSAGE_SENT", messageID, "SUCCESS");
                }
                
                @Override
                public void handleError(String messageID, JCSMPException exception, long timestamp) {
                    auditLog("MESSAGE_SEND_FAILED", messageID, 
                        "ERROR: " + exception.getMessage());
                }
            }
        );
        
        // Message encryption setup
        setupMessageEncryption();
    }
    
    private void setupMessageEncryption() {
        System.out.println("Setting up AES-256 message encryption");
        // Implementation for message-level encryption
    }
    
    public void publishSecureOrder(String orderId, String orderData, String userId) 
            throws JCSMPException {
        TextMessage secureMessage = JCSMPFactory.onlyInstance().createTextMessage();
        
        // Encrypt sensitive order data
        String encryptedData = encryptOrderData(orderData);
        secureMessage.setText(encryptedData);
        
        // Add security headers
        secureMessage.setApplicationMessageId(orderId);
        secureMessage.setUserId(userId);
        secureMessage.getProperties().putString("classification", "CONFIDENTIAL");
        secureMessage.getProperties().putString("source-system", "trading-engine");
        secureMessage.getProperties().putLong("audit-timestamp", System.currentTimeMillis());
        
        // Digital signature for integrity
        String signature = generateDigitalSignature(encryptedData);
        secureMessage.getProperties().putString("digital-signature", signature);
        
        Topic secureOrderTopic = JCSMPFactory.onlyInstance()
            .createTopic("secure/trading/orders");
        
        XMLMessageProducer producer = secureSession.getMessageProducer((JCSMPStreamingPublishEventHandler) null);
        producer.send(secureMessage, secureOrderTopic);
        
        auditLog("SECURE_ORDER_PUBLISHED", orderId, 
            "User: " + userId + ", Classification: CONFIDENTIAL");
    }
    
    private String encryptOrderData(String orderData) {
        // AES-256 encryption implementation
        try {
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            SecretKeySpec keySpec = new SecretKeySpec(getEncryptionKey(), "AES");
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);
            
            byte[] encrypted = cipher.doFinal(orderData.getBytes());
            return Base64.getEncoder().encodeToString(encrypted);
        } catch (Exception e) {
            throw new RuntimeException("Encryption failed", e);
        }
    }
    
    private byte[] getEncryptionKey() {
        // Secure key management - typically from key management service
        return "MySecretKey12345".getBytes(); // Mock key for example
    }
    
    private String generateDigitalSignature(String data) {
        // RSA digital signature implementation
        try {
            Signature signature = Signature.getInstance("SHA256withRSA");
            PrivateKey privateKey = loadPrivateKey();
            signature.initSign(privateKey);
            signature.update(data.getBytes());
            
            byte[] signed = signature.sign();
            return Base64.getEncoder().encodeToString(signed);
        } catch (Exception e) {
            throw new RuntimeException("Digital signature failed", e);
        }
    }
    
    private PrivateKey loadPrivateKey() throws Exception {
        // Load private key from secure key store
        return null; // Mock implementation
    }
    
    private void enableAuditLogging() {
        System.out.println("Audit logging enabled for compliance");
        // Setup comprehensive audit trail
    }
    
    private void auditLog(String action, String messageId, String details) {
        String auditEntry = String.format(
            "AUDIT: [%s] Action: %s, MessageId: %s, Details: %s, User: %s, Timestamp: %d",
            new Date(), action, messageId, details, getCurrentUser(), System.currentTimeMillis()
        );
        
        System.out.println(auditEntry);
        // Write to secure audit log storage
    }
    
    private String getCurrentUser() {
        // Get current authenticated user
        return "trading-user-001";
    }
}

// Role-Based Access Control (RBAC)
public class SolaceRBAC {
    private Map<String, Set<String>> rolePermissions = new HashMap<>();
    
    public void setupRoleBasedAccess() {
        // Define trading system roles
        rolePermissions.put("TRADER", Set.of(
            "trading/orders/create",
            "trading/orders/cancel",
            "trading/market-data/read"
        ));
        
        rolePermissions.put("RISK_MANAGER", Set.of(
            "trading/orders/read",
            "trading/positions/read",
            "risk/limits/modify",
            "risk/alerts/create"
        ));
        
        rolePermissions.put("COMPLIANCE_OFFICER", Set.of(
            "audit/logs/read",
            "compliance/reports/generate",
            "trading/*/read"
        ));
        
        rolePermissions.put("SYSTEM_ADMIN", Set.of(
            "*" // Full access
        ));
    }
    
    public boolean hasPermission(String userId, String resource) {
        String userRole = getUserRole(userId);
        Set<String> permissions = rolePermissions.get(userRole);
        
        if (permissions == null) {
            return false;
        }
        
        return permissions.contains("*") || 
               permissions.contains(resource) ||
               permissions.stream().anyMatch(p -> resource.startsWith(p.replace("*", "")));
    }
    
    private String getUserRole(String userId) {
        // Lookup user role from identity management system
        return "TRADER"; // Mock implementation
    }
}`
    },
    {
      title: 'Performance & Monitoring',
      icon: <BarChart size={20} />,
      overview: 'Solace provides comprehensive monitoring, metrics collection, and performance optimization tools for high-throughput trading systems with microsecond latency requirements.',
      definition: 'Performance monitoring includes real-time metrics, alerting, health checks, and optimization features to ensure trading systems meet strict latency and throughput requirements.',
      code: `// Performance Monitoring for Solace Trading Systems
public class SolacePerformanceMonitor {
    private final MeterRegistry meterRegistry;
    private final Counter messagesSent;
    private final Counter messagesReceived;
    private final Timer publishLatency;
    private final Timer processLatency;
    private final Gauge connectionHealth;
    
    public SolacePerformanceMonitor() {
        this.meterRegistry = Metrics.globalRegistry;
        
        // Initialize metrics
        this.messagesSent = Counter.builder("solace.messages.sent")
            .description("Total messages sent")
            .tag("system", "trading")
            .register(meterRegistry);
            
        this.messagesReceived = Counter.builder("solace.messages.received")
            .description("Total messages received")
            .tag("system", "trading")
            .register(meterRegistry);
            
        this.publishLatency = Timer.builder("solace.publish.latency")
            .description("Message publish latency")
            .tag("system", "trading")
            .register(meterRegistry);
            
        this.processLatency = Timer.builder("solace.process.latency")
            .description("Message processing latency")
            .tag("system", "trading")
            .register(meterRegistry);
            
        this.connectionHealth = Gauge.builder("solace.connection.health")
            .description("Connection health status")
            .register(meterRegistry, this, SolacePerformanceMonitor::getConnectionHealth);
    }
    
    public void monitorPublishPerformance(JCSMPSession session) throws JCSMPException {
        XMLMessageProducer producer = session.getMessageProducer(
            new JCSMPStreamingPublishEventHandler() {
                @Override
                public void responseReceived(String messageID) {
                    messagesSent.increment();
                    // Calculate end-to-end latency
                    long publishTime = extractTimestamp(messageID);
                    long latency = System.nanoTime() - publishTime;
                    publishLatency.record(latency, TimeUnit.NANOSECONDS);
                }
                
                @Override
                public void handleError(String messageID, JCSMPException exception, long timestamp) {
                    meterRegistry.counter("solace.publish.errors",
                        "error", exception.getClass().getSimpleName()).increment();
                }
            }
        );
        
        // High-frequency publishing with latency tracking
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
        scheduler.scheduleAtFixedRate(() -> {
            try {
                Timer.Sample sample = Timer.start(meterRegistry);
                publishTestMessage(producer);
                sample.stop(publishLatency);
            } catch (Exception e) {
                meterRegistry.counter("solace.monitor.errors").increment();
            }
        }, 0, 1, TimeUnit.MILLISECONDS); // 1ms intervals for high-frequency testing
    }
    
    public void monitorConsumerPerformance(JCSMPSession session) throws JCSMPException {
        XMLMessageConsumer consumer = session.getMessageConsumer(
            new XMLMessageListener() {
                @Override
                public void onReceive(BytesXMLMessage message) {
                    Timer.Sample sample = Timer.start(meterRegistry);
                    
                    try {
                        messagesReceived.increment();
                        
                        // Process message with latency tracking
                        processMessage(message);
                        
                        sample.stop(processLatency);
                        
                        // Track message age (time since creation)
                        long messageAge = calculateMessageAge(message);
                        meterRegistry.timer("solace.message.age").record(messageAge, TimeUnit.MILLISECONDS);
                        
                    } catch (Exception e) {
                        meterRegistry.counter("solace.process.errors").increment();
                        sample.stop(meterRegistry.timer("solace.process.latency.error"));
                    }
                }
                
                @Override
                public void onException(JCSMPException exception) {
                    meterRegistry.counter("solace.consumer.exceptions",
                        "exception", exception.getClass().getSimpleName()).increment();
                }
            }
        );
        
        consumer.addSubscription(JCSMPFactory.onlyInstance().createTopic("trading/>"));
        consumer.start();
    }
    
    public void setupAdvancedMonitoring() {
        // JVM metrics for performance tuning
        new ClassLoaderMetrics().bindTo(meterRegistry);
        new JvmMemoryMetrics().bindTo(meterRegistry);
        new JvmGcMetrics().bindTo(meterRegistry);
        new JvmThreadMetrics().bindTo(meterRegistry);
        
        // Custom trading system metrics
        setupTradingMetrics();
        
        // Health check endpoints
        setupHealthChecks();
        
        // Alerting rules
        setupAlerting();
    }
    
    private void setupTradingMetrics() {
        // Order processing metrics
        meterRegistry.gauge("trading.orders.pending", this, 
            SolacePerformanceMonitor::getPendingOrders);
        meterRegistry.gauge("trading.orders.processed.rate", this,
            SolacePerformanceMonitor::getOrderProcessingRate);
        
        // Market data metrics
        meterRegistry.gauge("trading.market.data.latency.p99", this,
            SolacePerformanceMonitor::getMarketDataLatencyP99);
        
        // Risk metrics
        meterRegistry.gauge("risk.position.utilization", this,
            SolacePerformanceMonitor::getPositionUtilization);
    }
    
    private void setupHealthChecks() {
        ScheduledExecutorService healthChecker = Executors.newScheduledThreadPool(1);
        healthChecker.scheduleAtFixedRate(() -> {
            // Check broker connectivity
            double brokerHealth = checkBrokerHealth();
            meterRegistry.gauge("solace.broker.health").set(brokerHealth);
            
            // Check queue depths
            checkQueueDepths();
            
            // Check system resources
            checkSystemResources();
            
        }, 0, 30, TimeUnit.SECONDS);
    }
    
    private double checkBrokerHealth() {
        // Implement broker health check
        try {
            // Send ping message and measure response time
            long startTime = System.nanoTime();
            sendPingMessage();
            long responseTime = System.nanoTime() - startTime;
            
            // Convert to milliseconds and return health score
            double latencyMs = responseTime / 1_000_000.0;
            return latencyMs < 1.0 ? 1.0 : (latencyMs < 5.0 ? 0.5 : 0.0);
            
        } catch (Exception e) {
            return 0.0; // Unhealthy
        }
    }
    
    private void checkQueueDepths() {
        // Monitor critical queue depths
        Map<String, Long> queueDepths = getQueueDepths();
        
        for (Map.Entry<String, Long> entry : queueDepths.entrySet()) {
            meterRegistry.gauge("solace.queue.depth",
                Tags.of("queue", entry.getKey()), entry.getValue());
            
            // Alert on high queue depths
            if (entry.getValue() > 10000) {
                triggerAlert("HIGH_QUEUE_DEPTH", entry.getKey(), entry.getValue());
            }
        }
    }
    
    private void setupAlerting() {
        // Setup alerting based on metrics thresholds
        ScheduledExecutorService alerter = Executors.newScheduledThreadPool(1);
        alerter.scheduleAtFixedRate(() -> {
            
            // Check latency thresholds
            if (publishLatency.mean(TimeUnit.MICROSECONDS) > 100) {
                triggerAlert("HIGH_PUBLISH_LATENCY", 
                    "Publish latency", publishLatency.mean(TimeUnit.MICROSECONDS));
            }
            
            // Check error rates
            double errorRate = getErrorRate();
            if (errorRate > 0.01) { // 1% error rate
                triggerAlert("HIGH_ERROR_RATE", "Error rate", errorRate);
            }
            
            // Check connection health
            if (getConnectionHealth() < 0.8) {
                triggerAlert("CONNECTION_DEGRADED", "Connection health", getConnectionHealth());
            }
            
        }, 0, 60, TimeUnit.SECONDS);
    }
    
    private void triggerAlert(String alertType, String component, Object value) {
        String alertMessage = String.format(
            "ALERT [%s]: %s = %s at %s",
            alertType, component, value, new Date()
        );
        
        System.err.println(alertMessage);
        
        // Send to monitoring system (Prometheus, Grafana, etc.)
        meterRegistry.counter("solace.alerts", "type", alertType).increment();
        
        // Implement notification logic (Slack, email, PagerDuty)
        sendNotification(alertType, alertMessage);
    }
    
    // Helper methods for metrics
    private double getConnectionHealth() { return 1.0; }
    private double getPendingOrders() { return 150.0; }
    private double getOrderProcessingRate() { return 1500.0; }
    private double getMarketDataLatencyP99() { return 0.5; }
    private double getPositionUtilization() { return 0.75; }
    private double getErrorRate() { return 0.005; }
    
    private long extractTimestamp(String messageID) { 
        return System.nanoTime(); // Mock implementation
    }
    
    private void publishTestMessage(XMLMessageProducer producer) throws JCSMPException {
        // Implementation for test message publishing
    }
    
    private void processMessage(BytesXMLMessage message) {
        // Mock message processing
        try { Thread.sleep(1); } catch (InterruptedException e) {}
    }
    
    private long calculateMessageAge(BytesXMLMessage message) {
        return System.currentTimeMillis() - message.getTimestamp();
    }
    
    private void sendPingMessage() throws Exception {
        // Implementation for ping message
    }
    
    private Map<String, Long> getQueueDepths() {
        return Map.of("trading.orders", 1500L, "market.data", 500L);
    }
    
    private void sendNotification(String alertType, String message) {
        // Implementation for alert notifications
        System.out.println("NOTIFICATION: " + message);
    }
}`
    }
  ];

  const tabStyle = (isActive) => ({
    padding: '12px 24px',
    backgroundColor: isActive ? 'rgba(16, 185, 129, 0.2)' : 'rgba(55, 65, 81, 0.3)',
    border: 'none',
    borderRadius: '8px',
    color: isActive ? '#10b981' : '#9ca3af',
    cursor: 'pointer',
    fontWeight: isActive ? '600' : '500',
    transition: 'all 0.2s',
    fontSize: '14px'
  });

  return (
    <div style={{ 
      padding: '40px',
      backgroundColor: '#0f172a',
      minHeight: '100vh',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      <div style={{
        maxWidth: '1400px',
        margin: '0 auto'
      }}>
        {/* Header */}
        <div style={{ marginBottom: '40px', textAlign: 'center' }}>
          <div style={{
            display: 'inline-flex',
            alignItems: 'center',
            gap: '16px',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            padding: '16px 32px',
            borderRadius: '16px',
            border: '1px solid rgba(16, 185, 129, 0.3)'
          }}>
            <Cloud size={32} color="#10b981" />
            <h1 style={{
              fontSize: '28px',
              fontWeight: '700',
              color: 'white',
              margin: 0
            }}>
              Solace PubSub+ Enterprise Messaging
            </h1>
          </div>
          <p style={{
            fontSize: '16px',
            color: '#94a3b8',
            marginTop: '16px',
            maxWidth: '800px',
            margin: '16px auto 0'
          }}>
            Enterprise-grade messaging platform with advanced routing, guaranteed delivery, 
            and hybrid cloud connectivity for mission-critical trading applications
          </p>
        </div>

        {/* Tab Navigation */}
        <div style={{
          display: 'flex',
          gap: '8px',
          marginBottom: '32px',
          justifyContent: 'center'
        }}>
          {['overview', 'definition', 'code'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              style={tabStyle(activeTab === tab)}
              onMouseEnter={(e) => {
                if (activeTab !== tab) {
                  e.currentTarget.style.backgroundColor = 'rgba(16, 185, 129, 0.1)';
                }
              }}
              onMouseLeave={(e) => {
                if (activeTab !== tab) {
                  e.currentTarget.style.backgroundColor = 'rgba(55, 65, 81, 0.3)';
                }
              }}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </button>
          ))}
        </div>

        <div style={{ display: 'flex', gap: '32px' }}>
          {/* Topics Sidebar */}
          <div style={{
            width: '320px',
            backgroundColor: 'rgba(30, 41, 59, 0.5)',
            borderRadius: '12px',
            padding: '24px',
            height: 'fit-content',
            border: '1px solid rgba(51, 65, 85, 0.8)'
          }}>
            <h3 style={{
              fontSize: '18px',
              fontWeight: '600',
              color: '#10b981',
              marginBottom: '20px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <MessageSquare size={20} />
              Solace Topics
            </h3>
            
            {topics.map((topic, index) => (
              <button
                key={index}
                onClick={() => setSelectedTopic(index)}
                style={{
                  width: '100%',
                  padding: '16px',
                  marginBottom: '12px',
                  backgroundColor: selectedTopic === index ? 'rgba(16, 185, 129, 0.2)' : 'transparent',
                  border: selectedTopic === index ? '2px solid #10b981' : '2px solid transparent',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  textAlign: 'left',
                  transition: 'all 0.2s',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px'
                }}
                onMouseEnter={(e) => {
                  if (selectedTopic !== index) {
                    e.currentTarget.style.backgroundColor = 'rgba(16, 185, 129, 0.1)';
                    e.currentTarget.style.borderColor = '#10b981';
                  }
                }}
                onMouseLeave={(e) => {
                  if (selectedTopic !== index) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.borderColor = 'transparent';
                  }
                }}
              >
                <div style={{ color: selectedTopic === index ? '#10b981' : '#94a3b8' }}>
                  {topic.icon}
                </div>
                <span style={{
                  color: selectedTopic === index ? '#10b981' : 'white',
                  fontWeight: selectedTopic === index ? '600' : '500',
                  fontSize: '14px'
                }}>
                  {topic.title}
                </span>
              </button>
            ))}
          </div>

          {/* Content Area */}
          <div style={{ flex: 1 }}>
            <div style={{
              backgroundColor: 'rgba(30, 41, 59, 0.5)',
              borderRadius: '12px',
              padding: '32px',
              border: '1px solid rgba(51, 65, 85, 0.8)'
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                marginBottom: '24px'
              }}>
                <div style={{ color: '#10b981' }}>
                  {topics[selectedTopic].icon}
                </div>
                <h2 style={{
                  fontSize: '24px',
                  fontWeight: '700',
                  color: 'white',
                  margin: 0
                }}>
                  {topics[selectedTopic].title}
                </h2>
              </div>

              <div style={{
                fontSize: '16px',
                lineHeight: '1.7',
                color: '#e2e8f0'
              }}>
                {activeTab === 'overview' && (
                  <div>
                    <p>{topics[selectedTopic].overview}</p>
                  </div>
                )}

                {activeTab === 'definition' && (
                  <div>
                    <p>{topics[selectedTopic].definition}</p>
                  </div>
                )}

                {activeTab === 'code' && (
                  <div style={{ marginTop: '20px' }}>
                    <SyntaxHighlighter
                      language="java"
                      style={oneDark}
                      customStyle={{
                        borderRadius: '8px',
                        fontSize: '14px',
                        lineHeight: '1.5'
                      }}
                    >
                      {topics[selectedTopic].code}
                    </SyntaxHighlighter>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Solace;
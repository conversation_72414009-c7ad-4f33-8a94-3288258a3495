export const varComponents = [
  // Data Layer
  {
    id: 'market-data',
    name: 'Market Data Feed',
    tech: ['Real-time & historical prices', 'Bloomberg/Reuters feeds', 'Exchange connectivity'],
    position: { top: 60, left: 100 },
    latency: '< 1ms',
    throughput: '10M+ ticks/s',
    category: 'data',
    keywords: ['market data', 'feeds', 'real-time', 'historical', 'bloomberg']
  },
  {
    id: 'portfolio-data',
    name: 'Portfolio Data Store',
    tech: ['Positions & instruments', 'Trade lifecycle', 'Audit trail'],
    position: { top: 60, left: 350 },
    category: 'storage',
    keywords: ['portfolio', 'positions', 'trades', 'audit', 'instruments']
  },
  {
    id: 'reference-data',
    name: 'Reference Data',
    tech: ['Static & metadata', 'Security master', 'Corporate actions'],
    position: { top: 60, left: 600 },
    category: 'storage',
    keywords: ['reference', 'static', 'metadata', 'security', 'master']
  },
  {
    id: 'risk-params',
    name: 'Risk Parameters',
    tech: ['Confidence levels & horizons', 'Methodology settings', 'Configuration'],
    position: { top: 60, left: 850 },
    category: 'infrastructure',
    keywords: ['risk', 'parameters', 'confidence', 'methodology', 'config']
  },

  // Processing Layer
  {
    id: 'data-validation',
    name: 'Data Validation',
    tech: ['Quality checks & cleansing', 'Outlier detection', 'Gap filling'],
    position: { top: 240, left: 100 },
    latency: '< 100ms',
    category: 'processing',
    keywords: ['validation', 'quality', 'cleansing', 'outlier', 'detection']
  },
  {
    id: 'data-enrichment',
    name: 'Data Enrichment',
    tech: ['FX rates & mappings', 'Derived analytics', 'Interpolation'],
    position: { top: 240, left: 350 },
    latency: '< 50ms',
    category: 'processing',
    keywords: ['enrichment', 'fx', 'rates', 'derived', 'interpolation']
  },
  {
    id: 'scenario-gen',
    name: 'Scenario Generator',
    tech: ['Monte Carlo & Historical', 'Correlation simulation', 'Stress scenarios'],
    position: { top: 240, left: 600 },
    latency: '< 2s',
    throughput: '1M scenarios/s',
    category: 'processing',
    keywords: ['scenario', 'monte carlo', 'historical', 'correlation', 'stress']
  },
  {
    id: 'pricing-engine',
    name: 'Pricing Engine',
    tech: ['Valuation models', 'Greeks calculation', 'Model calibration'],
    position: { top: 240, left: 850 },
    latency: '< 500ms',
    throughput: '100K valuations/s',
    category: 'processing',
    keywords: ['pricing', 'valuation', 'greeks', 'calibration', 'models']
  },

  // Calculation Layer
  {
    id: 'pnl-calc',
    name: 'P&L Calculator',
    tech: ['Scenario P&L computation', 'Attribution analysis', 'Full revaluation'],
    position: { top: 440, left: 225 },
    latency: '< 1s',
    throughput: '50K scenarios/s',
    category: 'processing',
    keywords: ['pnl', 'profit', 'loss', 'attribution', 'revaluation']
  },
  {
    id: 'var-engine',
    name: 'VaR Engine',
    tech: ['Core VaR calculations', 'Historical/Parametric/MC', 'Component VaR'],
    position: { top: 440, left: 475 },
    latency: '< 3s',
    category: 'processing',
    keywords: ['var', 'value at risk', 'historical', 'parametric', 'monte carlo']
  },
  {
    id: 'cvar-engine',
    name: 'CVaR Engine',
    tech: ['Tail risk calculations', 'Expected Shortfall', 'Coherent measures'],
    position: { top: 440, left: 725 },
    latency: '< 5s',
    category: 'processing',
    keywords: ['cvar', 'tail risk', 'expected shortfall', 'coherent', 'measures']
  },

  // Output Layer
  {
    id: 'report-gen',
    name: 'Report Generator',
    tech: ['Risk reports & analytics', 'PDF/Excel/HTML', 'Regulatory templates'],
    position: { top: 640, left: 100 },
    category: 'infrastructure',
    keywords: ['reports', 'analytics', 'pdf', 'excel', 'regulatory']
  },
  {
    id: 'api-gateway',
    name: 'API Gateway',
    tech: ['REST/gRPC endpoints', 'GraphQL queries', 'WebSocket streaming'],
    position: { top: 640, left: 350 },
    latency: '< 10ms',
    throughput: '10K req/s',
    category: 'infrastructure',
    keywords: ['api', 'rest', 'grpc', 'graphql', 'websocket']
  },
  {
    id: 'cache-layer',
    name: 'Cache Layer',
    tech: ['Redis/Hazelcast', 'Multi-level caching', 'Pre-computation'],
    position: { top: 640, left: 600 },
    latency: '< 1ms',
    category: 'storage',
    keywords: ['cache', 'redis', 'hazelcast', 'memory', 'precomputation']
  },
  {
    id: 'monitoring',
    name: 'Monitoring',
    tech: ['Metrics & alerting', 'System health', 'Risk limit tracking'],
    position: { top: 640, left: 850 },
    category: 'infrastructure',
    keywords: ['monitoring', 'metrics', 'alerting', 'health', 'limits']
  }
];

export const varComponentDetails = {
  'market-data': {
    details: {
      title: 'Market Data Feed',
      functions: [
        'Ingests real-time and historical market data from multiple sources',
        'Multi-source data aggregation (Bloomberg, Reuters, direct exchange feeds)',
        'Real-time streaming with sub-second latency',
        'Historical data storage with time-series optimization',
        'Support for equities, FX, commodities, fixed income, derivatives',
        'Protocol support: FIX, WebSocket, REST APIs'
      ]
    },
    workflow: `1. Connect to market data providers
2. Stream real-time prices and volumes
3. Validate and normalize data formats
4. Store in time-series database
5. Trigger risk calculations
6. Archive historical data`,
    code: `// Java implementation
@Service
public class MarketDataService {
    private final KafkaTemplate<String, MarketData> kafkaTemplate;
    
    public void processMarketData(MarketData data) {
        // Validate data quality
        if (validateData(data)) {
            // Normalize format
            MarketData normalized = normalize(data);
            
            // Store in time-series DB
            timeSeriesRepo.save(normalized);
            
            // Publish to downstream systems
            kafkaTemplate.send("market-data", normalized);
        }
    }
}`
  },
  'portfolio-data': {
    details: {
      title: 'Portfolio Data Store',
      functions: [
        'Maintains current positions, trades, and portfolio hierarchies',
        'Real-time position keeping with full audit trail',
        'Trade lifecycle management and settlement tracking',
        'Portfolio hierarchy and aggregation rules',
        'Historical position snapshots and versioning',
        'Event sourcing for complete audit trail'
      ]
    },
    workflow: `1. Receive trade and position updates
2. Validate against business rules
3. Update position records
4. Maintain audit trail
5. Calculate aggregated positions
6. Publish position changes`,
    code: `# Python implementation
class PortfolioDataStore:
    def __init__(self, db_connection):
        self.db = db_connection
        
    def update_position(self, trade):
        with self.db.transaction():
            # Update position
            position = self.get_position(trade.instrument_id)
            position.quantity += trade.quantity
            
            # Create audit record
            audit_record = AuditRecord(
                timestamp=datetime.now(),
                action='POSITION_UPDATE',
                old_quantity=position.quantity - trade.quantity,
                new_quantity=position.quantity
            )
            
            self.db.save(position)
            self.db.save(audit_record)`
  },
  'reference-data': {
    details: {
      title: 'Reference Data',
      functions: [
        'Centralizes static data including instrument definitions',
        'Security master with full instrument details',
        'Corporate actions and instrument lifecycle events',
        'Market calendars and holiday schedules',
        'Counterparty and legal entity data',
        'Graph databases for relationship mapping'
      ]
    },
    workflow: `1. Ingest reference data from vendors
2. Validate against existing records
3. Apply master data management rules
4. Update security master database
5. Propagate changes to subscribers
6. Maintain data lineage and versioning`,
    code: `// GraphQL schema for reference data
type Security {
    id: ID!
    isin: String!
    ticker: String
    name: String!
    assetClass: AssetClass!
    currency: String!
    exchangeCode: String
    corporateActions: [CorporateAction!]!
}

type CorporateAction {
    id: ID!
    type: CorporateActionType!
    exDate: Date!
    payDate: Date
    ratio: Float
    amount: Float
}`
  },
  'risk-params': {
    details: {
      title: 'Risk Parameters',
      functions: [
        'Manages configurable risk calculation parameters',
        'VaR confidence levels (95%, 99%, 99.9%)',
        'Time horizons (1-day, 10-day, custom periods)',
        'Calculation methodologies (Historical, Monte Carlo, Parametric)',
        'Stress test scenarios and shock parameters',
        'A/B testing framework for methodology comparison'
      ]
    },
    workflow: `1. Load configuration from files/database
2. Validate parameter consistency
3. Apply business rules and constraints
4. Version configuration changes
5. Distribute to calculation engines
6. Monitor parameter effectiveness`,
    code: `# YAML configuration example
risk_parameters:
  var_config:
    confidence_levels: [0.95, 0.99, 0.999]
    time_horizons: [1, 10, 22]  # days
    methodologies:
      - name: "historical"
        lookback_days: 252
        weighting: "equal"
      - name: "monte_carlo"
        simulations: 10000
        random_seed: 42
      - name: "parametric"
        distribution: "normal"
        correlation_method: "pearson"`
  },
  'data-validation': {
    details: {
      title: 'Data Validation',
      functions: [
        'Ensures data quality through comprehensive validation rules',
        'Completeness checks (missing data detection)',
        'Consistency validation (cross-source reconciliation)',
        'Outlier detection using statistical methods',
        'Automated data cleansing and gap filling',
        'Machine learning for anomaly detection'
      ]
    },
    workflow: `1. Receive data from upstream systems
2. Apply completeness validation rules
3. Cross-validate against multiple sources
4. Detect statistical outliers
5. Apply cleansing and correction rules
6. Flag data quality issues`,
    code: `# Python data validation with Great Expectations
import great_expectations as ge

def validate_market_data(df):
    dataset = ge.from_pandas(df)
    
    # Completeness checks
    dataset.expect_column_to_exist("price")
    dataset.expect_column_values_to_not_be_null("price")
    
    # Range checks
    dataset.expect_column_values_to_be_between(
        "price", min_value=0, max_value=1000000
    )
    
    # Outlier detection
    dataset.expect_column_values_to_be_between(
        "price", 
        min_value=dataset["price"].quantile(0.01),
        max_value=dataset["price"].quantile(0.99)
    )
    
    return dataset.validate()`
  },
  'scenario-gen': {
    details: {
      title: 'Scenario Generator',
      functions: [
        'Generates market scenarios using various methodologies',
        'Monte Carlo simulation with multiple distributions',
        'Historical simulation with configurable lookback',
        'Stress scenarios and hypothetical shocks',
        'Correlation matrix estimation and simulation',
        'Copula models for dependency structures'
      ]
    },
    workflow: `1. Load historical data and parameters
2. Estimate correlation matrices
3. Generate random scenarios (MC/Historical)
4. Apply correlation structures
5. Create stress test scenarios
6. Validate scenario distributions`,
    code: `import numpy as np
from scipy import stats

class ScenarioGenerator:
    def __init__(self, correlation_matrix, num_scenarios=10000):
        self.corr_matrix = correlation_matrix
        self.num_scenarios = num_scenarios
        
    def generate_monte_carlo(self, means, volatilities):
        # Generate correlated random variables
        L = np.linalg.cholesky(self.corr_matrix)
        independent_normals = np.random.normal(
            0, 1, (len(means), self.num_scenarios)
        )
        correlated_normals = L @ independent_normals
        
        # Apply means and volatilities
        scenarios = means[:, np.newaxis] + volatilities[:, np.newaxis] * correlated_normals
        return scenarios.T`
  },
  'var-engine': {
    details: {
      title: 'VaR Engine',
      functions: [
        'Core engine that calculates Value at Risk',
        'Historical VaR with various weighting schemes',
        'Parametric VaR (variance-covariance method)',
        'Monte Carlo VaR with full revaluation',
        'Component VaR and marginal VaR calculations',
        'Multiple confidence levels and time horizons'
      ]
    },
    workflow: `1. Receive portfolio positions and scenarios
2. Apply chosen VaR methodology
3. Calculate portfolio returns distribution
4. Compute VaR at specified confidence levels
5. Calculate component and marginal contributions
6. Store results and generate reports`,
    code: `import numpy as np
import pandas as pd

class VaREngine:
    def calculate_historical_var(self, returns, confidence_level=0.95):
        """Calculate Historical VaR"""
        return np.percentile(returns, (1 - confidence_level) * 100)
    
    def calculate_parametric_var(self, portfolio_value, volatility, confidence_level=0.95):
        """Calculate Parametric VaR using normal distribution"""
        from scipy.stats import norm
        z_score = norm.ppf(1 - confidence_level)
        return portfolio_value * volatility * z_score
    
    def calculate_component_var(self, portfolio_var, weights, marginal_vars):
        """Calculate Component VaR"""
        return weights * marginal_vars * portfolio_var / np.sum(weights * marginal_vars)`
  },
  'cvar-engine': {
    details: {
      title: 'CVaR Engine',
      functions: [
        'Calculates Conditional Value at Risk (Expected Shortfall)',
        'Tail risk analysis beyond VaR threshold',
        'Coherent risk measure properties',
        'Spectral risk measures for custom risk preferences',
        'Risk contribution analysis for portfolio components',
        'Extreme Value Theory for tail modeling'
      ]
    },
    workflow: `1. Calculate VaR threshold using VaR Engine
2. Identify losses beyond VaR threshold
3. Compute expected value of tail losses
4. Calculate component contributions to CVaR
5. Apply coherent risk measure properties
6. Generate tail risk decomposition`,
    code: `def calculate_cvar(returns, confidence_level=0.95):
    """
    Calculate Conditional Value at Risk (Expected Shortfall)
    
    Args:
        returns: Array of portfolio returns (negative values are losses)
        confidence_level: Confidence level (e.g., 0.95 for 95%)
    
    Returns:
        dict: VaR and CVaR values with tail ratio
    """
    # Calculate VaR threshold
    var_threshold = np.percentile(returns, (1 - confidence_level) * 100)
    
    # Find losses beyond VaR (tail losses)
    tail_losses = returns[returns <= var_threshold]
    
    # Calculate CVaR as mean of tail losses
    cvar = np.mean(tail_losses) if len(tail_losses) > 0 else var_threshold
    
    return {
        'var': var_threshold,
        'cvar': cvar,
        'tail_ratio': cvar / var_threshold if var_threshold != 0 else 1,
        'tail_observations': len(tail_losses),
        'total_observations': len(returns)
    }`
  },
  'api-gateway': {
    details: {
      title: 'API Gateway',
      functions: [
        'Provides secure, scalable API access to risk calculations',
        'RESTful APIs with OpenAPI documentation',
        'gRPC for high-performance internal communication',
        'WebSocket for real-time risk updates',
        'GraphQL for flexible data queries',
        'Rate limiting and authentication (OAuth2/JWT)'
      ]
    },
    workflow: `1. Receive API requests from clients
2. Authenticate and authorize requests
3. Route to appropriate microservices
4. Aggregate responses from multiple services
5. Apply rate limiting and caching
6. Return formatted responses`,
    code: `from fastapi import FastAPI, Depends, HTTPException
from fastapi.security import HTTPBearer
import asyncio

app = FastAPI()
security = HTTPBearer()

@app.get("/api/v1/risk/var/{portfolio_id}")
async def get_portfolio_var(
    portfolio_id: str,
    confidence_level: float = 0.95,
    time_horizon: int = 1,
    token: str = Depends(security)
):
    # Authenticate request
    user = await authenticate_token(token)
    
    # Check authorization
    if not user.has_access(portfolio_id):
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Calculate VaR
    var_result = await var_service.calculate_var(
        portfolio_id=portfolio_id,
        confidence_level=confidence_level,
        time_horizon=time_horizon
    )
    
    return {
        "portfolio_id": portfolio_id,
        "var": var_result.value,
        "confidence_level": confidence_level,
        "calculation_timestamp": var_result.timestamp,
        "methodology": var_result.methodology
    }`
  },
  'monitoring': {
    details: {
      title: 'Monitoring',
      functions: [
        'Comprehensive monitoring of system health and performance',
        'Real-time performance metrics collection',
        'Risk limit breach detection and alerting',
        'System health dashboards and visualization',
        'Audit logging and compliance tracking',
        'Distributed tracing for request flows'
      ]
    },
    workflow: `1. Collect metrics from all system components
2. Process and aggregate performance data
3. Check against defined thresholds
4. Generate alerts for limit breaches
5. Update real-time dashboards
6. Store metrics for historical analysis`,
    code: `# Prometheus metrics configuration
from prometheus_client import Counter, Histogram, Gauge
import time

# Define metrics
var_calculations_total = Counter('var_calculations_total', 'Total VaR calculations')
var_calculation_duration = Histogram('var_calculation_duration_seconds', 'VaR calculation time')
risk_limit_breaches = Counter('risk_limit_breaches_total', 'Risk limit breaches', ['portfolio', 'limit_type'])
portfolio_var_current = Gauge('portfolio_var_current', 'Current portfolio VaR', ['portfolio_id'])

# Usage in VaR calculation
@var_calculation_duration.time()
def calculate_portfolio_var(portfolio):
    var_calculations_total.inc()
    
    # Perform calculation
    var_value = perform_var_calculation(portfolio)
    
    # Update current VaR gauge
    portfolio_var_current.labels(portfolio_id=portfolio.id).set(var_value)
    
    # Check limits
    if var_value > portfolio.var_limit:
        risk_limit_breaches.labels(
            portfolio=portfolio.id, 
            limit_type='var'
        ).inc()
        send_alert(f"VaR limit breach: {portfolio.id}")
    
    return var_value`
  }
};